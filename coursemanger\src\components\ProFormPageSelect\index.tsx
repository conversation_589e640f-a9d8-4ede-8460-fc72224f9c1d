import {
  ProFormSelect,
  ProFormSelectProps,
  RequestOptionsType,
} from '@ant-design/pro-components';
import React, { useEffect, useMemo } from 'react';
import { useImmer } from 'use-immer';
import style from './index.less';
type Params = {
  current: number;
  pageSize: number;
  keyWords?: string;
};

/**
 * @description 简单易用的分页select
 */
const ProFormPageSelect: React.FC<ProFormPageSelectProps> = React.memo(
  ({
    pageSize = 40,
    options: propOptions,
    request,
    needScroll = true,
    onDataChange,
    compatibleLowerVersions = false,
    ...props
  }) => {
    const [oldData, setOldData] = useImmer<
      Record<number, RequestOptionsType[]>
    >({});
    const [params, setParams] = useImmer<Params>({
      current: 1,
      pageSize,
      keyWords: '',
    });
    const scrollClass = needScroll ? style.scroll : style.notUse;
    const [total, setTotal] = React.useState<number | undefined>();
    const [haveMore, setHaveMore] = React.useState<boolean>(true);
    // 记录blur和select的时间,以便在search多次触发判断, 小于一定时间不会去更新search
    const lastSelectOrBlurTimeRef = React.useRef<number>(0);
    const [loading, setLoading] = useImmer({
      value: false,
      isFirst: true,
    });
    const result = useMemo(() => {
      const result = [...Object.values(oldData).flat(Infinity)] as {
        label: string;
        value: string;
      }[];
      // 去重
      const obj = result.reduce((prev, cur) => {
        prev[cur.value] = cur;
        return prev;
      }, {} as Record<string, { label: string; value: string }>);
      return Object.values(obj);
    }, [oldData]);
    useEffect(() => {
      requestDataList(params);
    }, [params]);
    const requestDataList = async ({ keyWords, current, pageSize }: Params) => {
      try {
        if (loading.isFirst) {
          setLoading({
            value: true,
            isFirst: false,
          });
        }
        if (!request) {
          return setLoading({
            value: false,
            isFirst: false,
          });
        }
        const { total, data } = await request({
          keyWords,
          current,
          pageSize,
          oldData: Object.values(oldData).flat(Infinity),
        });
        const result = [...Object.values(oldData).flat(Infinity), ...data];
        setTotal(total);
        if (
          (pageSize !== 1 && data.length === 0) ||
          (result.length >= total && total !== 0)
        ) {
          setHaveMore(false);
        }
        setOldData(draft => {
          draft[current] = data;
        });
        setLoading({
          value: false,
          isFirst: false,
        });
        return result;
      } catch (error) {
        console.error(error);
        setLoading({
          value: false,
          isFirst: false,
        });
        return [];
      }
    };
    const options = useMemo(() => {
      return !!request ? result : propOptions;
    }, [request, result, propOptions]);
    useEffect(() => {
      onDataChange?.(options);
    }, [options, onDataChange]);
    const select = (
      <ProFormSelect
        {...props}
        params={params}
        options={options}
        fieldProps={{
          onBlur: e => {
            document.body.classList.remove(style.hidden);
            document.documentElement.classList.remove(scrollClass);
            lastSelectOrBlurTimeRef.current = Date.now();
          },
          onSelect: () => {
            lastSelectOrBlurTimeRef.current = Date.now();
          },
          onDropdownVisibleChange(open) {
            if (!open) {
              document.body.classList.remove(style.hidden);
              document.documentElement.classList.remove(scrollClass);
            }
          },
          onPopupScroll: haveMore
            ? e => {
                // https://www.zhangxinxu.com/study/202001/css-overscroll-behavior-demo.php
                e.stopPropagation();
                e.currentTarget.onmouseover = () => {
                  document.documentElement.classList.add(scrollClass);
                  document.body.classList.add(style.hidden);
                };
                e.currentTarget.onmouseout = () => {
                  document.body.classList.remove(style.hidden);
                  document.documentElement.classList.remove(scrollClass);
                };
                if (!haveMore) return;
                const { target } = e;
                e.nativeEvent.stopImmediatePropagation();
                const { scrollTop, scrollHeight, clientHeight } = target as any;
                if (scrollTop + clientHeight >= scrollHeight - 30) {
                  // 判断是否超过总数
                  if (total && total <= Object.keys(oldData).length) {
                    return;
                  }
                  setParams(draft => {
                    draft.current += 1;
                  });
                }
              }
            : undefined,
          onSearch(value) {
            // 应该是先清空的searchValue 再执行的onSearch 所以需要异步操作
            setTimeout(() => {
              const currentTime = Date.now();
              if (
                compatibleLowerVersions &&
                currentTime - lastSelectOrBlurTimeRef.current < 100
              ) {
                return;
              }
              if (params.keyWords !== value) {
                setLoading({
                  value: false,
                  isFirst: true,
                });
                setParams({
                  current: 1,
                  pageSize,
                  keyWords: value,
                });
                console.log('清空');
                setOldData({});
                props?.fieldProps?.onSearch?.(value);
              }
            }, 0);
          },
          loading: loading.value,
          getPopupContainer: triggerNode => {
            return triggerNode.parentElement;
          },
          ...props?.fieldProps,
        }}
      />
    );
    return select;
  },
);

type ProFormPageSelectProps = Omit<ProFormSelectProps, 'request'> & {
  request?: (params: {
    keyWords?: string;
    current: number;
    pageSize: number;
    oldData: RequestOptionsType[];
  }) => Promise<{
    data: RequestOptionsType[];
    total: number;
  }>;
  pageSize?: number;
  needScroll?: boolean;
  onDataChange?: (data?: RequestOptionsType[]) => void;
  /** 兼容4.0 antd 搜索出现问题 */
  compatibleLowerVersions?: boolean;
};
export default ProFormPageSelect;
ProFormPageSelect.displayName = 'ProFormPageSelect';
