import {
  downloadHomeworkFiles,
  downloadHomeworkFilesCheck,
  getDownLoadFiles,
  getHomeworkDetail,
  getSubmissionDetail,
  submitScore,
} from '@/api/homework';
import FilePreviewModal from '@/components/FilePreviewModal';
import { LeftOutlined, LoadingOutlined } from '@ant-design/icons';
import { Button, Spin, message } from 'antd';
import React, { FC, useEffect, useState } from 'react';
import { useDispatch, useLocation, useSelector } from 'umi';
import AnswerCard from './components/AnswerCard';
import HomeworkCheckItem from './components/HomeworkCheckItem';
import './homeworkDetail.less';
import { ensure, getPreviewType, handleDownload } from './utils/tools';
// @ts-ignore
import ImageEditorModal from '@/components/ImageEditorModal';
import useLocale from '@/hooks/useLocale';
import { getSensitiveWord } from '@/utils';
import $ from 'jquery';
import StudentDoHomework from './StudentDoHomework';
import ImageModal from './components/ImageModal';

interface IHomeworkDetail {
  homeworkDetail: any;
  submissionData: any;
  handleBack: (isRefresh?: boolean) => void;
  query: any;
  hasNext?: boolean;
  homeworkItem: any;
  isStu?: boolean
}

const HomeworkDetail: FC<IHomeworkDetail> = ({
  homeworkDetail,
  submissionData,
  query,
  hasNext = true,
  handleBack,
  homeworkItem,
  isStu
}) => {
  const location: any = useLocation();
  const [submissionList, setSubmissionList] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [topicData, setTopicData] = useState<any>([]);
  const [totalScore, setTotalScore] = useState<number>(0);
  const [subId, setSubId] = useState<string>('');
  const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;
  const [comment, setComment] = useState<string>('');
  const [isLast, setIsLast] = useState<boolean>(false);
  const dispatch = useDispatch();
  const [allSubmissions, setAllSubmissions] = useState<any[]>([]);
  const [curImage, setCurImage] = useState<string>('');
  const [imageVisible, setImageVisible] = useState<boolean>(false);
  const [showSubPage, setShowSubPage] = useState<boolean>(false);
  const [editorConfig, setEditorConfig] = useState<any>({});
  const [answers, setAnswers] = useState<any>({});
  const [answerCorrect, setAnswerCorrect] = useState<any>({});
  const [teacherFiles, setTeacherFiles] = useState<any>({});
  const { t } = useLocale();
  const files = useSelector<any, any>(state => state.homework.files);

  useEffect(() => {
    $(document).on(
      'click',
      '.homework-check-item .answer-item:not(.topic-answer-item) img, .homework-check-item .parse-item img',
      (e: any) => {
        setCurImage(e.target.src);
        setImageVisible(true);
      },
    );
    return () => {
      $(document).off(
        'click',
        '.homework-check-item .answer-item:not(.topic-answer-item) img, .homework-check-item .parse-item img',
      );
    };
  }, []);

  useEffect(() => {
    if (Date.now() < homeworkDetail.closeTime && homeworkDetail.resubmit) {
      message.info(t('本作业允许重复提交，请待截止日期后再做批改'));
    }
    if (submissionData.id) {
      dispatch({
        type: 'homework/handlePublicChange',
        payload: {
          submissionData,
        },
      });
    }
    setSubId(submissionData.id);
    handleGetHomeworkDetail(submissionData.homeworkId || submissionData.homeId);
  }, [submissionData]);

  useEffect(() => {
    if (subId) {
      getSubmission();
    }
  }, [subId]);

  useEffect(() => {
    if (Reflect.ownKeys(answers ?? {}).length > 0) {
      ensure(
        () =>
          !(
            Reflect.ownKeys(answers).some((item: any) =>
              answers[item][0].includes('img'),
            ) &&
            $('.subjective-topic-answer img:not(.homework-img-wrp img)')
              .length === 0
          ),
        () =>
          $('.subjective-topic-answer img:not(.homework-img-wrp img)').wrap(
            '<div class="homework-img-wrp" />',
          ),
        100,
      );
    }
  }, [answers]);

  const getSubmission = () => {
    getSubmissionDetail(subId).then((res: any) => {
      if (res.status === 200) {
        setSubmissionList(res.data);
        const aws = res.data?.answers ?? {};
        const temp: any = {};
        Reflect.ownKeys(res.data.answersExtends ?? {}).forEach((key: any) => {
          temp[key] = res.data.answersExtends[key].map(
            (item: any) => item.correct,
          );
        });
        setTeacherFiles(res.data?.teacherAttachment ?? {});
        setAnswers(aws);
        setAnswerCorrect(temp);
        // const scoresArr = Object.values(res.data.scores ?? {});
        // const total: number = scoresArr.length > 0 ? (scoresArr.reduce((pre: any, cur: any) => pre += cur) as number) : 0;
        setComment(res.data.comment ?? "");
        setTotalScore(res.data?.score ?? 0);
        dispatch({
          type: 'homework/handlePublicChange',
          payload: {
            files: res.data.hasAttachments ?? {},
          },
        });
        dispatch({
          type: 'homework/handlePublicChange',
          payload: {
            submissionData: {
              ...res.data,
            },
          },
        });
      }
    });
  };

  const handleGetHomeworkDetail = (id: string) => {
    setLoading(true);
    getHomeworkDetail(
      'resource',
      id,
      homeworkItem.parentId,
      location.query.id,
    ).then((res: any) => {
      if (res.status === 200) {
        setTopicData(
          res.data.questions.map((item: any, serialNo: number) => {
            item.question?.questions_options?.map(
              (item_: any, index: number) => {
                item_.seq = index + 1;
                item_.content = item_.content;
              },
            );
            return {
              ...item.question,
              serialNo: serialNo + 1,
              topicId: item.question?.id,
              ...item,
            };
          }),
        );
      }
      setLoading(false);
    });
  };
  const onScoreChange = (score: string, id: string) => {
    setSubmissionList({
      ...submissionList,
      scores: {
        ...submissionList.scores,
        [id]: score == '' ? '' : Number(score),
      },
    });
    const scoresArr = Object.values({
      ...submissionList.scores,
      [id]: Number(score),
    });
    const total: number = scoresArr.reduce(
      (pre: any, cur: any) => (pre += Number(cur)),
    ) as number;
    setTotalScore(total);
  };
  const onCommentChange = (comment: string, id: string) => {
    setSubmissionList({
      ...submissionList,
      question_comments: {
        ...submissionList.question_comments,
        [id]: comment ?? '',
      },
    });
  };
  const handleSubmit = (
    comment: string,
    isBack: boolean = false,
    checkNext: boolean = false,
  ) => {
    setLoading(true);
    const { homeworkId, homeId } = submissionData;
    if (Date.now() < homeworkDetail.closeTime && homeworkDetail.resubmit) {
      message.info(t('本作业允许重复提交，请待截止日期后再做批改'));
      setLoading(false);
      return Promise.resolve();
    } else {
      return getSensitiveWord(
        comment +
          Object.values(submissionList.question_comments ?? {}).join(''),
        '评语',
        () => {
          return submitScore(subId, {
            checkNext,
            courseId: location.query.id,
            order: query.order,
            orderType: query.orderType,
            parentId: homeworkItem.parentId,
            checkScores: submissionList.scores,
            homeworkId: homeworkId || homeId,
            comment,
            question_comments: submissionList.question_comments,
            answers,
            hasAttachments: files,
            teacherAttachment: teacherFiles,
          })
            .then((res: any) => {
              if (res.status === 200) {
                message.success(t('批改成功'));
                dispatch({
                  type: 'updata/changeQAlayout',
                });
                if (isBack) {
                  handleBack(true);
                }
                return res;
              } else {
                message.error(res.message);
                throw new Error();
              }
            })
            .finally(() => {
              setLoading(false);
            });
        },
        () => {
          setLoading(false);
          return Promise.reject('error');
        },
      );
    }
  };

  const handleCheckNext = (comment: string) => {
    // const hasSubmission = allSubmissions.filter((item: any) => item.submitState === 2);
    handleSubmit(comment, false, true).then((res: any) => {
      if (res.status === 200) {
        if (res.data?.record) {
          setSubId(res.data.record?.id);
        } else {
          message.info(t('当前所有已提交作业已批改完毕'));
          setIsLast(true);
        }
      }
    });
  };

  const downloadFiles = () => {
    const param = {
      courseId: location.query.id,
      homeworkId: submissionData.homeworkId || submissionData.homeId,
      parentId: homeworkItem.parentId,
      stuCode: submissionData.stuCode,
    };
    downloadHomeworkFilesCheck(param).then((res: any) => {
      if (res.status === 400) {
        message.error(res.message);
      } else {
        downloadHomeworkFiles(param).then((res: any) => {
          if (res?.status == 400) {
            message.error(res.message);
            return
          } 
          const timer = setInterval(() => {
            getDownLoadFiles({ scheduleId: res.data }).then((res: any) => {
              if (res.status === 200) {
                if (res.data.status == 2) {
                  clearInterval(timer);
                  let link = document.createElement('a');
                  // link.target = '_blank';
                  link.style.display = 'none';
                  link.href = res.data.tempDir;
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                } else if (res.data.status === 0) {
                  message.error(t('附件下载失败'));
                }
              }
            });
          }, 500);
        });
      }
    });
  };

  const [previewVisible, setPreviewVisible] = useState<boolean>(false);
  const [imageEditorVisible, setImageEditorVisible] = useState<boolean>(false);
  const [currentFile, setCurrentFile] = useState<any>({});
  const { fileMap } = useSelector<Models.Store, any>(state => state.global);

  const handlePreview = (file: any) => {
    const { attachmentSource, attachmentName } = file;
    setCurrentFile({
      filePath: attachmentSource,
      type: getPreviewType(attachmentSource, fileMap),
      extraData: attachmentName,
    });
    setPreviewVisible(true);
  };

  const handleImageClick = (
    id: string,
    answer: any,
    target: any,
    index: number,
  ) => {
    const anSrc = target.getAttribute('data-annotate-src');
    setEditorConfig({
      htmls: answer?.[0] ?? '',
      target,
      src: anSrc ? anSrc : target.src,
      id,
      index,
      originSrc: anSrc ? target.src : undefined,
    });
    setImageEditorVisible(true);
  };
  const handleAnnotation = (anSrc?: string, isReset?: boolean) => {
    if (isReset) {
      editorConfig.target.removeAttribute('data-annotate-src');
    } else {
      editorConfig.target.setAttribute('data-annotate-src', anSrc);
    }
    const html = $(
      `.question-item-${editorConfig.index} .subjective-topic-answer`,
    ).html();
    setTimeout(() => {
      $('.subjective-topic-answer img:not(.homework-img-wrp img)').wrap(
        '<div class="homework-img-wrp" />',
      );
    });
    setAnswers({
      ...answers,
      [editorConfig.id]: [html],
    });
    setImageEditorVisible(false);
  };

  // 主观题且未批改时，最终得分应该显示为“-”
  const isAllScored = Object.values(submissionList?.scores || {})?.filter((item: any) => typeof item === 'number')
  return (
    <Spin indicator={antIcon} spinning={loading}>
      {showSubPage ? (
        <StudentDoHomework
          homeworkItem={homeworkItem}
          handleBack={() => {
            setShowSubPage(false);
            handleBack();
          }}
          stuCode={submissionList.stuCode}
        />
      ) : (
        <div className="homeworkDetail-container">
          {/* <AnswerCard questions={topicData} answers={submissionList.answers} /> */}
          <div className="header-container">
            <div className="back-btn" onClick={() => handleBack()}>
              <LeftOutlined />
              {t('返回')}
            </div>
            <div className="top-btn">
              <div className="person-grade">
                <div className="person">
                  {t('提交人员：')}
                  <span>
                    {submissionList.stuName}（{submissionList.stuCode}）
                  </span>
                </div>
                <div className="grade">
                  {t('最终得分：')}
                  <span>
                    {isAllScored?.length === topicData?.length ? totalScore + t('分') : '-'}
                  </span>
                </div>
              </div>
                  <Button type="primary" onClick={downloadFiles}>
                    {t('下载附件')}
                  </Button>
            </div>
          </div>
          <div className="content-container">
            {topicData.map((item: any, index: number) => (
              <HomeworkCheckItem
                isStu={isStu}
                key={index}
                index={index + 1}
                data={item}
                recordId={subId}
                teacherFiles={teacherFiles[item.id]}
                submissionList={submissionList}
                comment={submissionList.question_comments?.[item.id]}
                answer={answers?.[item.id]}
                isCorrect={answerCorrect[item.id]}
                score={submissionList.scores?.[item.id]}
                onTeacherFilesChange={(files: any) => {
                  setTeacherFiles({
                    ...teacherFiles,
                    [item.id]: files
                  })
                }}
                onChange={onScoreChange}
                onDownload={handleDownload}
                onPreview={handlePreview}
                onCommentChange={onCommentChange}
                onImageClick={(target: any) =>
                  handleImageClick(
                    item.id,
                    answers?.[item.id],
                    target,
                    index + 1,
                  )
                }
              />
            ))}
          </div>
          {/* <div className="btn-container">
        <Button type="primary" onClick={handleSubmit}>确认</Button>
        <Button onClick={handleCheckNext}>确认并批改下一份</Button>
        </div> */}
          <AnswerCard
            isLast={isLast}
            isTeacher={ isStu ? false : true}
            hasNext={hasNext}
            answers={answers}
            questions={topicData}
            scores={submissionList.scores}
            comment={comment}
            onSubmit={handleSubmit}
            correctObj={answerCorrect}
            onHandOver={() => {
              dispatch({
                type: 'homework/handlePublicChange',
                payload: {
                  stuCode: submissionList.stuCode,
                },
              });
              setShowSubPage(true);
            }}
            onSubmitNext={allSubmissions ? handleCheckNext : undefined}
            onBack={handleBack}
          />

          <FilePreviewModal
            visible={previewVisible}
            onClose={() => setPreviewVisible(false)}
            file={currentFile}
            fileType={currentFile.type}
          />
          <ImageModal
            image={curImage}
            visible={imageVisible}
            onClose={() => setImageVisible(false)}
          />
          <ImageEditorModal
            src={editorConfig.src ?? ''}
            visible={imageEditorVisible}
            originSrc={editorConfig.originSrc}
            onClose={() => {
              setImageEditorVisible(false);
              setEditorConfig({});
            }}
            onConfirm={handleAnnotation}
          />
        </div>
      )}
    </Spin>
  );
};
export default HomeworkDetail;
