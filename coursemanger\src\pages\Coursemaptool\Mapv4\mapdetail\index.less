.mapv4_mapdetail_view{
    position: relative;
    width: 100%;
    height: 100%;
    background-image: url('../../../../assets/imgs/coursemap/v4/map_bg1.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    .custom_drawer{
        .ant-drawer-body{
            padding: 0 !important;
            padding-left: 20px !important;
        }
    }
    .ant-drawer .arrow{
        transform: translateY(-50%);
        top: 50%;
        left: 0;
        position: absolute;
        cursor: pointer;
    }
    .mapName_span{
        position: absolute;
        font-weight: 550;
        font-size: 28px;
        color: #333333;
        top: 10px;
        left: 30px;
    }
    .tabs_nav > .ant-tabs-nav{
        height:49px;

        .ant-tabs-tab-btn{
            font-size: 20px;
        }
    }

    .ant-tabs-top > .ant-tabs-nav::before{
        border-bottom: none !important;
    }

    .ant-tabs-content{
        width: 100%;
        height: 100%;

        .ant-tabs-tabpane{
            width: 100%;
            height: 100%; 
        } 
    }
    .ant-tabs-top > .ant-tabs-nav{
        margin:0 !important;
    }

    .render_view{
        position: relative;
        width: 100%;
        height: 100%;

        .jindu_box{
            position: absolute;
            width: 614px;
            height: 77px;
            background: rgba(255, 255, 255, 1);
            border-radius: 10px;
            top:10px;
            left: 30px;
            z-index: 1;
            display: flex;
            justify-content: space-evenly;
            
        }

        .right_box{
            position: absolute;
            right: 20px;
            top: 18px;
            width: 650px;
            height: 58px;
            background: #FFFFFF;
            border-radius: 10px;

            display: flex;
            align-items: center;
            justify-content: space-evenly;
            z-index: 1;
        }

        
        .node_detail_view{

                .ant-drawer-header{
                    padding: 0 !important;
                    
                }
            
                .ant-drawer-content{
                    background-color: transparent !important;
                }

                .ant-drawer-body{
                    background-color: #fff;
                }

                .ant-drawer-content-wrapper{
                    border-top-left-radius: 20px;
                }
        }


    }
}

.microMajor_detail_background {
    background-image: none !important;

    .render_view .jindu_box, .render_view .right_box, .render_view .node_detail_view .ant-drawer-body {
        background: rgba(255,255,255,0.6);
        box-shadow: 0px 10px 20px 0px rgba(119,110,245,0.05);
        border-radius: 10px;
        border: 2px solid #FFFFFF;
        backdrop-filter: blur(100px);

    }
}