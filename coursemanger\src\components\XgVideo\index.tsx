import React, { FC, useContext, useEffect, useRef, useState } from "react";
import Player from 'xgplayer';
import { CloseOutlined } from '@ant-design/icons';
import { useSelector, history } from 'umi';
import { IGlobal } from '@/models/global';
import { weaterMaskCanvasFn } from './waterMark';
import VideoPauseDemo from "./videoPauseDemo";

const FlvPlayer = require('xgplayer-flv');
// const Mp4Player = require('xgplayer-mp4');
const HlsPlayer = require('xgplayer-hls');
import "./index.less";
import { message } from "antd";
import { videoContext } from "../ResourcePreviewModal";
// @ts-ignore
import $ from "jquery";
import { RmanService } from "@/api/RmanService";
import { WebVTTGenerator } from "@/utils/vtt";
import { Utils } from "@/utils/utils";

export interface IXgVideo {
  url: string;
  id?: string;
  isAutoplay?: boolean;
  pip?: boolean;
  selectNode?: any;
  knowledge?: any;
  onListener?: any;
  centerednode?: (id: string) => void;
  onUpdate?: (currentTime: number, total?: number) => void;
  cover?: string;
  finishStatus?: boolean;  // 是否完成学习（即是否是第一次学习）
  seekTime?: number;  // 添加缺失的属性
}
interface IProgress {
  current: number;
  total: number;
  onSeek: (currentTime: number) => void;
  knowledge: any;
}
const TRANS_CONSTANTS = Math.pow(10, 7);
const addZero = (value: number) => `${value > 9 ? value : `0${value}`}`;
const dealTime = (time: number) => {
  if (time < 60) {
    return `00:${addZero(Math.ceil(time))}`;
  } else {
    const min = Math.floor(time / 60);
    const seconds = Math.ceil(time - min * 60);
    return `${addZero(min)}:${addZero(seconds)}`;
  }
};

const drawPoints = (pointIns: number[], duration: number, videoId: string = "videoElement") => {
  const dom = $(`#${videoId}`).find('.xgplayer-progress');
  if (dom.find('.marks').length) {
    $(`#${videoId} .marks`).remove();
  }
  if (pointIns?.length) {
    //绘制进度条小圆点
    let parent = document.createElement('div');
    parent.setAttribute('class', 'marks');
    pointIns.map((p: number) => {
      let item = document.createElement('div');
      item.style.width = '6px';
      item.style.height = '6px';
      item.style.borderRadius = '50%';
      item.style.backgroundColor = '#e31106';
      item.style.position = 'absolute';
      item.style.left = p / Math.pow(10, 7) / duration * 100 + '%';
      parent.appendChild(item);
    });
    dom.append(parent);
  }
};

const Progress: FC<IProgress> = ({ current, total, onSeek, knowledge }) => {
  if (knowledge?.inpoint && knowledge?.outpoint) {
    return (
      <div className="r-prg-wrp">
        <div className="r-prg-outer" onMouseEnter={() => { }} onMouseUp={(e: any) => {
          e.stopPropagation();
          const otherWidth = document.getElementsByClassName("r-prg-wrp")[0].getBoundingClientRect().left; // 父级到浏览器窗口左侧的位置
          const width = document.getElementsByClassName("r-prg-wrp")[0].clientWidth; // 父级的总长
          const newPoint = (e.clientX - otherWidth) / width * total;
          onSeek(newPoint);
        }}>
          <div className="r-prg-played" style={{ width: `${Math.min(Number((current / total).toFixed(6)) * 100, 100)}%` }}>
            <div className="r-prg-btn" style={{ left: `${Math.min(Number((current / total).toFixed(6)) * 100, 100)}%` }}></div>
          </div>
        </div>
      </div>);

  } else {
    return null;
  }
};
//百纳秒转换时分秒
const l100Ns2Hms = (l100Ns: any) => {
  const dHour = Math.floor(l100Ns / (3600 * Math.pow(10.0, 7)));
  let llResidue: any = l100Ns % (3600 * Math.pow(10.0, 7));
  const dMin = Math.floor(llResidue / (60 * Math.pow(10.0, 7)));
  llResidue = llResidue % Math.floor(60 * Math.pow(10.0, 7));
  const dSec = Math.ceil(llResidue / Math.pow(10.0, 7));
  // 补0
  const addZero = (value: number) => `${value > 9 ? value : `0${value > 0 ? value : 0}`}`;
  return `${addZero(dHour)}:${addZero(dMin)}:${addZero(dSec)}`;
};

// 秒换算成时分秒
const seconds2Hms = (seconds: any) => {
  const dHour = Math.floor(seconds / 3600);
  let llResidue: any = seconds % 3600;
  const dMin = Math.floor(llResidue / 60);
  llResidue = llResidue % 60;
  const dSec = Math.ceil(llResidue);
  // 补0
  const addZero = (value: number) => `${value > 9 ? value : `0${value > 0 ? value : 0}`}`;
  return `${addZero(dHour)}:${addZero(dMin)}:${addZero(dSec)}`;
};

// 自定义时间组件
const Time: FC<any> = ({ current, knowledge }) => {
  if (knowledge?.inpoint && knowledge?.outpoint) {
    return (
      <div className="time_box">
        <span style={{ color: '#fff' }}>{seconds2Hms(current)} / </span>
        <span style={{ color: 'hsla(0,0%,100%,.5)' }}>{l100Ns2Hms(knowledge.outpoint - knowledge.inpoint)}</span>
      </div>);

  } else {
    return null;
  }
};

// 生成视频唯一标识符
const generateVideoKey = (url: string, inpoint?: number, outpoint?: number) => {
  return `video_${url}_${inpoint || 0}_${outpoint || 0}`;
};

// 获取存储的播放位置
const getStoredPlayTime = (key: string): number | null => {
  const stored = sessionStorage.getItem(key);
  return stored ? parseFloat(stored) : null;
};

// 存储播放位置
const storePlayTime = (key: string, time: number) => {
  sessionStorage.setItem(key, time.toString());
};

const XgVideo: FC<IXgVideo> = ({ url, id, isAutoplay = true, centerednode, selectNode, seekTime, finishStatus, pip, knowledge, onListener, onUpdate, cover, ...rest }) => {
  const playerRef = useRef<any>(null);
  const videoRef = useRef<any>(null);
  const [knowledgePointModal, setKnowledgePointModal] = useState<any>({
    open: false,
    loading: false,
    data: null,
  });

  const [currentTime, setCurrentTime] = useState<number>(0);
  const [knowledgeTotalTime, setKnowledgeTotalTime] = useState<number>(0);
  const { userInfo } = useSelector<any, IGlobal>((state) => state.global);
  const { courseDetail } = useSelector<Models.Store, any>(
    (state) => state.moocCourse);

    const { location } = history;

  const entityData = courseDetail?.entityData ?? { ratio: 0 };
  const userOnlyStudent = (userInfo?.roles?.length === 1 && userInfo?.roles?.[0]?.roleCode === 'r_student') || location?.query?.isJoin === 'true';

  const { topics } = useContext(videoContext);

  // 重置视频卡片数据，避免卡片数据混乱
  const resetVedioCardData = (params?: any) => {
    setKnowledgePointModal({
      open: false,
      loading: false,
      data: null,
      ...params
    })
  }




  // 生成视频唯一标识符
  const videoKey = generateVideoKey(url, knowledge?.inpoint, knowledge?.outpoint);

  useEffect(() => {
    if (topics.length > 0 && playerRef.current?.duration) {
      const pointIns = topics.map((item: any) => item.pointIn);
      drawPoints(pointIns, playerRef.current?.duration, id);
    }
  }, [topics, playerRef.current?.duration]);

  useEffect(() => {
    let listenerState = false;
    let isMounted = true;

    const initPlayer = async () => {
      if (!isMounted) return;

      await unmount();

      if (!isMounted) return;

      const nativeTextTrack = [];
      try {
        const { inpoint = 0, outpoint, contentId } = knowledge ?? {};

        if (!knowledge || !contentId) {
          throw new Error('knowledge is null');
        }
        const response = await RmanService.voiceSelect({
          contentId,
          inpoint,
          outpoint,
          "metadataType": "model_sobey_smart_voice_",
        });

        const texts = (response?.data?.data?.data ?? [])?.[0]?.metadata;
        if (texts && texts.length > 0) {
          const chineseUrl = WebVTTGenerator.generateVTTUrl(
            texts.map(
              item => {
                const { _in, _out, text } = item;
                return {
                  start: Utils.bn2s(_in),
                  end: Utils.bn2s(_out),
                  text
                }
              }
            )
          );
          // 检查DOM中是否已存在字幕元素，防止重复创建
            if (!document.querySelector(`#${id ?? 'videoElement'} track[label="简体中文"]`)) {nativeTextTrack.push({
            src: chineseUrl,
            label: '简体中文',
            default: true
          });
        }
      }} catch (error) {
        console.error(error);}

        // 确保清除之前的video元素
        if (videoRef.current) {
          const existingVideos = videoRef.current.querySelectorAll('video');
          if (existingVideos.length > 1) {
            // 保留第一个video元素，移除其他的
            for (let i = 1; i < existingVideos.length; i++) {
              existingVideos[i].remove();
            }
          }
      }

      if (!isMounted) return; // 再次检查组件是否已卸载

      const newUrl = url?.includes('http') ? url : `${document.location.origin}${url}`;

      // 获取存储的播放位置
      const storedPlayTime = getStoredPlayTime(videoKey);

      const option: any = {
        ...rest,
        playsinline: true,
        url: newUrl,
        autoplay: isAutoplay,
        nativeTextTrack,
        lang: 'zh-cn',
        pip: pip,
        fluid: true,
        // fitVideoSize: 'auto',
        poster: cover,
        screenShot: {
          saveImg: true,
          quality: 0.92,
          type: 'image/png',
          format: '.png'
        },
        flvOptionalConfig: {
          fixAudioTimestampGap: false,
          enableWorker: true,
          enableStashBuffer: true,
          stashInitialSize: 10240,
          reuseRedirectedURL: true,
          autoCleanupSourceBuffer: false,
          lazyLoad: false,
          deferLoadAfterSourceOpen: false
        },
        ignores: knowledge?.inpoint && knowledge?.outpoint ? ['progress', 'time'] : [],
        lastPlayTime: storedPlayTime || (knowledge?.inpoint ? Number((knowledge?.inpoint / TRANS_CONSTANTS).toFixed(6)) : 0),
        lastPlayTimeHideDelay: 5
      };
      option.el = videoRef.current;

      if (knowledge?.inpoint && knowledge?.outpoint) {
        option.lastPlayTime = Number((knowledge?.inpoint / TRANS_CONSTANTS).toFixed(6));
      }

      if (!isMounted) return; // 再次检查组件是否已卸载

      if (url.includes('.flv')) {
        playerRef.current = new FlvPlayer(option);
      } else if (url.includes('.m3u8')) {
        playerRef.current = new HlsPlayer(option);
      } else if (url.includes(".mp4")) {
        playerRef.current = new Player(option);
      } else {
        message.error("暂不支持该格式视频！");
        return;
      }

      // 设置事件监听
      setupPlayerListeners();
    };

    const setupPlayerListeners = () => {
      if (!playerRef.current) return;

      const percentComplete = (listenerState: boolean): boolean => {
        let status = listenerState;
        if (playerRef) {
          const videoDuration = playerRef.current?.duration || 0;
          const videoCurrentTime = playerRef.current?.currentTime || 0;

          if (!entityData?.ratio) {
            videoCurrentTime === videoDuration && onListener && onListener('ended');
            status = true;
          }

          if (entityData?.ratio && videoCurrentTime / videoDuration >= entityData.ratio / 100 && !status) {
            onListener && onListener('ended');
            status = true;
          }
        }
        return status;
      };

      playerRef.current.on('play', (e: any) => {
        // console.info('开始播放啦或则  继续播放啦~~~~~', e.video.currentTime, e.video.duration)
          // 继续播放时，关闭推荐知识点弹窗
          if (e.video.currentTime > 0) {
            resetVedioCardData();
          }
        const silderDom: any = document.getElementsByClassName("xgplayer-progress")[0];
        silderDom && userOnlyStudent && !finishStatus && entityData.allowed_drag && (silderDom.style.display = "none");

        entityData.showWatermark && userOnlyStudent && weaterMaskCanvasFn({
          text: `${userInfo.nickName}-${userInfo.userCode}`,
          opacity: '.5',
          fontSize: '18',
          color: '#FDFDFD',
          rotate: '-30',
          len: 6,
        });

          // 父元素容器。注意，这里是放置水印的父容器。
          // 视频水印元素监听：不允许用户操作元素，防止手动删除水印(销毁移除监听)
          const fatherDom: any = document.getElementById('water-mark');
          entityData.showWatermark && userOnlyStudent && fatherDom && observer.observe(fatherDom, {
            childList: true, // 子节点的变动
            attributes: true, // 属性的变动
            subtree: true, // 所有后代节点的变动
            attributeOldValue: true, // 表示观察attributes变动时，是否需要记录变动前的属性
            characterData: true, // 节点内容或节点文本的变动
            characterDataOldValue: true, // 表示观察characterData变动时，是否需要记录变动前的值
          })
        });

        // 当视频点击暂停且未播放完时，弹开 推荐知识点弹窗
        playerRef.current.on('pause', (e: any) => {
          // console.info('暂停住啦~~~~~', e.video.currentTime, e.video.duration)
          if (e.video.currentTime !== e.video.duration) {
            /**
             * 当前data为演示假数据，需根据暂停的这一帧页面，通过后端接口匹配相应知识点展示，此处仅为演示效果，按需修改。
             */
            resetVedioCardData({ loading: true }); // 请求前：重置清空卡片数据；

            // 请求数据：
            // setKnowledgePointModal({
            //   open: true,
            //   loading: false,
            //   data: {
            //     selectNodeName: selectNode?.label,
            //     knowLedgePoints: [
            //       { name: '知识点1', id: "5526603a3aaa419bb9abf80ab7d54cad" },
            //       { name: '知识点2', id: "1751c38936ba43cfa42c702780327425" },
            //       { name: '知识点3', id: "567334a11ccb4f17b899931a18b06eac" },
            //       // { name: '知识点4', id: "5526603a3aaa419bb9abf80ab7d54cad" },
            //       // { name: '知识点5', id: "1751c38936ba43cfa42c702780327425" },
            //       // { name: '知识点6', id: "567334a11ccb4f17b899931a18b06eac" },
            //     ]
            //   }
            // })
          }
        });
        // --------------  演示功能结束end  ---------------



      playerRef.current.on('ended', (e: any) => {
        if (onListener) {
          onListener('ended');
        }
      });

      if (seekTime) {
        playerRef.current.currentTime = seekTime;
      }

      if (knowledge?.inpoint || knowledge?.outpoint) {
        const total = Number(((knowledge.outpoint - knowledge.inpoint) / TRANS_CONSTANTS).toFixed(6));
        setKnowledgeTotalTime(total);

        playerRef.current.on("timeupdate", (e: any) => {
          if (!isMounted) return; // 检查组件是否已卸载

          const cur = Number((e.video.currentTime - knowledge.inpoint / TRANS_CONSTANTS).toFixed(6));
          setCurrentTime(cur);
          onUpdate?.(cur, total);

          // 存储播放位置
          storePlayTime(videoKey, e.video.currentTime);

          if (e.video.currentTime < knowledge.inpoint / TRANS_CONSTANTS) {
            playerRef.current.currentTime = knowledge.inpoint / TRANS_CONSTANTS;
          }
          if (e.video.currentTime > knowledge.outpoint / TRANS_CONSTANTS) {
            playerRef.current.pause();
            if (onListener) {
              onListener('ended');
            }
            playerRef.current.currentTime = knowledge.inpoint / TRANS_CONSTANTS;
          }
        });
      } else {
        playerRef.current.on("timeupdate", (e: any) => {
          if (!isMounted) return; // 检查组件是否已卸载

          const status = percentComplete(listenerState);
          listenerState = status;
          onUpdate?.(e.video.currentTime, e.video.duration);

          // 存储播放位置
          storePlayTime(videoKey, e.video.currentTime);
        });
      }
    };

    initPlayer();

    return () => {
      isMounted = false; // 标记组件已卸载
      unmount();
    };
  }, [url, knowledge?.inpoint, knowledge?.outpoint]); // 只依赖inpoint和outpoint的变化

  //注销组件
  const unmount = () => {
    // 停止观察observer
    observer?.disconnect();
    // 返回一个 promise，当播放器销毁完成后，resolve
    return new Promise((resolve) => {
      if (playerRef.current != null) {
        // 先销毁
        try {
          playerRef.current?.pause();
          if (playerRef.current.flv) {
            playerRef.current.flv.destroy().then(() => {
              resolve(true);
            });
          }
          if (playerRef.current.hls) {
            playerRef.current.hls.destroy().then(() => {
              resolve(true);
            });
          }
          playerRef.current?.destroy(true);
          playerRef.current = null;
          resolve(true);
        } catch (err) {
          resolve(true);
        }
      } else {
        resolve(true);
      }
    });
  };

  const onSeek = (currentTime: any) => {
    playerRef.current.currentTime = currentTime + knowledge.inpoint / TRANS_CONSTANTS;
    onUpdate?.(currentTime);
    setCurrentTime(currentTime);
  };


  let observer = new MutationObserver((mutations, config) => {
    mutations.forEach((mutation) => {
      if (mutation.attributeName === "style") {
        // console.log('div水印样式被修改');
        weaterMaskCanvasFn({
          text: `${userInfo.nickName}-${userInfo.userCode}`,
          opacity: '.5',
          fontSize: '18',
          color: '#FDFDFD',
          rotate: '-30',
          len: 6,
        })
      }

    })
  })

  return (
    <div id="video-dom" style={{ width: "100%" }} key={`${id ?? 'videoElement'}-${knowledge?.inpoint}-${knowledge?.outpoint}`}>
      <div ref={videoRef} id={id ?? 'videoElement'} className="video-container"></div>
      <VideoPauseDemo knowledgePointModal={knowledgePointModal} centerednode={centerednode} canclePauseCard={resetVedioCardData} />
      <Progress current={currentTime} total={knowledgeTotalTime} onSeek={onSeek} knowledge={knowledge} />
      <Time current={currentTime} knowledge={knowledge}></Time>
    </div>);

};

export default XgVideo;
