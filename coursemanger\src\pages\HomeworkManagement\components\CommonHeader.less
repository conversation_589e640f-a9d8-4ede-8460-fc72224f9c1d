.common-header {
  margin-bottom: 10px;

  .homework-header-style {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .title {
    display: inline-block;
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 5px;
    color: var(--primary-color);
    &.preview {
      cursor: pointer;
      text-decoration: underline;
    }
  }
  .no-show-from-box {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .info {
      margin: 0;
    }
    .grade-time {
      margin-right: 0;
    }
  }

  .info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 28px;
  }

  .from {
    flex: 1;
    //line-height: 36px;
    color: #333;
    font-size: 14px;
    display: flex;
    gap: 40px;
    color: rgba(0,0,0,0.5);
     p {
       margin: 0 !important;
       word-wrap: break-word;
     }
  }

  .grade-time {
    //width: 388px;
    //height: 36px;
    background: #F3F7FF;
    border-radius: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    padding: 0 18px;
    color: #333;

    span {
      font-weight: 500;
      color: #3C3C3C;
    }
  }
}
@media  screen and (max-width: 768px) {
  .common-header {
    .from {
      //line-height: 20px;
      color: #333;
      font-size: 14px;
      display: flex;
      //flex-direction: column;
      justify-content: space-between;
      align-items: flex-start;
      gap: 10px;
      color: rgba(0,0,0,0.5);
      flex-wrap: wrap;
    }
    .grade-time {
      background: #F3F7FF;
      border-radius: 25px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      font-size: 16px;
      padding: 0 20px;
      color: #333;

      span {
        font-weight: 500;
        color: #3C3C3C;
      }
    }
  }

}
