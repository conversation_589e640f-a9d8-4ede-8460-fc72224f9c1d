import React, { FC, useState, useEffect, useImperativeHandle, forwardRef, useMemo } from 'react';
import './index.less';
import { getSeting, updateSeting, getCertification, updateCertificationSetting, queryCourseManager, reqImportCourse } from '@/api/learnSet';
import { useSelector, useDispatch, useHistory, Prompt } from 'umi';
import { InputNumber, Button, message, Checkbox, Slider, Select, Space, Tooltip, Modal } from 'antd';
import CertificateSetting from './components/CertificateSetting';
import { getteacherlist } from '@/api/teacher';
import { queryColleges } from '@/api/course';
import CourseDownloadModal from './components/CourseDownloadModal';
import { CUSTOMER_NPU } from '@/permission/moduleCfg';
import useLocale from '@/hooks/useLocale';
import StudyRangeModal from './components/StudyRangeModal';
import { omit, pick } from 'lodash';
import StepComponent from './components/StepComponent';
import ImportAndCopyModal from './components/ImportAndCopyModal';

export interface ILearningRef {
  canPageEdit: boolean;
  setCanPageEdit: React.Dispatch<React.SetStateAction<boolean>>;
}

const LearningSettings = forwardRef<ILearningRef, {}>((props, ref) => {
  useImperativeHandle(ref, () => ({
    canPageEdit,
    setCanPageEdit
  }));
  const { t } = useLocale();
  const history: any = useHistory();
  const [video, setVideo] = useState<boolean>(true);
  const [download, setDownload] = useState<number>(0);
  const [downloadSettingVisible, setDownloadSettingVisible] = useState<boolean>(false);
  const [finishTime, setFinishTime] = useState<number>(0);
  const [maxStuNum, setMaxStuNum] = useState<number>(200);
  const [isNumberStudents, setIsNumberStudents] = useState<boolean>(true);
  const [maxStuTip, setMaxStuTip] = useState<string>("");
  const [unLoginShow, setUnLoginShow] = useState<boolean>(false);
  const [showWatermark, setShowWatermark] = useState<boolean>(false);
  const [allowedDrag, setAllowedDrag] = useState<boolean>(false);
  const [allowedDoubleSpeed, setAllowedDoubleSpeed] = useState<boolean>(false);
  const [isAllowSit, setAllowSit] = useState<boolean>(false);
  const [showCertificate, setShowCertificate] = useState<boolean>(false);
  const [shoCurriculum, setShoCurriculum] = useState<boolean>(true);
  const [certificateVisible, setCertificateVisible] = useState<boolean>(false);
  const [teacherList, setTeacherList] = useState<any[]>([]);
  const [courseFinishContinueLearning, setCourseFinishContinueLearning] = useState<boolean>(false);
  const [isTeacher,setIsTeacher] = useState<boolean>(false);
  const [isStudent,setIsStudent] = useState<boolean>(false);
  const [colleges, setColleges] = useState<any>([]);
  const [courseManagerJurisdiction, setCourseManagerJurisdiction] = useState<boolean>(false);
  const [canPageEdit, setCanPageEdit] = useState<boolean>(false);
  const [studyRange, setStudyRange] = useState<number>(1);
  const [studyComment, setstudyComment] = useState<number>(0);
  const [studyRangeVis, setStudyRangeVis] = useState<boolean>(false);
  const [studyData, setStudyData] = useState<any>({});
  const { courseDetail, microPermission } = useSelector<Models.Store, any>(
    state => state.moocCourse,
  );
  // const userInfo = useSelector<Models.Store, any>(
  //   state => state.global.userInfo,
  // );
  const { userInfo, parameterConfig } = useSelector<{ global: any; }, any>((state) => state.global);

  const isMicroSuper = useMemo(() => {
    if (history.location.query.type !== "microMajor") return true;
    const codes = userInfo.roles?.map((item: any) => item.roleCode)
    return codes?.some((item: string) => ["r_sys_manager", "r_course_manager", "r_second_manager", "admin_S1"].includes(item)) || microPermission?.isManager;
  }, [userInfo, microPermission]);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  const [isImport, setIsImport] = useState<boolean>(true);
  const [visible, setVisible] = useState<boolean>(false);
  const [courseImportOrCopyVo, setCourseImportOrCopyVo] = useState<any>();

  const unMapAndMicroMajor = history.location.query.type !== "map" && history.location.query.type !== "microMajor"; // 该课程非图谱 且 非微专业
  const isGk = history.location.query.type === "mooc";  //是不是公开课
  useEffect(() => {
    getset();
    queryTeachers();
    queryCourseSearch();
    getCourseManagerRoles();
  }, []);

  const queryTeachers = () => {
    getteacherlist({ id: history.location.query.id }).then((res: any) => {
      if (res.status === 200) {
        setTeacherList(res.data ?? []);
      }
    });
  };
  const getCourseManagerRoles = () => {
    queryCourseManager(history.location.query.id).then((res: any) => {
      if (res.status === 200) {
        // getCourseManagerRoles()
        const roles = res.data.roles.map((item: any) => item.code);
        const temp = roles.includes('r_sys_manager') || //是否是系统管理员
          roles.includes('r_course_manager') || //是否是课程管理员
          roles.map((item: any) => item.roleCode)?.includes('r_second_manager') || //第二权限
          roles.map((item: any) => item.roleCode)?.includes('admin_S1');
        setCourseManagerJurisdiction(temp);
      }
    });
  };

  const getset = () => {
    getSeting(history.location.query.id).then((res: any) => {
      if (res && res.message === 'OK') {
        setVideo(!!res.data.isAllowDragging);
        setDownload(res.data.isAllowDownloads);
        setFinishTime(Number(res.data.ratio));
        setShowWatermark(res.data.showWatermark);
        setAllowedDrag(res.data.allowed_drag || false);
        setAllowedDoubleSpeed(res.data.allowed_double_speed || false);
        setShowCertificate(res.data.showCertificate || false);
        setShoCurriculum(res.data.shoCurriculum);

        setCourseFinishContinueLearning(res.data.course_finish_continue_learning || false);
        if (history.location.query.type === "mooc" || (history.location.query.type === "map" && courseDetail?.entityData?.publishType === 1)) {
          setIsNumberStudents(!res.data.isNumberStudents);
          setMaxStuNum(res.data.numberStudents ?? 200);
          setUnLoginShow(!!res.data.unLoginShow);
        }

        console.log(res.data.join_role,'333');
         if ( 'join_role' in res.data && res.data?.join_role !== null) {
           const role = res.data?.join_role || ''
           console.log(res.data.join_role,'444');
           if (role === 2) {
             setIsStudent(true)
             setIsTeacher(true)
           } else if (role === 1) {
             setIsTeacher(true)
           } else if (role === 0) {
             setIsStudent(true)
           }
         }
        setStudyData(pick(res.data ?? {}, ["necessaryLearnType", "necessaryLearnResourceId", "necessaryLearnNumber"]));
        setStudyRange(res.data.necessaryLearn ?? 1);
        setstudyComment(res.data.courseComment ?? 0);
      }
    });
  };
  const changevideo = (checked: boolean) => {
    setVideo(checked);
  };
  const changeDownload = (value: any) => {
    setDownload(value);
  };
  const changefinishTime = (value: any) => {
    setFinishTime(value);
  };
  const preservation = () => {
    console.log(finishTime === 0);
    if (studyRange === 0 && studyData.selectNum < studyData.necessaryLearnNumber) {
      message.error("设置必需完成选学数不能大于选学数量最大值！");
      return;
    }
    if (finishTime !== null) {
      let param = {
        isAllowDragging: 0,
        // "isAllowDragging": video ? 1 : 0,
        ratio: finishTime,
        isAllowDownloads: download,
        showWatermark,
        allowed_double_speed: allowedDoubleSpeed,
        allowed_drag: allowedDrag,
        course_finish_continue_learning: courseFinishContinueLearning,
        showCertificate,
        shoCurriculum,
        necessaryLearn: studyRange,
        courseComment:studyComment,
        ...(omit(studyData, ["selectNum"]))
      };
      if (history.location.query.type === "mooc" || (history.location.query.type === "map" && courseDetail?.entityData?.publishType === 1)) {
        param = Object.assign(param, {
          numberStudents: maxStuNum,
          isNumberStudents: isNumberStudents ? 0 : 1,
          unLoginShow,
        });
      }
      //如果是公开课
      if (isGk) {
        let join_role = isTeacher && isStudent ? 2 : isTeacher ? 1 : isStudent ? 0 : null
        param = Object.assign(param, {join_role})
        console.log(join_role,param,'222');
      }
      updateSeting(history.location.query.id, param).then((res: any) => {
        if (res && res.message === 'OK') {
          message.success(t('保存成功'));
          setMaxStuTip("");
          initCertificate();
          setCanPageEdit(false);
        } else if (res.status === 1002) {
          message.error(t("保存失败"));
          setMaxStuTip(res.message);
        } else {
          message.error(res.message);
        }
      });
    } else {
      message.info(t('请填写完成时长'));
    }
  };
  const initCertificate = () => {
    if (!showCertificate) return;
    getCertification(history.location.query.id).then((res: any) => {
      if (res.message === 'OK' && !res.data?.certificateSetting) {
        return res.data;
      } else {
        throw new Error(res.message);
      }
    }).then((data: any) => {
      updateCertificationSetting({
        courseManager: teacherList?.[0]?.userCode,
        title: t("认证证书"),
        text: t("经考核合格。"),
        courseId: history.location.query.id,
        courseLevel: 0
      });
    });
  };
  const maxStuNumChange = (data: any) => {
    if (data == null) {
      setMaxStuNum(200);
    } else {
      setMaxStuNum(data);
    }

  };
  const isNumStuChange = (e: any) => {
    setIsNumberStudents(e.target.checked);
  };
  const queryCourseSearch = () => {
    queryColleges().then((res) => {
      if (res.status === 200) {
        setColleges(res.data?.organization ?? []);
      }
    });
  };
  const handleStudyRangeChange = (value: number) => {
    setStudyRange(value);
    if (value === 0) {
      setStudyRangeVis(true);
    }
  };

  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleImportAndCopyOk = () => {
    Modal.info({
      content: "后续不会自动同步修改，若需同步请再次复制/导入。"
    });
    setIsModalOpen(false);
    setIsImport(true);
  };



  return (
    <div className={`learning-settings ${parameterConfig.target_customer === CUSTOMER_NPU && "npu"}`}>
      <div className='learning-box'>
        <>
          <div className='title-container'>
            <div className="text">{t("学习设置")}</div>
            <div className="line"></div>
          </div>
          <div className='item first-item'>
            <div className='title'>{t("音视频类课程资源学习完成时长：")}
              <Tooltip trigger="hover" title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
                <Slider
                  disabled={!canPageEdit}
                  style={{ width: "300px" }}
                  min={0}
                  max={100}
                  onChange={changefinishTime}
                  step={11}
                  marks={{ 0: "0%", 10: "10%", 20: "20%", 30: "30%", 40: "40%", 50: "50%", 60: "60%", 70: "70%", 80: "80%", 90: "90%", 100: "100%" }}
                  value={finishTime} />

              </Tooltip>
            </div>
            <span className='sp'>{t("设为0则代表不要求学习时长，学生进入学习页面即视为已完成学习")}</span>
          </div>
          {unMapAndMicroMajor && <div className='item'>
            <div className='title download'>
              <span>{t("教学内容")}</span>
              <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
                <Select disabled={!canPageEdit} value={download} onChange={changeDownload}>
                  <Select.Option value={0} key="1">{t("不允许")}</Select.Option>
                  <Select.Option value={2} key="2">{t("部分允许")}</Select.Option>
                  <Select.Option value={1} key="3">{t("全部允许")}</Select.Option>
                </Select>
              </Tooltip>
              <span>{t("下载")}</span>
            </div>
            <span className='sp'>{t("不允许下载时，对于压缩包等无法直接观看的教学内容，将无法进行任何操作")}</span>
            {download === 2 && canPageEdit && <a onClick={() => setDownloadSettingVisible(true)}>{t("选择允许下载的教学内容")}</a>}
          </div>}
          {unMapAndMicroMajor && <div className='item first-item'>
            <div className='title must-study'>
              <span>{t("教学内容")}</span>
              <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
                <Select disabled={!canPageEdit} value={studyRange} onChange={handleStudyRangeChange}>
                  <Select.Option value={1} key="3">{t("全部")}</Select.Option>
                  <Select.Option value={0} key="2">{t("部分")}</Select.Option>
                </Select>
              </Tooltip>
              <span>{t("必学")}</span>
              {studyRange === 0 && <>
                <span>，且{studyData.selectNum}个选学内容中需完成学习不少于
                  <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
                    <InputNumber min={0} max={studyData.selectNum} disabled={!canPageEdit} value={studyData.necessaryLearnNumber} onChange={value => {
                      if (value > studyData.selectNum && studyData.selectNum > 0) {
                        setStudyData({ ...studyData, necessaryLearnNumber: studyData.selectNum });
                      }
                      setStudyData({ ...studyData, necessaryLearnNumber: value });
                    }} />
                  </Tooltip>个</span>
                {canPageEdit && <a onClick={() => setStudyRangeVis(true)}>{t("设置必学教学内容")}</a>}
              </>}
            </div>
          </div>}
          <div className="item">
            <div className='title'>
              <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
                <Checkbox disabled={!canPageEdit} checked={showWatermark} onChange={(e: any) => setShowWatermark(e.target.checked)}></Checkbox>
              </Tooltip>{t("添加视频水印")}

            </div>
            <span className='sp'>{t("在视频上显示学生的姓名、学号水印，以防录屏")}</span>
          </div>
          <div className="item">
            <div className='title'>
              <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
                <Checkbox disabled={!canPageEdit} checked={allowedDrag} onChange={(e: any) => setAllowedDrag(e.target.checked)}></Checkbox>
              </Tooltip>{t("第一次学习不允许拖拽")}
            </div>
            <span className='sp'>{t("在第一次学习完成前，无法拖拽进度条，同时无法使用知识点列表快速定位至知识点")}</span>
          </div>
          {unMapAndMicroMajor && <div className="item">
            <div className='title'>
              <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
                <Checkbox disabled={!canPageEdit} checked={allowedDoubleSpeed} onChange={(e: any) => setAllowedDoubleSpeed(e.target.checked)}></Checkbox>
              </Tooltip>{t("第一次学习不允许倍速播放")}
            </div>
            <span className='sp'>{t("在第一次学习完成前，无法倍速播放")}</span>
          </div>}
          {unMapAndMicroMajor && <div className="item">
            <div className='title'>
              <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
                <Checkbox disabled={!canPageEdit} checked={showCertificate} onChange={(e: any) => setShowCertificate(e.target.checked)}></Checkbox>
              </Tooltip>{t("给学生颁发电子证书")}

            </div>
            <span className='sp'>{t("学生在完成课程教学内容学习与考核后，可获得课程电子证书")}{canPageEdit && <a onClick={() => setCertificateVisible(true)}>{t("设置证书内容")}</a>}</span>
          </div>}
          {unMapAndMicroMajor && <div className="item">
            <div className='title'>
              <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
                <Checkbox disabled={!canPageEdit} checked={courseFinishContinueLearning} onChange={(e: any) => setCourseFinishContinueLearning(e.target.checked)}></Checkbox>
              </Tooltip>{t("结课后学生可继续学习")}

            </div>
            <span className='sp'>{t("课程结束后学生仍能加入课程，查看和学习教学内容、作业等，但学习数据不再计入统计")}</span>
          </div>}
        </>
        {
          isGk &&
          <div className="item">
            <div className='title'>
              <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
              <Checkbox disabled={!canPageEdit}  checked={isTeacher} onChange={(e: any) => setIsTeacher(e.target.checked)} />{t("老师")}
              <Checkbox disabled={!canPageEdit} checked={isStudent} onChange={(e: any) => setIsStudent(e.target.checked)} />{t("学生")}
            </Tooltip>
                {/*<Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>*/}
              {/*  <Checkbox disabled={!canPageEdit} checked={courseFinishContinueLearning} onChange={(e: any) => setCourseFinishContinueLearning(e.target.checked)}></Checkbox>*/}
              {/*</Tooltip>{t("结课后学生可继续学习")}*/}

            </div>
            <span className='sp'>{t("学习者角色")}</span>
          </div>

        }
        {unMapAndMicroMajor && <>
          <div className='title-container'>
            <div className="text">{t("其他设置")}</div>
            <div className="line"></div>
          </div>
          <Tooltip title="将其他课程的章节、资料、公告、课程设置等导入本课程">
            <Button style={{ marginRight: 10 }} onClick={showModal}>导入课程</Button>
          </Tooltip>
          <Tooltip title="将本课程的章节、资料、公告、课程设置等复制到其他课程">
            <Button onClick={() => { showModal(); setIsImport(false); }}>复制到其他课程</Button>
          </Tooltip>
        </>}


        <>
          {(history.location.query.type === "mooc" || (history.location.query.type === "map" && courseDetail?.entityData?.publishType === 1)) &&
          <div className='title-container' style={{marginTop:'30px'}}>
            <div className="text">{t("课程加入设置")}</div>
            <div className="line"></div>
          </div>}
          {/* {history.location.query.type === "spoc" &&  <div className='item'>
             <div className='title'>
                 <Checkbox checked={isAllowSit} onChange={(e: any) => setAllowSit(e.target.checked)}></Checkbox>
                 允许旁听
             </div>
             <span className='sp'>勾选则不是班内的学生也能作为旁听生，对课程进行学习，旁听生不会影响统计数据。</span>
            </div>} */}

          {(history.location.query.type === "mooc" || (history.location.query.type === "map" && courseDetail?.entityData?.publishType === 1)) ? <>
            <div className="item">
              <div className='title'>
                <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
                  <Checkbox disabled={!canPageEdit} checked={isNumberStudents} onChange={isNumStuChange}></Checkbox>
                </Tooltip>{t("限制最大参与学习人数：")}

                <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
                  <InputNumber disabled={!isNumberStudents || !canPageEdit} min={0} max={99999} style={{ width: 80 }} onChange={maxStuNumChange} value={maxStuNum} />
                </Tooltip>
              </div>
              <div className='max-error'>{maxStuTip}</div>
            </div>
            <div className="item">
              <div className='title'>
                <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
                  <Checkbox disabled={!canPageEdit} checked={unLoginShow} onChange={(e: any) => setUnLoginShow(e.target.checked)}></Checkbox>
                </Tooltip>{t("课程对校外人员开放")}

              </div>
              <span className='sp'>{t("未登录的游客也可直接加入课程，进行课程学习")}</span>
            </div>
          </> : ''}

          <div className='title-container'>
            <div className="text">{t("课程评论设置")}</div>
            <div className="line"></div>
          </div>
          <div className="item">
            <div className='title' style={{ width: '100px' }}>
              <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
                {/* setShoCurriculum  */}
                <Checkbox disabled={!canPageEdit} checked={shoCurriculum} onChange={(e: any) => setShoCurriculum(e.target.checked)}></Checkbox>
              </Tooltip>{t("允许对课程")}
            </div>
            <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
              <Select disabled={!canPageEdit || !shoCurriculum } value={studyComment} onChange={(e: any) => setstudyComment(e)}>
                <Select.Option value={0} key="0">{t("评星+评论")}</Select.Option>
                <Select.Option value={1} key="1">{t("评星")}</Select.Option>
                <Select.Option value={2} key="2">{t("评论")}</Select.Option>
              </Select>
            </Tooltip>
          </div>
        </>
      </div>

      {isMicroSuper && <div className="btn-wrp">
        {canPageEdit ? <Space>
          <Button type="primary" className='preservation' onClick={preservation}>{t("保存")}</Button>
          <Button onClick={() => {
            setCanPageEdit(false);
            getset();
          }}>{t("取消")}</Button>
        </Space> : <Button type="primary" onClick={() => setCanPageEdit(true)}>{t("编辑")}</Button>}
      </div>}
      <CertificateSetting onClose={() => setCertificateVisible(false)} colleges={colleges} jurisdiction={courseManagerJurisdiction} visible={certificateVisible} teacherList={teacherList} />
      <CourseDownloadModal
        visible={downloadSettingVisible}
        onClose={() => setDownloadSettingVisible(false)}
      />

      <StudyRangeModal
        data={studyData}
        visible={studyRangeVis}
        onClose={() => setStudyRangeVis(false)}
        onConfirm={(data: any) => setStudyData(data)}
      />
      <ImportAndCopyModal open={isModalOpen} isImport={isImport} onOk={handleImportAndCopyOk} onClose={() => { setIsModalOpen(false); setIsImport(true); }} />

      <Prompt when={canPageEdit} message={t("你所做的更改可能未保存，确认离开？")} />
    </div>);

});

export default LearningSettings;
