import React, { useState, useEffect, FC } from 'react';
import {
  login,
  metaData,
  teacherInfo,
  getUserJurisdictionV2
} from
  '@/api/course';
import './index.less';
import '@/assets/styles/index.css';
import { IGlobalModelState } from '@/models/global';
import { CUSTOMER_NPU, CUSTOMER_CXD, ModuleCfg2 } from '@/permission/moduleCfg';
import perCfg from '@/permission/config';

import MyHeader from '@/components/MyHeader/MyHeader';
import Sidebar from '@/components/Sidebar/Sidebar';
import Loading from '@/components/loading/loading';
import Header from "@/components/Header";
import NPUHeader from "@/components/NPUHeader/index";
import { Layout, message } from 'antd';
import "@/components/NPUHeader/style.css";

import { useDispatch, useLocation, useSelector } from 'umi';
import { HashRouter } from 'react-router-dom';
import LeftMenu from '@/components/LeftMenu';
import useLocale from '@/hooks/useLocale';
import useCkeckRoles from '@/hooks/useCkeckRoles';

const { Content, Sider } = Layout;
const excludePaths = ["/course/microData", "/course/resource"]

const App: FC<any> = ({ children }) => {
  const [hash, setHash] = useState(window.location.hash);
  // const [showTop, setShowTop] = useState(false);
  const { parameterConfig, buttonPermission, permission, userInfo } = useSelector<
    { global: any; },
    { buttonPermission: string[]; parameterConfig: any; permission: any; userInfo: any }>(
      (state) => state.global);
  //移动端适配
  const { mobileFlag, leftRightVisible } = useSelector<{ config: any; }, any>(
    ({ config }) => config);

  const dispatch = useDispatch();
  const { homePageConfig } = useSelector<
    { global: IGlobalModelState; },
    IGlobalModelState>(
      (state) => state.global);
  let path = useLocation().pathname;
  const active = useLocation().pathname ? useLocation().pathname.split('/')[1] : '';

  const { t } = useLocale();
  useEffect(() => {
    // if (path.includes("/course/resource") || path.includes("/course/microData")) {
    //   setShowTop(false);
    // } else {
    //   setShowTop(true);
    // }
    // getLogin()
    getJurisdiction();
    getMetaData();
    // getTeacherInfo();
    screenResize();
    window.addEventListener('resize', function () {
      //移动端判定
      screenResize();
      return () => {
        window.removeEventListener('resize', screenResize);
      };
    });
  }, []);

  useEffect(() => { }, [hash]);

  const getHash = () => {
    setHash(window.location.hash);
  };

  window.onhashchange = () => {
    getHash();
  };

  localStorage.setItem('refresh', JSON.stringify(false));

  const getLogin = () => {
    login('admin', 'SobeyHive2016')
      // login('cq', '123456')
      .then((res) => {
        console.log(res);
      }).
      catch((error) => {
        console.log(error);
      });
  };
  const getJurisdiction = () => {
    getUserJurisdictionV2().
      then((res) => {
        if (
          res &&
          res.errorCode === 'success' &&
          typeof res.extendMessage.moduleFeatures === 'object') {
          const permission = res.extendMessage.moduleFeatures;
          dispatch({
            type: 'jurisdiction/updateState',
            payload: {
              jurisdictionList: Object.keys(permission).
                map((key) => permission[key]).
                flat(2),
              modules: res.extendMessage.modules || []
            }
          });
        } else {
          message.error(t('权限信息获取失败！'));
        }
      }).
      catch((error) => {
        message.error(t('权限信息获取失败！'));
      });
  };
  useEffect(() => {
    if (userInfo?.roles) {
      let roles = userInfo.roles?.map((item: any) => item.roleCode) || [];
      useCkeckRoles(roles);
    }
  }, [userInfo?.roles])

  const getTeacherInfo = () => {
    teacherInfo().
      then((res) => {
        if (res && res.success && res.data) {
          dispatch({
            type: 'microCourse/updateState',
            payload: {
              teacherInfo: res.data
            }
          });
        } else {
          message.error(t("获取教师信息失败，") + res.error.title);
        }
      }).
      catch((error) => { });
  };
  // 获取元数据
  const getMetaData = () => {
    //1 是川大  2是成信大 3高教 4职教 （3,4都是省平台）
    // metaData(['3','4'].includes(homePageConfig.banner_plate_type)?true:false)
    metaData(true).
      then((res) => {
        if (res && res.success) {
          dispatch({
            type: 'microCourse/updateState',
            payload: {
              formList: res.data
            }
          });
          localStorage.setItem('meta', JSON.stringify(res.data));
        } else {
          console.error(res);
        }
      }).
      catch((error) => {
        message.error(t("元数据获取失败，") + error);
      });
  };
  const screenResize = () => {
    //移动端判定
    if (
      navigator.userAgent.match(/Mobi/i) ||
      navigator.userAgent.match(/Android/i) ||
      navigator.userAgent.match(/iPhone/i) ||
      window.innerWidth < 768) {
      dispatch({
        type: 'config/updateState',
        payload: {
          mobileFlag: true,
          menuShowChange: false
        }
      });
    } else {
      dispatch({
        type: 'config/updateState',
        payload: {
          mobileFlag: false,
          menuShowChange: true
        }
      });
    }
  };
  const sidebarClick = (item: any) => {
    // console.log('sidebarClick',item);
    if (item && mobileFlag) {
      dispatch({
        type: 'config/updateState',
        payload: {
          menuShow: false,
          leftRightVisible: false
        }
      });
    }
  };
  return (
    <HashRouter>
      <div className={`App${mobileFlag ? ' mobileContainer' : ''}`}>
        <Layout>
          {!excludePaths.some(item => path.includes(item)) && (parameterConfig.target_customer === CUSTOMER_NPU ? <NPUHeader /> : <Header />)}
          <Layout>
            {/* {path == '/course' ||
              path == '/course/mooccourse' ||
              path == '/course/mapCourse' ||
              path == '/course/microcourse' ||
              path.includes('/course/dockcourse') ||
              path == '/course/trainingCourse' ||
              // path == '/course/rainCourse' ||
              // path == '/course/schoolMooc' ||
              // path == '/course/superstarCourse' ||
              // path == '/course/schoolMooc' ||
              path == '/course/list' ||
              path == '/course/classreview' ||
              path == "/course/minemap" ||
              path == '/course/classreview/visibleSetting' ||
              path == '/course/myReview' ||
              path == '/course/classreview' ||
              path == '/course/myLive' ||
              path == '/course/liveCourse' ||
              path === "/course/videolist" ||
              path === "/course/recycle" ||
              // path == '/course/pmphmoocCourse' ||
              // path == '/course/umoocCourse' ||
              // path == '/course/silverLearning' ||
              // path == '/course/schoolOnline' ||
              // path == '/course/zhihuishuCourse' ||
              path == '/course/space' ? */
              !excludePaths.some(item => path.includes(item)) && path.startsWith?.("/course") ?
                <div className={`micro-wrp${leftRightVisible ? ' mobile_left' : ' hideDom'}`}>
                  {/*<LeftMenu />*/}
                  {(parameterConfig.target_customer != null && parameterConfig.target_customer !== CUSTOMER_NPU && (parameterConfig.target_customer !== CUSTOMER_CXD)) && <LeftMenu />}
                  <Sider
                    width={186}
                    theme="light"
                    className={`site-layout-background`}>

                    <Sidebar code="micro" clickCallback={sidebarClick} />
                  </Sider>
                </div> :
                null}

            <Layout
              // style={
              //   path == '/course' ||
              //   path == '/course/mooccourse' ||
              //   path == '/course/spoccourse' ||
              //   path == '/course/dockcourse' ||
              //   path == '/course/rainCourse' ||
              //   path == '/course/schoolMooc' ||
              //   path == '/course/superstarCourse' ||
              //   path == '/course/schoolMooc' ||
              //   path == '/course/liveCourse' ||
              //   path == '/course/pmphmoocCourse' ||
              //   path == '/course/umoocCourse' ||
              //   path == '/course/zhihuishuCourse' ||
              //   path == '/course/list'||
              //   path == '/course/classreview' ||
              //   path == '/course/myReview' ||
              //   path == '/course/classreview/visibleSetting' ||
              //   path == '/course/space'
              //     // ? { padding: '0 0 0 10px' }
              //     ? { padding: '15px 15px 0px 15px',background:'#F7F9FA' }
              //     : {}
              // }
              className={leftRightVisible ? ' none' : ' mobile_right'}>
              <Content
                className="site-layout-background"
                style={{
                  // padding: '19px 20px 0', //内部微课开课界面顶部背景图没有铺满 另外加样式
                  margin: 0,
                  minHeight: 280,
                  overflowX: 'hidden',
                  overflowY: 'auto'
                }}>

                {children}
              </Content>
            </Layout>
            <Loading />
          </Layout>
        </Layout>
      </div>
    </HashRouter>);

};

export default App;
