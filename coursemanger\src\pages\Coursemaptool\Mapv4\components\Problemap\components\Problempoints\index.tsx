import React, { useState, use<PERSON><PERSON>o, Dispatch, SetStateAction } from 'react';
import { <PERSON><PERSON>, Card, Checkbox, Input } from 'antd';
import { DeleteOutlined, LinkOutlined, EditOutlined, MenuOutlined } from '@ant-design/icons';
import './index.less';
import { createguid } from '@/pages/Coursemaptool/Editmap/util';
import { SortableContainer, SortableElement, SortableHandle, SortEndHandler } from 'react-sortable-hoc';
import { arrayMoveImmutable } from 'array-move';
import { useBus, useListener } from 'react-bus';

const { TextArea } = Input;

interface ProblemPoint {
  id: string;
  title: string;
  code: string;
  linknodes?: any[];
}

interface linklistProps {
  source: string;
  target: string;
}

interface ProblemPointsProps {
  title: string;
  code: string;
  points: ProblemPoint[];
  linklist: linklistProps[];
  onAdd: (point: ProblemPoint) => void;
  onDelete: (id: string) => void;
  onLink: (point: ProblemPoint) => void;
  onLinkNodes?: (point: ProblemPoint) => void;
  onEdit: (id: string, newTitle: string) => void;
  onSort: (newPoints: ProblemPoint[]) => void;
  searchResult: string[];
  editmode: boolean;
  onClick: (point: ProblemPoint) => void;
  filterPoints: string[];
}

interface SortableItemProps {
  point: ProblemPoint;
  editingId: string | null;
  editingTitle: string;
  linkCounts: Record<string, number>;
  code: string;
  bglinearcolors: Record<string, string>;
  onDelete: (id: string) => void;
  onLink: (point: ProblemPoint) => void;
  onLinkNodes?: (point: ProblemPoint) => void;
  handleEdit: (point: ProblemPoint) => void;
  handleEditCancel: () => void;
  handleEditConfirm: () => void;
  setEditingTitle: Dispatch<SetStateAction<string>>;
  editmode: boolean;
  filterPoints: string[];
  onClick: (point: ProblemPoint) => void;
}

interface SortableListProps extends Omit<SortableItemProps, 'point'> {
  points: ProblemPoint[];
  onSortEnd: SortEndHandler;
  useDragHandle: boolean;
  searchResult: string[];
  editmode: boolean;
  onClick: (point: ProblemPoint) => void;
  filterPoints: string[];
}

// 添加拖动手柄组件
const DragHandle = SortableHandle(() => (
  <MenuOutlined style={{ cursor: 'grab', marginRight: '8px' }} />
));

// 创建可排序的卡片项组件
const SortableItem = SortableElement<SortableItemProps>(({ 
  point,
  editingId,
  editingTitle,
  linkCounts,
  code,
  bglinearcolors,
  onDelete,
  onLink,
  onLinkNodes,
  handleEdit,
  handleEditCancel,
  handleEditConfirm,
  setEditingTitle,
  editmode,
  filterPoints,
  onClick
}: SortableItemProps) => {
  if (editingId === point.id && editmode) {
    return (
      <Card 
        key={point.id} 
        className="pointCard"
        bodyStyle={{ paddingBottom: '10px' }}
      >
        <div className="top_heard_bg" style={{ background: bglinearcolors[code]}}></div>
        <div className="pointHeader">
          <TextArea
            className="titleInput"
            value={editingTitle}
            onChange={(e) => setEditingTitle(e.target.value)}
            placeholder="请输入"
            autoFocus
            autoSize={{ minRows: 2, maxRows: 6 }}
            maxLength={150}
            showCount
            onFocus={e => {
              const len = e.target.value.length;
              e.target.setSelectionRange(len, len);
            }}
          />
        </div>
        <div className="actions">
          <div className="divider" />
          <Button 
            type="text" 
            onClick={handleEditCancel}
          >
            取消
          </Button>
          <Button 
            type="text"
            onClick={handleEditConfirm}
          >
            完成
          </Button>
        </div>
      </Card>
    );
  }

  if(filterPoints.length > 0 && !filterPoints.includes(point.id)) {
    return null;
  }

  return (
    <Card 
      key={point.id} 
      id={point.id} 
      onClick={(e)=>{
        onClick(point);
        e.stopPropagation();
        e.preventDefault();
      }}
      className="pointCard"
      bodyStyle={{ paddingBottom: '10px' }}
    > 
      <div className="top_heard_bg" style={{ background: bglinearcolors[code]}}></div>
      <div className="pointHeader">   
        {editmode && <DragHandle />}           
        <span className="pointTitle">{point.title}</span>
        <span className="pointNumber">{linkCounts[point.id]}</span>
        {editmode && <Checkbox value={point.id} />}
      </div>
      {code !== 'basicQuestion' && <div className='point_box'></div>}
      {editmode ? <div className="actions">
        <div className="divider" />
        {point.linknodes && point.linknodes.length > 0 && <span className='linknodes' onClick={() => onLinkNodes?.(point)}>关联知识点{point.linknodes.length}个</span>}
        {editmode && <>
          <Button 
            type="text" 
            icon={<DeleteOutlined />} 
            onClick={(e:any) => {
              onDelete(point.id);
              e.stopPropagation();
              e.preventDefault();
            }}
          >
            删除
          </Button>
          <Button 
            type="text" 
            icon={<LinkOutlined />}             
            onClick={(e:any) => {
              onLink(point)
              e.stopPropagation();
              e.preventDefault();
            }}
          >
            关联
          </Button>
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={(e:any) => {
              handleEdit(point)
              e.stopPropagation();
              e.preventDefault();
            }}
          >
          编辑
          </Button>
        </>}
      </div>:
        code == 'basicQuestion' && <div className="actions">
        <div className="divider" />
        <span className='linknodes' onClick={() => onLinkNodes?.(point)}>关联知识点{point.linknodes?.length || 0}个</span>
      </div>
      }
    </Card>
  );
});

// 创建可排序的容器
const SortableList = SortableContainer<SortableListProps>(({ 
  points,
  searchResult,
  editmode,
  onClick,
  filterPoints,
  ...props 
}: SortableListProps) => {
  return (
    <div className="pointsList">
      {points.map((point: ProblemPoint, index: number) => {
        if(point.code !== props.code) {
          return null;
        }
        if(searchResult.length > 0 && !searchResult.includes(point.id)) {
          return null;
        }
        return (
          <SortableItem
            key={point.id}
            index={index}
            point={point}
            editmode={editmode}
            filterPoints={filterPoints}
            onClick={onClick}
            {...props}
          />
        );
      })}
    </div>
  );
});

const ProblemPoints: React.FC<ProblemPointsProps> = ({
  title,
  code,
  points,
  linklist,
  onAdd,
  onDelete,
  onLink,
  onLinkNodes,
  onEdit,
  onSort,
  searchResult,
  editmode,
  onClick,
  filterPoints
}) => {
  const bus = useBus();
  const [isAdding, setIsAdding] = useState(false);
  const [newPointTitle, setNewPointTitle] = useState('');
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState('');
  // 三组渐变色
  const bglinearcolors:any = {
    'doubleChoice': 'linear-gradient( 270deg, #FEBF61 0%, #F5673B 100%)',
    'groupQuestion': 'linear-gradient( 270deg, #C2DC85 0%, #1FC5B6 100%)',  
    'basicQuestion': 'linear-gradient( 270deg, #84EDE4 0%, #2587C0 100%)'
  };

  const handleAdd = () => {
    setIsAdding(true);
  };

  // 自定义一个监听事件
  useListener('savePointContent', (promise:any) => {
    // 触发2个完成事件
    if(!isAdding && editmode){
      handleEditConfirm();
    }
    if(isAdding && editmode ){
      handleConfirm();
    }
  });

  const handleConfirm = () => {
    if (newPointTitle.trim()) {
      onAdd({
        id:createguid(),
        title:newPointTitle,
        code:code
      });
      setNewPointTitle('');
      setIsAdding(false);
    }
  };

  const handleEdit = (point: ProblemPoint) => {
    setEditingId(point.id);
    setEditingTitle(point.title);
  };

  const handleEditConfirm = () => {
    if (editingId && editingTitle.trim()) {
      onEdit(editingId, editingTitle.trim());
      setEditingId(null);
      setEditingTitle('');
    }
  };

  const handleEditCancel = () => {
    setEditingId(null);
    setEditingTitle('');
  };

  const linkCounts = useMemo(() => {
    return points.reduce((acc, point) => {
      if (point.code !== code) return acc;
      
      if (code === 'basicQuestion') {
        acc[point.id] = point.linknodes?.length || 0;
      } else {
        acc[point.id] = linklist.filter(item => item.source === point.id).length || 0;
      }
      return acc;
    }, {} as Record<string, number>);
  }, [points, code, linklist]);

  // 添加排序处理函数
  const onSortEnd: SortEndHandler = ({ oldIndex, newIndex }) => {
    // 过滤出当前类型的点
    const currentPoints = points.filter(p => p.code === code);
    const allOtherPoints = points.filter(p => p.code !== code);
    
    // 在当前类型的点中进行排序
    const newCurrentPoints = arrayMoveImmutable([...currentPoints], oldIndex, newIndex);
    
    // 保持原始顺序，只更新当前类型的点
    const newPoints = points.map(point => {
      if (point.code === code) {
        const index = currentPoints.findIndex(p => p.id === point.id);
        return newCurrentPoints[index];
      }
      return point;
    });

    // 调用父组件的onSort
    onSort(newPoints);
  };

  return (
    <div className="ProblemPoint-container">
      <div className="title" style={{ background: bglinearcolors[code]}}>{title}</div>
      
      <SortableList
        points={points}
        onSortEnd={onSortEnd}
        useDragHandle
        code={code}
        searchResult={searchResult}
        editingId={editingId}
        editingTitle={editingTitle}
        linkCounts={linkCounts}
        bglinearcolors={bglinearcolors}
        onDelete={onDelete}
        onLink={onLink}
        onLinkNodes={onLinkNodes}
        handleEdit={handleEdit}
        handleEditCancel={handleEditCancel}
        handleEditConfirm={handleEditConfirm}
        setEditingTitle={setEditingTitle}
        editmode={editmode}
        onClick={onClick}
        filterPoints={filterPoints}
      />

      {isAdding && editmode && (
        <Card 
          className="pointCard"
          bodyStyle={{ paddingBottom: '10px' }}
        >
          <div className="pointHeader">
            <TextArea
              className="titleInput"
              value={newPointTitle}
              onChange={(e) => setNewPointTitle(e.target.value)}
              placeholder="请输入"
              autoFocus
              autoSize={{ minRows: 2, maxRows: 6 }}
              maxLength={150}
              showCount
            />
          </div>
          <div className="actions">
            <div className="divider" />
            <Button 
              type="text" 
              onClick={() => setIsAdding(false)}
            >
              取消
            </Button>
            <Button 
              type="text"
              onClick={handleConfirm}
            >
              完成
            </Button>
          </div>
        </Card>
      )}

      {!isAdding && editmode && (
        <Button 
          block 
          type="dashed" 
          onClick={handleAdd}
          className="addButton"
        >
          + 添加
        </Button>
      )}
    </div>
  );
};

export default ProblemPoints;
