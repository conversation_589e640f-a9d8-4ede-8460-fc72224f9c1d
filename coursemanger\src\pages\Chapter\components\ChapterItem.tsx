import React, { useState, useEffect, useMemo } from 'react';
import { FC } from 'react';
import StatusIcon from './StatusIcon';
import { Space, Button, Input, message, Popover } from 'antd';
import {
  PlusCircleFilled,
  CloseOutlined,
  DeleteOutlined,
  EditOutlined,
  CheckOutlined,
} from '@ant-design/icons';
import { IGlobalModelState } from '@/models/global';
import { useSelector, useLocation } from 'umi';
import { IconFont } from '@/components/iconFont';
import './ChapterSectionItem.less';
import { getSensitiveWord } from '@/utils';
import usePermission from '@/hooks/usePermission';
import useLocale from '@/hooks/useLocale';
import { CUSTOMER_PPSUC } from '@/permission/moduleCfg';

/*
 * @Author: 李晋
 * @Date: 2021-12-13 18:24:08
 * @LastEditTime: 2022-03-15 16:54:29
 * @Description: file information
 * @Company: Sobey
 */
export interface IChapterProps {
  info: {
    className: string;
    name: string;
    children: any[];
    guid: string;
    id: string;
    layer: number;
    orders: number[];
    status: number; // "1"--已发布；“-1”--草稿；“0”--待发布
  };
  showChapterPre?: boolean;
  canEdit: boolean;
  editId: string;
  selectKeys: any[];
  onEditSuccess: (info: IChapterProps['info']) => void;
  onEdit: (info: IChapterProps['info']) => void;
  onChangeName: (info: IChapterProps['info'], inputValue: string) => void;
  onChapterDelete: (info: IChapterProps['info'], e?: React.MouseEvent) => void;
  onOffItem: (itemIds: string[], status: number) => void;
  onDraftItem: (itemIds: string[], status: number) => void;
  onToPublishItem: (itemIds: string[], status: number) => void;
  onAdd: (info: IChapterProps['info'], type: string, e?: React.MouseEvent) => void;
}

const ChapterItem: FC<IChapterProps> = ({
  editId,
  info,
  selectKeys,
  canEdit = true,
  showChapterPre = true,
  onEditSuccess,
  onEdit,
  onAdd,
  onChapterDelete,
  onOffItem,
  onDraftItem,
  onToPublishItem,
  onChangeName,
}) => {
  const location: any = useLocation();
  const [inputValue, setInputValue] = useState<string>(info.name);
  const { parameterConfigObj, parameterConfig } = useSelector<any, any>(
    state => state.global,
  );
  const [isAddHomework, setIsAddHomework] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);
  const { getPermission } = usePermission();
  const { t } = useLocale();

  useEffect(() => {
    if (Object.keys(parameterConfigObj).length > 0) {
      if (location.pathname.includes("tempatedetail")) {
        setIsAddHomework(parameterConfigObj.kczx?.includes('course_library_school_assignment_display'));
      } else {
        setIsAddHomework(getPermission(['mooc', 'spoc', 'training', 'map'], '_school_assignment_display', true));
      }
    }
  }, [parameterConfigObj]);

  const showCase = useMemo(() => {
    if (location.pathname.includes("tempatedetail")) {
      return parameterConfigObj.kczx?.includes('course_library_case_display');
    } else {
      return getPermission(['mooc', 'spoc', 'training', 'map'], '_case_display', true);
    }
  }, [parameterConfigObj]);

  const handleEditFinish = () => {
    if (inputValue === '') {
      message.error(t('输入不能为空！'));
      return;
    }
    onChangeName(info, inputValue);
    onEditSuccess(info);
  };
  const content = useMemo(() => {
    return (<div>
      <div className='pop-item' onClick={(e) => { onAdd(info, "courseware", e); setOpen(false); }}>{t("课程资源")}</div>
      {isAddHomework && <div className='pop-item' onClick={(e) => { onAdd(info, "homework", e); setOpen(false); }}>{t("作业")}</div>}
      <div className='pop-item' onClick={(e) => { onAdd(info, "reference", e); setOpen(false); }}>{t("参考资料")}</div>
      <div className='pop-item' onClick={(e) => { onAdd(info, "hyperlink", e);setOpen(false); }}>{t("超链接")}</div>
      {showCase && <div className='pop-item' onClick={(e) => { onAdd(info, "case", e); setOpen(false); }}>{t("案例")}</div>}
      {parameterConfig.show_yunque_material === "true" && <div className='pop-item' onClick={(e) => { onAdd(info, "material", e); setOpen(false); }}>{t("数字教材")}</div>}
      {parameterConfig.target_customer === CUSTOMER_PPSUC && <div className='pop-item' onClick={(e) => { onAdd(info, "training_case", e); setOpen(false); }}>{t("实训案例")}</div>}
    </div>);
  }, [showCase, isAddHomework, parameterConfig])
  return (
    <div
      key={info.id}
      className={`learn_item_container ${
        info.status === 1 ? 'publish' : 'unpublish'
      } ${editId === '' ? 'noedit' : 'editing'}`}
    >
      <div className="info_container">
        {/* <StatusIcon
          info={info}
          canEdit={canEdit}
          className="status_icon"
          onOffItem={onOffItem}
          onDraftItem={onDraftItem}
          onToPublishItem={onToPublishItem} /> */}

        <div className="item_title">
          {showChapterPre && (
            <span className="item_order">
              {t('第{name}章', info.orders?.join('.'))}
            </span>
          )}
          {editId === info.id && canEdit ? (
            <Input
              className="title_input"
              value={inputValue}
              onChange={e => {
                console.log(e);
                setInputValue(e.target.value);
              }}
              showCount
              maxLength={99}
              // onPressEnter={handleEditFinish}
              // onBlur={handleEditFinish}
            />
          ) : (
            <span className={info.status !== 1 ? 'unpublish_name' : ''}>
              {info.name}
            </span>
          )}
        </div>

        {canEdit && <StatusIcon
          info={info}
          canEdit={canEdit}
          className="status_icon"
          onOffItem={onOffItem}
          onDraftItem={onDraftItem}
          onToPublishItem={onToPublishItem}
        />}

        {// info.status !== 1 &&
        canEdit && // 未发布显示
          (editId !== info.id ? (
            <>
              {/* 编辑icon */}
              <IconFont
                type="iconedit"
                className="delete_wrapper"
                onClick={e => {
                  e.stopPropagation();
                  onEdit(info);
                }}
              />

              {/* 删除icon */}
              <IconFont
                type="icondelete"
                className="delete_wrapper"
                onClick={e => {
                  e.stopPropagation();
                  onChapterDelete(info, e);
                }}
              />
            </>
          ) : (
            <>
              <CheckOutlined
                onClick={(e: any) => {
                  e.stopPropagation();
                  getSensitiveWord(inputValue, t('章节'), handleEditFinish);
                }}
              />

              <CloseOutlined
                onClick={(e: any) => {
                  e.stopPropagation();
                  setInputValue(info.name);
                  onEditSuccess(info);
                }}
              />
            </>
          ))}
      </div>

      {selectKeys.includes(info.id) &&
      canEdit && ( // 1、选择当前按钮时显示/2、未发布显示
          <Space className="opt_container opt_container_chapter">
            <Button
              type="primary"
              ghost
              icon={<PlusCircleFilled />}
              onClick={e => onAdd(info, "section", e)}
            >
              {t('小节')}
            </Button>
            <Popover
              content={content}
              placement="bottom"
              open={open}
              trigger="click"
              onOpenChange={(open: boolean) => setOpen(open)}
            >
              <Button
                type="primary"
                ghost
                icon={<PlusCircleFilled />}
                onClick={(e: any) => e.stopPropagation()}
              >
                {t('教学内容')}
              </Button>
            </Popover>
            {/* {info.status !== 1 && */}
            {/* ( */}
            {/* <>
           <Button
             type="primary"
             ghost
             icon={<PlusOutlined />}
             onClick={e => onCoursewareAdd(info, e)}
           >
             课件
           </Button>
           {isAddHomework && <Button
             type="primary"
             ghost
             icon={<PlusOutlined />}
             onClick={e => onHomeworkAdd(info, e)}
           >
             作业
           </Button>}
           <Button
             type="primary"
             ghost
             icon={<PlusOutlined />}
             onClick={e => onReferenceAdd(info, e)}
           >
             参考资料
           </Button>
           <Button
             type="primary"
             ghost
             icon={<PlusOutlined />}
             onClick={e => onHyperlinkAdd(info, e)}
           >
             超链接
           </Button>
          </> */}
            {/* ) */}
          </Space>
        )}
    </div>
  );
};

export default ChapterItem;
