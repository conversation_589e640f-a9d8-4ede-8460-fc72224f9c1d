import React, { useState, useEffect, useRef } from 'react';
import { Typography } from 'antd';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { useSize } from 'ahooks';

interface TextProps {
  text: string;
  rows?: number;
}
const { Paragraph } = Typography;
const TextMore: React.FC<TextProps> = ({ text, rows = 3 }) => {
  const [visible, setVisible] = useState(false);
  const ref = useRef(null);
  const size = useSize(ref);
  return (
    <div style={{ position: 'relative', width: '100%'}}>
      <Paragraph
        ellipsis={
          visible
            ? false
            : {
                rows: rows,
                expandable: true,
                symbol: (
                  <a style={{ visibility: 'hidden' }}>
                    展开
                  </a>
                ),
              }
        }
        ref={ref}
      >
        {text}
        {visible && (
          <a onClick={() => setVisible(false)} style={{ marginLeft: 2 }}>
            收起
          </a>
        )}
      </Paragraph>
      {/* 默认情况判断下高度 */}
      {!visible && size?.height >= 22 && text.length>20 && (
        <div style={{ position: 'absolute', bottom: 0, right: 0 }}>
          <a
            onClick={() => {
              setVisible(true);
            }}
          >
            展开
          </a>
        </div>
      )}
    </div>
  );
};

export default TextMore;