import React, { memo } from 'react';
import { Spin } from 'antd';
import Entity from '@/components/entity/entity';

export interface ResourcePreviewProps {
  loading: boolean;
  previewEntity: any;
  visible: number;
  currentvideotype: number;
  onFinish?: () => void;
  type?: number;
  selectNode?: any;
  centerednode?: any;
}

// 使用React.memo来记忆组件，减少不必要的重渲染
const ResourcePreview = ({
  previewEntity,
  onFinish,
  type = 1,
  selectNode,
  centerednode
}: ResourcePreviewProps) => {
  console.log(`EntityPreview rendered for type ${type}`);
  if(previewEntity?.src){
    return (
          <div className="entity-preview" style={{ width: '100%' }}>
            <div className="video-wrap">
              <Entity
                type={previewEntity.type}
                src={previewEntity.src}
                finishStatus={previewEntity.finishStatus}
                id={'previewVideo'}
                selectNode={selectNode}
                centerednode={centerednode}
                isAutoplay={false}
                pip={false}
                cover={previewEntity.cover}
                knowledge={previewEntity.knowledge}
                onListener={(e: any) => {
                  if (e === 'ended') {
                    onFinish?.();
                  }
                }}
              />
            </div>
          </div>
    );
  }else{
    return null
  }
}

// 自定义比较函数，只有当previewEntity.src或previewEntity.knowledge发生变化时才重新渲染
const arePropsEqual = (prevProps: ResourcePreviewProps, nextProps: ResourcePreviewProps) => {
  // 如果src不同，需要重新渲染
  if (prevProps.previewEntity?.src !== nextProps.previewEntity?.src) {
    return false;
  }

  // 如果knowledge不同，需要重新渲染
  if (JSON.stringify(prevProps.previewEntity?.knowledge) !== JSON.stringify(nextProps.previewEntity?.knowledge)) {
    return false;
  }

  // 其他属性变化不触发重新渲染
  return true;
};

export default memo(ResourcePreview, arePropsEqual);
