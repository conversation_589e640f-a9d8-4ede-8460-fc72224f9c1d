import { resourceDetail } from '@/api/addCourse';
import { getSemester, metaData, queryColleges } from '@/api/course';
import { cultivationList, getCourseFloorDetail } from '@/api/mooclist';
import { getteacherlist } from '@/api/teacher';
import TagInput from '@/components/AddCourseForm/TagInput';
import CoverModal from '@/components/CoverModal';
import FilePreviewModal from '@/components/FilePreviewModal';
import VideoModal from '@/components/VideoModal';
import { IconFont } from '@/components/iconFont';
import useCover from '@/hooks/useCover';
import useLocale from '@/hooks/useLocale';
import usePermission from '@/hooks/usePermission';
import { CUSTOMER_SHTECH, CUSTOMER_PPSUC, CUSTOMER_UTCM } from '@/permission/moduleCfg';
import { getLabels, toArray } from '@/utils';
import { CloudUploadOutlined, EyeOutlined } from '@ant-design/icons';
import {
  Button,
  DatePicker,
  Form,
  Image as Img,
  Input,
  Modal,
  Select,
  Tooltip,
  TreeSelect,
  message,
} from 'antd';
import moment from 'moment';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { useDispatch, useLocation, useSelector } from 'umi';
import { cultivationLevel, languageList, offlineArr } from './constants';
import './index.less';
const { SHOW_CHILD } = TreeSelect;
const { RangePicker } = DatePicker;

export interface ITree {
  title: string;
  key: string;
  children?: ITree[];
}
interface ISemesters {
  id: string;
  code: string;
  name: string;
  showSemester?: string;
}

export const setTreeData = (t: any[]): any => {
  return t.map(item => {
    if (item.children && item.children.length > 0) {
      return {
        value: item.categoryName,
        title: item.categoryName,
        key: item.categoryName,
        children: setTreeData(item.children),
        disabled: item.categoryType !== 'major',
      };
    } else {
      return {
        value: item.categoryName,
        title: item.categoryName,
        key: item.categoryName,
        disabled: item.categoryType !== 'major',
      };
    }
  });
};

export interface IBaseInfoRef {
  preservation: () => Promise<boolean | Promise<{ param: any; check: number }>>;
}


const BaseInfo = forwardRef<IBaseInfoRef, {}>((props, ref) => {
  useImperativeHandle(ref, () => ({
    preservation,
  }));

  const { getPermission } = usePermission();
  const { parameterConfig } = useSelector<any, any>(state => state.global);

  const location: any = useLocation();
  const [form] = Form.useForm();
  // const [listData, setListData] = useState([]);
  // const [total, setTotal] = useState(0);
  // const [fileList, setFileList] = useState([
  //     {
  //         uid: '-1',
  //         name: 'image.png',
  //         status: 'done',
  //         url: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
  //     },
  // ]);
  const dispatch = useDispatch();
  const [courseOption, setCourseOption] = useState<any[]>([]);
  const [uploadLoading, setUploadLoading] = useState(false);
  // const [cover, setCover] = useState(defaultCover);
  // const defaultCover = '/rman/static/images/spoc.png';
  const [cover, setCover] = useState<string>('');
  const [selectedCourse, setSelectedCourse] = useState<any>({});
  const [detailData, setDetailData] = useState<any>({});
  const [courseCategoryList, setcourseCategoryList] = useState<any>({});
  const [courseSubjectList, setcourseSubjectList] = useState<any>({});
  const [collegeList, setCollegeList] = useState<any>([]);
  const [getCultivationList, setCultivationList] = useState<any>([]);
  const [majorList, setMajorList] = useState<ITree[]>([]);
  // const [curMajorList, setcurMajorList] = useState<ITree[]>([]); // 选中学院后联动专业
  const { courseDetail, canPageEdit } = useSelector<Models.Store, any>(
    state => state.moocCourse,
  );

  const [semesters, setSemesters] = useState<ISemesters[]>([]);
  const courseDetailRef = useRef<any>({});
  const [coverModalVisible, setCoverModalVisible] = useState<boolean>(false);
  const [teacherNames, setTeacherNames] = useState<string[]>([]);

  const [offlineList, setOfflineList] = useState<any[]>(offlineArr);
  const [videoPreviewVisible, setVideoPreviewVisible] = useState<boolean>(
    false,
  );
  const subjectObject = useRef<any>({
    primary_classification: '',
    secondary_classification: '',
  });

  const { t } = useLocale();

  const timer = useRef<any>(null);
  const [resource, setResource] = useState<any>({});
  const [videoVisible, setVideoVisible] = useState<boolean>(false);

  const [collegelength, setCollegelength] = useState<number>(0);
  const [majorlength, setMajorlength] = useState<number>(0);
  const { coverSetting, setCoverSetting, handleChangeCover } = useCover(
    location.query,
  );
  const [nameChangeTag, setNameChangeTag] = useState<boolean>(false);
  // const restoreDefault = () => {
  //   isEdit = true;
  //   setCover(defaultCover);
  // };
  useEffect(() => {
    form.setFieldsValue({ cover });
  }, [cover]);

  useEffect(() => {
    if (Object.keys(courseDetail).length > 0) {
      if (courseDetail.entityData.cover_video) {
        setResource(JSON.parse(courseDetail.entityData.cover_video));
      }

      setCover(
        courseDetail.entityData.cover ? courseDetail.entityData.cover : '',
      );

      const { name, entityData } = courseDetail;
      setOfflineList(
        offlineArr
          .map((item: any) => ({
            ...item,
            value:
              item.key in entityData
                ? item.key === 'classTime'
                  ? entityData[item.key]?.split(';')
                  : entityData[item.key]
                : '',
          }))
          .filter(item => item.value != null && item.value != ''),
      );

      let {
        college,
        major,
        cover,
        tag,
        describe,
        target,
        start_time,
        end_time,
        related_courses,
        subject,
        course_level,
        classification,
        semester_teaching_courses,
        languages,
        educational,
      } = entityData;

      subjectObject.current = {
        primary_classification: entityData.primary_classification,
        secondary_classification: entityData.secondary_classification,
        parentCode: '',
      };
      let tempSubject = subject;
      if (subject?.length === 2) {
        //第一个是父级节点code
        subjectObject.current.parentCode = subject[0];
        tempSubject = [subject[1]];
      }
      if (related_courses) {
        getCourseOption(Number(related_courses));
      }
      setCollegelength(college?.length || 0);
      setMajorlength(major?.length || 0);
      form.setFieldsValue({
        name,
        coure: Number(related_courses),
        college,
        majors: major,
        cover,
        tag,
        describe,
        semester_teaching_courses,
        target,
        course_level,
        date: (start_time === 0 || start_time == null ) ? [] : [moment(start_time), moment(end_time)],
        subject: tempSubject || [],
        classification: classification ?? undefined,
        languages,
        educational,
      });
      courseDetailRef.current = courseDetail;
    }
  }, [courseDetail]);
  const [tagLabels, setTagLabels] = useState<string[]>([]);
  useEffect(() => {
    // getCourseOption(1, 50);
    getCultivationLevel();
    getCollegeList();
    querySemester();
    courseCategory();
    queryTeachers();
    getLabels(undefined, location.query.id).then(labels => {
      setTagLabels(labels?.slice(0, 12) ?? []);
    });
  }, []);

  useEffect(() => {
    if (resource.resourceId && !resource.src) {
      loopGetResource();
    }
  }, [resource.resourceId]);

  const getCollegeList = () => {
    queryColleges().then(res => {
      if (res.status === 200) {
        setCollegeList(res.data?.organization ?? []);
      }
    });
  };
  const getCultivationLevel = () => {
    cultivationList().then(res => {
      console.log(res.data);
      if (res.status === 200) {
        setCultivationList(res.data);
      }
    }).catch(()=>{})
  };
  const queryTeachers = () => {
    getteacherlist({ id: location.query.id }).then(res => {
      if (res.status === 200) {
        const names = res.data.map((item: any) => item.name);
        setTeacherNames(names);
      }
    });
  };
  const querySemester = () => {
    getSemester().then((res: any) => {
      if (res.status === 200) {
        setSemesters(res.data);
      } else {
        message.error(res.message);
      }
    });
  };

  const layout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 15 },
  };
  const tailLayout = {
    wrapperCol: { offset: 8, span: 16 },
  };
  // 课程分类
  const courseCategory = () => {
    metaData(true).then(res => {
      if (res && res.success) {
        res.data.forEach((item: any) => {
          if (item.fieldName === 'classification') {
            setcourseCategoryList(JSON.parse(item.controlData));
          }
          if (item.fieldName === 'subject') {
            // setcourseSubjectList(['3','4'].includes(homePageConfig.banner_plate_type)?item:JSON.parse(item.controlData));
            setcourseSubjectList(item);
          }
          if (item.fieldName === 'major') {
            let majorList = setTreeData(JSON.parse(item.controlData));
            setMajorList(majorList);
            // setcurMajorList(majorList);
          }
          // if (item.fieldName === 'college') {
          //   setCollegeList(JSON.parse(item.controlData));
          // }
        });
      }

      // getCourseDetail();
    });
  };
  
  // 课程选择
  const getCourseOption = (id: any) => {
    getCourseFloorDetail({ id }).then(res => {
      if (res && res.error_code === 'cloud_sc.0000.0000') {
        const results = [res.extend_message ?? {}];
        let option = results.map((item: any) => {
          const {
            id,
            course_name,
            course_no,
            college,
            major,
            teacher,
            course_category,
            college_name,
            major_name,
          } = item;
          return {
            name: `${course_name} （${course_no}）`,
            value: id,
            college,
            major,
            teacher,
            course_category,
            college_name,
            major_name,
          };
        });
        setCourseOption(option);
      }
    });
  };

  const onFinish = (values: any) => {
    console.log('Success:', values);
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  const normFile = (e: any) => {
    console.log('Upload event:', e);
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  // const onChange = (info: any) => {
  //   setUploadLoading(true);
  //   const formData = new FormData()
  //   formData.append("file", info, info.name)
  //   uploadFile(formData).then((res: any) => {
  //     if (res.message === 'OK') {
  //       setCover(res.data.httpPath);
  //       // isEdit = true;
  //       setCoverModalVisible(false)
  //       message.success(`封面上传成功！`);
  //     } else {
  //       message.error(res.message);
  //     }
  //   }).catch(() => {
  //     setUploadLoading(false);
  //     message.error(`封面上传失败！`);
  //   }).finally(() => {
  //     setUploadLoading(false);
  //   })
  // };

  // /**
  //  *
  //  * 学院专业联动
  //  * @param {string[]} colleges
  //  */
  // const handleSelectCollege = (colleges: string[]) => {
  //   setcurMajorList(
  //     majorList.filter((item: any) => colleges.includes(item.key)),
  //   );
  //   form.setFieldsValue({ ...form.getFieldsValue(), majors: [] });
  // };

  const onDateChange = (dates: any, dateStrings: [string, string]) => {
    // form.setFieldsValue({
    //     date: dates
    // })
    // console.log(form.getFieldsValue());
  };
  const courseChange = (value: string) => {
    let selectlist: any = {};
    courseOption.forEach((item: any) => {
      if (item.value === value) {
        selectlist = item;
      }
    });

    form.setFieldsValue({
      college: selectlist.college_name,
      majors: selectlist.major_name ? selectlist.major_name.split(',') : [],
    });
    setSelectedCourse(selectlist);
  };

  const getDate = (date: any, isStart: boolean) => {
    if (!date) return 0;
    const year = date?.year();
    const month = date?.month() + 1;
    const day = date?.date();
    if (isStart) {
      return new Date(`${year}-${month}-${day}`).valueOf();
    } else {
      return new Date(`${year}-${month}-${day} 23:59:59:999`).valueOf();
    }
  };

  const preservation = () => {
    const detail = courseDetailRef.current;
    const checkArr = ["name"]
    if (detail.entityData.publishStatus === 1 && parameterConfig.target_customer !== CUSTOMER_SHTECH) {
      if (parameterConfig.target_customer === CUSTOMER_UTCM) {
        checkArr.push("subject")
      }
      if (!["training", "microMajor"].includes(location.query.type)) {
        checkArr.push("semester_teaching_courses", "college")
      }
      if (parameterConfig.target_customer === CUSTOMER_PPSUC) {
        checkArr.push("date", "semester_teaching_courses", "college")
      }
    }
    return form
      .validateFields(checkArr)
      .then(() => {
        const data = form.getFieldsValue(true);
        let change = JSON.stringify(selectedCourse) === '{}';
        let check = 0;
        if (parameterConfig.target_customer === CUSTOMER_SHTECH) {
          check = 1;
        } else if (
          (data.college?.length > 0 && data?.semester_teaching_courses || ["training", "microMajor"].includes(location.query.type)) &&
          getDate(data.date?.[0], true) > 0 && 
          (parameterConfig.target_customer !== CUSTOMER_PPSUC || (parameterConfig.target_customer === CUSTOMER_PPSUC && data.educational))
        ) {
          check = 1;
        }
        let tempSubject = Array.isArray(data.subject)
          ? data.subject
          : [data.subject];
        if (subjectObject.current.primary_classification) {
          subjectObject.current.parentCode &&
            tempSubject.unshift(subjectObject.current.parentCode);
        }
        let param: any = {
          contentId: detail.contentId,
          updateData: {
            name: data.name,
            start_time: getDate(data.date?.[0], true),
            end_time: getDate(data.date?.[1], false),
            // college: change
            //   ? detailData.entityData.college.split(',')[0]
            //   : selectedCourse.course_college,
            college: data.college || [],
            // major: change ? arrmajors : selectedCourse.course_major?.split(','),
            major: data.majors || [],
            classification: data.classification || null,
            cover: data.cover,
            describe: data.describe,
            target: data.target,
            tag: data.tag,
            course_level: data.course_level,
            // subject: Array.isArray(data.subject)?data.subject:[data.subject],
            subject: tempSubject,
            publishStatus: detail.entityData.publishStatus
              ? detail.entityData.publishStatus
              : 0,
            semester_teaching_courses: data.semester_teaching_courses,
            semester_show_name: location.query.type !== "microMajor" ? (semesters.find(item => item.name === data.semester_teaching_courses)?.showSemester ?? data.semester_teaching_courses) : null,
            related_courses: change
              ? detail.entityData.related_courses
              : selectedCourse.value,
            courseType: detail.entityData.courseType,
            cover_video: JSON.stringify(resource),
            languages: toArray(data.languages),
            educational: toArray(data.educational),
          },
        };
        param.updateData.primary_classification =
          subjectObject.current.primary_classification;
        param.updateData.secondary_classification =
          subjectObject.current.secondary_classification;
        return new Promise<{ param: any; check: number }>(resolve => {
          if (nameChangeTag) {
            Modal.confirm({
              content: t('检测到课程名已改变，是否按当前课程名更新课程封面？'),
              onOk() {
                const setting = {
                  ...coverSetting,
                  courseName: data.name,
                };
                handleChangeCover(setting).then(res => {
                  if (res) {
                    param.updateData.cover = res;
                    setCover(res);
                    resolve({ param, check });
                    resolve({ param, check });
                  } else {
                    throw new Error();
                  }
                });
              },
              onCancel() {
                resolve({ param, check });
              },
            });
          } else {
            resolve({ param, check });
          }
        }).catch(() => {
          return false;
        });
      })
      .catch(() => {
        return false;
      });
  };
  //专业TreeSelect的change函数
  const onProfessionChange = (
    value: any,
    label: any,
    extra: any,
    fieldName: any,
  ) => {
    console.log(value, label, extra, fieldName);
    if (fieldName === 'subject') {
      // setSubjectName(label);
      if (!extra.triggerNode.props) {
        subjectObject.current = {};
        return;
      }
      const parentName = extra.triggerNode.props.parentName;
      subjectObject.current = {
        primary_classification: parentName || extra.triggerNode.props.title,
        secondary_classification: parentName
          ? extra.triggerNode.props.title
          : '',
        parentCode: extra.triggerNode.props.parentCode, //后端让把父节点的code也传过去
      };
      return;
    }
  };
  // 遍历目录树 为了把父亲节点的name带上
  const forTree = (tree: any, parent?: any) => {
    return tree.map((item: any) => {
      return {
        key: item.categoryCode,
        title: item.categoryName,
        parentName: parent ? parent.categoryName : '',
        parentCode: parent ? parent.categoryCode : '',
        value: item.categoryCode,
        children: item.children ? forTree(item.children, item) : [],
      };
    });
  };
  const loopGetResource = () => {
    getResourceDetail(true).then(res => {
      if (res === true) {
        timer.current = setInterval(() => {
          getResourceDetail(false);
        }, 2000);
      }
    });
  };

  const getResourceDetail = (isFirst: boolean) => {
    return resourceDetail(resource.resourceId).then((res: any) => {
      if (res.success) {
        const { fileGroups } = res.data;
        const keyframeArr = fileGroups?.filter(
          (item: any) => item.typeCode === 'keyframefile',
        );
        const previewFile = fileGroups?.filter(
          (item: any) => item.typeCode === 'previewfile',
        );
        if (keyframeArr.length > 0 && previewFile.length > 0) {
          clearInterval(timer.current);
          timer.current = null;
          const src = previewFile?.[0]?.fileItems?.[0]?.displayPath;
          const keyframe = keyframeArr?.[0]?.fileItems?.[0]?.displayPath;
          setResource({
            resourceId: resource.resourceId,
            src,
            keyframe,
          });
        }
      }
      if (isFirst) {
        return true;
      }
    });
  };

  const handleCoverConfirm = (images: string, setting: any) => {
    setCover(images);
    setCoverSetting(setting);
  };

  return (
    <div className="base-info">
      <div className="container">
        <Form
          form={form}
          // {...layout}
          name="basic"
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          onValuesChange={(values: any) => {
            if (values.name != undefined) {
              setNameChangeTag(true);
            }
          }}
        >
          <Tooltip title={!canPageEdit && parameterConfig.target_customer !== CUSTOMER_SHTECH && t('点击 “ 编辑 ” 按钮进行内容编辑')}>
            <Form.Item
              label={t(`${location.query.type === "microMajor" ? '微专业' : "课程"}名称`)}
              name="name"
              rules={[
                { required: true, message: t('请填写课程名称!') },
                {
                  type: 'string',
                  max: 50,
                },
              ]}
            >
              <Input
                disabled={!canPageEdit || parameterConfig.target_customer === CUSTOMER_SHTECH}
                autoComplete="off"
                showCount
                maxLength={50}
              />
            </Form.Item>
          </Tooltip>

          {/* <Form.Item
             label="课程专题"
             name="classification"
             // rules={[{ required: true, message: '请选择课程分类!' }]}
            >
             <Select
               mode="multiple"
               filterOption={(input, option: any) => {
                 return (
                   option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                 );
               }}
             >
               {courseCategoryList &&
                 Object.keys(courseCategoryList).map((key: any) => {
                   return (
                     <Select.Option value={key} key={key}>
                       {courseCategoryList[key].split(',')[0]}
                     </Select.Option>
                   );
                 })}
             </Select>
            </Form.Item> */}
          {!['training', "microMajor"].includes(location.query.type) && parameterConfig.target_customer !== CUSTOMER_SHTECH && (
            <Tooltip
              title={!canPageEdit && t('点击 “ 编辑 ” 按钮进行内容编辑')}
            >
              <Form.Item
                label={t('学科专业')}
                name="subject"
                rules={[
                  {
                    required: parameterConfig.target_customer === CUSTOMER_UTCM,
                    message: t('请选择学科专业!'),
                  },
                ]}
              >
                <TreeSelect
                  disabled={!canPageEdit}
                  treeData={forTree(
                    JSON.parse(courseSubjectList?.controlData || '[]'),
                  )}
                  // value={courseData.profession}
                  onChange={(value: any, label: any, extra: any) =>
                    onProfessionChange(value, label, extra, 'subject')
                  }
                  // treeCheckable={courseSubjectList?.isMultiSelect}
                  // showCheckedStrategy={SHOW_PARENT}
                  // placeholder={`请选择${courseSubjectList.showName}`}
                  showSearch
                  allowClear={true}
                  treeNodeFilterProp="title"
                  // defaultValue={[]}
                  getPopupContainer={triggerNode => triggerNode.parentNode}
                />
              </Form.Item>
            </Tooltip>
          )}
          {parameterConfig.target_customer !== CUSTOMER_SHTECH && <Tooltip title={!canPageEdit && t('点击 “ 编辑 ” 按钮进行内容编辑')}>
            <Form.Item
              label={t(`${location.query.type === "microMajor" ? '运行' : "开课"}时间`)}
              name="date"
              rules={[
                {
                  type: 'array',
                  required: true,
                  message: t('请选择开课时间！'),
                },
              ]}
            >
              <RangePicker disabled={!canPageEdit} onChange={onDateChange} />
            </Form.Item>
          </Tooltip>}
          {parameterConfig.target_customer !== CUSTOMER_SHTECH && !['training', "microMajor"].includes(location.query.type) && (
            <>
              <Tooltip
                title={!canPageEdit && t('点击 “ 编辑 ” 按钮进行内容编辑')}
              >
                <Form.Item
                  label={t('开课学期')}
                  name="semester_teaching_courses"
                  rules={[{ required: true, message: t('请选择所属学期!') }]}
                >
                  <Select disabled={!canPageEdit} allowClear>
                    {semesters.map((item: ISemesters) => (
                      <Select.Option key={item.id} value={item.name}>
                        {item.showSemester || item.name}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Tooltip>
              <Tooltip
                title={!canPageEdit && t('点击 “ 编辑 ” 按钮进行内容编辑')}
              >
                <Form.Item
                  label={t('开课学院')}
                  name="college"
                  rules={[{ required: true, message: t('请填写开课学院!') }]}
                >
                  <Select
                    disabled={!canPageEdit}
                    mode="multiple"
                    showArrow
                    allowClear
                    suffixIcon={
                      <span style={{ color: '#9D9D9D', fontSize: '14px' }}>
                        {collegelength}/10
                      </span>
                    }
                    onChange={(e: any, label: any) => {
                      if (e.length > 10) {
                        message.error(t('最多选择10个开课学院'));
                        let value = form.getFieldValue('college');
                        // 截取前10个
                        form.setFieldValue('college', value.slice(0, 10));
                        setCollegelength(10);
                      } else {
                        form.setFieldValue('college', e);
                        setCollegelength(e.length);
                      }
                    }}
                    // onChange={handleSelectCollege}
                    filterOption={(input, option: any) => {
                      return (
                        option.children
                          .toLowerCase()
                          .indexOf(input.toLowerCase()) >= 0
                      );
                    }}
                  >
                    {collegeList.map((item: any) => {
                      return (
                        <Select.Option value={item.code} key={item.code}>
                          {item.name}
                        </Select.Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Tooltip>
              <Tooltip
                title={!canPageEdit && t('点击 “ 编辑 ” 按钮进行内容编辑')}
              >
                <Form.Item
                  label={t('适用专业')}
                  name="majors"
                  rules={[{ required: false, message: t('请填写适用专业!') }]}
                >
                  <TreeSelect
                    disabled={!canPageEdit}
                    style={{ width: '100%' }}
                    placeholder={t('请选择专业')}
                    // treeData={curMajorList}
                    allowClear
                    showArrow
                    suffixIcon={
                      <span style={{ color: '#9D9D9D', fontSize: '14px' }}>
                        {majorlength}/10
                      </span>
                    }
                    onChange={(e: any, label: any) => {
                      if (e.length > 10) {
                        message.error(t('最多选择10个适用专业'));
                        let value = form.getFieldValue('majors');
                        // 截取前10个
                        form.setFieldValue('majors', value.slice(0, 10));
                        setMajorlength(10);
                      } else {
                        form.setFieldValue('majors', e);
                        setMajorlength(e.length);
                      }
                    }}
                    treeData={majorList}
                    multiple
                    treeCheckable
                    showCheckedStrategy={SHOW_CHILD}
                    // showCheckedStrategy={SHOW_PARENT}
                    treeNodeFilterProp="title"
                    filterTreeNode
                  />
                </Form.Item>
              </Tooltip>
              <Tooltip
                title={!canPageEdit && t('点击 “ 编辑 ” 按钮进行内容编辑')}
              >
                <Form.Item
                  label={t('培养层次')}
                  name="educational"
                  rules={[{ required: parameterConfig.target_customer ===  CUSTOMER_PPSUC, message: t('请选择培养层次!') }]}
                >
                  <Select
                    disabled={!canPageEdit}
                    style={{ width: '100%' }}
                    optionFilterProp="children"
                    mode='multiple'
                    allowClear
                    getPopupContainer={triggerNode => triggerNode.parentNode}
                  >
                    {cultivationLevel.concat(parameterConfig.target_customer === CUSTOMER_PPSUC ? ["继续教育", "其他"] : []).map((item: any, index: number) => (
                      <Select.Option key={index} value={item}>
                        {item}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Tooltip>
              {location.query.type !== "microMajor" && <>
                <Tooltip
                  title={!canPageEdit && t('点击 “ 编辑 ” 按钮进行内容编辑')}
                >
                  <Form.Item label={t('语种')} name="languages">
                    <Select
                      disabled={!canPageEdit}
                      style={{ width: '100%' }}
                      optionFilterProp="children"
                      allowClear
                      getPopupContainer={triggerNode => triggerNode.parentNode}
                    >
                      {languageList.map((item: any, index: number) => (
                        <Select.Option key={index} value={item}>
                          {t(item)}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Tooltip>
                <Tooltip
                  title={!canPageEdit && t('点击 “ 编辑 ” 按钮进行内容编辑')}
                >
                  <Form.Item label={t('课程等级')} name="course_level">
                    <Select
                      disabled={!canPageEdit}
                      style={{ width: '100%' }}
                      optionFilterProp="children"
                      // onChange={(e:any,label:any)=>onSelectChange(e,label,'course_level')}
                      allowClear
                      getPopupContainer={triggerNode => triggerNode.parentNode}
                    >
                      {[t('校级'), t('省级'), t('国家级')].map(
                        (item: any, index: number) => (
                          <Select.Option key={index} value={item}>
                            {item}
                          </Select.Option>
                        ),
                      )}
                    </Select>
                  </Form.Item>
                </Tooltip>
              </>}
            </>
          )}

          <Tooltip title={!canPageEdit && t('点击 “ 编辑 ” 按钮进行内容编辑')}>
            <Form.Item key={'tag'} name={'tag'} label={t('标签')}>
              <TagInput disabled={!canPageEdit} tagLabels={tagLabels} />
            </Form.Item>
          </Tooltip>

          <div className="img-video-wrp">
            <Form.Item
              name="cover"
              className="img-control"
              label={t(`${location.query.type === "microMajor" ? '微专业' : "课程"}封面`)}
              // valuePropName="fileList"
              getValueFromEvent={normFile}
              // extra="longgggggggggggggggggggggggggggggggggg"
              // rules={[{ required: true, message: '请上传课程封面!' }]}
            >
              <div className="img-box">
                <Img width={200} src={cover} />
              </div>
              {canPageEdit && (
                <div className="upload-buttons">
                  <Button
                    type="primary"
                    ghost
                    onClick={() => setCoverModalVisible(true)}
                  >
                    {t('编辑封面')}
                  </Button>
                  {/* <Button type="primary" onClick={restoreDefault}>
                   恢复默认
                  </Button> */}
                </div>
              )}
            </Form.Item>
            {parameterConfig.target_customer !== CUSTOMER_SHTECH && <Form.Item
              name="cover_video"
              label={t('宣传视频：')}
              // valuePropName="fileList"
              getValueFromEvent={normFile}
              // extra="longgggggggggggggggggggggggggggggggggg"
              // rules={[{ required: true, message: '请上传课程封面!' }]}
            >
              <div className="video-box">
                {resource.resourceId ? (
                  <div className="video-view-wrp wrp-box">
                    {resource.keyframe ? (
                      <Img
                        width={200}
                        src={resource.keyframe}
                        preview={false}
                      />
                    ) : (
                      <div className="trans-coding">{t('视频转码生成中')}</div>
                    )}
                    {resource.src && (
                      <div
                        className="mask"
                        onClick={() => setVideoPreviewVisible(true)}
                      >
                        <EyeOutlined />
                        {t('预览')}
                      </div>
                    )}
                  </div>
                ) : (
                  <div
                    className="video-btn wrp-box"
                    onClick={() => {
                      canPageEdit && setVideoVisible(true);
                    }}
                  >
                    {canPageEdit ? (
                      <>
                        <CloudUploadOutlined />
                        <div className="text">{t('上传视频')}</div>
                      </>
                    ) : (
                      <div className="text">{t('暂无视频')}</div>
                    )}
                  </div>
                )}
                {resource.resourceId && canPageEdit && (
                  <div className="video-btn-wrp">
                    <Button
                      style={{ marginRight: '48px' }}
                      type="primary"
                      ghost
                      onClick={() => setVideoVisible(true)}
                    >
                      {t('重新上传')}
                    </Button>
                    <Button
                      type="primary"
                      ghost
                      onClick={() => setResource({})}
                    >
                      {t('删除')}
                    </Button>
                  </div>
                )}
              </div>
            </Form.Item>}
          </div>

          <div className="tips-wrp">
            封面建议尺寸：640 * 360
            {parameterConfig.target_customer !== CUSTOMER_SHTECH && t('，有宣传视频即展示宣传视频，没有即不展示')}
          </div>
        </Form>
        {offlineList.length > 0 &&
          getPermission(['spoc'], '_offline_class_display', true) && (
            <div
              className={`offline-container ${!getPermission(
                ['spoc'],
                '_show_template_dsplay',
                true,
              ) && 'down'}`}
            >
              <div className="offline-title">
                <IconFont type="iconqingdan" />
                {t('线下课程信息')}
              </div>
              <div className="offline-content">
                {offlineList.map((item: any) => (
                  <div className="offline-item" key={item.key}>
                    <span className="offline-label">{t(item.label)}：</span>
                    <span className="offline-value">
                      {item.value instanceof Array
                        ? item.value.map((i: any) => <p>{i}</p>)
                        : item.value}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
      </div>

      <CoverModal
        visible={coverModalVisible}
        name={form.getFieldValue('name')}
        coverSetting={coverSetting}
        teacher={teacherNames || []}
        coverConfirm={handleCoverConfirm}
        coverCancel={() => {
          setCoverModalVisible(false);
        }}
      />
      <VideoModal
        visible={videoVisible}
        onSave={(resourceId?: string) => {
          if (resourceId) {
            setResource({ resourceId });
          } else {
            setResource({});
          }
        }}
        onClose={() => setVideoVisible(false)}
      />

      <FilePreviewModal
        visible={videoPreviewVisible}
        onClose={() => setVideoPreviewVisible(false)}
        fileType="video"
        width={1067}
        file={{ filePath: resource.src, extraData: t('视频预览') }}
      />
    </div>
  );
});

export default BaseInfo;
