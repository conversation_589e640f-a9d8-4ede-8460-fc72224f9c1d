import { getKnowledgeStudyRate, querymapbyid } from '@/api/coursemap';
import statisticsApi, {getKnowledgeCompletion, getKnowlegeStudy} from '@/api/statistics';
import {Checkbox, Empty, Select, Space, Tag} from 'antd';
import React, { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useLocation, useSelector } from 'umi';
import Echart from '../Detail/components/Echart';
import RadarChart from '../Detail/components/RadarChart';
import PieChartTwo from '../Newstatistics/components/PieChartTwo';
import WordEchart from './components/Echart';
import Preview from './components/Preview'
import './index.less';
import { dataVal } from './contant'
import {IconFont} from "@/components/iconFont";
import TopicItem from "@/pages/HomeworkManagement/components/TopicItem";
import { SortableContainer, SortableElement } from 'react-sortable-hoc';
const StudentStatistics: FC<any> = ({mainStyle = {}}) => {
  const location: any = useLocation();
  const { userInfo } = useSelector<any, any>(state => state.global);
  const courseId = location.query.id;
  const courseSemester = location.query.sm;
  const [mapId, setMapId] = useState('');
  const [mapName, setMapName] = useState('');
  const [personInfo, setPersonInfo] = useState<any>({});
  const [wordData, setWordData] = useState<any>([]);
  const [resource,setResource] = useState({ id: '4665138fb75d4a709e19dd7296b9cd3b',
    name: '[1.5]--第一章第二节',
    type: 'video',
    sourceId: 'da982c63b3f04eb59da2d23a5b31cd1e', })
  // 是否是微专业
  const isMicroMajor = useRef(location?.query?.type === 'microMajor')
  //#region 微专业的模块
  const CourseStatus = ['processing', 'warning', 'error', 'success', 'default']
  const CourseStatusText = ['进行中', '提交结课申请', '驳回结课申请', '已结课', '已免修']
  //选择的微专业模块
  const [selectedMicroMajor, setSelectedMicroMajor] = useState<any>({})
  const [microMajorModuleList, setMicroMajorModuleList] = useState<any[]>([])
  const getMicroMajorModuleList = (course_id: string, sm: string) => {
    statisticsApi.queryMicroMajorModuleList({courseId: course_id, courseSemester: sm}).then((res: any) => {
      if (res?.status == 200) {
        setMicroMajorModuleList(res?.data?.data || [])
        //endClassStatus 0进行中 1提交结课申请 2驳回结课申请 3已结课 4已免修
        // endClassTime
        setSelectedMicroMajor(res?.data?.data?.[0])
      }
    })
  }
  useEffect(() => {
    if (!courseId) return
    getMicroMajorModuleList(courseId, courseSemester)
  }, [])
  //#endregion
  const getmapId = () => {
    statisticsApi
      .getMapId({
        courseId,
        isShow: 2,
        courseSemester,
      })
      .then(res => {
        if (res.data.status === 200) {
          setMapId(res.data.data[0]?.id);
          setMapName(res.data.data[0]?.mapName);
        }
      });
  };
  useEffect(() => {
    getmapId();
  }, []);

  //
  const [nodeIdList, setNodeIdList] = useState<string[]>([])
  const getMapNodesInfo = (mapID: string) => {
    // chapterApis.getTree({mapId: mapID})
    querymapbyid({
      mapId: mapID,
    }).then((res: any) => {
      if (res?.status == 200) {
        const { nodesVos } = res.data;
        const newnodesVos = nodesVos.map((item: any) => item.nodeId);
        setNodeIdList(newnodesVos || [])
      }
    })
  }
  useEffect(() => {
    if (!mapId) return
    getMapNodesInfo(mapId)
  }, [mapId])

  const getPersonInfo = () => {
    const param: any = {
      courseId,
      semester: courseSemester,
      mapId,
    }
    if (isMicroMajor.current) {
      param.nodeId = selectedMicroMajor?.nodeId
    }
    statisticsApi
      .getPersonalOverview(param)
      .then(res => {
        if (res.data.status === 200) {
          setPersonInfo(res.data.data);
        }
      });
  };
  useEffect(() => {
    if (isMicroMajor.current && !selectedMicroMajor?.nodeId) return
    if (mapId && courseId && courseSemester) {
      getPersonInfo();
    }
  }, [mapId, courseId, courseSemester, selectedMicroMajor]);

  const [courseTip, setCcourseTip] = useState<any>([]);
  const [tableData, setTableData] = useState([]);
  const [couresSyllabusCode, setCouresSyllabusCode] = useState('');
  const [detailStates, setDetailStates] = useState<any[]>([]);
  const previewVal = useRef<any>();
  const getCode = () => {
    statisticsApi
      .getCourseCode({
        contentId: courseId,
        courseSemester,
      })
      .then(res => {
        if (res.data.status === 200) {
          setCouresSyllabusCode(res.data.data.entityData.couresSyllabusCode);
        }
      });
  };
  const getCourseTips = () => {
    statisticsApi
      .getCourseTips({
        courseId: couresSyllabusCode,
      })
      .then(res => {
        if (res && res.status == 200) {
          if (res.data?.data?.courseTargets) {

            let tableList = res.data.data.courseTargets.map(
              (item: any, index: any) => {
                return {
                  title: item.name,
                  subTitle: item.desc,
                  id: item?.id
                };
              },
            );
            setTableData(tableList);
            const statusList = Array(tableList.length).fill(false);
            statusList[0] = true;
            setDetailStates(statusList);
          }
        }
      });
  };
  useEffect(() => {
    if (courseId && courseSemester && !isMicroMajor.current) {
      getCode();
    }
  }, [courseId, courseSemester]);

  useEffect(() => {
    if (couresSyllabusCode !== '') {
      getCourseTips();
    }
  }, [couresSyllabusCode]);

    //目标列表
    const [achievementList, setAchievementList] = useState<any[]>([]);
    //获取目标（雷达图数据， 列表数据）
    const getTargetData = (params: any) => {
      statisticsApi.getGoalAchievementData(params).then(res => {
        if (res?.status === 200) {
          setAchievementList( formatRadarData(res.data?.data || [], 'targetAchievementDegree') );
          const tips =
            res.data?.data?.map((item: any) => ({
              name: item?.targetName,
              max: 100,
            })) || [];
          setCcourseTip(tips);
          getAchievementDetailByTargetId(res.data?.data?.[0]?.targetId, () => {});
        }
      });
    };
    //获取数据
    useEffect(() => {
      if (!courseId || !userInfo?.userCode || !couresSyllabusCode) return;

      const queryParam: any = {};
      queryParam.courseId = courseId;
      queryParam.userCode = userInfo?.userCode;
      queryParam.param = {
        type: 1,
        courseId,
        courseCode: couresSyllabusCode || '',
      };
      getTargetData(queryParam);
    }, [courseId, userInfo?.userCode, couresSyllabusCode]);

  const getRadar = () => {
    return {
      tooltip: {
        trigger: 'item',
        valueFormatter: (value: any) => `${value.toFixed(1)}%`,
      },
      radar: {
        shape: 'circle',
        triggerEvent: true,
        nameGap: 10,
        center: ['50%', '55%'],
        radius: ['0%', '68%'],
        axisName: {
          color: '#76A2D2',
          backgroundColor: '#E8F3FE',
          lineHeight: 27,
          borderRadius: [5, 5, 5, 5],
          padding: [0, 5, 0, 5],
        },
        splitArea: {
          areaStyle: {
            color: ['#fff', '#fff', '#fff', '#fff'],
            shadowColor: 'rgba(0, 100, 0, 0.3)',
          },
        },
        indicator: courseTip,
      },
      series: [
        {
          type: 'radar',
          symbolSize: 0,
          data: [
            {
              value: achievementList?.map(
                item => (Number(item?.radarData) > 100 ? 100 : item?.radarData) || 0,
              ),
              name: '数据',
              itemStyle: {
                normal: {
                  color: 'rgba(5, 128, 242, 0.8)',
                },
              },
              areaStyle: {
                normal: {
                  color: '#B8D6FF',
                },
              },
            },
          ],
        },
      ],
    };
  };

    //目标数据加载状态
    const [goalsLoading, setGoalsLoading] = useState(false);
    // 根据目标id获取具体信息
    const getAchievementDetailByTargetId = (targetId: string, callBack: any) => {

      if (!courseId || !targetId || !userInfo.userCode) return;

      setGoalsLoading(true);
      callBack();

      statisticsApi
        .getGoalAchievemetDetailById(userInfo.userCode, courseId, {
          courseCode: couresSyllabusCode || '',
          targetId,
          type: 1,
        })
        .then(res => {
          if (res?.status === 200) {
            goalsAchievementMap.current?.set(targetId, res?.data?.data || []);
            setShowData(res?.data?.data || []);
          }
        })
        .finally(() => {
          setGoalsLoading(false);
        });
    };

      //根据雷达图的点击更新右边的柱状图
  const handleChangeIndexByChart = (index: number) => {
    setDetailStates(pre => {
      return pre.map((item, i) => {
        if (i == index) {
          if (pre[i] == true) {
            return false;
          } else {
            return true;
          }
        }
        return false;
      });
    });
  };
  const radarChartClick = (params: any) => {
    tableData.forEach((item: any, index) => {
      if (item.title == params) {
        if (goalsAchievementMap.current?.has(item.id)) {
          setShowData(goalsAchievementMap.current.get(item.id));
          handleChangeIndexByChart(index);
        } else {
          getAchievementDetailByTargetId(item.id, () =>
            handleChangeIndexByChart(index),
          );
        }
      }
    });
  };

    // 根据目标列表的点击事件更新目标的展示状态
    const handleChangeGoals = (index: number) => {
      let newStates = [...detailStates];
      newStates.map((item, i) => {
        if (i == index) {
          if (newStates[i] == true) {
            newStates[i] = false;
          } else {
            newStates[i] = true;
          }
        } else {
          newStates[i] = false;
        }
      });
      setDetailStates(newStates);
    };
    // 存储多个目标的数据，在多次切换时减少接口请求
    const goalsAchievementMap = useRef<Map<any, any>>(new Map());
    // 要展示的目标 的数据
    const [showData, setShowData] = useState<any[]>([]);
    // 目标列表的点击事件
    const toggleDetail = (index: number, targetId: string) => {
      if (goalsAchievementMap.current?.has(targetId)) {
        setShowData(goalsAchievementMap.current.get(targetId));
        handleChangeGoals(index);
      } else {
        getAchievementDetailByTargetId(targetId, () => handleChangeGoals(index));
      }
    };

    //#region 总体课程目标达成度
    const [achievementOfCourse, setAchievementOfCourse] = useState(0)
    const getAchievementOfCourse = () => {
      getKnowledgeStudyRate({
        courseId: courseId,
        mapId: mapId,
        nodeIdList: nodeIdList,
        semester: courseSemester,
      }).then((res: any) => {
        if (res?.status == 200) {
          setAchievementOfCourse(Math.floor(Number(res?.data?.achievingRateTotal || 0) * 100) / 100)
        }
      })
    }
    useEffect(() => {
      if (!courseId || !mapId || !nodeIdList.length || isMicroMajor.current) return
      getAchievementOfCourse()
    }, [courseId,mapId, nodeIdList, courseSemester])
    //#endregion

    //格式化雷达图数据
  const formatRadarData = (formatData: any[], key: string) => {
    return formatData.map(item => ({
      ...item,
      radarData: item[key]
    }))
  }

    //#region 微专业按模块查询达成度
    const getReachByMicroMajorModule = (param: any) => {
      statisticsApi.queryReachByMicroMajorModule(param).then((res: any) => {
        if (res?.status == 200 && res?.data?.status == 200) {
          //初始化雷达图
          setAchievementList(formatRadarData(res?.data?.data?.allReach || [], 'rate'))
          const tips =
            res?.data?.data?.allReach?.map((item: any) => ({
              name: item?.targetName,
              max: 100,
            })) || [];
          setCcourseTip(tips);
          setAchievementOfCourse(res?.data?.data?.totalReach || 0)
          //初始化柱状图
          const tableList =
          res?.data?.data?.allReach?.map((item: any, index: any) => {
              return {
                title: item.targetName,
                // subTitle: item.desc,
                id: index,
              };
            }) || [];
          setTableData(tableList);
          res?.data?.data?.allReach?.forEach((item: any, index: number) => {
            const formatArr = item?.knowledgeVOs?.map((cell: any) => ({...cell, entity: cell.knowledgeName}))
            goalsAchievementMap.current?.set(index, formatArr || []);
            if (index === 0) {
              setShowData(formatArr || []);
            }
          })
          // handleInitStatus(tableList)
          const statusList = Array(tableList.length).fill(false);
            statusList[0] = true;
            setDetailStates(statusList);
        }
      })
    }

    useEffect(() => {
      if (isMicroMajor.current) {
        getReachByMicroMajorModule({courseId: courseId, courseSemester: courseSemester || 1, nodeId: selectedMicroMajor?.nodeId, userCode: userInfo?.userCode})
      }
    }, [selectedMicroMajor])

    //#endregion

  const getBar = useCallback(() => {
    return {
      color: ['#3398DB'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        valueFormatter: (value: any) => `${(Number(value) || 0).toFixed(1)}%`,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '4%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: showData.map(item => item?.entity),
          axisTick: {
            show: false,
            alignWithLabel: true,
          },
          axisLabel: {
            interval: 0,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
        },
      ],
      series: [
        {
          name: '掌握率',
          type: 'bar',
          barWidth: '15px',
          itemStyle: {
            borderRadius: [15, 15, 0, 0],
            color: 'rgba(104,164,251,1)',
          },
          data: showData.map(item => item?.masterRate),
        },
        {
          name: '完成率',
          type: 'bar',
          barWidth: '15px',
          itemStyle: {
            borderRadius: [15, 15, 0, 0],
            color: 'rgba(133,193,233,1)', // 第二根柱状图的颜色
          },
          data: showData.map(item => item?.finishRate),
        },
      ],
    };
  },[showData])
  const getPieChart = () => {
    return {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c} ({d}%)',
      },
      series: [
        {
          name: '总体课程目标达成度',
          type: 'pie',
          radius: '68%',
          center: ['50%', '50%'],
          clockwise: false,
          data: [
            {
              value: achievementOfCourse.toFixed(1),
              name: '完成',
            },
            {
              value: (100 - achievementOfCourse).toFixed(1),
              name: '未完成',
            },
          ],
          label: {
            normal: {
              textStyle: {
                color: '#999',
                fontSize: 14,
              },
            },
          },
          labelLine: {
            normal: {
              show: false,
            },
          },
          itemStyle: {
            normal: {
              borderWidth: 1,
              borderColor: '#ffffff',
            },
            emphasis: {
              borderWidth: 0,
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
      color: ['#7ADCEB', '#ffffff'],
    };
  };

  //饼状散点图数据获取
  const [knowledgePointData, setKnowledgePointData] = useState<any>(null);
  function transformData(originalData: any[]) {
    let transformedData: { name: any; value: any; data: never[] }[] = [];
    originalData.forEach(chapter => {
      let chapterObj: any = {
        name: chapter.chapterNodeName,
        value: chapter.knowledgeNodeNumber,
        data: [],
      };
      chapter.knowledgeLearningInfos.forEach(
        (knowledge: {
          finishNotExamine: any;
          masterNotExamine: any;
          nodeName: any;
          masterRate: string;
        }) => {
          let knowledgeObj = {
            name: knowledge.nodeName,
            value: parseFloat(knowledge.masterRate) || 0,
            finishNotExamine: knowledge.finishNotExamine,
            masterNotExamine: knowledge.masterNotExamine,
          };
          chapterObj.data.push(knowledgeObj);
        },
      );

      transformedData.push(chapterObj);
    });

    return transformedData;
  }
  function transformData2(originalData: any[]) {
    let transformedData: { name: any; value: any; data: never[] }[] = [];
    originalData.forEach(chapter => {
      let chapterObj: any = {
        name: chapter.chapterNodeName,
        value: chapter.knowledgeNodeNumber,
        data: [],
      };
      chapter.knowledgeLearningInfos.forEach(
        (knowledge: {
          finishNotExamine: any;
          masterNotExamine: any;
          nodeName: any;
          finishRate: string;
        }) => {
          let knowledgeObj: any = {
            name: knowledge.nodeName,
            value: parseFloat(knowledge.finishRate) || 0,
            finishNotExamine: knowledge.finishNotExamine,
            masterNotExamine: knowledge.masterNotExamine,
          };
          chapterObj.data.push(knowledgeObj);
        },
      );

      transformedData.push(chapterObj);
    });

    return transformedData;
  }

  const [pieDate, setPieDate] = useState<any>([]);
  const knowledgePointOptions = () => {
    const param: any = {
      courseId: courseId,
      courseSemester: courseSemester,
      userCode: userInfo?.userCode,
    }
    if (isMicroMajor.current) {
      param.nodeId = selectedMicroMajor?.nodeId
    }
    statisticsApi
      .getKnowledgeCompletion(param)
      .then((res: any) => {

        if (res?.data?.status == 200) {
          //完成率假数据
          let data: any = [];

          data = res.data?.data?.chapterKnowledgeInfos || [];
          setPieDate(data);
          setKnowledgePointData(transformData(data));

        }
      });
  };
  useEffect(() => {
    if (isMicroMajor.current && !selectedMicroMajor?.nodeId) return
    knowledgePointOptions();
  }, [selectedMicroMajor]);
  const [button, setButton] = useState([0, 0]);
  let allWord: {name: string,realName: string,value: number}[][] = [];
  type ResType = Record<string, string | number> & { nodeName : string }
  async function fetchAllDataSafely( pageSize = 10) {
    let currentPage = 1
    let allData: ResType[] = []
    let total = Infinity // 初始设置为极大值

    try {
      while ((currentPage - 1) * pageSize < total) {
        const { data: { data } } = await statisticsApi.getKnowlegeStudy({ courseId,courseSemester:1,courseSemester:1,courseType:4,page: currentPage,size: pageSize })
        console.log(data)
        allData = [...allData, ...data.results]
        total = data.total // 每次更新总数（防止中途数据变化）
        console.log(total)
        currentPage++
      }
      return allData
    } catch (error) {
      console.error('Fetch failed at page', currentPage)
      throw error
    }
  }
  const getButton = () => {
    const param: any = {
      courseId,
      mapId,
      semester: courseSemester,
    }
    if (isMicroMajor.current) {
      param.nodeId = selectedMicroMajor?.nodeId
    }
   fetchAllDataSafely().then(data => {
     console.log('data',data)
     const result = data.filter(item => item.masterRate && item.masterRate != '-' && Number(item.masterRate) > 0 && Number(item.masterRate) < 30  )
     console.log(result)
     if (result.length > 0) {
       allWord[0] = result.map(
         (item: ResType, index: number) => {
           const name = item.nodeName.length > 15 ? item.nodeName.slice(0, 15) + '...' : item.nodeName;
           return {
             name,
             realName: item.nodeName,
             value: Number(item.masterRate),
           };
         },
       ) || [];
     } else {
       allWord[0] = []
     }
     console.log(allWord,Array.isArray(allWord))
     console.log(allWord[0])
     allWord[0].push({ name: '13-1浅谈WebSocket...',realName: '13-1浅谈WebSocket安全0413_ev',value: 10 },
       { name: '薄弱二',realName: '薄弱二',value: 20 },
       { name: '薄弱三',realName: '薄弱二',value: 30 },
       { name: '13-4浅谈WebSocket...',realName: '13-4浅谈WebSocket安全0413_ev',value: 30 })
     allWord[1] = [{ name: '13-2浅谈WebSocket',realName: '13-2浅谈WebSocket安全0413_ev',value: 10 },
       { name: '薄弱二',realName: '薄弱二',value: 20 },
       { name: '薄弱三',realName: '薄弱二',value: 30 },
       { name: '薄弱四',realName: '薄弱二',value: 30 }]
     setWordData(allWord);
   })

    // statisticsApi
    //   .getoutline(param)
    //   .then(res => {
    //     if (res.data.status === 200) {
    //       let num1 = res.data.data.lessEffective.length;
    //       let num2 = res.data.data.selfLearned.length;
    //       setButton([num1, num2]);
    //       allWord[0] = res.data.data.lessEffective.map(
    //         (item: any, index: any) => {
    //           return {
    //             name: item,
    //             value: Math.floor(Math.random() * 51),
    //           };
    //         },
    //       );
    //       allWord[0].push({ name: '薄弱一',value: 100 },{ name: '薄弱二',value: 200 },{ name: '薄弱三',value: 300 },{ name: '薄弱四',value: 400 })
    //       allWord[1] = res.data.data.selfLearned.map(
    //         (item: any, index: any) => {
    //           return {
    //             name: item,
    //             value: Math.floor(Math.random() * 51),
    //           };
    //         },
    //       );
    //
    //     }
    //   });
  };
  useEffect(() => {
    // if (isMicroMajor.current && !selectedMicroMajor?.nodeId) return
    // if (courseId && mapId && courseSemester) {
    //   getButton();
    // }
    getButton();

  }, [courseId, mapId, courseSemester, selectedMicroMajor]);

  //按钮切换
  const [leftActiveTab, setLeftActiveTab] = useState();
  const leftClick = useCallback((tab: any) => {
    setLeftActiveTab(tab);
    if (tab == 1 || tab == 3) {
      setKnowledgePointData(transformData(pieDate));
    } else {
      setKnowledgePointData(transformData2(pieDate));
    }
  }, [pieDate])
  useEffect(() => {
    if (!couresSyllabusCode) {
      leftClick(2)
    } else {
      leftClick(1)
    }
  }, [couresSyllabusCode, leftClick])
  const [rightActiveTab, setRightActiveTab] = useState(0);
  const rightClick = (tab: any) => {
    setRightActiveTab(tab);
  };
  // 总览-雷达图
  const overviewRadar = useMemo(() => {
    return courseTip.length > 0 ? (
      <RadarChart options={getRadar()} onClick={radarChartClick} />
    ) : (
      <Empty style={{ marginTop: 100 }} />
    );
  }, [courseTip, tableData]);
  // 课程目标完成度-饼状图
  const completionOfCourseObjectives = useMemo(() => {
    return !!knowledgePointData ? (
      <PieChartTwo
        chartsDes="散点颜色与完成率/掌握率关系"
        dataSource={knowledgePointData}
        leftActiveTab={leftActiveTab}
      />
    ) : (
      <Empty style={{ marginTop: '100px' }} />
    );
  }, [knowledgePointData]);
  const ListItem = SortableElement<any>((props: any) => {
    const { data, serial, canEdit, tempState, onChange } = props;
    return <div className='drag-topic-item'>
      <div className='drag-topic-item-content'>
        <TopicItem index={serial} data={data} />
      </div>
    </div>;
  });
  const SortableList = SortableContainer<any>(({ list, onChange, tempState }: any) => {
    return <div className="questions-wrp">
      {list.map((item: any, index: number) =>
        <ListItem data={item} index={index} serial={index} key={item.id}  onChange={onChange} tempState={tempState} />)}
    </div>;
  });

  return (
    <div className="main_content" style={mainStyle}>
      {isMicroMajor.current ? (
        <div className="select-container">
          <Select
            options={microMajorModuleList.map(item => ({
              label: item?.nodeName,
              value: item?.nodeId,
            }))}
            value={selectedMicroMajor?.nodeId || undefined}
            getPopupContainer={triggerNode => triggerNode.parentElement}
            onChange={value => {
              const item = microMajorModuleList.find(
                item => item?.nodeId === value,
              );
              setSelectedMicroMajor(item);
            }}
            className="micro-select"
            size="large"
            bordered={false}
            style={{ minWidth: 120 }}
          />
          <div style={{ display: 'flex' }}>
            <Tag color={CourseStatus[selectedMicroMajor?.endClassStatus || 0]}>
              {CourseStatusText[selectedMicroMajor?.endClassStatus || 0]}
            </Tag>
            <div style={{ marginLeft: '20px' }}>
              {selectedMicroMajor?.endClassTime}
            </div>
          </div>
        </div>
      ) : (
        <div className="title"> {mapName} </div>
      )}
      <div className="top_content">
        <div className="block1">
          <img
            src={require('@/assets/imgs/EditCourse/student_icon1.png')}
          ></img>
          <div className="info">
            <div className="number">{personInfo.nodeTotal || 0}</div>
            <div className="name">知识点总数</div>
          </div>
          <div className="info info1">
            <div className="number">{personInfo?.resourceTotal || 0}</div>
            <div className="name">资源总数</div>
          </div>
          <div className="info info1">
            <div className="number">{personInfo?.homeworkTotal || 0}</div>
            <div className="name">试题总数</div>
          </div>
          <div className="info info1">
            <div className="number">{personInfo.completedTotal || 0}</div>
            <div className="name" title='图谱中已看完挂载资源的知识点'>已完成</div>
          </div>
        </div>
        <div className="block1">
          <img
            src={require('@/assets/imgs/EditCourse/student_icon2.png')}
          ></img>
          <div className="info">
            <div className="number color1">
              {personInfo?.finishRate?.toFixed(1) || 0}
              <span>%</span>
            </div>
            <div className="name" title='图谱中已看完挂载资源与全部挂载资源之比'>完成率</div>
          </div>
          <div className="info info1">
            <div className="number color1">{personInfo.finishRank || 0}</div>
            <div className="name">排名</div>
          </div>
        </div>
        <div className="block1">
          <img
            src={require('@/assets/imgs/EditCourse/student_icon3.png')}
          ></img>
          <div className="info">
            <div className="number color2">
              {personInfo?.masterRate?.toFixed(1) || 0}
              <span>%</span>
            </div>
            <div className="name" title='图谱中完成试题的加权正确率，权重与试题的难度、认知层次成正比'>掌握率</div>
          </div>
          <div className="info info1">
            <div className="number color2">{personInfo.masterRank || 0}</div>
            <div className="name">排名</div>
          </div>
        </div>
        {
          // 判断是否选择了微专业 
          <div className="block2">
            <div className="panel">
              <div className="number">{personInfo.goodNodeTotal || 0}</div>
              <div className="name" title='掌握率在71%-100%的知识点'>掌握较好知识点</div>
            </div>
            <div className="panel">
              <div className="number">{personInfo.generalNodeTotal || 0}</div>
              <div className="name split" title='掌握率在31%-70%的知识点'>掌握一般知识点</div>
            </div>
            <div className="panel">
              <div className="number">{personInfo.poorNodeTotal || 0}</div>
              <div className="name split" title='掌握率在1%-30%的知识点'>掌握较差知识点</div>
            </div>
            <div className="panel">
              <div className="number">{personInfo.exemptionNodeTotal || 0}</div>
              <div className="name split" title='未挂载试题知识点'>免考核知识点</div>
            </div>
          </div> 
        //  <div className="block2">
        //   <div className="panel">
        //     <div className="number">2</div>
        //     <div className="name" title='实验课'>实验课</div>
        //   </div>
        //   <div className="panel">
        //     <div className="number">{0 || 0}</div>
        //     <div className="name split" title='阶段测试'>阶段测试</div>
        //   </div>
        //   <div className="panel">
        //     <div className="number">{0 || 0}</div>
        //     <div className="name split" title='线上测试'>线上测试</div>
        //   </div>
        //   <div className="panel">
        //     <div className="number">27</div>
        //     <div className="name split" title='答疑测试'>答疑测试</div>
        //   </div>
        // </div>
        }
      </div>
      {!!couresSyllabusCode && (
        <div className="center_content">
          <div className="top">
            <div className="header">总览</div>
          </div>
          <div className="main_content">
            <div className="left">
              <div className="image_content">
                <img
                  className="image"
                  src={require('@/assets/imgs/EditCourse/info_background.png')}
                ></img>
                <div className="bg_content">
                  <div className="mychart">
                    {achievementOfCourse === 0 ? null : (
                      <Echart options={getPieChart()} />
                    )}
                  </div>
                  <div className="number">
                    {achievementOfCourse?.toFixed(1)}
                    <span>%</span>
                  </div>
                  <div className="name">总体课程目标达成度</div>
                </div>
              </div>
            </div>
            <div className="center">{overviewRadar}</div>
            <div className="right">
              {tableData.map((item: any, index) => {
                return (
                  <div className="top_right_container" key={index}>
                    <div className="header">
                      <span className="text">
                        <span
                          className={
                            detailStates[index] ? 'text1 active' : 'text1'
                          }
                        >
                          {item.title}
                        </span>
                        <span className="text2">{item.subTitle}</span>
                      </span>
                      <div
                        className="end"
                        onClick={() => toggleDetail(index, item?.id)}
                      >
                        {detailStates[index] ? '收起' : '展开'}
                        {detailStates[index] ? (
                          <img
                            src={require('@/assets/imgs/EditCourse/up.png')}
                          ></img>
                        ) : (
                          <img
                            src={require('@/assets/imgs/EditCourse/down.png')}
                          ></img>
                        )}
                      </div>
                    </div>
                    <div
                      className={
                        detailStates[index] ? 'detail' : 'detail hidden'
                      }
                    >
                      <Echart
                        showLoading={goalsLoading && detailStates[index]}
                        options={getBar()}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}
      <div className="bottom_content">
        <div className="bottom_left">
          <Space size={'large'} className="antd_space">
            {!!couresSyllabusCode && (
              <span
                className={`btn ${leftActiveTab === 1 ? 'active' : ''}`}
                onClick={() => leftClick(1)}
              >
                课程目标达成度
              </span>
            )}
            <span
              className={`btn ${leftActiveTab === 2 ? 'active' : ''}`}
              onClick={() => leftClick(2)}
            >
              完成率
            </span>
            <span
              className={`btn ${leftActiveTab === 3 ? 'active' : ''}`}
              onClick={() => leftClick(3)}
            >
              掌握率
            </span>
          </Space>
          <div className="container">{completionOfCourseObjectives}</div>
        </div>
        <div className="bottom_right">
          <Space size={'large'} className="antd_space">
            {isMicroMajor.current ? (
              <span className={`btn active`}>薄弱知识点 {button[0]}</span>
            ) : (
              <>
                <span
                  className={`btn ${rightActiveTab === 0 ? 'active' : ''}`}
                  onClick={() => rightClick(0)}
                >
                  {/*收效甚微 {button[0]}*/}
                  薄弱知识点
                </span>
                <span
                  className={`btn ${rightActiveTab === 1 ? 'active' : ''}`}
                  onClick={() => rightClick(1)}
                >
                  {/*无师自通 {button[1]}*/}
                  落后知识点
                </span>
                <span
                  className={`btn ${rightActiveTab === 2 ? 'active' : ''}`}
                  onClick={() => rightClick(2)}
                >
                  {/*无师自通 {button[1]}*/}
                  学习助力包
                </span>
              </>
            )}
          </Space>
          {rightActiveTab === 2 ? (
            <div className="container study_bag" style={{boxSizing: 'content-box',height: 700,overflowY:'scroll',}}>
              <div style={{ minHeight: '100%' }}>
                <div className="study_bag_item">
                  <span className="study_bag_item_title">经典资源</span>
                </div>
                <Preview ref={previewVal} modalVisible={true}
                         resource={resource}
                />
                <div className="study_bag_item">
                  <span className="study_bag_item_title">经典试题</span>
                </div>
                <div className="scroll_view">
                  <SortableList
                    useDragHandle
                    helperClass='custom-row-dragging'
                    list={dataVal.map((item: any) => ({
                      ...(item.question ?? {}),
                      topicId: item.question?.id,
                      ...item
                    }))}
                  />
                </div>
              </div>


            </div>
          ) : (
            <div className="container">
              {wordData.length > 0 ? (
                <WordEchart
                  dataSource={wordData}
                  rightActiveTab={rightActiveTab}
                ></WordEchart>
              ) : null}
            </div>
          )}
          {/*<div className="container">*/}
          {/*  {wordData.length > 0 ? (*/}
          {/*    <WordEchart*/}
          {/*      dataSource={wordData}*/}
          {/*      rightActiveTab={rightActiveTab}*/}
          {/*    ></WordEchart>*/}
          {/*  ) : null}*/}
          {/*</div>*/}
          {/*<div className="container">*/}
          {/*  {wordData.length > 0 ? (*/}
          {/*    <WordEchart*/}
          {/*      dataSource={wordData}*/}
          {/*      rightActiveTab={rightActiveTab}*/}
          {/*    ></WordEchart>*/}
          {/*  ) : null}*/}
          {/*</div>*/}
        </div>
      </div>
    </div>
  );
};
export default StudentStatistics;
