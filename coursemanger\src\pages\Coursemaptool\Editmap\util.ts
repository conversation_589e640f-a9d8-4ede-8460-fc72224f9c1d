import { getKnowledgeByResourceIds, getKnowledgeStudyRate } from '@/api/coursemap';
import { getUuid } from '@/utils';
// 递归查找子节点
export const findItem =(obj: any, id: string):any => {
    if (obj.id === id) {
        return {
            parent: null,
            node: obj,
        }
    }
    const { children }: any = obj
    if (children) {
        for (let i = 0, len = children.length; i < len; i++) {
            const res = findItem(children[i], id)
            if (res) {
                return {
                    parent: res.parent || obj,
                    node: res.node,
                }
            }
        }
    }
    return null
}

// 构建模块下试题数据  
export const buildKnowledgedata = (rootnode:any,allnodes:any,alledges:any) => {
  // 最终构建出来的数据
  let modelQuestion: any = [];
  // 临时存储模块下的知识点变量
  let modulenodeList: any = [];

  // 递归查找所有知识点
  const loopdata = (id:string) => {
    // 先找当前节点的下一级 节点id
    const newedegs = alledges.filter((edg:any)=>edg.source.cell == id && edg.data.isnew == false);
    // 如果有节点
    if(newedegs.length){
      newedegs.forEach((element:any) => {
        modulenodeList.push(element.target.cell);
        loopdata(element.target.cell);
      });
    }
  }

  // 找到所有二级节点
  const townodeids = alledges.filter((edg:any)=>edg.data.isnew == false && edg.source.cell == rootnode.id).map((item:any)=>item.target.cell);
  const townodes = allnodes.filter((item:any)=>townodeids.includes(item.id));
  // 循环所有二级节点
  townodes.forEach((item:any)=>{
    loopdata(item.id);    
    // 判断有没有子节点
    if(modulenodeList.length){
      // 找到当前节点下所有的节点 并且有 试题的节点
      let allKnowlenode = allnodes.filter((node:any)=>modulenodeList.includes(node.id) && node.data.type == 2 && node.data.homework.length > 0);
      if(allKnowlenode.length){
        // 构建数据
        modelQuestion.push({
          name:item.data.label,
          key:item.id,
          modulenodeList:allKnowlenode.map((item2:any)=>{
            return {
              name:item2.data.label,
              nodeId:item2.id,
              homework:item2.data.homework
            }
          })
        })
      }
      // 如果在根节点下有知识点 也存储起来
    }else if(item.data.type == 1 && item.data.homework.length > 0){
      modelQuestion.push({
        name:item.data.label,
        key:item.id,
        modulenodeList:[{
          name:item.data.label,
          nodeId:item.id,
          homework:item.data.homework
        }]
      })
    }
    modulenodeList = [];
  })

  return modelQuestion
}
 

// 计算出要置灰的点
export const computingKnowledge = (moudelNodedata:any,nodes:any) =>{
  // 得到所有的已完成的知识点
  let newobj:any = [];
  nodes.forEach((item:any)=>{
    if(Number(item.finishRate)>=100 || moudelNodedata.notNeedToStudy.includes(item.nodeId) || item.notNeedToStudy){
      newobj.push(item.nodeId)
    }
  })
  let grayedoutarr:any = [];
  moudelNodedata.allhouxiuedegs.forEach((item:any)=>{
    // 判断先修的点是否都已经完成
    let xianxiuarr = moudelNodedata.modulenodeList[item.source.cell] || [];
    if(xianxiuarr.length){
      // 如果先修的节点都已经完成了
      const flage = xianxiuarr.every((item2:any)=>newobj.includes(item2));
      // 如果有没有完成的节点就不能学习后面的模块 把相应的节点置灰
      if(!flage){
        let houxiuarr = moudelNodedata.modulenodeList[item.target.cell];
        grayedoutarr = [...grayedoutarr,...houxiuarr,item.target.cell]
      }
    }
  })
  return grayedoutarr
}

// 获取模块下所有的知识点
export const getKnowledgeBymouldes = (allmodule: any,res:any) => {
    let knowledgeList: any = []
    let obj:any = {};
    let notNeedToStudy:any = [];
    // 所有后修连线
    const alledegs = res.edges.filter((edg:any)=>edg.data.type == 5);
    // 递归查找
    const loopdata = (id:string,list: any) => {
        list.forEach((item: any) => {
          if(knowledgeList.includes(item.target.cell)){
            obj[id] = [...obj[id],item.target.cell];
          }
          const newedegs = res.edges.filter((edg:any)=>edg.source.cell == item.target.cell && edg.data.isnew == false);
          if(newedegs.length){
            loopdata(id,newedegs);
          }
        })
    }
    // 如果有后修关系才要处理
    if(alledegs.length){
      // 找到所有的知识点
      knowledgeList = res.nodes.filter((item:any)=>(item.data.type == 2 || item.data.type == 1))?.map((item:any)=>{   
        // 是否为免考核节点
        if(item.data.bindresource.length == 0 && item.data.referenceMaterials.length == 0){
          notNeedToStudy.push(item.id);
        } 
        return item.id
      }) || [];
      
      for (let i = 0; i < allmodule.length; i++) {
        obj[allmodule[i].value] = [];
        const newedegs = res.edges.filter((edg:any)=>edg.source.cell == allmodule[i].value && edg.data.isnew == false);
        loopdata(allmodule[i].value,newedegs);
      }
      return {
        allhouxiuedegs:alledegs,
        modulenodeList:obj,
        notNeedToStudy:notNeedToStudy
      }
    }else{
      return {
        allhouxiuedegs:[],
        modulenodeList:{},
        notNeedToStudy:[]
      }
    }    
}

// 判断是否要弹窗提醒先后修
export const checkshowmessage = (node: any, moudelNodedata: any,courseId:string,mapId:string,semester:string) => {
   const flage:any = Object.keys(moudelNodedata.modulenodeList).find((item:any)=>moudelNodedata.modulenodeList[item].includes(node.id));
   if(flage){     
    let needstudeynode:any = [];
    moudelNodedata.allhouxiuedegs.forEach((item:any)=>{
      if(item.target.cell == flage){
        needstudeynode = moudelNodedata.modulenodeList[item.source.cell];        
      }
    })
    if(needstudeynode.length){
      return new Promise((resolve, reject) => {
        getKnowledgeStudyRate({
          courseId: courseId,
          mapId: mapId,
          nodeIdList: needstudeynode,
          semester: semester,
        }).then((res)=>{
          if(res.status == 200){
            if(res.data.detailDTOList.length){
              // 除了免修节点 其他的都是要学习完成 否则就提醒
              const flage2 = res.data.detailDTOList.every((item:any)=>{
                if(item.notNeedToStudy){
                  return true;
                  // 判断是否没有资源
                }else if(moudelNodedata.notNeedToStudy.includes(item.nodeId)){
                  return true
                }else{
                  return item.finishRate >= 100
                }
              })
              return resolve(flage2);
            }
          }
          return resolve(true);
        }).catch(()=>{
          console.log('知识点学习情况接口调用出现异常！')
          return resolve(true);
        })
      })
    }else{
      return new Promise((resolve, reject) => {
        return resolve(true)
      })
    }
   }else{
    return new Promise((resolve, reject) => {
      return resolve(true)
    })
   }
}


export const tranListToTreeData = (nodes:any,edges:any,hasnodekey:string[]= [])=>{
  let issort = localStorage.getItem('isSort') == 'true';
  // 最终要产出的树状数据的数组
  const treeList:any = []
  // 所有项都使用对象存储起来
  const map:any = {}
  // // 根据y轴排序 防止左侧的顺序和右侧的顺序不一致
  if(issort){
    nodes.sort((a:any,b:any)=>{
      return a.position.y-b.position.y
    });
    localStorage.removeItem('isSort');
  }  
  // 建立一个映射关系：通过id快速找到对应的元素
  nodes.forEach((item:any) => {
    item.children = []
    map[item.id] = item
  })
  // map:
  // {
  //   "312c": { 'id': '312c', 'pid': '',     'name': '财务部',    children: [{ 'id': '312d', 'pid': '312c', 'name': '财务核算部',children: []}] },
  //   "312d": { 'id': '312d', 'pid': '312c', 'name': '财务核算部',children: []}
  // }
  nodes.forEach((item:any) => {

    // 对于每一个元素来说，先找它的上级
    //    如果能找到，说明它有上级，则要把它添加到上级的children中去
    //    如果找不到，说明它没有上级，直接添加到 treeList
    let pid:any = null;
    // item.disabled = Number(item.data.type) !== 2
    let myedge = edges.find((edge:any)=>(edge.target.cell === item.id));
    // 因为有的地方使用的是cell 有的是id 所以要判断一下
    if(myedge?.source.cell){
      pid = myedge.source.cell
    }else{
      pid = myedge?.source
    }

    const parent = map[pid]
    // 如果存在上级则表示item不是最顶层的数据
    if (parent) {
      parent.children.push({
        ...item,
        parentId:pid,
        disableCheckbox:hasnodekey.includes(item.id)
      })
    } else {
      // 如果不存在上级 则是顶层数据,直接添加
      treeList.push({
        ...item,
        parentId:pid || null,
        disableCheckbox:hasnodekey.includes(item.id)
      })
    }
  })
  // 返回出去
  return treeList
}


export const tranTreeDataToList = (treeData:any)=>{
  const list:any = [];
  const tran = (data:any)=>{
    data.forEach((item:any)=>{
      list.push(item);
      if(item.children){
        tran(item.children);
      }
    })
  }
  tran([treeData]);
  return list;
}


export function createguid() {
    return getUuid();
  }

export const base64ToFile = (urlData:any, fileName:string) => {
  const arr = urlData.split(',')
  const mime = arr[0].match(/:(.*?);/)[1]
  const bytes = atob(arr[1])
  let n = bytes.length
  const ia = new Uint8Array(n)
  while (n--) {
    ia[n] = bytes.charCodeAt(n)
  }
  return new File([ia], fileName, { type: mime })
}



  // 链接桩
  export const ports = {
    groups: {
      top: {
        position: 'top',
        attrs: {
          circle: {
            stroke: '#5F95FF',
            strokeWidth: 1,
            r: 6,
            magnet: true,
          },
        },
      },
      bottom: {
        position: 'bottom',
        attrs: {
          circle: {
            stroke: '#5F95FF',
            strokeWidth: 1,
            r: 6,
            magnet: true,
          },
        },
      },
      left: {
        position: 'left',
        attrs: {
          circle: {
            stroke: '#5F95FF',
            strokeWidth: 1,
            r: 6,
            magnet: true,
          },
        },
      },
      right: {
        position: 'right',
        attrs: {
          circle: {
            stroke: '#5F95FF',
            strokeWidth: 1,
            r: 6,
            magnet: true,
          },
        },
      },
    },
    items: [
      {
        id: 'port-1',
        group: 'top',
        tip: 'port-1-tip',
      },
      {
        id: 'port-2',
        group: 'bottom',
        tip: 'port-2-tip',
      },
      {
        id: 'port-3',
        group: 'left',
        tip: 'port-1-tip',
      },
      {
        id: 'port-4',
        group: 'right',
        tip: 'port-1-tip',
      },
    ],
  }


  /**
 *@param {*} fondSize 字体大小
 *@param {*} text
 *returns 返回文本像素的宽高
 */
export const getTextSize = (text: string, fontSize: any) => {
  // 创建临时元素
  const _span = document.createElement('span');
  // 放入文本,限制最大60个字符
  _span.innerText = text.length > 60 ? text.substring(0, 60) : text;
  // 设置文字大小
  _span.style.fontSize = fontSize + 'px';
  // span元素转块级
  _span.style.position = 'absolute';
  // span放入body中
  document.body.appendChild(_span);
  // 获取span的宽度
  let width = _span.offsetWidth || 0;
  // 从body中删除该span
  document.body.removeChild(_span);
  // 返回span宽度
  return width;
};


 // 计算节点的宽度
 export const getNodewidth = (id:string,data: any,edges:any,nodesize:number) => {
  // 获取子节点
  let childlength = edges.filter((item: any) => {
    return item.source == id
  })
  let divwidth = getTextSize(data.label || '知识点', nodesize);

  if (data.type == 1 || data.type == 5) {
    if (childlength.length) {
      divwidth = divwidth + 105;
    }else{
      divwidth = divwidth + 85;
    }
    if (divwidth < 150) {
      divwidth = 150
    }
    
  } else if (data.type == 2) {
    // 如果是有子节点
    if (childlength.length) {
      divwidth = divwidth + 85;
    }else{
      divwidth = divwidth + 55;
    }
    // 设置最小宽度 防止没有输入的时候有问题
    if (divwidth < 150) {
      divwidth = 150
    }
  }

  if(data.type == 5){
    if (childlength.length) {
      divwidth = divwidth + 120;
    }else{
      divwidth = divwidth + 70;
    }
  }
  
  return divwidth;
}


 // 防止直接关闭页面
 export function beforeunload(e: any) {
  let confirmationMessage = '你确定离开此页面吗?';
  (e || window.event).returnValue = confirmationMessage;
  return confirmationMessage;
}

// 默认的节点data
export const defaultNodeData:any  = {
  isCollapsed: true, //是否展开
  isedit: false,//是否编辑
  isyinandian: false,//是否是重难点
  explanation: '',//详解
  bindresource: [],//绑定的资源
  isDiscriminate: false,// 是否有对比辨析
  quoteKnowledge: [],//引用的知识点
  bindlink: [],//绑定的外部链接
  referenceMaterials: [],//参考资料
  homework:[], //作业
  linkmapid:[],//关联的其他地图id和节点的id    
  iscoreknowledge:false,// 核心知识点        
  isexpandknowledge:false,// 是否是拓展知识点
  iscase:false,// 是否是案例
  isexperiment:false,// 是否是实验
  caselist:[],//案例列表
  teachingModuleManager:[],// 教学模块负责人
  score:0, //分数
  studytime:[], //学习时间段
  notNeedToStudy:false,// 是否免修
  createuser:'',//创建人
  status: 0, //0未编辑 1已编辑
  attribute:'',//属性 
  referenceStudyTime:0,//参考学时
  nochangedId: '',// 唯一id
  labelsconfig:[],//标签配置
}

// 默认的边的data
export const defaultEdgeData:any = {
  visible: true,
  type: 1,
  isnew: false,
}

// 默认的node节点
export const defaultNode = ()=> {
  let ids = [createguid(),createguid(),createguid(),createguid(),createguid()]
  return {
    nodesVos: [
      {
        nodeId: ids[0],
        valueMap: JSON.stringify({
          data:{
            label: '导图模式可双击输入课程名',
            type:1,
            layoutname:1, //默认布局
            ...defaultNodeData,
            isroot: true,//是不是根节点
          }
        }),
      },      
      {
        nodeId: ids[1],
        valueMap: JSON.stringify({
          data:{
            isroot: false,//是不是根节点
            label: '这是分类节点，仅做分类，不能绑定教学内容',
            type:1,
            ...defaultNodeData,
          }
        }),
      },
      {
        nodeId: ids[2],
        valueMap: JSON.stringify({
          data:{
            isroot: false,//是不是根节点
            label: '这是知识点，承载教学内容，右键-查看详情中可绑定教学内容',
            type:2,
            ...defaultNodeData,
          }
        }),
      },
      {
        nodeId: ids[3],
        valueMap: JSON.stringify({
          data:{
            isroot: false,//是不是根节点
            label: '选中节点，可Enter键生成同级节点，Tab键生成下级节点，Delete键删除节点',
            type:1,
            ...defaultNodeData,
          }
        }),
      },
      {
        nodeId: ids[4],
        valueMap: JSON.stringify({
          data:{
            isroot: false,//是不是根节点
            label: '完成编辑后，需发布才能对学生展示',
            type:1,
            ...defaultNodeData,
          }
        }),
      }
    ],
    relationVos: [
      {
        data: "{\"visible\":true,\"type\":1,\"isnew\":false}",
        source: ids[0],
        target: ids[1],
        type: 1
      },
      {
        data: "{\"visible\":true,\"type\":1,\"isnew\":false}",
        source: ids[0],
        target: ids[2],
        type: 1
      },
      {
        data: "{\"visible\":true,\"type\":1,\"isnew\":false}",
        source: ids[0],
        target: ids[3],
        type: 1
      },
      {
        data: "{\"visible\":true,\"type\":1,\"isnew\":false}",
        source: ids[0],
        target: ids[4],
        type: 1
      }      
    ]
  }
}

// 默认的微专业节点
export const defaultMicromajorNode = ()=> {
  let ids = [createguid(),createguid(),createguid(),createguid(),createguid()]
  return {
    nodesVos: [
      {
        nodeId: ids[0],
        valueMap: JSON.stringify({
          data:{
            label: '导图模式可双击输入课程名',
            type:1,
            layoutname:1, //默认布局
            ...defaultNodeData,
            isroot: true,//是不是根节点
          }
        }),
      },
      {
        nodeId: ids[1],
        valueMap: JSON.stringify({
          data:{
            isroot: false,//是不是根节点
            label: '请右键根节点添加教学模块',
            type:1,
            ...defaultNodeData,
          }
        }),
      },
      {
        nodeId: ids[2],
        valueMap: JSON.stringify({
          data:{
            isroot: false,//是不是根节点
            label: '这是分类节点，仅做分类，不能绑定教学内容',
            type:1,
            ...defaultNodeData,
          }
        }),
      },
      {
        nodeId: ids[3],
        valueMap: JSON.stringify({
          data:{
            isroot: false,//是不是根节点
            label: '这是知识点，承载教学内容，右键-查看详情中可绑定教学内容',
            type:2,
            ...defaultNodeData,
          }
        }),
      },
      {
        nodeId: ids[4],
        valueMap: JSON.stringify({
          data:{
            isroot: false,//是不是根节点
            label: '选中节点，可Enter键生成同级节点，Tab键生成下级节点，Delete键删除节点',
            type:1,
            ...defaultNodeData,
          }
        }),
      }
    ],
    relationVos: [
      {
        data: "{\"visible\":true,\"type\":1,\"isnew\":false}",
        source: ids[0],
        target: ids[1],
        type: 1
      },
      {
        data: "{\"visible\":true,\"type\":1,\"isnew\":false}",
        source: ids[0],
        target: ids[2],
        type: 1
      },
      {
        data: "{\"visible\":true,\"type\":1,\"isnew\":false}",
        source: ids[0],
        target: ids[3],
        type: 1
      },
      {
        data: "{\"visible\":true,\"type\":1,\"isnew\":false}",
        source: ids[0],
        target: ids[4],
        type: 1
      }
    ]
  }
}

// 专业地图默认的节点
export const defaultMarjorNode = (label:string)=> {
  return {
    nodesVos: [
      {
        nodeId: createguid(),
        valueMap: JSON.stringify({
          data:{
            isroot: true,//是不是根节点
            label: label,
            type:1,
            layoutname:1, //默认布局
            ...defaultNodeData,
          }
        }),
      }
    ],
    relationVos: []
  }
}

// 章节转换为课程地图
export const formatChaptertoMap = async (mapid:any="",courseDetail:any,generationmode:string="auto",chapterTreeData:any=[],courseId:any=null) => {
  // 初始化根节点的id
  let rootId = createguid();
  // 内置根节点
  let nodeData:any = [{
    entity: courseDetail.name,
    nodeId: rootId,
    value:{
      data:JSON.stringify({
        isroot: true,//是不是根节点
        label: courseDetail.name,
        isCollapsed: true, //是否展开
        isedit: false,//是否编辑
        type: 1,//1是子节点 2是知识节点
        isyinandian: false,//是否是重难点
        explanation: '',//详解
        bindresource: [],//绑定的资源
        isDiscriminate: false,// 是否有对比辨析
        quoteKnowledge: [],//引用的知识点
        bindlink: [],//绑定的外部链接
        referenceMaterials: [],//参考资料
        homework:[] //作业
      })
    }
  }];
  let edgeData:any = [];
  let pointarr:any = [];
  // 循环生成数据 并存储到mapData中
  const loop = (data: any,parentid:any) => {
    data.forEach((item:any) => {     
        // 节点的类型
        let courseware:any = [];
        let reference:any = [];
        let homework = [];
        let hyperlink:any = [];
        let caselist:any = []; //案例列表
        let nodetype = 1;
        // 这里是防止 知识节点 的子节点 是分类节点的情况 违反了课程地图的 规则
        if(generationmode == 'auto'){
          nodetype = 1;
        }else if(generationmode == 'classification'){
          nodetype = 1;
        }else if(generationmode == 'knowledge'){
          nodetype = 2;
        }
        // 资源
        if(item.resourceAttributes == "courseware"){            
          courseware.push({
            contentId: item.describe,
            contentId_: item.describe,
            keyframe_: "",
            name: item.name,
            tempid: createguid(),
            type: "biz_sobey_"+item.resourseType,
          });
          nodetype = 2;
          if(item.knowledgePoints>0){
            pointarr.push({
              describe:item.describe,
              entity: item.name,
              nodeId: item.id,
            })
          }
        }
        // 参考资料
        if(item.resourceAttributes == "reference"){
          reference.push({
            contentId: item.describe,
            contentId_: item.describe,
            keyframe_: "",
            name: item.name,
            tempid: createguid(),
            type: "biz_sobey_"+item.resourseType,
          });
          nodetype = 2;
          if(item.knowledgePoints>0){
            pointarr.push({
              describe:item.describe,
              entity: item.name,
              nodeId: item.id,
            })
          }
        }

        // 作业 暂不支持先不用
        if(item.resourceAttributes == "homework"){
          homework.push(item);
          nodetype = 3;
        }

        // 超链接
        if(item.resourceAttributes == "hyperlink"){
          hyperlink.push(item.describe);
          nodetype = 2;
        }

        // 案例
        if(item.resourceAttributes == "case"){
          caselist.push({
            name: item.name,
            contentId: item.describe,
            contentId_: item.describe,
            type: 'biz_sobey_case',
            keyframe_: null,
            tempid: createguid(),            
          });
          nodetype = 2;
        }

        // 数字教材
        if(item.resourceAttributes == "material"){
          hyperlink.push(`https://182.150.63.207:11023/web/#/Previewtextbook?contentId_=${item.describe}`);
          nodetype = 2;
        }

        console.log('nodetype',nodetype)
        // 转换成节点数据
        if(nodetype!=3){
          nodeData.push({
            entity: item.name,
            nodeId: item.id,
            value:{
              data:JSON.stringify({
                isroot: false,//是不是根节点
                label: item.name,
                isCollapsed: item.children.length ? true : false, //是否展开
                isedit: false,//是否编辑
                type: nodetype,//1是子节点 2是知识节点
                isyinandian: false,//是否是重难点
                explanation: '',//详解
                bindresource: courseware,//绑定的资源
                isDiscriminate: false,// 是否有对比辨析
                quoteKnowledge: [],//引用的知识点
                bindlink: hyperlink,//绑定的外部链接
                referenceMaterials: reference,//参考资料
                homework:[], //作业
                caselist:caselist,//案例列表
              })
            }
          })
          // 转换边
          if(parentid){
            edgeData.push({
              data: `{\"visible\":true,\"type\":1,\"isnew\":false}`,
              source: parentid,
              target: item.id,
              type: 1
            })
          }
          // 递归 
          if(item.children && item.children.length){
            loop(item.children,item.id);          
          }
        }   
    });
  };
  loop(chapterTreeData,rootId);
  if(pointarr.length){      
    // 去重
    let newarr = Array.from(new Set(pointarr.map((item:any)=>item.describe)));
    // 根据资源id获取知识点详情
    let data =  await getKnowledgeByResourceIds(newarr);
    if(data.success){
      pointarr.forEach((item2:any)=>{
        data.data[item2.describe].map((item3:any)=>{            
          let nodeid = item3.guid_;            
          nodeData.push({
            entity: item3.title,
            nodeId: nodeid,
            value:{
              data:JSON.stringify({
                isroot: false,//是不是根节点
                label: item3.title,
                isCollapsed: false, //是否展开
                isedit: false,//是否编辑
                type: 2,//1是子节点 2是知识节点
                isyinandian: false,//是否是重难点
                explanation: '',//详解
                bindresource: [{
                  recousetype:'point',
                  name: item3.title,
                  contentId: item3.guid_,
                  contentId_: item3.guid_,
                  type: 'biz_sobey_point',
                  keyframe_: item3.keyframepath,
                  tempid: createguid(),
                  keyframeno:item3.keyframeno,
                  parentcontentId:item2.describe,
                  fragment_description:item3.fragment_description,
                  inpoint:item3.inpoint,
                  outpoint:item3.outpoint,
                  parentname:item2.entity,
                  createDate: item3.fragment_time,
                  // suffix:item3.suffix
                  // contentId: item3.describe,
                  // contentId_: item3.describe,
                  // keyframe_: "",
                  // name: item3.title,
                  // tempid: createguid(),
                  // type: "biz_sobey_point",
                }],//绑定的资源
                isDiscriminate: false,// 是否有对比辨析
                quoteKnowledge: [],//引用的知识点
                bindlink: [],//绑定的外部链接
                referenceMaterials: [],//参考资料
                homework:[] //作业
              })
            }
          })
          // 转换边
          edgeData.push({
            data: `{\"visible\":true,\"type\":1,\"isnew\":false}`,
            source: item2.nodeId,
            target: nodeid,
            type: 1
          })
        });      
      })
    }
  }
  // 返回生成的数据
  let json = {
    courseId:courseId  || null,
    // isFormat:true,
    mapId:mapid,
    nodesVos:nodeData,
    relationVos:edgeData,
  }
  console.log(json,pointarr);
  return json;
}

// 用户连线的样式
export const attrs = {
  line: {
    stroke: "#a3b1bf", // 指定 path 元素的填充色
    strokeDasharray: 3,
    targetMarker: 'classic'
  },
}

// mapv4 节点的颜色
export const mapv4colors = [
  {
    start:"255, 144, 144",
    end:"226, 17, 17",    
  },
  {
    start:"255, 125, 10",
    end:"245, 182, 0"
  },
  {
    start:"255, 134, 180",
    end:"236, 9, 62"
  },
  {
    start:"237, 71, 71",
    end:"255, 141, 58"
  },
  {
    start:"140, 212, 142",
    end:"55, 184, 137"
  },
  {
    start:"126, 180, 255",
    end:"84, 156, 255"
  }
]


export const mubiao1 = [
  {
    index:1,
    name:[
      "数据预处理概览",  
      "数据清洗",  
      "冗余数据处理",  
      "数据集成实例——卡方统计、方差、协方差",  
      "有参数据归约——线性回归、多元线性回归及对数模型",  
      "无参数据归约",  
      "数据转换基本概念",  
      "标准化",  
      "离散化及其实例",  
      "概念层次生成",  
      "数据降维的概念",  
      "数据降维方法：PCA、启发式搜索等"
    ],
    content:'了解数据仓库的特点和建立方法'
  },
  {
    index:2,
    name:[
      "模式与模式发现",  
      "FP-Growth算法"
    ],
    content:'学会联机分析'
  },
  {
    index:3,
    name:[
      "项集与频繁项集"
    ],
    content:'掌握分类、 关联规则、 聚类等数据挖掘方法'
  },
  {
    index:4,
    name:[
      "Apriori算法",  
      "监督学习与无监督学习",  
      "分类流程：模型训练、验证与测试",  
      "决策树",  
      "其他分类算法：朴素贝叶斯、KNN、随机森林等",  
      "模型的评价与选择：混淆矩阵、评价指标、留出与交叉验证",  
      "朴素贝叶斯算法",  
      "聚类的基本概念",  
      "聚类的评判标准与应用",  
      "聚类的使用要点及应用难点",  
      "K-Means算法",  
      "数据挖掘软件Weka简单使用" 
    ],
    content:'理解知识发现的过程'
  }
]


// 计算两个字符串的相似度
export const getSimilarity = (str1: string, str2: string): number => {
  if (str1 === str2) return 1;
  if (!str1.length || !str2.length) return 0;
  
  const track = Array(str2.length + 1).fill(null).map(() =>
    Array(str1.length + 1).fill(null));
  
  for (let i = 0; i <= str1.length; i += 1) track[0][i] = i;
  for (let j = 0; j <= str2.length; j += 1) track[j][0] = j;
  
  for (let j = 1; j <= str2.length; j += 1) {
    for (let i = 1; i <= str1.length; i += 1) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      track[j][i] = Math.min(
        track[j][i - 1] + 1, // 删除操作
        track[j - 1][i] + 1, // 插入操作
        track[j - 1][i - 1] + indicator // 替换操作
      );
    }
  }
  
  const longerLength = Math.max(str1.length, str2.length);
  return 1 - track[str2.length][str1.length] / longerLength;
}


export const mubiao2 = [
  {
    index:1,
    name:[
      "肺炎",
      "肺脓肿",
      "支气管扩张",
      "原发性支气管肺癌",
      "气胸",
      "肺结核",
      "胸腔积液",
      "间质性肺炎",
      "慢性支气管炎",
      "COPD",
      "呼吸衰竭",
      "慢性肺心病",
      "肺血栓栓塞",
      "支气管哮喘"
    ],
    content:'熟悉并了解内科学医学的思想、 观念、 原则以及内科医生工作的基本方法和基本理论'
  },{
    index:2,
    name:[
      "肾小球肾炎",
      "肾病综合征",
      "IgA肾病",
      "尿路感染",
      "肾衰",
      "甲亢",
      "糖尿病",
      "甲减",
      "库欣综合征",
      "原发性醛固酮增多症",
      "胰腺炎",
      "胃食管反流",
      "慢性胃炎",
      "肝性脑病",
      "消化性溃疡",
      "原发性肝癌",
      "肝硬化",
      "肠结核",
      "结核性腹膜炎",
      "溃疡性结肠炎",
      "克罗恩病",
      "肠易激综合征",
      "缺铁性贫血"
    ],
    content:'真正理解以人为中心以及防治结合的医疗新观念； 认识到内科医疗服务在国家卫生服务体系中的重要功能、 地位； 在学习中应注意理论联系实际、 循序渐进的原则， 通过思考发现问题、 分析问题， 不断提高解决问题的能力。 加强三基训练（基本理论、 基本知识和基本技能）和临床实践， 提高对内科常见疾病的诊治能力；'
  },{
    index:3,
    name:[
      "再生障碍性贫血",
      "溶血性贫血",
      "骨髓增生异常综合征",
      "特发性血小板减少性紫癜",
      "白血病",
      "淋巴瘤",
      "心衰",
      "心律失常",
      "心包炎",
      "心肌炎",
      "原发性心肌病",
      "心脏骤停",
      "高血压",
      "感染性心内膜炎",
      "心瓣膜病",
      "动脉粥样硬化与冠心病"
    ],
    content:'了解内科医疗的服务模式和内科医生的角色， 初步认识到为满足国家和人民健康的需要应尽的职责；能认同内科医生的工作， 与临床内科医生密切合作； 能选择内科医疗服务、 内科学医学研究作为自己的终身职业。'
  }
]
// 判断富文本里面是否有内容
export const hasRichTextContent = (richText: string): boolean =>{
  // 创建一个DOMParser实例
  const parser = new DOMParser();
  
  // 解析富文本字符串为DOM文档
  const doc = parser.parseFromString(richText, 'text/html');
  
  // 获取文档中的所有文本节点
  const textNodes = doc.body.textContent || '';
  
  // 检查文本节点是否有内容
  return textNodes.trim().length > 0;
}

  // 获取富文本里面的字数
  export const getContentLength = (content: any) => {
    if (!content) return 0;
    // 移除HTML标签
    const text = content.replace(/<[^>]+>/g, '');
    // 移除空格和换行符
    const cleanText = text.replace(/\s+/g, '');
    // 返回字符串长度
    return cleanText.length;
  }


  // 遍历树形结构 生成节点和边
  export const traversalTree = (tree:any,parentnode:any) => {
    let nodes:any = [{
      entity: parentnode.data.label,
      nodeId: parentnode.id,
      valueMap:JSON.stringify({
        data:{
          ...defaultNodeData,
          isroot:true,
          type:1, //1分类节点 2知识点 
          label: parentnode.data.label
        }
      })
    }];
    let edges:any = [];
    tree.forEach((item:any) => {
      // 先把资源名称作为一级节点加入到nodes中
      const level1 = createguid()
      nodes.push({
        entity: item.videoName,
        nodeId: level1,
        valueMap:JSON.stringify({
          data:{
            ...defaultNodeData,
            type:1, //1分类节点 2知识点 
            label: item.videoName,
            // bindresource:[{
            //   contentId: item.videoId,
            //   name: item.videoName,
            //   createDate: item.createDate || '',
            //   suffix: item.suffix || '',
            //   duration: item.duration || 0
            // }]
          }
        })
      })
      // 存储边
      edges.push({
        data: `{\"visible\":true,\"type\":1,\"isnew\":false}`,
        source: parentnode.id,
        target: level1,
        type: 1
      })
      // 遍历二级
      item.knowledgeGraphs.forEach((item2:any) => {
        const level2 = createguid();
        nodes.push({
          entity: item2.title,
          nodeId: level2,
          valueMap:JSON.stringify({
            data:{
              ...defaultNodeData,
              label: item2.title,
              type: 2,
              explanation: item2.content || '',
              bindresource:[{
                recousetype: 'point',
                    name: item2.title,
                    contentId: item2.guid,
                    contentId_: item2.guid,
                    type: 'biz_sobey_point',
                    keyframe_: item2.keyframe,
                    tempid: createguid(),
                    keyframeno: item2.keyframeno || 0,
                    parentcontentId: item.videoId,
                    fragment_description: item2.fragment_descriptio || '',
                    inpoint: item2.inPoint,
                    outpoint: item2.outPoint,
                    parentname: item.videoName,
                    createDate: item2.createDate || '',
                    suffix: item2.suffix || '',
                    duration: item2.outPoint - item2.inPoint || 0             
              }]
            }
          })
        })
        // 存储边
        edges.push({
          data: `{\"visible\":true,\"type\":1,\"isnew\":false}`,
          source: level1,
          target: level2,
          type: 1
        })

        // 遍历三级
        item2.children.forEach((item3:any) => {
          const level3 = createguid();
          nodes.push({
            entity: item3.title,
            nodeId: level3,
            valueMap:JSON.stringify({
              data:{
                ...defaultNodeData,
                label: item3.title,
                type: 2,
                explanation: item3.content || '',
                bindresource:[{
                  recousetype: 'point',
                      name: item3.title,
                      contentId: item3.guid,
                      contentId_: item3.guid,
                      type: 'biz_sobey_point',
                      keyframe_: item3.keyframe,
                      tempid: createguid(),
                      keyframeno: item3.keyframeno || 0,
                      parentcontentId: item.videoId,
                      fragment_description: item3.fragment_descriptio || '',
                      inpoint: item3.inPoint,
                      outpoint: item3.outPoint,
                      parentname: item.videoName,
                      createDate: item3.createDate || '',
                      suffix: item3.suffix || '',
                      duration: item3.outPoint - item3.inPoint || 0             
                }]
              }
            })
          })
          // 存储边
          edges.push({
            data: `{\"visible\":true,\"type\":1,\"isnew\":false}`,
            source: level2,
            target: level3,
            type: 1
          })
        })
      })
    })
    return {
      data:{
        nodesVos: nodes,
        relationVos: edges
      }
    }
  }

  // 生成节点数据
  export const createNodeoredge = (tree:any,parentnode:any) => {
    let nodes:any = [];
    let edges:any = [];
    tree.forEach((item:any) => {
      // 先把资源名称作为一级节点加入到nodes中
      const level1 = createguid();      
      nodes.push({
        id: level1, // String，可选，节点的唯一标识
        // width: data.data.width,   // Number，可选，节点大小的 width 值  
        width: (getTextSize(item.videoName, 18) + 120),
        height: 53, // Number，可选，节点大小的 height 值
        label: item.videoName,
        data: {
          ...defaultNodeData,
          label: item.videoName,
          type:2
        }, //注册的组件 通过 data 获取参数
        shape: 'react-shape',
        component: 'react-compont',
        magnet: true,
        visible: true
      });
      // 存储边
      edges.push({
        source: {
          cell: parentnode.id,
          anchor: 'right', //midSide
        }, // String，必须，起始节点 id
        target: {
          cell: level1,
          anchor: 'left',
        }, // String，必须，目标节点 id
        connector: {
          name: 'smooth',
          args: {
            direction: 'H',
          },
        },
        visible: true,
        data: {
          type: 1, //1包含 2等价 3后续 4关联
          isnew: false, //是否是用户新建的边
        },
        attrs: {
          line: {
            stroke: '#a3b1bf', // 指定 path 元素的填充色
            targetMarker: null,
          },
        },
      })
      // 遍历二级
      item.knowledgeGraphs.forEach((item2:any) => {
        const level2 = createguid()       
        nodes.push({
          id: level2, // String，可选，节点的唯一标识
          // width: data.data.width,   // Number，可选，节点大小的 width 值  
          width: (getTextSize(item2.title, 18) + 120),
          height: 53, // Number，可选，节点大小的 height 值
          label: item2.title,
          data: {
            ...defaultNodeData,
            label: item2.title,
            type: 2,
            explanation: item2.content || '',
            bindresource:[{
              recousetype: 'point',
                  name: item2.title,
                  contentId: item2.guid,
                  contentId_: item2.guid,
                  type: 'biz_sobey_point',
                  keyframe_: item2.keyframe,
                  tempid: createguid(),
                  keyframeno: item2.keyframeno || 0,
                  parentcontentId: item.videoId,
                  fragment_description: item2.fragment_descriptio || '',
                  inpoint: item2.inpoint,
                  outpoint: item2.outpoint,
                  parentname: item.videoName,
                  createDate: item2.createDate || '',
                  suffix: item2.suffix || '',
                  duration: item2.outpoint - item2.inpoint || 0             
            }]
          }, //注册的组件 通过 data 获取参数
          shape: 'react-shape',
          component: 'react-compont',
          magnet: true,
          visible: true
        });
        // 存储边
        edges.push({
          source: {
            cell: level1,
            anchor: 'right', //midSide
          }, // String，必须，起始节点 id
          target: {
            cell: level2,
            anchor: 'left',
          }, // String，必须，目标节点 id
          connector: {
            name: 'smooth',
            args: {
              direction: 'H',
            },
          },
          visible: true,
          data: {
            type: 1, //1包含 2等价 3后续 4关联
            isnew: false, //是否是用户新建的边
          },
          attrs: {
            line: {
              stroke: '#a3b1bf', // 指定 path 元素的填充色
              targetMarker: null,
            },
          },
        })

        // 遍历三级
        item2.children.forEach((item3:any) => {
          const level3 = createguid();          
          nodes.push({
            id: level3, // String，可选，节点的唯一标识
            // width: data.data.width,   // Number，可选，节点大小的 width 值  
            width: (getTextSize(item3.title, 18) + 120),
            height: 53, // Number，可选，节点大小的 height 值
            label: item3.title,
            data: {
              ...defaultNodeData,
              label: item3.title,
              type: 2,
              explanation: item3.content || '',
              bindresource:[{
                recousetype: 'point',
                    name: item3.title,
                    contentId: item3.guid,
                    contentId_: item3.guid,
                    type: 'biz_sobey_point',
                    keyframe_: item3.keyframe,
                    tempid: createguid(),
                    keyframeno: item3.keyframeno || 0,
                    parentcontentId: item.videoId,
                    fragment_description: item3.fragment_descriptio || '',
                    inpoint: item3.inpoint,
                    outpoint: item3.outpoint,
                    parentname: item.videoName,
                    createDate: item3.createDate || '',
                    suffix: item3.suffix || '',
                    duration: item3.outpoint - item3.inpoint || 0             
              }]
            }, //注册的组件 通过 data 获取参数
            shape: 'react-shape',
            component: 'react-compont',
            magnet: true,
            visible: true
          });
          
          // 存储边
          edges.push({
            source: {
              cell: level2,
              anchor: 'right', //midSide
            }, // String，必须，起始节点 id
            target: {
              cell: level3,
              anchor: 'left',
            }, // String，必须，目标节点 id
            connector: {
              name: 'smooth',
              args: {
                direction: 'H',
              },
            },
            visible: true,
            data: {
              type: 1, //1包含 2等价 3后续 4关联
              isnew: false, //是否是用户新建的边
            },
            attrs: {
              line: {
                stroke: '#a3b1bf', // 指定 path 元素的填充色
                targetMarker: null,
              },
            },
          })
        })
      })
    })
    return {nodes, edges}
  }