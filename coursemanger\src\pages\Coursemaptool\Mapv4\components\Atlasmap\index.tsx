import { getKnowledgeStudyRate, querymapbyid, recordCurrentNode } from '@/api/coursemap';
import { Drawer, message } from 'antd';
import React, { FC, useEffect, useRef, useState } from "react";
import { useLocation } from 'umi';
import { checkshowmessage, computingKnowledge, defaultNode, getKnowledgeBymouldes } from '../../../Editmap/util';
import MapX6 from "../../../Mapv3/components/mapX6";
import { MapConfig, defaultNodeData } from '../../../Mapv3/components/util';
import Header from "../heard";
import './index.less';
// 搜索的组件
import Search from '../../../Mapv3/components/Search';
// 详情组件
import Rdrawer from '../../../Rdrawer';
// 课程详情
import CourseInfo from '../../../Mapv3/components/CourseInfo';
// 展示跨课关系
import RelationMap from '../../../components/RelationMap';
// 防抖
import debounce from "lodash/debounce";
// 展示教学模块
import Teachingmodule from '../../../components/Teachingmodule'
// 知识点达成度
import statisticsApi from "@/api/statistics";
import useLocale from "@/hooks/useLocale";
import { useListener } from "react-bus";
import Achievementdegree from '../../../Achievementdegree';
import ProgressBox from "../ProgressBox";
import { Utils } from '@/utils/utils';
import { CheckService } from '@/api/check';
import close_icon1 from '@/assets/imgs/coursemap/v3/close.png';
import close_icon2 from '@/assets/imgs/coursemap/v3/close2.png';
import title_bg1 from '@/assets/imgs/coursemap/v3/title_bg.png';
import title_bg2 from '@/assets/imgs/coursemap/v3/title_bg_dark.png';
const Atlasmap: FC<any> = ({newmapid,courseid,showrete = true, isMain = false /** 是否是教师端 */ ,coursename='',couresSyllabusCode=''}) => {

  const { t } = useLocale();
  const [mapinfo, setMapInfo] = useState<any>(null);
  const [graph, setGraph] = useState<any>(null);
  // 搜索组件的ref 里面暴露了方法出来
  const searchref = useRef<any>();
  // 0是关闭状态  1显示知识节点弹窗  2显示对比辨析弹窗  3关联节点   4知识节点对比辨析  5绑定管理  6搜素   7 excel word 导入  8 对比辨析详情页面  9课程大钢  10课程地图按课件生成  11 地图保存记录 12 知识点达成度 13选择添加的课程  14课程详情
  // 15是导入xmind  16是引用地图  17是添加地图关系  18是展示跨课关系
  const [visible, setVisible] = useState<number>(0);
  // 查询类型 0是全部  1是子节点  2是知识节点
  const [querytype, setQuerytype] = useState<string>('0');
  //  查询节点的类型  疑难点  对比辨析节点
  const [selecttype, setSelecttype] = useState<string>('0');
  // 搜索框的参数
  const [inputtext, setInputtext] = useState<string>('');
  // 详情的参数
  const [drawerdata, setDrawerdata] = useState<any>(null);
  // 当前选择的课程id
  const [selectcourseid, setSelectcourseid] = useState<any>(null);
  // 当前的状态
  const perviewtype: number = 3; //0是工具端  1是老师端打开 2是学生端 3是专业路径
  // 当前选择的地图id
  const [selectmapid, setSelectmapid] = useState<any>(null);
  // 获取url参数
  const { query }: any = useLocation();
  // 当前是专业地图还是课程地图
  const [maptype, setMaptype] = useState<number>(2); //1是专业地图  2是课程地图
  // 异常情况展示
  const [errorinfo, setErrorinfo] = useState<any>(0); //0是正常  1参数错误  2当前专业暂无课程数据 3暂无专业图谱数据  4当前课程地图已下架  5当前课程地图已被删除
  // 当前选择的节点
  const [selectnode, setSelectnode] = useState<any>(null);
  // 当前布局模式
  const [layouttype, setLayouttype] = useState<number>(1);
  // 图片的整体 完成率 和 掌握率
  const [noderate, setNoderate] = useState<any>(null);
  // 当前所有节点的字典表
  const [allnodes, setAllnodes] = useState<any>(null);
  // 教学模块的ref
  const TeachingmoduleRef = useRef<any>(null);
  // 模块下所有知识点信息 包含  所有后修连线
  const moudelNodedata = useRef<any>({
    allhouxiuedegs:[],
    modulenodeList:{},
    notNeedToStudy:[]
  });
  // 当前节点是否要记录学习进度
  const [notrecord, setNotrecord] = useState<boolean>(false);
  // 当前要置灰的点
  const [grayedoutarr, setGrayedoutarr] = useState<any>([]);
  const [previewEntity, setPreviewEntity] = useState<any>({});
  // 绑定的大纲id
  const [bindCourseId, setBindCourseId] = useState('');
  // 当前皮肤模式
  const [skin, setSkin] = useState<string>('2');

  useListener('nodelearning', debounce(({id,finishRate,masterRate}) => {
    if(showrete){
      refresh();
    }
  },1000))

  async function getInfo() {
    const [err, res] = await Utils.tryitRequestDefaultFn(
      CheckService.infoInstalments,
    )({
      mapId: newmapid,
    });
    if (err) {
      // message.error(err.message ?? '获取信息失败');
    } else {
      setBindCourseId(res?.data?.data?.courseCode || '');
    }
  }

  useEffect(() => {
    // 图谱的编辑页面和课程里的图谱编辑
    if (!newmapid) return
    getInfo()
  }, [newmapid])

  // 刷新页面统计
  const refresh = () => {
    getTeacherOrStudentRate(newmapid, isMain ? 0 : 1)
    // getKnowledgeStudyRate({
    //   courseId: query.id,
    //   mapId: newmapid,
    //   nodeIdList: [],
    //   semester: query.sm,
    // }).then((res: any) => {
    //   if (res.status == 200) {
    //     setNoderate({
    //       achievingRateTotal:res.data.achievingRateTotal,
    //       finishRateTotal: res.data.finishRateTotal,
    //       masterRateTotal: res.data.masterRateTotal,
    //     });
    //   }
    // });
  }

  useEffect(() => {
    initcoursemap(newmapid);
    setMaptype(2);

    return () => {
      if(graph){
        graph.current = null;
      }
    };
  }, []);

  //查询课程地图的节点完成率和掌握率
  const getknowlerate = (mapId:string,nodeIdList:any[]) => {
    getKnowledgeStudyRate({
        "courseId": courseid,
        "mapId": mapId,
        "nodeIdList": nodeIdList,
        "semester": query.sm
      }).then((res:any)=>{
        if(res.status == 200){
          setNoderate({
            achievingRateTotal:res.data.achievingRateTotal || 0,
            finishRateTotal: res.data.finishRateTotal || 0,
            masterRateTotal: res.data.masterRateTotal || 0
          })
          // let obj:any = {};
          // res.data.detailDTOList.forEach((item:any)=>{
          //   obj[item.nodeId] = {
          //     finishRate: item.finishRate,
          //     masterRate: item.masterRate
          //   }
          // })
          // setAllnodes(obj);
        }
    })
  }

    // 获取知识点掌握率
    const getTeacherOrStudentRate = (mapId:string, type: number) => {
      statisticsApi.getTeacherOrStudentAllRate({
        courseId: courseid,
        mapId: mapId,
        /** 0 教师端, 1学生端 */
        type: type
      }).then((res: any) => {
        if (res.data.status == 200) {
          setNoderate({
            achievingRateTotal: res.data.data.nodeAttainment || 0,
            finishRateTotal: res.data.data.totalFinishRate || 0,
            masterRateTotal: res.data.data.totalMasterRate || 0,
          });
        }
      });
    };

  const initcoursemap = (mapid: string = '') => {
    querymapbyid({
      mapId: mapid,
      courseId:courseid,
      statistics:perviewtype === 0 ? 0 : 1,  //是否返回统计数据
      isTe: isMain ? 0 : 1 // 0是老师 1是学生
    }).then((res: any) => {
      if (res.status == 200) {
        let newmapdata: any = null;
        if(showrete){
          let arr:any = [];
          res.data.nodesVos.forEach((item: any) => {
            let {data} = JSON.parse(item.valueMap);
            if(data.type == 3){
              arr.push(item.nodeId);
            }
          });
          getTeacherOrStudentRate(mapid, isMain ? 0 : 1)
          // if (isMain) {
          //   getTeacherOrStudentRate(mapid, 0)
          // } else {
          //   getknowlerate(mapid,arr);
          // }
        }
        // 如果地图有数据
        if (res.data.nodesVos.length) {
          newmapdata = makedata(res.data.nodesVos, res.data.relationVos);
        } else {
          // 如果没有数据 就显示默认的节点
          const defaultNodes: any = defaultNode();
          newmapdata = makedata(defaultNodes.nodesVos, defaultNodes.relationVos);
        }
        setMapInfo(newmapdata);
        console.log(newmapdata);
      } else if (res.status == 400) {
        // 已经删除
        setErrorinfo(5);
        // message.info('当前课程地图已被删除！')
      } else {
        setErrorinfo(6);
        message.error(res.message);
      }
    });
  };

  const makedata = (nodesVos: any, relationVos: []) => {
    let linkmapid: any = [];
    let allllmodulearr:any = [];
    let newnodes = nodesVos.map((item: any) => {
      let data = JSON.parse(item.valueMap);
      if (data.data.type == 5) {
        allllmodulearr.push({ label: data.data.label, value: item.nodeId });
      }
      let obj = {
        ...item,
        id: item.nodeId,
        data: {
          ...defaultNodeData,
          ...data.data,
          mapId: item.mapId || null
        },
        zIndex: 3,
        shape: 'react-shape',
        component: 'react-compont-v3',
        type: data.data.type,
        visible: data.data.visible == undefined ? true : data.data.visible
      };
      // 把跨界的节点存起来 下面建立连线
      if (obj.data.linkmapid.length) {
        obj.data.linkmapid.forEach((item2: any) => {
          linkmapid.push({
            ...item2,
            thisobj: obj
          });
        });
      }
      if (data.data.type == 4) {
        obj.size = [MapConfig.marjor.size[0], MapConfig.marjor.size[1]];
        obj.width = MapConfig.marjor.size[0];
        obj.height = MapConfig.marjor.size[1];
      } else if (data.data.type == 3) {
        obj.size = [MapConfig.course.size[0], MapConfig.course.size[1]];
        obj.width = MapConfig.course.size[0];
        obj.height = MapConfig.course.size[1];
      } else if (data.data.type == 1) {
        if (data.data.isroot) {
          obj.size = [MapConfig.course.size[0], MapConfig.course.size[1]];
          obj.width = MapConfig.course.size[0];
          obj.height = MapConfig.course.size[1];
        } else {
          obj.size = [MapConfig.fenlei.size[0], MapConfig.fenlei.size[1]];
          obj.width = MapConfig.fenlei.size[0];
          obj.height = MapConfig.fenlei.size[1];
        }
      } else if (data.data.type == 2) {
        obj.size = [MapConfig.knowledge.size[0], MapConfig.knowledge.size[1]];
        obj.width = MapConfig.knowledge.size[0];
        obj.height = MapConfig.knowledge.size[1];
      } else if(data.data.type == 5){
        obj.size = [MapConfig.teachingmodule.size[0], MapConfig.teachingmodule.size[1]];
          obj.width = MapConfig.teachingmodule.size[0];
          obj.height = MapConfig.teachingmodule.size[1];
      }else {
        console.log('未知类型');
      }
      return obj;
    });

    let newedges = relationVos.map((item: any) => {
      const data = JSON.parse(item.data);
      //每个边的初始化数据
      const obj = {
        ...item,
        data: {
          ...data,
          newadd: data.isnew || false
        },
        visible: data.visible == undefined ? true : data.visible
      };
      if (data.type == 1) {//包含
        obj.attrs = {
          line: {
            stroke: "#B3BAC4", // 指定 path 元素的填充色
            strokeWidth: 1,
            targetMarker: {
              args: { size: 8 },
              name: 'classic'
            }
          }
        };
      } else if (data.type == 2) {//等价
        obj.attrs = {
          line: {
            stroke: "#B3BAC4", // 指定 path 元素的填充色
            strokeWidth: 1,
            targetMarker: null,
            strokeDasharray: 5
          }
        };
      } else if (data.type == 3) {//后续
        obj.attrs = {
          line: {
            stroke: "#B3BAC4", // 指定 path 元素的填充色
            strokeWidth: 1,
            // targetMarker: null
            // #333333
            strokeDasharray: 5,
            targetMarker: {
              args: { size: 8 },
              name: 'classic'
            }
          }
        };
      } else if (data.type == 4) {//关联
        obj.attrs = {
          line: {
            stroke: "#B3BAC4", // 指定 path 元素的填充色
            strokeWidth: 1,
            // targetMarker: null
            // #333333
            strokeDasharray: 5,
            targetMarker: {
              args: { size: 8 },
              name: 'classic'
            }
          }
        };
      }else if (data.type == 5) {//后续
        obj.attrs = {
          line: {
            stroke: "#B3BAC4", // 指定 path 元素的填充色
            strokeWidth: 1,
            // targetMarker: null
            // #333333
            strokeDasharray: 5,
            targetMarker: {
              args: { size: 8 },
              name: 'classic'
            }
          }
        };
      }else if (data.type == 6) {//前序
        obj.attrs = {
          line: {
            stroke: "#B3BAC4", // 指定 path 元素的填充色
            strokeWidth: 1,
            // targetMarker: null
            // #333333
            strokeDasharray: 5,
            targetMarker: {
              args: { size: 8 },
              name: 'classic'
            }
          }
        };
      }
      return obj;
    });
    moudelNodedata.current = getKnowledgeBymouldes(allllmodulearr,{
      nodes: newnodes,
      edges: newedges.map((item:any)=>{
        return{
          ...item,
          source:{
            cell:item.source
          },
          target:{
            cell:item.target
          }
        }
      })
    })
    // 计算出来要置灰的点
    const grayedoutarr = computingKnowledge(moudelNodedata.current,newnodes);
    setGrayedoutarr(grayedoutarr);
    // 如果有跨课的节点  就建立连线
    if (linkmapid.length) {
      linkmapid.forEach((item: any) => {
        //先查找有没有这个节点
        let getnode = newnodes.find((item2: any) => item2.id == item.nodeId);
        if (getnode) {
          const obj: any = {
            data: { visible: true, type: item.type, isnew: true },
            source: item.thisobj.id,
            target: item.nodeId,
            type: item.type
          };
          if (item.type == 1) {//包含
            obj.attrs = {
              line: {
                stroke: "#B3BAC4", // 指定 path 元素的填充色
                strokeWidth: 1,
                targetMarker: {
                  args: { size: 8 },
                  name: 'classic'
                }
              }
            };
          } else if (item.type == 2) {//等价
            obj.attrs = {
              line: {
                stroke: "#B3BAC4", // 指定 path 元素的填充色
                strokeWidth: 1,
                targetMarker: null,
                strokeDasharray: 5
              }
            };
          } else if (item.type == 3) {//后续
            obj.attrs = {
              line: {
                stroke: "#B3BAC4", // 指定 path 元素的填充色
                strokeWidth: 1,
                // targetMarker: null
                // #333333
                strokeDasharray: 5,
                targetMarker: {
                  args: { size: 8 },
                  name: 'classic'
                }
              }
            };
          } else if (item.type == 4) {//后续
            obj.attrs = {
              line: {
                stroke: "#B3BAC4", // 指定 path 元素的填充色
                strokeWidth: 1,
                // targetMarker: null
                // #333333
                strokeDasharray: 5,
                targetMarker: {
                  args: { size: 8 },
                  name: 'classic'
                }
              }
            };
          }
          newedges.push(obj);
        }
      });
    }

    return {
      nodes: newnodes,
      edges: newedges
    };
  };

  // 更新节点的对比辨析字段
  const updatanodecompare = (compare: any) => {
    const allnodes = graph.getNodes();
    // 先把所有的都改成false
    allnodes.forEach((element: any) => {
      let data = element.getData();
      element.updateData({
        ...data,
        isDiscriminate: false
      });
    });
    // 再更新节点
    compare.forEach((id: any) => {
      const node = graph.getCellById(id);
      if (node) {
        let data = node.getData();
        node.updateData({
          ...data,
          isDiscriminate: true
        });
      }
    });
  };

  // 更新节点的data
  const updatanode = (id: string, data: any) => {
    const node = graph.getCellById(id);
    // console.log('更新节点的data', node.getData());
    let nodedata = node.getData();
    let newdata = {
      ...nodedata,
      ...data
    };
    node.updateData(newdata);
  };

  // 居中节点
  const centerednode = (id: string) => {
    let cell = graph.getCellById(id);
    graph.getNodes().forEach((item: any) => {
      // 设置节点透明
      item.attr('foreignObject/opacity', 0.2);
    });
    graph.getEdges().forEach((item: any) => {
      // 设置节点透明
      item.attr('line/stroke', '#333333');
    });
    // 设置节点高亮
    cell.attr('foreignObject/opacity', 1);
    graph.centerCell(cell, { animation: { duration: 400 } });
  };


  // 设置状态 这里用防抖是因为 点击跨课 标签的点击事件 和 图谱 节点点击事件 会同时触发 取最后一次的
  const setVisiblestate = debounce((e: any) => {
    setVisible(e);
  }, 100);

  return (
    <div className={skin == '1' ? "Atlasmap_view" : "Atlasmap_view_dark"}>
      {/*  1参数错误  2当前专业暂无课程数据 3暂无专业图谱数据  4当前课程地图已下架  5当前课程地图已被删除  6其他异常 */}
      {
        errorinfo !== 0 &&
        <div className="message_view">
          {errorinfo == 1 && <span>{t("参数错误")}</span>}
          {errorinfo == 2 && <span>{t("当前专业暂无课程数据")}</span>}
          {errorinfo == 3 && <span>{t("暂无专业图谱数据")}</span>}
          {errorinfo == 4 && <span>{t("当前课程地图已下架")}</span>}
          {errorinfo == 5 && <span>{t("当前课程地图已被删除")}</span>}
          {errorinfo == 6 && <span>{t("其他异常")}</span>}
        </div>}


        {showrete && <div className='jindu_box'>
            <div style={{width: '200px',height:'100%'}}>
                <ProgressBox label="目标达成度"
                data={noderate?.achievingRateTotal}
                color="#549CFF"></ProgressBox>
            </div>
            <div style={{width: '200px',height:'100%'}}>
                <ProgressBox label="完成率"
                 data={noderate?.finishRateTotal}
                color="#44D6E5"></ProgressBox>
            </div>
            <div style={{width: '200px',height:'100%'}}>
                <ProgressBox label="掌握率"
                data={noderate?.masterRateTotal}
                 color="#F3B764"></ProgressBox>
            </div>
        </div>  }
      <Header skin={skin} mapdata={mapinfo} graph={graph} maptype={maptype} perviewtype={query.perviewtype || 3} querytype={querytype} inputtext={inputtext}
        setInputtext={(e: any) => setInputtext(e.target.value)}
        setSelecttype={setSelecttype}
        typeonselect={(LabeledValue: any) => {
          setQuerytype(LabeledValue);
          setVisible(6);
          setInputtext('');
          searchref.current.querynode();
        }}
        search={(e: any) => {
          setVisible(6);
          setQuerytype('0');
          searchref.current.querynode();
        }}
        updataskin={setSkin}
        setVisible={setVisible}  setLayouttype={setLayouttype} >
      </Header>
      <div className="map_content_view">
        <MapX6 skin={skin} mapdata={mapinfo} scale={maptype == 1} showzoombtn={true} maptype={maptype} showunfold={true} layouttype={layouttype} showmindmap={true}
          nodeClick={(node: any) => {
            const data = node.getData();
            if (data.type == 2) {
              setDrawerdata(node);
              setSelectmapid(data.mapId);
              recordCurrentNode({
                "courseId":courseid,
                "mapId": data.mapId,
                "nodeId":  node.id,
                "semester": query.sm
              })
              if(query.type == 'microMajor'){
                checkshowmessage(node,moudelNodedata.current,query.id,data.mapId,query.sm).then((res:any)=>{
                  if(!res){
                    setNotrecord(true);
                    message.warning('现在学习该节点将不计掌握率完成率，请先完成先修模块的学习！')
                  }
                  setVisiblestate(1);
                })
              }else{
                setVisiblestate(1);
              }
            } else if (data.type == 3) {
              setSelectcourseid(data.course.courseId);
              setVisiblestate(14);
            } else if(data.type == 5){
              if (TeachingmoduleRef.current) {
                TeachingmoduleRef.current.setaddForm({
                  nodeId: node.id,
                  ...data
                })
              }
              setVisiblestate(20);
            }else{
              console.log('其他类型的节点不处理');
            }
          }}
          initover={(e: any) => {
            setGraph(e);
            if(grayedoutarr.length){
              grayedoutarr.forEach((item:any) => {
                let element = e.getCellById(item);
                if(element){
                  const data: any = element.getData();
                  element.updateData({
                    ...data,
                    showgrey: true
                  });
                }
              });
            }
          }}
          clicktag={(e: any) => {
            setSelectnode(e);
            setVisiblestate(18);
          }}>
        </MapX6>
        {/* 搜索管理 */}
        <Search skin={skin} ref={searchref} graph={graph} querytype={querytype} centerednode={(e: any) => { }} selecttype={selecttype} inputtext={inputtext} visible={visible} setVisible={(e: number) => setVisible(e)}></Search>
        {/* 知识点详情 */}
        {/* 节点详情 */}
        <Drawer
          placement="right"
          mask={false}
          title={
            <div style={{ position: 'relative', width: '100%', height: '100%' }}>
              <img style={{ width: '100%', height: 'auto' }} src={skin == '1'? title_bg1:title_bg2 } />
              <img onClick={() => setVisible(0)} style={{ position: 'absolute', right: '20px', width: '15px', top: '22px' }} src={skin == '1' ? close_icon1: close_icon2} alt="" />
            </div>}
          closable={false}
          onClose={() => {
            setVisible(0);
            setDrawerdata(null);
          }}
          visible={visible == 1}
          getContainer={false}
          style={{ position: 'absolute' }}
          width="600px"
          className="node_detail_view">

          <Rdrawer graph={graph} x6node={drawerdata} visible={visible} onback={() => setDrawerdata(null)}
            updatanodecompare={updatanodecompare}
            centerednode={centerednode}
            updatanode={updatanode}
            perviewtype={2}
            mapid={selectmapid}
            previewEntity={previewEntity}
            setPreviewEntity={setPreviewEntity}
            courseid={courseid}
            coursename={coursename || ''}
            isedit={false}
            isv3={true}
            isMain={isMain}
            notrecord={notrecord}
            setVisible={(e: number) => setVisible(e)}
            courseCode={couresSyllabusCode || bindCourseId || ''}>
            
          </Rdrawer>
        </Drawer>
        {/* 课程详情 */}
        <CourseInfo visible={visible} courseid={selectcourseid} onCancel={() => setVisible(0)}></CourseInfo>
        {/* 展示跨课关系 */}
        {
          maptype == 2 && visible == 18 &&
          <RelationMap mapid={newmapid} graph={graph} selectnode={selectnode} visible={visible} onClose={() => setVisible(0)}></RelationMap>}

        {/* 知识点达成度 */}
        {
          maptype == 2 && visible == 12 &&
          <Achievementdegree visible={visible} centerednode={centerednode} courseid={courseid} isv3={true} mapid={newmapid} perviewtype={query.perviewtype || perviewtype} setVisible={setVisible} onCancel={() => setVisible(0)}></Achievementdegree>}

        {/* 教学模块展示 */}
        <Teachingmodule
            visible={visible}
            setVisible={setVisible}
            titlemodel={2}
            skin={skin}
            graph={graph}
            moudelNodedata={moudelNodedata.current}
            ref={TeachingmoduleRef}></Teachingmodule>

      </div>
    </div>);

};

export default Atlasmap;
