import React, { useEffect, useState } from "react";
import './index.less';
import { Button, Form, Input, InputNumber, Modal, Table, message, Space, DatePicker, Switch } from "antd";
import type { ColumnsType } from 'antd/es/table';
import { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { insertlabel, getLabels, deleteLabel, updateLabel, setAllowTeacher, getIsAllowTeacher} from '@/api/labelConfig';
import { useSelector } from "umi";
const { RangePicker } = DatePicker;
// 类型定义
interface LabelItem {
  id: number;
  name: string;
  displayName: string;
  backgroundColor: string;
  font: string;
  priority: number | null;
  type: number;
  createCode: string;
  createBy: string;
  updateBy: string;
  createTime: string;
  updateTime: string;
  contentid?: string;
}

interface LabelFormValues {
  name: string;
  priority: number;
  labelcolor: string;
  bgcolor: string;
  showname: string;
}

// 修改筛选表单接口
interface FilterFormValues {
  name?: string;
  createBy?: string;
  createTime?: string;  // 修改为createTime
}

const LabelTable: React.FC<{ type: number }> = ({ type }) => {
  const [dataSource, setDataSource] = useState<LabelItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAddToPublicModalOpen, setIsAddToPublicModalOpen] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<LabelItem | null>(null);
  const [switchEnabled, setSwitchEnabled] = useState(false);
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();
  const [filterValues, setFilterValues] = useState<any>({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [isAllowTeacher, setIsAllowTeacher] = useState(false);
  const { userInfo } = useSelector<any, any>((state) => state.global);

  // 将 columns 定义移到组件内部
  let columns: ColumnsType<LabelItem> = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center' as const,
    },
    {
      title: '显示顺序',
      dataIndex: 'priority',
      key: 'priority',
      align: 'center' as const,
      render: (priority: number | null) => priority || '-'
    },
    {
      title: '标签样式',
      dataIndex: 'displayName',
      key: 'displayName',
      align: 'center' as const,
      render: (text: string, record: LabelItem) => (
        <div style={{ 
          backgroundColor: record.backgroundColor, 
          color: record.font, 
          padding: '2px 7px', 
          borderRadius: '4px',
          display: 'inline-block',
          textAlign: 'center'
        }}>
          {text}
        </div>
      ),
    },
    {
      title: '创建人',
      dataIndex: 'createBy',
      key: 'createBy',
      align: 'center' as const,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center' as const,
      render: (text: string) => {
        const date = new Date(text);
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
      },
    },
    {
      title: '操作',
      key: 'action',
      align: 'center' as const,
      render: (_: any, record: LabelItem) => (
        <Space>
          <Button 
            type="text" 
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
          {
            type === 1 && isAllowTeacher && (
              <Button 
                type="text" 
                icon={<PlusOutlined />}
                onClick={() => handleAddToPublic(record)}
              >
                新增至公共标签
              </Button>
            )
          }
          {/* 资源标签新增至公共标签 需要再编辑确认 */}
          {
            type === 2 && isAllowTeacher && (
              <Button 
                type="text" 
                icon={<PlusOutlined />}
                onClick={() => handleAddToPublic(record)}
              >
                新增至公共标签
              </Button>
            )

          }
        </Space>
      ),
    },
  ];

  if(type === 2){
    columns = [
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        align: 'center' as const,
        width: '50%',
      },        
      {
        title: '更新时间',
        dataIndex: 'updatetime',
        key: 'updatetime',
        align: 'center' as const,
      },
      {
        title: '操作',
        key: 'action',
        align: 'center' as const,
        render: (_: any, record: LabelItem) => (
          <Space>
            <Button 
              type="text" 
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            >
              编辑
            </Button>
            <Button 
              type="text" 
              danger 
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record)}
            >
              删除
            </Button>            
            {/* 资源标签新增至公共标签 需要再编辑确认 */}
            {isAllowTeacher && <Button 
              type="text" 
              icon={<PlusOutlined />}
              onClick={() => handleAddToPublic(record)}
            >
              新增至公共标签
            </Button>}
          </Space>
        ),
      },
    ]
  }

  // 获取当前是否可以新增至公共标签
  const canAddToPublic = async () => {
    const res = await getIsAllowTeacher();
    if(res.status == 200){
      setIsAllowTeacher(res.data);
      setSwitchEnabled(res.data);
    }
  }

  // 修改获取标签列表函数
  const fetchLabels = async (page = 1, pageSize = 10, filters: any = {}) => {
    try {
      setLoading(true);
      const params = {
        page,
        size: pageSize,
        type: type,
        ...filters,
        createTime: filters.createTime,  // 使用单个日期参数
      };
      
      const res = await getLabels(params);
      if (res) {
        setDataSource(res.results || []);
        setPagination({
          current: res.page,
          pageSize,
          total: res.total || 0
        });
      }
    } catch (error) {
      // Error handling might need adjustment if the API function rejects promises
      message.error('获取标签列表失败');
      // Consider logging the actual error for debugging
      console.error('获取标签列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLabels();
    canAddToPublic();
  }, []);

  const handleTableChange = (newPagination: any) => {
    fetchLabels(newPagination.current, newPagination.pageSize, filterValues);
  };

  const handleFilter = (values: any) => {
    // 格式化日期
    let formattedValues:any = null;
    if(type === 2){
      formattedValues = {
        labelNames:values.value ? [values.value] : null,
        fragmentStartDateTime: values.createTime ? values.createTime[0].format('YYYY-MM-DD hh:mm:ss') : null,
        fragmentEndDateTime: values.createTime? values.createTime[1].format('YYYY-MM-DD hh:mm:ss') : null
      };
    }else{
      formattedValues = {
        createTime: values.createTime ? values.createTime.format('YYYY-MM-DD') : null,
        value:values.value
      };
    }
    setFilterValues(formattedValues);
    fetchLabels(1, pagination.pageSize, formattedValues);
  };

  const handleReset = () => {
    setFilterValues({});
    fetchLabels(1, pagination.pageSize);
  };

  const handleSubmit = async (values: LabelFormValues) => {
    try {
      const params = {
        backgroundColor: values.bgcolor,
        displayName: values.showname,
        font: values.labelcolor,
        name: values.name,
        priority: values.priority,
        type: type, //标签类型 0公共 1自定义
        divToPublic: false
      };
      const res = await insertlabel(params);
      if (res) {
        message.success('创建成功');
        setIsModalOpen(false);
        form.resetFields();
        fetchLabels(); // 刷新列表
      }
    } catch (error) {
      message.error('创建失败');
    }
  };

  const handleDelete = (record: LabelItem) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个标签吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          const res = await deleteLabel({ id: record.id, type: record.type });
          if (res) {
            message.success('删除成功');
            fetchLabels(1, pagination.pageSize, filterValues);
          }
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  const handleEdit = (record: LabelItem) => {
    setCurrentRecord(record);
    editForm.setFieldsValue({
      name: record.name,
      priority: record.priority || 1,
      labelcolor: record.font,
      bgcolor: record.backgroundColor,
      showname: record.displayName,
    });
    setIsEditModalOpen(true);
  };

  const handleEditSubmit = async (values: LabelFormValues) => {
    try {
      if (!currentRecord) return;
      
      const params = {
        id: currentRecord.id,
        backgroundColor: values.bgcolor,
        displayName: values.showname,
        font: values.labelcolor,
        name: values.name,
        priority: values.priority,
        type: currentRecord.type
      };
      
      const res = await updateLabel(params);
      if (res) {
        message.success('更新成功');
        setIsEditModalOpen(false);
        editForm.resetFields();
        setCurrentRecord(null);
        fetchLabels(pagination.current, pagination.pageSize, filterValues);
      }
    } catch (error) {
      message.error('更新失败');
    }
  };

  const handleSwitchChange = (checked: boolean) => {
    setSwitchEnabled(checked);
    // 这里可以添加开关变化时的处理逻辑
    setAllowTeacher({mark:checked}).then(res => {      
      if(res.status == 200){
        message.success('设置成功');
      }else{
        message.error('设置失败');
      }
    });
  };

// 资源标签新增至公共标签
  const handleAddToPublic = (record: LabelItem) => {
    setCurrentRecord(record);
    editForm.setFieldsValue({
      name: record.name,
      showname: record.name.slice(0,1),
    });
    setIsAddToPublicModalOpen(true);
  };

  // 自定义标签新增至公共标签
  const handleAddToPublicCustom = (record: LabelItem) => {
    Modal.confirm({
        title: '确认添加',
        content: '确定要将此标签添加至公共标签吗？',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
         // TODO: 这里需要调用添加至公共标签的API
         const newdata:any =  {...record};
         delete newdata.id;
         const res = await insertlabel({
           ...record,
           type: 0,
           divToPublic:true
         });
         if(res.status == 200){
           message.success('添加成功');
         }else{
           message.error(res.message ||'添加失败');
         }
        },
      });
  }

  /**
   * 处理资源标签添加到公共标签的提交
   *
   * @param values 表单值
   */
  const handleAddToPublicSubmit = async (values: LabelFormValues) => {
    try {
      if (!currentRecord) return;
      const params = {
        id: currentRecord.id,
        backgroundColor: values.bgcolor,
        displayName: values.showname,
        font: values.labelcolor,
        name: values.name,
        priority: values.priority,
        type: 0, // 设置为公共标签
        divToPublic:true,      
        contentid: currentRecord.contentid,
      };
      
      const res = await insertlabel(params);
      if (res.status == 200) {
        message.success('添加成功');
        setIsAddToPublicModalOpen(false);
        editForm.resetFields();
        setCurrentRecord(null);
        fetchLabels(pagination.current, pagination.pageSize, filterValues);
      }else{
        message.error(res.message ||'添加失败');
      }
    } catch (error) {
      message.error('添加失败');
    }
  };

  const isSuper = userInfo.roles?.map((item: any) => item.roleCode)?.includes('r_sys_manager') || //是否是系统管理员
    userInfo.roles?.map((item: any) => item.roleCode)?.includes('r_course_manager') || //是否是课程管理员
    userInfo.roles?.map((item: any) => item.roleCode)?.includes('r_second_manager') || //第二权限
    userInfo.roles?.map((item: any) => item.roleCode)?.includes('admin_S1'); // admin

  return (
    <div className="LabelTable_view">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px',height: (type !=0 || isSuper) ? '52px' :'0px' }}>
        {isSuper && <Space>
          {/* 当 type 不为 2 时显示新建按钮 */}
          {type !== 2 && (
            <Button onClick={() => setIsModalOpen(true)} icon={<PlusOutlined />}>
              新建
            </Button>
          )}
        </Space>}
        {type === 0 && isSuper && (
            <div style={{display:'flex',alignItems:'center'}}>
                <Switch
                checked={switchEnabled}
                onChange={handleSwitchChange}
                checkedChildren="开启"
                unCheckedChildren="关闭"
                />
                <span style={{marginLeft:'10px'}}>教师可新增至公共标签</span>
            </div>
        )}
        {/* 当 type 为 1 或 2 时显示筛选表单 */}
        {(type === 1 || type === 2) && <FilterForm onFilter={handleFilter} onReset={handleReset} type={type} />}
      </div>
      <Table 
        dataSource={dataSource} 
        columns={columns} 
        loading={loading}
        rowKey="id"
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条`,
        }}
        onChange={handleTableChange}
      />
      <Modal 
        title="新建标签" 
        open={isModalOpen} 
        onOk={() => form.submit()} 
        onCancel={() => {
          setIsModalOpen(false);
          form.resetFields();
        }} 
        getContainer={false}
        destroyOnClose
        maskClosable={false}
      >
        <LabelForm form={form} onSubmit={handleSubmit} />
      </Modal>
      <Modal 
        title="编辑标签" 
        open={isEditModalOpen} 
        onOk={() => editForm.submit()} 
        onCancel={() => {
          setIsEditModalOpen(false);
          editForm.resetFields();
          setCurrentRecord(null);
        }} 
        getContainer={false}
        destroyOnClose
        maskClosable={false}
      >
        <LabelForm form={editForm} onSubmit={handleEditSubmit} />
      </Modal>
      <Modal 
        title="新增至公共标签" 
        open={isAddToPublicModalOpen} 
        onOk={() => editForm.submit()} 
        onCancel={() => {
          setIsAddToPublicModalOpen(false);
          editForm.resetFields();
          setCurrentRecord(null);
        }} 
        getContainer={false}
        destroyOnClose
        maskClosable={false}
      >
        <LabelForm form={editForm} onSubmit={handleAddToPublicSubmit} isInsert={true} />
      </Modal>
    </div>
  );
};

// 标签表单组件
const LabelForm: React.FC<{
  form: any;
  onSubmit: (values: LabelFormValues) => Promise<void>;
  isInsert?: boolean;
}> = ({ form, onSubmit, isInsert = false }) => {
  const [preview, setPreview] = useState({
    text: '',
    bgColor: '#1890ff',
    textColor: '#ffffff',
  });

  // 添加 useEffect 监听表单值变化
  useEffect(() => {
    const values = form.getFieldsValue();
    if (values.showname) {
      setPreview(prev => ({ ...prev, text: values.showname }));
    }
    if (values.labelcolor) {
      setPreview(prev => ({ ...prev, textColor: values.labelcolor }));
    }
    if (values.bgcolor) {
      setPreview(prev => ({ ...prev, bgColor: values.bgcolor }));
    }
  }, [form]);

  const handleColorChange = (field: 'bgColor' | 'textColor', value: string) => {
    setPreview(prev => ({ ...prev, [field]: value }));
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    // 当名称输入后，自动设置显示名为第一个字
    if (name) {
      form.setFieldValue('showname', name.charAt(0));
      setPreview(prev => ({ ...prev, text: name.charAt(0) }));
    }
  };

  return (
    <Form
      form={form}
      name="basic"
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 16 }}
      onFinish={onSubmit}
      autoComplete="off"
    >
      <Form.Item
        label="名称"
        name="name"
        rules={[{ required: true, message: '请输入名称' }]}
      >
        <Input 
          placeholder="请输入名称" 
          onChange={handleNameChange}
          disabled={isInsert} // 只在新增模式下禁用
        />
      </Form.Item>

      <Form.Item
        label="显示顺序"
        name="priority" 
        initialValue={1}
      > 
        <InputNumber min={1} defaultValue={1} />
      </Form.Item>
      <Form.Item
        label="标签文本颜色"
        name="labelcolor"
        initialValue="#FFFFFF"
        rules={[{ required: true, message: '请选择标签样式' }]}
      >
        <Input 
          type="color" 
          style={{width:'100px'}} 
          onChange={(e) => handleColorChange('textColor', e.target.value)} 
        />
      </Form.Item>
      <Form.Item
        label="标签背景色"
        name="bgcolor"
        initialValue="#1890ff"
        rules={[{ required: true, message: '请选择标签样式' }]}
      >
        <Input 
          type="color" 
          style={{width:'100px'}} 
          onChange={(e) => handleColorChange('bgColor', e.target.value)} 
        />
      </Form.Item>
      <Form.Item
        label="显示名"
        name="showname"
        rules={[
          { required: true, message: '请输入显示名' },
          { max: 2, message: '显示名不能超过2个字' }
        ]}
      >
        <Input 
          maxLength={2}
          onChange={(e) => setPreview(prev => ({ ...prev, text: e.target.value }))} 
          placeholder="请输入显示名" 
        />
      </Form.Item>
      <Form.Item
        label="预览"
        name="preview"
      >
        <div 
          className="pewview_tag" 
          style={{
            background: preview.bgColor,
            color: preview.textColor,
            padding: '2px 7px',
            borderRadius: '4px',
            display: 'inline-block',
            textAlign: 'center',
            minWidth: '24px',
            height: '24px',
            lineHeight: '20px'
          }}
        >
          {preview.text || '预览'}
        </div>
      </Form.Item>
    </Form>
  );
};

// 修改筛选表单组件
const FilterForm: React.FC<{
  onFilter: (values: FilterFormValues) => void;
  onReset: () => void;
  type:number;
}> = ({ onFilter, onReset, type }) => {
  const [form] = Form.useForm();

  const handleReset = () => {
    form.resetFields();
    onReset();
  };

  return (
    <Form
      form={form}
      layout="inline"
      onFinish={onFilter}
      style={{ marginBottom: '20px' }}
    >
      <Form.Item name="value">
        <Input placeholder="请输入标签名称" allowClear />
      </Form.Item>
      <Form.Item name="createTime">
        {type==2 ? <RangePicker style={{width:'400px'}} showTime /> :<DatePicker style={{width:'200px'}} /> }
      </Form.Item>
      <Form.Item>
        <Space>
          <Button type="primary" icon={<SearchOutlined />} htmlType="submit">
            搜索
          </Button>
          <Button onClick={handleReset}>重置</Button>
        </Space>
      </Form.Item>
    </Form>
  );
};

export default LabelTable;