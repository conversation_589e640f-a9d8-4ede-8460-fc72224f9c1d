import {
  addmap,
  copymap,
  deletemap,
  querymap,
  updatamapstatus,
} from '@/api/coursemap';
import { IconFont } from '@/components/iconFont';
import useLocale from '@/hooks/useLocale';
import { ApiOutlined, DashOutlined, PlusCircleFilled } from '@ant-design/icons';
import {
  Button,
  Checkbox,
  Divider,
  Dropdown,
  Form,
  Input,
  List,
  MenuProps,
  message,
  Modal,
  Pagination,
  Popover,
  Select,
  Space,
  Table,
  Tag,
  Tooltip,
  TableColumnsType,
} from 'antd';
import debounce from 'lodash/debounce';
import moment from 'moment';
import React, { FC, useEffect, useState } from 'react';
import { useHistory, useSelector } from 'umi';
import Search from '../components/Search';
import './index.less';
import { useScreenWidth } from '@/hooks/useScreenWidth';
import { usePermission } from '@/hooks/usePermission';
const nomapcover = require('@/assets/imgs/coursemap/nomap.png');
const { confirm } = Modal;
const { Option } = Select;
const CheckboxGroup = Checkbox.Group;

const Minemap: FC<{}> = () => {
  const { location }: any = useHistory();
  const { t } = useLocale();
  // form表单
  const [form] = Form.useForm();
  // 设置 indeterminate 状态，只负责样式控制
  const [indeterminate, setIndeterminate] = useState<boolean>(false);
  // 指定当前是否选中
  const [checkAll, setCheckAll] = useState<boolean>(false);
  // 表格数据
  const [tableData, setTableData] = useState<any[]>([]);
  // 弹窗的开关
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  // 发布范围
  const [shareRange, setShareRange] = useState<any[]>([]);
  // 地图名称
  const [mapName, setMapName] = useState<string>('');
  // table分页
  const [pagination, setPagination] = useState<any>({
    current: 1,
    pageSize: 18,
    total: 0,
    showQuickJumper: true,
    showSizeChanger: true,
    showTotal: (total: number) => t('共{name}条', String(total)),
  });
  const { mobileFlag, leftRightVisible } = useSelector<{ config: any }, any>(
    ({ config }) => config,
  );

  // 当前选中的行
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  // 检索的添加
  const [searchform, setSearchfrom] = useState<any>({
    search_name: null,
    search_status: null,
  });
  // 是否排序
  const [sorter, setSorter] = useState<any>('0');

  // 加载完毕
  const [loadingover, setLoadingover] = useState<boolean>(false);
  // 将要修改的状态
  const [mapstatus, setMapstatus] = useState<number>(0);
  // 修改地图状态弹窗的开关
  const [isModalVisible2, setIsModalVisible2] = useState<boolean>(false);
  // 当前选择的行
  const [selectedid, setSelectedId] = useState<any>(null);
  // 是否是批量
  const [isbatch, setIsbatch] = useState<boolean>(false);
  // 视图切换
  const [modeSwitch, setModeSwitch] = useState<boolean>(
    JSON.parse(localStorage.getItem('learn_view_mode') || '{}').maplist != '0',
  );

  const [operatMenuVisible, setOpreatMenuVisible] = useState<boolean>(false);
  // 发布按钮是否显示
  const [showsharebtn, setShowSharebtn] = useState<boolean>(false);
  // 删除按钮是否显示
  const [showdeletebtn, setShowDeletebtn] = useState<boolean>(false);
  // 下架按钮是否显示
  const [showdownbtn, setShowdownbtn] = useState<boolean>(false);
  // loading
  const [loading, setLoading] = useState<boolean>(false);
  // 动态判断是否是大于1920的尺寸
  const isLargeScreen = useScreenWidth();

  const { hasPermission } = usePermission();

  // 权限判断
  const canAdd = hasPermission('coursemap_add');
  const canEdit = hasPermission('coursemap_edit');
  const canDelete = hasPermission('coursemap_delete');
  const canPublish = hasPermission('coursemap_publish');
  const canCopy = hasPermission('coursemap_copy');

  const moreBtnNode = (info: any) => {
    const items = [];

    if (canCopy) {
      items.push({
        label: t('复制'),
        key: 'copy',
      });
    }

    if (info.isShow !== 2 && canDelete) {
      items.push({
        label: t('删除'),
        key: 'delete',
      });
    }

    return items;
  };

  const parameterConfig = useSelector<any, any>(
    ({ global }) => global.parameterConfig,
  );

  // 初始化数据
  useEffect(() => {
    initmap(1, pagination.pageSize);
  }, []);

  useEffect(() => {
    const temp = localStorage.getItem('learn_view_mode') || '{}';
    const value = {
      ...JSON.parse(temp),
      maplist: modeSwitch ? '1' : '0',
    };
    localStorage.setItem('learn_view_mode', JSON.stringify(value));
  }, [modeSwitch]);
  // 监听查询条件变化数据
  useEffect(() => {
    if (loadingover) {
      initmap(1, pagination.pageSize, searchform);
    }
  }, [searchform, sorter]);

  //如果是工作台跳转过来打开新建页面
  useEffect(() => {
    if (location.query.openShow == 'true') {
      setIsModalVisible(true);
    }
  }, [location]);
  // 获取的我地图列表
  const initmap = (page: number, size: number, searchvalue: any = null) => {
    let temp = searchvalue;
    // setSearchfrom((pre: any) => {
    //   temp = pre;
    //   return pre;
    // });
    let field = 0;  //排序字段  0更新时间  1创建时间
    let sort = true;  //是否逆序
    if(sorter == '2'){
      sort = false;
    }else if(sorter == '3'){
      field = 1;
    }else if(sorter == '4'){
      field = 1;
      sort = false;
    }

    querymap({
      name: temp?.search_name,
      isShow: temp?.search_status == null ? null : temp?.search_status,
      page: page,
      size: size,
      field:field,
      sort: sort,
    }).then((res: any) => {
      setTableData(res.data.results);
      let obj = {
        ...pagination,
        current: res.data.page,
        pageSize: res.data.size,
        total: res.data.total,
      };
      setPagination(obj);

      setLoadingover(true);
    });
  };

  // 添加地图
  const addminemap = debounce(() => {
    setLoading(true);
    addmap({
      mapName: mapName,
    }).then((res: any) => {
      if (res.status === 200) {
        message.success(t('新建成功！'));
        setIsModalVisible(false);
        setMapName('');
        initmap(1, pagination.pageSize);
        if(location.query.course_id){
          window.location.href = `/canvas-lms-adapter/Graph/BindingCourse?course_id=${location.query.course_id}&map_id=${res.data.id}`;
        }else{
          window.open(
            `${window.location.pathname}#/mapv4/editmap?id=${res.data.id}&key=3`,
          );
        }
      } else {
        message.error(t('操作失败！'));
      }
      setLoading(false);
    });
  }, 300);

  // 删除地图
  const deletemamap = (arr: String[]) => {
    confirm({
      title: t('确认删除?'),
      onOk() {
        deletemap(arr).then((res: any) => {
          if (res.status === 200) {
            message.success(t('删除成功！'));
            setSelectedRowKeys([]);
            initmap(1, pagination.pageSize);
          } else {
            message.error(t('操作失败！'));
          }
        });
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };

  // 复制地图
  const copymymap = (arr: String[]) => {
    confirm({
      title: t('确认复制?'),
      onOk() {
        copymap(arr).then((res: any) => {
          if (res.status === 200) {
            message.success(t('复制成功！'));
            initmap(1, pagination.pageSize);
          } else {
            message.error(t('操作失败！'));
          }
        });
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };

  // 修改地图状态 0 下架 1发布 2共享
  const updatamap = (arr: String[], status: number) => {
    let newarr = arr;
    if (isbatch) {
      newarr = selectedRowKeys;
    }
    if (shareRange.length === 0 && status == 2) {
      message.error(t('请选择发布范围！'));
      return;

    }
    const paramShareRange = shareRange.join(',');
    const params = status == 2 ? `${status}&releaseScope=${paramShareRange},2` : status; // 默认勾选 2：修改同步至发布对象
    updatamapstatus(newarr, params).then((res: any) => {
      if (res.status === 200) {
        setIsModalVisible2(false);
        if (status == 1) {
          message.success(t('发布成功！'));
        } else if (status == 0) {
          message.success(t('下架成功！'));
        } else {
          message.success(t('发布成功！'));
        }
        setSelectedRowKeys([]);
        setSelectedId(null);
        initmap(pagination.current, pagination.pageSize);
      } else {
        message.error(t('操作失败！'));
      }
    }).finally(() => { setShareRange([])});
  };

  // 发布弹窗 发布范围
  const changeShareParams = (value: any) => {
    setShareRange(value);
  }

  // 发布弹窗内容
  const modalShareContainer = () => {
    return (
      <div className='share-content-style'>
        <div className='title'>{t('请选择发布范围 :')}</div>
        <div className='checkbox-style'>
          <Checkbox.Group onChange={changeShareParams}>
            <Checkbox value={0}>{t('仅门户查看')}</Checkbox>
            <Checkbox value={1}>{t('可引用编辑')}</Checkbox>
          </Checkbox.Group>

        </div>
        <div className='tip'>{t('(修改配置可前往"权限管理-发布权限")')}</div>
      </div>
    )
  }

  // 表格操作列
  const renderOperations = (text: any, record: any) => {
    return (
      <>
        <Tooltip title={t('预览')}>
          <Button
            type="text"
            icon={<IconFont type="iconyulan" />}
            onClick={() => {
              window.open(
                `${window.location.pathname}#/perviewemap?mapid=${record.id}`,
              );
            }}
          />
        </Tooltip>
        {canEdit && (
          <Tooltip title={t('编辑')}>
            <Button
              type="text"
              icon={<IconFont type="iconbianji-heise" />}
              onClick={() => {
                window.open(
                  `${window.location.pathname}#/mapv4/editmap?id=${record.id}&key=3`,
                );
              }}
            />
          </Tooltip>
        )}
        {!location.query.course_id && canPublish && (
          <>
            {record.isShow == 0 ? (
              <Tooltip title={t('发布')}>
                <Button
                  type="text"
                  icon={<IconFont type="iconfabu-heise1" />}
                  onClick={() => {
                    setSelectedId(record.id);
                    setMapstatus(1);
                    setIsModalVisible2(true);
                    setIsbatch(false);
                  }}
                />
              </Tooltip>
            ) : (
              <Tooltip title={t('下架')}>
                <Button
                  type="text"
                  icon={<IconFont type="icon11" />}
                  onClick={() => {
                    setSelectedId(record.id);
                    setMapstatus(0);
                    setIsModalVisible2(true);
                    setIsbatch(false);
                  }}
                />
              </Tooltip>
            )}
          </>
        )}
        {canDelete && (
          <Tooltip title={t('删除')}>
            <Button
              type="text"
              disabled={record.isShow == 2}
              icon={<IconFont type="iconshanchu-heise-copy" />}
              onClick={() => deletemamap([record.id])}
            />
          </Tooltip>
        )}
      </>
    );
  };

  // 表格列定义
  const columns: TableColumnsType<any> = [
    {
      title: t('名称'),
      dataIndex: 'mapName',
      key: 'mapName',
      align: 'center' as const,
      render: (text: any, record: any) => {
        return (
          <span
            style={{ cursor: 'pointer' }}
            onClick={() => {
              window.open(
                `${window.location.pathname}#/mapv4/editmap?id=${record.id}&key=3`,
              );
            }}
          >
            {text}
          </span>
        );
      },
    },
    {
      title: t('状态'),
      dataIndex: 'isShow',
      key: 'isShow',
      align: 'center',
      render: (text: any, record: any) => {
        if (text == 2) {
          return <Tag color="#f50">{t('已发布')}</Tag>;
        } else {
          return <Tag color="#549cff">{t('未发布')}</Tag>;
        }
      },
    },
    {
      title: t('来源'),
      dataIndex: 'createSite',
      key: 'createSite',
      align: 'center',
      render: (text: any) => {
        return text === 0 ? t('课程') : t('知识地图');
      },
    },
    {
      title: t('适用课程'),
      dataIndex: 'courseName',
      key: 'courseName',
      align: 'center',
    },
    {
      title: t('学科专业'),
      dataIndex: 'subjectName',
      key: 'subjectName',
      align: 'center',
    },
    {
      title: t('创建人'),
      dataIndex: 'teacherName',
      key: 'teacherName',
      align: 'center',
    },
    {
      title: t('创建时间'),
      dataIndex: 'createDate',
      key: 'createDate',
      align: 'center',
      render: (text: any) =>text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: t('更新时间'),
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      render: (text: any) => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: t('操作'),
      dataIndex: 'address',
      key: 'address',
      align: 'center' as const,
      render: renderOperations,
    },
  ];

  // 西工大定制的表格列定义
  const columns_npu: TableColumnsType<any> = [
    {
      title: t('名称'),
      dataIndex: 'mapName',
      key: 'mapName',
      align: 'center' as const,
      render: (text: any, record: any) => {
        return (
          <span
            style={{ cursor: 'pointer' }}
            onClick={() => {
              window.open(
                `${window.location.pathname}#/mapv4/editmap?id=${record.id}&key=3`,
              );
            }}
          >
            {text}
          </span>
        );
      },
    },
    {
      title: t('状态'),
      dataIndex: 'editTag',
      key: 'editTag',
      align: 'center',
      render: (text: any, record: any) => {
        if (text == 0) {
          return <span>未编辑</span>;
        } else {
          return <span>已编辑</span>;
        }
      },
    },
    {
      title: t('所属课程信息'),
      dataIndex: 'courseName',
      key: 'courseName',
      align: 'center',
    },
    {
      title: t('课程领域信息'),
      dataIndex: 'subjectName',
      key: 'subjectName',
      align: 'center',
    },
    {
      title: t('课程类型'),
      dataIndex: 'labels',
      key: 'labels',
      align: 'center',
      render: (text: any, record: any) => {
        let labelarr = text?.split(',');
        if(!text || labelarr.length==0){
          return '-'
        }else{
          return <>
            <Popover content={(<>
              {
                  record.labels?.split(',') ?.map((item2:any)=>{
                    if(item2!=''){
                      return <Tag color="#549cff">{item2}</Tag>
                    }
                  })
              }
              </>)} title={`${labelarr.length}个`}>
              <Tag color="#549cff">{labelarr?.length>1 ? labelarr[0] + ` 等${labelarr.length}个` :  labelarr?.[0] || '-' }</Tag>
            </Popover>
          </>
        }
      }
    },
    {
      title: t('信息管理部门'),
      dataIndex: 'college',
      key: 'college',
      align: 'center',
    },
    {
      title: t('维护权限信息'),
      dataIndex: 'college',
      key: 'college',
      align: 'center',
      render: (text: any, record: any) => {
        if(record.mapManagerList?.length>0){
          return <span>{record.mapManagerList.length>1 ? record.mapManagerList[0].userName + " 等": record.mapManagerList[0].userName}</span>
        }
      }
    },
    {
      title: t('访问权限信息'),
      dataIndex: 'isShow',
      key: 'isShow',
      align: 'center',
      render: (text: any, record: any) => {
        if (text == 2) {
          return <Tag color="#f50">{t('已发布')}</Tag>;
        } else {
          return <Tag color="#549cff">{t('未发布')}</Tag>;
        }
      },
    },
    {
      title: t('描述信息'),
      dataIndex: 'describe',
      key: 'describe',
      align: 'center',
    },
    {
      title: t('创建人'),
      dataIndex: 'teacherName',
      key: 'teacherName',
      align: 'center',
    },
    {
      title: t('创建时间'),
      dataIndex: 'createDate',
      key: 'createDate',
      align: 'center',
      render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss') || '-',
    },
    {
      title: t('更新时间'),
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      render: (text: any) => text? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: t('操作'),
      dataIndex: 'address',
      key: 'address',
      align: 'center' as const,
      render: renderOperations,
    },
  ]

  // 表格的多选事件
  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
      setSelectedRowKeys(selectedRowKeys);
    },
    selectedRowKeys: selectedRowKeys,
  };

  useEffect(() => {
    checkbtn();
  }, [selectedRowKeys]);

  const checkbtn = () => {
    // 校验是否选择了多个不同的状态的地图 这种情况下就禁止操作按钮
    let arr = tableData.filter(item => selectedRowKeys.includes(item.id));
    // 判断是不是勾选了状态不一致的按钮
    if (arr.length > 1) {
      let flag = false;
      let status = arr[0].isShow;
      arr.forEach(item => {
        if (item.isShow !== status) {
          flag = true;
        }
      });
      // 如果状态不一致 全部禁用
      if (flag) {
        setShowSharebtn(flag);
        setShowdownbtn(flag);
        setShowDeletebtn(flag);

      } else if (status == 2) {
        setShowSharebtn(true);
        setShowdownbtn(false);
        setShowDeletebtn(true);
      } else {
        setShowSharebtn(false);
        setShowdownbtn(true);
        setShowDeletebtn(false);
      }
    } else {
      // 判断是不是勾选了已经共享的按钮
      if (arr.length > 0) {
        let flag = false;
        let flag2 = false;
        arr.forEach(item => {
          // 如果有状态是已经共享的就不显示共享按钮
          if (item.isShow === 2) {
            flag = true;
          }
          // 如果有状态是未共享的就不显示下架按钮
          if (item.isShow !== 2) {
            flag2 = true;
          }
        });
        setShowSharebtn(flag);
        setShowDeletebtn(flag);
        setShowdownbtn(flag2);
      }
    }
  };

  return (
    <div className="minemap">
      <div className="search_box">
        <Search
          form={form}
          page="mine"
          mobileFlag={mobileFlag}
          onSearch={(values: any) => setSearchfrom(values)}
        />
      </div>
      <div className="options_box">
        <div className="left_box">
          <Checkbox
            indeterminate={(() => {
              if (selectedRowKeys.length) {
                if (selectedRowKeys.length == tableData.length) {
                  return false;
                } else {
                  return true;
                }
              } else {
                return false;
              }
            })()}
            onChange={e => {
              if (e.target.checked) {
                setSelectedRowKeys(tableData.map(item => item.id));
              } else {
                setSelectedRowKeys([]);
              }
            }}
            checked={selectedRowKeys.length == tableData.length}
          >
            {t('全部')}
          </Checkbox>
          <Space
            size={mobileFlag ? 0 : 10}
            split={
              mobileFlag ? null : (
                <Divider type="vertical" style={{ gap: '0px !important' }} />
              )
            }
          >
            {canAdd && (
              <Button
                type="primary"
                shape="round"
                className={mobileFlag ? 'map-mobile-btn' : ''}
                icon={
                  mobileFlag ? (
                    <IconFont type="iconicon-test" />
                  ) : (
                    <PlusCircleFilled />
                  )
                }
                onClick={() => {
                  if (/Mobi|Android|iPhone/i.test(navigator.userAgent)) {
                    message.info(t('暂不支持手机端，请前往电脑端操作'));
                    return;
                  }
                  setIsModalVisible(true);
                }}
              >
                {t('新建')}
              </Button>
            )}
            {!mobileFlag && (
              <>
                {canDelete && (
                  <Button
                    onClick={() => deletemamap(selectedRowKeys)}
                    type="text"
                    ghost
                    icon={<IconFont type="iconshanchu-heise-copy" />}
                    disabled={selectedRowKeys.length === 0 || showdeletebtn}
                  >
                    {t('删除')}
                  </Button>
                )}
                {canCopy && (
                  <Button
                    onClick={() => copymymap(selectedRowKeys)}
                    icon={<IconFont type="iconfuzhi1" />}
                    disabled={!selectedRowKeys.length}
                    type="text"
                    ghost
                  >
                    {t('复制')}
                  </Button>
                )}
                {canPublish && !location.query.course_id && (
                  <>
                    <Button
                      onClick={() => {
                        setMapstatus(1);
                        setIsModalVisible2(true);
                        setIsbatch(true);
                      }}
                      type="text"
                      ghost
                      icon={<IconFont type="iconfabu-heise1" />}
                      disabled={selectedRowKeys.length == 0 || showsharebtn}
                    >
                      {t('发布')}
                    </Button>
                    <Button
                      onClick={() => {
                        setMapstatus(0);
                        setIsModalVisible2(true);
                        setIsbatch(true);
                      }}
                      type="text"
                      ghost
                      icon={<IconFont type="icon11" />}
                      disabled={selectedRowKeys.length == 0 || showdownbtn}
                    >
                      {t('下架')}
                    </Button>
                  </>
                )}
                {location.query.course_id && (
                  <Button
                    onClick={() => {
                      if(selectedRowKeys.length == 1){
                        confirm({
                          title: '确认要绑定吗？',
                          onOk() {
                            window.location.href = `/canvas-lms-adapter/Graph/BindingCourse?course_id=${location.query.course_id}&map_id=${selectedRowKeys[0]}`;
                          },
                          onCancel() {
                            console.log('Cancel');
                          },
                        });
                      }else{
                        message.info(t('请选择一个地图进行绑定！'))
                      }
                    }}
                    type="text"
                    ghost
                    icon={<ApiOutlined />}
                    disabled={selectedRowKeys.length == 0}
                  >
                    {t('绑定')}
                  </Button>
                )}
              </>
            )}
          </Space>
        </div>
        <div className="right_box">
          <Select
            style={{ width: 120 }}
            defaultValue="1"
            onChange={(value:any)=>setSorter(value)}
          >
            <Select.Option value="1">
              {t('更新时间')}<IconFont type="iconchangjiantou-xia" />
            </Select.Option>
            <Select.Option value="2">
              {t('更新时间')}<IconFont type="iconchangjiantou-shang" />
            </Select.Option>
            <Select.Option value="3">
              {t('创建时间')}<IconFont type="iconchangjiantou-xia" />
            </Select.Option>
            <Select.Option value="4">
              {t('创建时间')}<IconFont type="iconchangjiantou-shang" />
            </Select.Option>

            {/* <Select.Option value="name,0">
                             课程名称
                             <IconFont type="iconchangjiantou-shang" />
                         </Select.Option>
                         <Select.Option value="name,1">
                             课程名称
                             <IconFont type="iconchangjiantou-xia" />
                         </Select.Option> */}
          </Select>
          {mobileFlag ? null : (
            <div className="mode_switch_wrapper">
              <div onClick={() => setModeSwitch(true)} className="mode_switch">
                <Tooltip title={t('图例模式')}>
                  <IconFont
                    type="iconhebingxingzhuangfuzhi2"
                    className={modeSwitch ? 'active' : ''}
                  />
                </Tooltip>
              </div>
              <div onClick={() => setModeSwitch(false)} className="mode_switch">
                <Tooltip title={t('列表模式')}>
                  <IconFont
                    type="iconliebiao"
                    className={modeSwitch ? '' : 'active'}
                  />
                </Tooltip>
              </div>
            </div>
          )}
        </div>
      </div>
      {modeSwitch ? (
        <List
          grid={{ gutter: 16, column: isLargeScreen ? 8 : 6, sm: 2, xs: 2 }}
          dataSource={tableData}
          renderItem={item => (
            <List.Item key={item.id}>
              <div
                className="card_item"
                onClick={() => {
                  if (mobileFlag) {
                    message.info(t('暂不支持手机端，请前往电脑端操作'));
                    return;
                  }
                  window.open(
                    `${window.location.pathname}#/mapv4/editmap?id=${item.id}&key=3`,
                  );
                }}
              >
                <div className="icon_box">
                  <img
                    src={item.mapCover ? item.mapCover : nomapcover}
                    onError={(e: any) => {
                      e.target.src = nomapcover;
                    }}
                  ></img>
                  {item.isShow == 2 && (
                    <div className="status_box">{t('已发布')}</div>
                  )}
                  {item.type == 5 && (
                    <div className="process_box">{t('转换中')}</div>
                  )}
                  {item.createSite === 0 && (
                    <div className="course_box" >{t('课程')}</div>
                  )}
                  <div className="option_view" onClick={e => e.stopPropagation()}>
                    <div
                      className="item_caidan"
                      onClick={() => {
                        if (mobileFlag) {
                          message.info(t('暂不支持手机端，请前往电脑端操作'));
                          return;
                        }
                        window.open(
                          `${window.location.pathname}#/perviewemap?mapid=${item.id}`,
                        );
                      }}
                    >
                      <div className="caidan_icon">
                        <IconFont type="iconyulan" />
                      </div>
                      <div className="caidan_name">
                        <span>{t('预览')}</span>
                      </div>
                    </div>
                    {canEdit && (
                      <div
                        className="item_caidan"
                        onClick={() => {
                          if (mobileFlag) {
                            message.info(t('暂不支持手机端，请前往电脑端操作'));
                            return;
                          }
                          window.open(
                            `${window.location.pathname}#/mapv4/editmap?id=${item.id}&key=3`,
                          );
                        }}
                      >
                        <div className="caidan_icon">
                          <IconFont type="iconbianji-heise" />
                        </div>
                        <div className="caidan_name">
                          <span>{t('编辑')}</span>
                        </div>
                      </div>
                    )}
                    {!location.query.course_id && canPublish && (
                      <>
                        {item.isShow == 2 ? (
                          <div
                            className="item_caidan"
                            onClick={() => {
                              setSelectedId(item.id);
                              setMapstatus(0);
                              setIsModalVisible2(true);
                              setIsbatch(false);
                            }}
                          >
                            <div className="caidan_icon">
                              <IconFont type="icon11" />
                            </div>
                            <div className="caidan_name">
                              <span>{t('下架')}</span>
                            </div>
                          </div>
                        ) : (
                          <div
                            className="item_caidan"
                            onClick={() => {
                              setSelectedId(item.id);
                              setMapstatus(1);
                              setIsModalVisible2(true);
                              setIsbatch(false);
                            }}
                          >
                            <div className="caidan_icon">
                              <IconFont type="iconfabu-heise1" />
                            </div>
                            <div className="caidan_name">
                              <span>{t('发布')}</span>
                            </div>
                          </div>
                        )}
                      </>
                    )}
                    {(canDelete || canCopy) && (
                      <Dropdown
                        menu={{
                          items: moreBtnNode(item),
                          onClick: e => {
                            if (e.key == 'delete') {
                              deletemamap([item.id]);
                            } else if (e.key == 'copy') {
                              copymymap([item.id]);
                            }
                          },
                        }}
                      >
                        <a onClick={e => e.preventDefault()} style={{ flex: '1' }}>
                          <div className="item_caidan">
                            <div className="caidan_icon">
                              <DashOutlined />
                            </div>
                            <div className="caidan_name">
                              <span>{t('更多')}</span>
                            </div>
                          </div>
                        </a>
                      </Dropdown>
                    )}
                  </div>
                </div>
                <div className="name_box">
                  <div className="name">
                    <Checkbox
                      key={item.id}
                      onClick={e => e.stopPropagation()}
                      checked={selectedRowKeys.includes(item.id)}
                      onChange={e => {
                        if (e.target.checked) {
                          setSelectedRowKeys([...selectedRowKeys, item.id]);
                        } else {
                          let arr = selectedRowKeys.filter(
                            item2 => item2 != item.id,
                          );
                          setSelectedRowKeys(arr);
                        }
                      }}
                    ></Checkbox>
                    <span className="name_span" title={item.mapName}>
                      {item.mapName}
                    </span>
                  </div>
                  <div className="user">
                    <span>{item.teacherName}</span>
                    {parameterConfig.target_customer == 'npu' && (
                      <div className="right_tag">
                        {item.labels?.split(',')?.map((item2: any) => {
                          if (
                            item2.indexOf('思政') != -1 ||
                            item2.indexOf('总师') != -1
                          ) {
                            return <span className="label_tag">{item2}</span>;
                          }
                        })}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </List.Item>
          )}
        />
      ) : (
        <Table
          rowSelection={{
            type: 'checkbox',
            ...rowSelection,
          }}
          dataSource={tableData}
          scroll={{ y: 'calc(100vh - 310px)' }}
          columns={parameterConfig.target_customer == 'npu' ? columns_npu : columns}
          rowKey="id"
          pagination={false}
        />
      )}

      <Pagination
        style={{ textAlign: 'center', marginBottom: '14px',marginTop:'10px' }}
        {...{
          size: 'small',
          showQuickJumper: true,
          showTotal: total => t('共{name}条', String(total)),
          current: pagination.current,
          total: pagination.total,
          pageSize: pagination.pageSize,
          showSizeChanger: true,
          pageSizeOptions: ['18', '24', '36', '48', '60'],
          onChange: (page, pageSize) => {
            initmap(page, pageSize, searchform);
          },
        }}
      />

      <Modal
        title={t('添加地图')}
        visible={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setMapName('');
        }}
        footer={
          <div className="footerclass">
            <Button
              onClick={() => {
                setIsModalVisible(false);
                setMapName('');
              }}
            >
              {t('取消')}
            </Button>
            <Button
              type="primary"
              loading={loading}
              onClick={() => addminemap()}
            >
              {t('确认')}
            </Button>
          </div>
        }
        className="Mooc-addcourse"
      >
        <label>
          {t('地图名称')}：
          <Input
            style={{ width: 300 }}
            placeholder={t('请输入地图名称')}
            value={mapName}
            showCount
            maxLength={30}
            onChange={e => setMapName(e.target.value)}
            onPressEnter={() => addminemap()}
          />
        </label>
      </Modal>

      <Modal
        title={t('提示')}
        visible={isModalVisible2}
        destroyOnClose
        onCancel={() => {
          setIsModalVisible2(false);
          setShareRange([]);
        }}
        footer={
          <div className="footerclass">
            <Button type="primary" onClick={() => {setIsModalVisible2(false); setShareRange([]); }}>
              {t('取消')}
            </Button>
            {/* <Button type="primary" onClick={() => updatamap([selectedid], mapstatus)}>确认</Button> */}
            {mapstatus === 1 ? (
              <Button type="primary" onClick={() => updatamap([selectedid], 2)}>
                {t('发布')}
              </Button>
            ) : (
              <Button
                type="primary"
                onClick={() => updatamap([selectedid], mapstatus)}
              >
                {t('确认')}
              </Button>
            )}
          </div>
        }
      >
        {(() => {
          if (mapstatus == 1) {
            return modalShareContainer();
          } else if (mapstatus == 0) {
            return t('确认下架?');
          } else {
            return t('确认发布?');
          }
        })()}
      </Modal>
    </div>
  );
};

export default Minemap;
