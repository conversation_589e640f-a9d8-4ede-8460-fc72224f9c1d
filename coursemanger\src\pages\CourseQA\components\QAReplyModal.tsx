import { Modal, Pagination, message } from "antd";
import React, { FC, useEffect, useState } from "react";
import { useLocation, useSelector } from "umi";
import CustomComment from "./customComment";
import QAService from "@/api/qa";
import useLocale from "@/hooks/useLocale";
import { getSensitiveWord, getUuid } from "@/utils";
import "./QAReplyModal.less";
import ReplyInput from "./replyInput";

interface IProps {
  open: boolean;
  comment: any;
  teachersCode: any[];
  usersAvatar: any;
  refresh: () => void;
  onClose: () => void;
}

const QAReplyModal: FC<IProps> = ({ open, comment, teachersCode, usersAvatar, refresh, onClose }) => {
  const { t } = useLocale();
  const location: any = useLocation();
  const [pagination, setPagination] = useState<any>({
    page: 1,
    size: 12
  });
  const [total, setTotal] = useState<number>(0);
  const [showTotal, setShowTotal] = useState<number>(0);
  const { userInfo } = useSelector<any, any>((state) => state.global);
  const [replyList, setReplyList] = useState<any>([]);
  const [currentItem, setCurrentItem] = useState<QA.Comment | QA.Reply | null>(null);
  const canDelete = (id: string): boolean => {
    return userInfo.userCode == id ||
      userInfo.roles?.map((item: any) => item.roleCode)?.includes('r_sys_manager') || //是否是系统管理员
      userInfo.roles?.map((item: any) => item.roleCode)?.includes('r_course_manager') || //是否是课程管理员
      teachersCode.includes(userInfo.userCode);
  };
  useEffect(() => {
    if (comment?.comment_id) {
      getReplyList();
    }
  }, [comment, pagination]);
  const getReplyList = () => {
    const params = {
      comment_id: comment.comment_id,
      user_id_of_unread: userInfo.userCode,
      ...pagination
    };
    const data = {};
    QAService.getReply(params, data).then((res: any) => {
      if (res.error_code === "forumservice.0000.0000") {
        setReplyList(res.extend_message.results);
        setTotal(res.extend_message.count);
        setShowTotal(res.extend_message.count);
      }
    });
  };
  const handleCommentShow = (item: any) => {
    if (item.reply_id === currentItem?.reply_id) {
      setCurrentItem(null);
      return;
    }
    setCurrentItem(item);
  };
  const handlePageChange = (page: number, size: number) => {
    setPagination({ page, size });
  };
  const handleAdd = async (replyMsg: string, cur?: any) => {
    if (replyMsg === '') {
      // 评论不为空
      message.info(t('内容不能为空'));
      return false;
    }
    const canAdd: any = await getSensitiveWord(replyMsg, "回复", () => true, () => false);
    if (canAdd) {
      const result = await QAService.addComment({
        link_id: location.query.id,
        content: replyMsg,
        user_id: userInfo.userCode,
        user_name: userInfo.nickName,
        user_roles: userInfo.roles.map((item: any) => item.roleCode),
        comment_id: comment.comment_id,
        target_reply_id: cur ? cur.reply_id : undefined
      });
      if (result.error_msg === 'Success') {
        message.success(t('评论成功'));
        setShowTotal(showTotal + 1);
        setReplyList((r: any) => ([...r, result.extend_message]));
        refresh();
        return true;
      }
    }
    return false;
  };
  const handleDelete = async (cur: any) => {
    const result = await QAService.deleteComment([cur.reply_id], location.query.id);
    if (result.error_msg === 'Success') {
      message.success(t('删除成功'));
      getReplyList();
      refresh();
      return true;
    }
    return false;
  };
  return <Modal title="回复" open={open} destroyOnClose wrapClassName="qa-reply-modal" onCancel={onClose} footer={null}>
    <div className="header-wrp">
      <CustomComment
        avatar={usersAvatar ? usersAvatar?.[comment?.user_id]?.avatar_url : undefined}
        ifTeacher={comment?.user_roles?.
          map((item: any) => item?.code ?? item)?.
          includes('r_teacher')}
        comment={comment}
      />
    </div>
    <div className="total-wrp">{showTotal}条回复</div>
    <div className="reply-ls-wrp">
      {replyList.map((item: any) =>
        <CustomComment
          key={item.reply_id}
          canDelete={canDelete(item.user_id)}
          avatar={usersAvatar ? usersAvatar[item.user_id]?.avatar_url : undefined}
          ifTeacher={item.user_roles.map((item: any) => item?.code ?? item).includes('r_teacher')}
          type="comment"
          ifCommenting={
            (currentItem as QA.Comment)?.reply_id === item.reply_id}

          comment={item}
          onComment={handleCommentShow}
          onDelete={handleDelete}
          onReply={handleAdd}
        />)}

    </div>
    <Pagination
      size="small"
      showQuickJumper
      hideOnSinglePage
      current={pagination.page}
      total={total}
      pageSize={pagination.size}
      onChange={handlePageChange}
      pageSizeOptions={['12', '24', '36', '48', '60']}
    >
    </Pagination>
    {comment?.can_reply && <ReplyInput canReply={comment?.can_reply} permissions={comment?.permissions} onReply={handleAdd} />}
  </Modal >;
};

export default QAReplyModal;