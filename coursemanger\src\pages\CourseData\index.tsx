import { searchfolder } from '@/api/addCourse';
import { courseDataOrder, downloadSetting, entityCopy, hideResource, hyperlinkAdd, renameentity, renamefolder, resourceAdd, resourceRrowsing } from "@/api/course";
import { getteacherlist } from "@/api/teacher";
import { IconFont } from "@/components/iconFont";
import { ArrowDownOutlined, PlusCircleFilled } from "@ant-design/icons";
import {
  Breadcrumb,
  Button,
  Checkbox,
  Empty,
  Input,
  message,
  Modal,
  Pagination,
  Popconfirm,
  Popover,
  Select,
  Spin,
  Tabs,
  Tooltip
} from "antd";
import React, { FC, useEffect, useRef, useState } from "react";
import { arrayMove, SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc';
import { Prompt, useDispatch, useHistory, useLocation, useSelector } from "umi";
import "./index.less";

import UploadModal from '@/components/UploadModal';

import ResourcePreviewModal from '@/components/ResourcePreviewModal';
import useLocale from "@/hooks/useLocale";
import { ModuleCfg2 } from "@/permission/moduleCfg";
import { changesize, getSensitiveWord, operateCode } from "@/utils";
import AddHyperlinkModal from "./components/AddHyperlinkModal";
import CopyAndMoveModal from './components/CopyAndMoveModal';
import DeleteModal from './components/deleteModal';
import DownloadModal from './components/downloadModal';
import Keyframe from './components/keyframe';
import NewFolder from './components/newfolder';
const CheckboxGroup = Checkbox.Group;

const DragHandle = SortableHandle(() => <IconFont type="icontuozhuai" title="拖拽可排序" style={{ cursor: 'grab' }} />);

const ListItem = SortableElement((props: any) => {
  const dispatch = useDispatch();
  let history: any = useHistory();
  const inputRef = useRef<Input | null>(null);
  const { item, goDetail, contentitemBox, handleResourcePreview, breadCrumb, isStudent, setHyperlinkModalVisible, setHyperlinkData, setIsAddHyperlink, setDownloadSettingVisible, checkedList, deleteShow, setallrename, refresh, setDownloadModalVisible, opendownloadbox, getFolderList } = props;
  const { t } = useLocale();
  const [rename, setRename] = useState<boolean>(false); // 重命名
  const [moreMenuVisible, setMoreMenuVisible] = useState<boolean>(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState<boolean>(false);
  const [copyAndMoveModallVisible, setCopyAndMoveModalVisible] = useState<boolean>(false);
  const [read, setRead] = useState<any>({ total: 0 })
  const [unread, setunRead] = useState<any>({ total: 0 })
  const [readOpen, setReadOpen] = useState<boolean>(false);
  const [readPage, setReadPage] = useState<number>(1)
  const [unreadPage, setunReadPage] = useState<number>(1)
  // 重命名
  useEffect(() => {
    if (rename) {
      inputRef.current?.focus();
    }
  }, [rename]);
  const refilename = () => {
    setRename(true);
    setallrename && setallrename(true);
  };
  const edit = () => {
    setHyperlinkModalVisible(true)
    setHyperlinkData(item)
    setIsAddHyperlink(false)
  }
  const confirm = (visible: boolean) => {
    hideResource([{ contentId: item.contentId_, isFolder: item.type_ === 'folder', isHide: visible }]).then(res => {
      if (res.success) {
        message.success('设置成功')
        getFolderList()
      }
    })
  }
  let buttonList: any = []

  if (!isStudent) {
    if(item.hide === 1){
      buttonList.push({
        title: item.type_ === 'folder' ? <Popconfirm
          title={`文件夹设为可见则文件夹及其内所有内容均会可见，是否确认操作？`}
          onConfirm={() => confirm(false)}
          okText="确认"
          cancelText="取消"
        >
          <Tooltip>
            <IconFont title="设为可见" type="iconkejianxing-kejian" />
          </Tooltip>
        </Popconfirm> : <Tooltip>
          <IconFont title="设为可见" type="iconkejianxing-kejian" />
        </Tooltip>,
        func: () => item.type_ === 'folder' ? '' : confirm(false),
      })
    }
    if(!item.hide) {
      buttonList.push({
        title: item.type_ === 'folder' ? <Popconfirm
          title={`文件夹设为隐藏则文件夹及其内所有内容均会隐藏，是否确认操作？`}
          onConfirm={() => confirm(true)}
          okText="确认"
          cancelText="取消"
        >
          <Tooltip>
            <IconFont title="设为隐藏" type="iconkejianxing-bukejian" />
          </Tooltip>
        </Popconfirm> : <Tooltip>
          <IconFont title="设为隐藏" type="iconkejianxing-bukejian" />
        </Tooltip>,
        func: () => item.type_ === 'folder' ? '' : confirm(true),
      })
    }
    buttonList.push(
      {
        title: <Tooltip>
          <IconFont title="移动" type="iconyidong" className="icon" />
        </Tooltip>,
        func: () => setCopyAndMoveModalVisible(true),
        disabled:
          checkedList.length === 0 ||
          checkedList.some((item: any) => item?.operateCode_ < 15),
      })
    buttonList.push({
      title: <Tooltip>
        <IconFont title="删除" type="icondelete" className="icon" />
      </Tooltip>,
      func: () => setDeleteModalVisible(true),
      disabled: checkedList.length === 0 || !deleteShow,
    })

    if (item.type_ !== 'biz_sobey_hyperlink') {
      buttonList.push({
        title: t('重命名'),
        func: () => refilename(),
        icon: (
          <div onClick={refilename} key="11">
            <Tooltip title={t('重命名')}>
              <IconFont type="iconedit" className="icon" />
            </Tooltip>
          </div>
        ),
      })
    }
    if (item.type_ !== 'folder' && item.type_ !== 'biz_sobey_hyperlink') {
      buttonList.push({
        title: t('下载设置'),
        func: () => { setDownloadSettingVisible(true) },
        icon: <div>
          <Tooltip>
            <IconFont type="iconshezhi1" className="icon" />
          </Tooltip>
        </div>,
      })
      buttonList.push({
        title: t('下载'),
        func: () => {
          dispatch({
            type: 'download/changedownload',
            payload: {
              value: [item],
            },
          });
          if (isStudent && item.download === 0) {
            message.error('该文件不允许下载！');
          }
          else {
            setDownloadModalVisible(true);
          }
        },
        disabled: checkedList.length === 0,
        icon: <div>
          <ArrowDownOutlined className="icon" />
        </div>
      })
    }
    if (item.type_ === 'biz_sobey_hyperlink') {
      buttonList.push({
        title: t('编辑'),
        func: () => edit(item),
        icon: (
          <div onClick={refilename} key="11">
            <Tooltip title={t('编辑')}>
              <IconFont type="iconedit" className="icon" />
            </Tooltip>
          </div>
        ),
      })
    }
  }
  else {
    if (item.type_ !== 'folder' && item.type_ !== 'biz_sobey_hyperlink') {
      buttonList.push({
        title: <ArrowDownOutlined title="下载" className="icon" />,
        func: () => {
          dispatch({
            type: 'download/changedownload',
            payload: {
              value: [item],
            },
          });
          if (isStudent && item.download === 0) {
            message.error('该文件不允许下载！');
          }
          else {
            setDownloadModalVisible(true);
          }
        },
        disabled: checkedList.length === 0,
      })
    }
  }

  const popoverClick = (func: any) => {
    func();
    setMoreMenuVisible(false);
  };
  const getReadList = () =>{
    resourceRrowsing({
      courseId: history.location.query.id,
      readPage,
      readSize: 10,
      resourceId: item.contentId_,
      courseSemester: +history.location.query.sm
    }).then(res => {
      if (res.status === 200) {
        setRead(res.data.readList)
      }
    })
  }
  const getUnReadList = () =>{
    resourceRrowsing({
      courseId: history.location.query.id,
      page:unreadPage,
      size: 10,
      resourceId: item.contentId_,
      courseSemester: +history.location.query.sm
    }).then(res => {
      if (res.status === 200) {
        setunRead(res.data.unTotalList)
      }
    })
  }
  const handleHoverChange = (open: boolean) => {
    setReadOpen(open)
    setReadPage(1)
    setunReadPage(1)
  }
  useEffect(() => {
    readOpen && getReadList()
  }, [readPage, readOpen])
  useEffect(() => {
    readOpen && getUnReadList()
  }, [unreadPage, readOpen])
  const changeName = async (
    e: KeyboardEvent<HTMLInputElement> | FocusEvent<HTMLInputElement> | any,
  ) => {
    e.preventDefault();
    let name = e.target.value;
    if (item.name_ === name) {
      //未做变更
      setRename(false);
      setallrename && setallrename(false);
      return;
    }
    if (name === '') {
      message.error(t('文件或文件夹姓名不能为空'));
      return;
    }
    let re = /^[^#\x22]*$/;
    if (!re.test(name) && item.type_ === 'folder') {
      message.error(t('文件夹姓名中不能包含'));
      return;
    }
    if (item.type_ === 'folder') {
      getSensitiveWord(name, '文件夹名', async () => {
        renamefolder({
          resourceId: item.contentId_,
          newName: name,
          oldName: item.name_,
        })
          .then(async res => {
            if (res && res.success && res.data) {
              message.success(t('重命名成功'));
              // await refresh('', item.contentId_, name);
              await refresh('', {
                name: '重命名',
                contentId_: item.contentId_,
                newName: name,
              });
            } else {
              message.error(t('重命名失败'));
              await refresh();
            }
          });
      });
    }
    else {
      // 资源是否为公共资源
      getSensitiveWord(name, t('文件名'), async () => {
        renameentity(
          {
            resourceId: item.contentId_,
            newName: name,
            oldName: item.name_,
          }
        )
          .then(async res => {
            if (res && res.success && res.data) {
              message.success(t('重命名成功'));
              await refresh('', item.contentId_, name);
            } else {
              message.error(t('重命名失败'));
              await refresh();
            }
          });
      });
    }
    setRename(false);
    setallrename && setallrename(false);
  };
  return <div className={`list_content ${item.hide === 1 ? 'hide' : ''}`}>
    {/*!isStudent &&*/}
    {!isStudent && <div className="item drag_handle"><DragHandle /> </div>}
    <div className="item checkbox">
      <Checkbox value={item} />
    </div>
    <div className="contenttitle item">
      <div className="item checkbox">
        <Keyframe
          type={item.type_}
          root={contentitemBox}
          damaged={item.damaged}
          src={item.keyframe_ ? item.keyframe_ : item.keyframe}
          isDelete={item.isDelete === 1}
        ></Keyframe>
      </div>
      {rename ? <Input
        className="content_word"
        size="small"
        onPressEnter={changeName}
        onBlur={changeName}
        defaultValue={item.name_}
        ref={inputRef}
        autoComplete={'off'}
      /> : <span className="content_word" onClick={() => handleResourcePreview(item)}>
        <Tooltip title={item.name_} placement="topLeft">
          {item.name_}
        </Tooltip>
        {item.hide === 1 && <span className="status">已隐藏</span>}
      </span>}
      <div
        className='content_links'
      >
        {buttonList.slice(0, 3).map((item: any, index: number) => {
          return (
            <div
              className="left_btn"
              key={index}
              onClick={() => popoverClick(item.func)}
            >
              {item.title}
            </div>
          );
        })}
        {buttonList.slice(3, buttonList.length).length > 0 && <Popover
          placement="bottomLeft"
          getPopupContainer={(e: any) => e.parentElement} //父级用了缩放后不能在使用了
          open={moreMenuVisible}
          onOpenChange={(newOpen: boolean) =>
            setMoreMenuVisible(newOpen)
          }
          content={
            <div className="moreOpt">
              {buttonList.slice(3, buttonList.length).map((item: any, index: number) => {
                return (
                  <div
                    key={index}
                    onClick={() => popoverClick(item.func)}
                  >
                    {item.icon}
                    <span>{item.title}</span>
                  </div>
                );
              })}
            </div>
          }
        >
          <div
            onClick={(e: any) => {
              e.preventDefault();
              e.stopPropagation();
              setMoreMenuVisible(!moreMenuVisible);
            }}
          >
            <span>...</span>
          </div>
        </Popover>}
      </div>
    </div>
    <div className="dl item width1" >{item.type_ === 'folder' || item.type_ === 'biz_sobey_hyperlink'? '-' : (item.course_resource_download_total)}</div>
    <Popover
      onOpenChange={(open: boolean) => {
        handleHoverChange(open)
      }} getPopupContainer={(e: any) => e.parentElement} content={
      <Tabs
        defaultActiveKey="1"
        type="card"
        size='small'
      >
        <Tabs.TabPane tab={`已读学员（${read.total}）`} key="item-1">
          {read.results?.map(item => (
            <div className="student" key={item.code}>{item.name}（{item.code}）</div>
          ))}
          {read.total> 0 &&<Pagination
            current={readPage}
            pageSize={10}
            total={read.total}
            size="small"
            showQuickJumper
            onChange={(page) => setReadPage(page)}
          />}
        </Tabs.TabPane>
        <Tabs.TabPane tab={`未读学员（${unread.total}）`} key="item-2">
          {unread.results?.map(item => (
            <div className="student" key={item.code}>{item.name}（{item.code}）</div>
          ))}
          {unread.total>0 &&<Pagination
            current={unreadPage}
            pageSize={10}
            total={unread.total}
            size="small"
            showQuickJumper
            onChange={(page) => setunReadPage(page)}
          />}
        </Tabs.TabPane>
      </Tabs>
    } title="Title" trigger={isStudent ? '' : 'click'}>
      <div className="read item width1">{item.type_ === 'folder' ? '-' : (item.course_resource_hit_total || 0)}</div>
    </Popover>
    <div className="size item width1">{ item.type_ === 'folder' ? '-' : item.download == 1 ? '是' : '否'}</div>
    <div className="size item width1">{item.filesize ? changesize(item.filesize) : ''}</div>
    <div className="people item width1">{item.importuser || '-'}</div>
    <div className="time item">{item.createDate_}</div>
    <DeleteModal
      modalVisible={deleteModalVisible}
      modalClose={() => setDeleteModalVisible(false)}
      refresh={getFolderList}
      deletelist={[item]}
    />
    {copyAndMoveModallVisible && <CopyAndMoveModal
      modalVisible={copyAndMoveModallVisible}
      modalClose={() => setCopyAndMoveModalVisible(false)}
      breadCrumb={breadCrumb}
      copyAndMovelist={[item]}
    />}
  </div>
});



const SortableList = SortableContainer(({ loading,allList, breadCrumb, checkedList, deleteShow, contentitemBox, isStudent, goDetail, setallrename, setHyperlinkData, setIsAddHyperlink, refresh, setHyperlinkModalVisible, setDownloadSettingVisible, setCopyAndMoveModalVisible, handleResourcePreview, setDeleteModalVisible, setDownloadModalVisible, opendownloadbox, getFolderList }) => {
  return (
    <Spin  spinning={loading}>
      <div>
        {allList.map((item: any, index: number) =>
          <ListItem
            index={index}
            key={item.id || item.realId}
            item={item}
            checkedList={checkedList}
            deleteShow={deleteShow}
            isStudent={isStudent}
            breadCrumb={breadCrumb}
            contentitemBox={contentitemBox}
            setCopyAndMoveModalVisible={setCopyAndMoveModalVisible}
            setDeleteModalVisible={setDeleteModalVisible}
            setDownloadSettingVisible={setDownloadSettingVisible}
            setHyperlinkModalVisible={setHyperlinkModalVisible}
            setDownloadModalVisible={setDownloadModalVisible}
            opendownloadbox={opendownloadbox}
            setallrename={setallrename}
            refresh={refresh}
            setIsAddHyperlink={setIsAddHyperlink}
            setHyperlinkData={setHyperlinkData}
            getFolderList={getFolderList}
            goDetail={goDetail}
            handleResourcePreview={handleResourcePreview} />)}
      </div>
    </Spin>);
});
const handleDealList = (value: any, key: string, list: any, cur: any) => {
  return list.map((item_: any) => {
    if (item_.value === cur.value) {
      return {
        ...item_,
        [key]: value
      };
    } else {
      return item_;
    }
  });
};

const CourseData: FC = () => {
  const { t } = useLocale();
  const [loading, setLoading] = useState<boolean>(false);
  const dispatch = useDispatch();
  const location: any = useLocation();
  const { courseDetail, canPageEdit, saveLoading } = useSelector<Models.Store, any>(
    (state) => state.moocCourse);
  const downlist = useSelector<{ download: any }>(({ download }) => {
    return download.downlist;
  });
  const [hyperlinkModalVisible, setHyperlinkModalVisible] = useState<boolean>(false);
  const [modalContentVisible, setModalContentVisible] = useState<boolean>(false);
  const [downloadModalVisible, setDownloadModalVisible] = useState<boolean>(false);
  const [downloadSettingVisible, setDownloadSettingVisible] = useState<boolean>(false);
  const [checkedList, setCheckedList] = useState<Array<any>>([]); //选中的列表
  const [downloadCheckd, setDownloadCheckd] = useState<boolean>(true);
  const [folderId, setFolderId] = useState<string>(''); //文件夹ID
  const [contentConfirmLoading, setContentConfirmLoading] = useState<boolean>(false);
  const [toSave, setToSave] = useState<boolean>(false);
  const [getFolder, setGetFolder] = useState<any>(null); //新建文件夹
  const [open, setOpen] = useState<boolean>(false);
  const canEditPageRef = useRef<boolean>(false);
  const [damaged, setDamaged] = useState<boolean>(false);
  const [deleteShow, setDeleteShow] = useState<boolean>(false);
  const [copyShow, setCopyShow] = useState<boolean>(false);
  const [totalPage, setTotalPage] = useState<number>(0); //素材总数
  const [current, setCurrent] = useState<number>(1); //当前页码
  const [pageSize, setPageSize] = useState<number>(50); //每页条数
  const [formData, setFormData] = useState<any>({});
  const [copyAndMoveModallVisible, setCopyAndMoveModalVisible] = useState<boolean>(false);
  const [indeterminate, setIndeterminate] = useState<boolean>(false);
  const [checkAll, setCheckAll] = useState<boolean>(false);
  const [allList, setAllList] = useState<Array<any>>([]); //总的的列表
  const [isAddHyperlink, setIsAddHyperlink] = useState<boolean>(true);
  const [deleteModalVisible, setDeleteModalVisible] = useState<boolean>(false);
  const [collation, setCollation] = useState<string>('course_resource_hit_total,down'); //排序
  let history: any = useHistory();
  const [folderPath, setFolderPath] = useState<string>('global_sobey_defaultclass/public/课程资源区' + '/' + history.location.query.id)
  const [templateInfo, setTemplateInfo] = useState<any>({
    state: -1
  });
  const [hyperlinkData, setHyperlinkData] = useState<any>({});
  const [entityModalVisible, setEntityModalVisible] = useState(false);
  const [entityPreview, setEntityPreview] = useState<any>(null);

  const [breadCrumb, setBreadCrumb] = useState<Array<BreadCrumb>>([
    { name: '', folderId: '', path: '', item: undefined },
  ]);
  const { parameterConfig, buttonPermission, permission } = useSelector<
    { global: any; },
    { buttonPermission: string[]; parameterConfig: any; permission: any; }>(
    (state) => state.global);
  const sourcePreview = useRef<any>();
  const [isStudent, setIsStudent] = useState<boolean>(history.location.pathname === "/courseData"); //是否为学生
  const { userInfo } = useSelector<any, any>((state) => state.global);
  const content = () => {
    return (<div>
      <div className='pop-item' onClick={() => { setModalContentVisible(true); setOpen(false) }}>{t("添加文件")}</div>
      <div className='pop-item' onClick={() => { setHyperlinkModalVisible(true); setOpen(false); setIsAddHyperlink(true) }}>{t("添加超链接")}</div>
    </div>);
  };
  useEffect(() => {
    canEditPageRef.current = canPageEdit;
  }, [canPageEdit]);;

  useEffect(() => {
    setDownloadCheckd(checkedList.every(item => item.download !== 0))
  }, [checkedList])
  useEffect(() => {
    getFolderList()
  }, [current, pageSize, folderPath, folderId, collation])
  type OrderType = {
    order: number
  }
  const getFolderList = () => {
    setLoading(true)
    searchfolder({
      folderId: '',
      folderPath,
      pageIndex: current,
      pageSize: pageSize,
      sortFields: [
        {
          field: collation.indexOf(',') != -1 ? collation.split(',')[0] : 'name_',
          isDesc: collation.indexOf('total,down') != -1 ? true : false,
        },
      ],
      anonymous: true
    }, {
      courseId: history.location.query.id
    }).then(res => {
      console.log(res)
      if (res && res.data && res.success) {
        setTotalPage(res.data.recordTotal);
        const pathData = res.data.breadcrumbs[0].showPaths;
        const pathArr = pathData.splice(1, pathData.length).filter(item => item.value !== location.query.id)
        const path = pathArr.map(el => {
          return {
            path: el.key,
            name: el.value
          }
        })
        console.log(path,'path')
        setBreadCrumb(path);
        if (res.data.pageTotal < 0 && current !== 1) {
          setCurrent(current - 1);
          return;
        }
        if (isStudent) {
          setAllList(res.data.data.filter((item: any) => item.hide !== 1))
        }
        else {
          const saveData = res.data.data;
          const results = saveData.sort((a:OrderType,b:OrderType) => a.order - b.order)
          console.log('results',results)
          setAllList(results);
        }
      } else {
        setAllList([]);
      }
    }).finally(() => {
      setLoading(false)
    })
  }
  // 导航条跳转
  const goBreadcrumb = (item: any) => {
    // console.log('item',item)
    // if (item.folderId !== folderId) {
    //针对我的分享 文件夹回退
    if (item.path !== folderPath) {
      // setFolderId(item.folderId);
      setFolderPath(item.path);

      //取上级路径备用
      const parentPath_ = item.path.split('/');
      if (parentPath_[parentPath_.length - 1] !== 'public') parentPath_.pop(); //回退到公共资源时不能剔除最后一级路径
      // 2021-12-15 处理导航条跳转重命名bug
    }
  };
  // 页码切换
  const changepage = (page: number, size: any) => {
    if (page === 0) {
      setCurrent(1);
      setPageSize(size || 0);
    } else {
      setCurrent(page);
      setPageSize(size || 0);
    }
  }
  /**
   * 资源预览
   *
   * @param {IResourceItemProps['info']} info
   */
  const handleResourcePreview = (info: any) => {
    // const parent = getTreeItem(chapterTreeData, info.parentId);
    if (info.type_ === 'folder') {
      goDetail(info, 1)
    }
    else if (info.type_ === 'biz_sobey_hyperlink') {
      hyperlinkAdd({contentId: info.contentId_}).then(res => {
        resourceAdd({
          courseId: history.location.query.id,
          resourceId: info.contentId_,
          courseSemester: +history.location.query.sm
        }).then(res => {
          window.open(info.link_url);
          getFolderList()
        })
      })
    }
    else {
      resourceAdd({
        courseId: history.location.query.id,
        resourceId: info.contentId_,
        courseSemester: +history.location.query.sm
      }).then(res => {
        setEntityPreview({
          id: info.contentId_,
          name: info.name_,
          type: info.type,
          // sourceId: parent.guid
        });
        setEntityModalVisible(true);
      })
    }
  };
  const getTeachList = () => {
    return getteacherlist({ id: location.query.id }).then((res: any) => {
      if (res.status === 200) {
        return res.data;
      }
    });
  };
  const onSortEnd = ({ oldIndex, newIndex }: { oldIndex: number; newIndex: number; }) => {
    const arr = arrayMove(allList, oldIndex, newIndex);
    const parmas = arr.map((item, index) => (
      {
        contentId: item.contentId_,
        isFolder: item.type_ === 'folder',
        order: index
      }
    ))
    courseDataOrder(parmas).then(res => {
      console.log('res',res)
      if (res.success) {
        getFolderList()
      }
    })
  };

  const handleInfoSave = () => {
    setToSave(false);
  };

  const handleEditChange = (canPageEdit: boolean) => {
    dispatch({
      type: 'moocCourse/handlePageEditChange',
      payload: { canPageEdit }
    });
  };
  /**
   * 添加/编辑 资源确认事件
   *
   */
  const onContentModalOk = async (resource: any, isEdit: boolean) => {
    setContentConfirmLoading(true);
    const requests = [];
    for (let i = 0; i < resource.length; i++) {
      const request = new Promise((resolve) => {
        entityCopy({
          contentId: resource[i].contentId_,
          copyEntity: {
            tree: [folderPath],
            type: resource[i].type_
          }
        }).then(res => {
          resolve(res)
        })
      });
      requests.push(request);
    }

    Promise.all(requests).then(() => {
      onContentModalClose();
      setContentConfirmLoading(false);
      getFolderList()
    })
  };
  const onContentModalClose = () => {
    setModalContentVisible(false);
  };
  useEffect(() => {
    // 判断是否包含文件夹
    let i = checkedList.some((item: any) => {
      return item.type_ === 'folder';
    });
    // 判断是否有编辑权限
    let e = checkedList.every((item: any) => {
      return operateCode(item.operateCode_).includes(2);
    });
    // 判断是否包含损坏文件 有损坏文件不可下载
    let d = checkedList.some((item: any) => item.damaged);
    // ['biz_sobey_video','biz_sobey_audio','biz_sobey_picture','biz_sobey_document']
    let k = checkedList.every((item: any) => {
      return [
        'biz_sobey_video',
        'biz_sobey_audio',
        'biz_sobey_picture',
        'biz_sobey_document',
      ].includes(item.type_);
    });
    // 判断是否全为视频、文档、图片 供绑定知识点使用
    let l = checkedList.every((item: any) => {
      return (
        item.type_ === 'biz_sobey_video' ||
        item.type_ === 'biz_sobey_picture' ||
        item.type_ === 'biz_sobey_audio' ||
        item.type_ === 'biz_sobey_document'
      );
    });
    // 判断是否全都有删除权限
    let j = checkedList.every((item: any) => {
      return operateCode(item.operateCode_).includes(8);
    });
    setDeleteShow(j);
    setCopyShow(i);
    setDamaged(d);
  }, [checkedList]);
  const confirm = (visible) => {
    let PromiseList: any = [];
    checkedList.forEach((item: any) => {
      PromiseList.push(hideResource([{ contentId: item.contentId_, isFolder: item.type_ === 'folder', isHide: visible }]));
    })
    Promise.all(PromiseList).then(_res => {
      message.success('设置成功')
      getFolderList()
    })
  }
  let buttonList: any = [];
  if (isStudent) {
    buttonList = [
      {
        title: t('下载'),
        func: () => opendownloadbox(),
        disabled: checkedList.length === 0 || copyShow || damaged || (isStudent && checkedList.some(item => item.download === 0)) || checkedList.some(item => item.type_ === 'biz_sobey_hyperlink'),
        dom: <ArrowDownOutlined className="icon" />,
      }
    ]
  }
  else {
    buttonList = [
      {
        title: checkedList.some((item => item.type_ === 'folder')) ? <Popconfirm
          title={`文件夹设为可见则文件夹及其内所有内容均会可见，是否确认操作？`}
          onConfirm={() => confirm(false)}
          okText="确认"
          cancelText="取消"
        >
          {t('设为可见')}
        </Popconfirm> : t('设为可见'),
        disabled: checkedList.length === 0,
        func: () => checkedList.some((item => item.type_ === 'folder')) ? '' : confirm(false),
        dom: (
          <div>
            <Tooltip>
              <IconFont title="设为可见" type="iconkejianxing-kejian" />
            </Tooltip>
          </div>
        )
      },
      {
        title: checkedList.some((item => item.type_ === 'folder')) ? <Popconfirm
          title={`文件夹设为隐藏则文件夹及其内所有内容均会隐藏，是否确认操作？`}
          onConfirm={() => confirm(true)}
          okText="确认"
          cancelText="取消"
        >
          {t('设为隐藏')}
        </Popconfirm> : t('设为隐藏'),
        disabled: checkedList.length === 0,
        func: () => checkedList.some((item => item.type_ === 'folder')) ? '' : confirm(true),
        dom: (
          <div>
            <Tooltip>
              <IconFont title="设为隐藏" type="iconkejianxing-bukejian" />
            </Tooltip>
          </div>
        )
      },
      {
        title: t('移动'),
        func: () => setCopyAndMoveModalVisible(true),
        disabled:
          checkedList.length === 0 ||
          !deleteShow ||
          damaged ||
          checkedList.some((item: any) => item?.operateCode_ < 15),
        dom: <IconFont type="iconyidong" />,
      },
      {
        title: t('下载设置'),
        func: () => { setDownloadSettingVisible(true) },
        disabled: checkedList.length === 0 || checkedList.some(item => item.type_ === 'folder' || item.type_ === 'biz_sobey_hyperlink'),
        dom: <IconFont type="iconshezhi1" />,
      },
      {
        title: t('下载'),
        func: () => opendownloadbox(),
        disabled: checkedList.length === 0 || copyShow || damaged || checkedList.some(item => item.type_ === 'biz_sobey_hyperlink'),
        dom: <ArrowDownOutlined className="icon" />,
      },
      {
        title: t('删除'),
        func: () => setDeleteModalVisible(true),
        disabled: checkedList.length === 0 || !deleteShow,
        dom: <IconFont type="icondelete" />,
      }
    ]
  }
  const opendownloadbox = () => {
    dispatch({
      type: 'download/changedownload',
      payload: {
        value: checkedList,
      },
    });
    setDownloadModalVisible(true)
  };
  // 新建文件夹
  const newFolder = () => {
    console.log('新建文件夹')
    setGetFolder({
      type_: 'folder',
      name_: `${t('新建文件夹')}`,
      fatherTreePath: folderPath,//针对面包屑没有返回ID的情况 只能用path
      keyframe: '/rman/static/images/folder.png',
    });
  };
  // 全选
  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    setCheckedList(e.target.checked ? allList : []);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };
  // 单选
  const onChange = (check: Array<any>) => {
    setCheckedList(check);
    setIndeterminate(!!check.length && check.length < allList.length);
    setCheckAll(check.length === allList.length);
  };
  // 跳转到详情  type  1是点击的文件名跳转  2是点击所在目录 跳转
  const goDetail = (item: any, type: number) => {
    if (item.type_ === 'folder') {
      //针对我的分享得单独处理
      // 文件夹
      if (type == 1) {
        // 点击名称
        setFolderId(item.contentId_);
        setFolderPath(item.tree_[0] + '/' + item.name_);

      } else {
        // 点击目录
        setFolderId('');
        setFolderPath(item.tree_[0]);
        //所在目录在左侧树其实是选中上一级目录 只能重新查
      }
    }
    else {
      if (type == 1) {
      } else {
        // 点击目录
        setFolderId('');
        setFolderPath(item.tree_[0]);
      }
    }
  };
  const sort = [
    {
      label: 'course_resource_hit_total,down',
      value: `${t('阅读人数')}`,
      icon: <IconFont type="iconchangjiantou-xia" className="icon" />,
    },
    {
      label: 'course_resource_hit_total,up',
      value: `${t('阅读人数')}`,
      icon: <IconFont type="iconchangjiantou-shang" className="icon" />,
    },
    {
      label: 'course_resource_download_total,down',
      value: `${t('下载人数')}`,
      icon: <IconFont type="iconchangjiantou-xia" className="icon" />,
    },
    {
      label: 'course_resource_download_total,up',
      value: `${t('下载人数')}`,
      icon: <IconFont type="iconchangjiantou-shang" className="icon" />,
    },
  ];
  // 排序切换
  const handleChange = (value: string) => {
    setCollation(value);
    setCurrent(1);
  };
  return <div className='course-data'>
    <div className="box_top">
      {!isStudent && <><Popover
        content={content}
        getPopupContainer={(e: any) => e.parentElement}
        placement="bottom"
        open={open}
        trigger="click"
        onOpenChange={(open: boolean) => {
          setOpen(open)
        }}>
        <Button
          type="primary"
          icon={<PlusCircleFilled />}
          onClick={(e: any) => e.stopPropagation()}>
          {t("添加资料")}

        </Button>
      </Popover>
        <Button
          type="primary"
          ghost
          style={{ margin: '0 35px 0 8px' }}
          onClick={(e: any) => newFolder()}>
          {t("新建文件夹")}
        </Button></>}
      <div className='links_box'>
        {buttonList.map((item: any, index: number) => {
          return (
            <div
              key={index}
              className={item.disabled ? 'disabled' : ''}
              onClick={!item.disabled && item.func}
            >
              {item.dom}
              <span>{item.title}</span>
            </div>
          );
        })
        }
      </div>
    </div>
    <div className="breadcrumb">
      <Breadcrumb separator={'>'}>
        {breadCrumb.map((item, index) => (
          <Breadcrumb.Item key={index} >
            <a onClick={() => goBreadcrumb(item)}>{item?.name}</a>
          </Breadcrumb.Item>
        ))}
      </Breadcrumb>
      <div className="_right">
        <Select
          value={collation}
          style={{ width: 120 }}
          onChange={handleChange}
        >
          {sort.map((item, index) => (
            <Option value={item.label} key={index}>
              {item.value}
              {item.icon}
            </Option>
          ))}
        </Select>
      </div>
    </div>
    <div className="contentitem_box">
      <div className="pc_show" style={{ width: '100%' }}>
        <div className="list_content list_contenttop">
          {!isStudent && <div className="item drag_handle"><DragHandle /> </div>}
          {/*<div className="item drag_handle"><DragHandle /> </div>*/}
          <div className="item checkbox">
            <Checkbox
              indeterminate={indeterminate}
              onChange={onCheckAllChange}
              checked={checkAll}
            >
            </Checkbox>
          </div>
          <div className="contenttitle item">{t('素材名')}</div>
          <div className="dl item width1">{t('下载人数')}</div>
          <div className="read item width1">{t('阅读人数')}</div>
          <div className="read item width1">{t('允许下载')}</div>
          <div className="size item width1">{t('大小')}</div>
          <div className="people item width1">{t('创建人')}</div>
          <div className="time item">{t('创建时间')}</div>
        </div>
      </div>
      {allList.length === 0 && getFolder === null ? (
        <Empty image={Empty.PRESENTED_IMAGE_DEFAULT} />
      ) : (
        ''
      )}
      <CheckboxGroup
        value={checkedList}
        onChange={onChange}
        // className={`${modeSwitch ? (allList.length === 0 && getFolder === null ?'height1_empty':'height1') : 'height2'} height`}
        // modeSwitch 为true时是图例模式
        className='height2 height'
        style={{ width: '100%' }}
      >
        {getFolder === null ? (
          ''
        ) : (
          <NewFolder
            detail={getFolder}
            refresh={() => {
              setGetFolder(null);
              getFolderList()
            }}
            // refresh={refresh}
            // refresh={refresh}
            // setallrename={item => setRenameShow(item)}
          />
        )}
        <SortableList
          loading={loading}
          useDragHandle
          helperClass='course-data-row-dragging'
          allList={allList}
          getFolderList={getFolderList}
          checkedList={checkedList}
          breadCrumb={breadCrumb}
          isStudent={isStudent}
          onSortEnd={onSortEnd}
          setDownloadSettingVisible={setDownloadSettingVisible}
          setHyperlinkModalVisible={setHyperlinkModalVisible}
          opendownloadbox={opendownloadbox}
          handleResourcePreview={handleResourcePreview}
          setDeleteModalVisible={setDeleteModalVisible}
          setIsAddHyperlink={setIsAddHyperlink}
          setHyperlinkData={setHyperlinkData}
          setDownloadModalVisible={setDownloadModalVisible}
          setCopyAndMoveModalVisible={setCopyAndMoveModalVisible}
          // setallrename={setallrename}
          refresh={getFolderList}
          goDetail={goDetail}
        />
      </CheckboxGroup>
    </div>
    {allList.length > 0 &&<div className="pagination">
      <Pagination
        current={current}
        pageSize={pageSize}
        total={totalPage}
        size="small"
        showQuickJumper
        onChange={changepage}
        showTotal={total => `共${total}条`}
        showSizeChanger={true}
        pageSizeOptions={['30', '40', '50', '100']}
        // showTotal={total => intl.formatMessage({
        //   id: "page-total",
        //   defaultMessage: `共 ${total} 页`
        // })}
      />
    </div>}
    {hyperlinkModalVisible && <AddHyperlinkModal data={hyperlinkData} isAdd={isAddHyperlink} visible={hyperlinkModalVisible} tree={folderPath} onClose={() => { setHyperlinkModalVisible(false); getFolderList() }} />}
    {modalContentVisible && <UploadModal
      visible={modalContentVisible}
      showCheckbox={true}
      contentConfirmLoading={contentConfirmLoading}
      onlyVideo={false}
      formData={formData}
      folderPath={folderPath}
      courseCopyId={courseDetail.copyFrom ?? courseDetail.entityData?.templateChange}
      showTemplateModal={(courseDetail.copyFrom || courseDetail.entityData?.templateChange) && permission.includes(ModuleCfg2.template) && templateInfo.state !== -1}
      onClose={() => {
        setTimeout(() => {
          getFolderList()
        }, 500)
        onContentModalClose()
      }}
      onSave={(data: any) => onContentModalOk(data, false)} />}
    <ResourcePreviewModal
      ref={sourcePreview}
      modalVisible={entityModalVisible}
      modalClose={(isRefresh?: boolean) => {
        // if (isRefresh) getChapter();
        setEntityModalVisible(false);
        getFolderList()
      }}
      resource={entityPreview}
      onAddTopic={(type: number) => {
        // setTopicType(type);
        // setShowAddTopic(true);
      }} />
    {copyAndMoveModallVisible && <CopyAndMoveModal
      modalVisible={copyAndMoveModallVisible}
      modalClose={() => setCopyAndMoveModalVisible(false)}
      breadCrumb={breadCrumb}
      refresh={getFolderList}
      copyAndMovelist={checkedList}
    />}
    <Prompt when={canPageEdit} message={t("你所做的更改可能未保存，确认离开？")} />
    {
      downloadSettingVisible && <Modal
        title='下载设置'
        wrapClassName="resource-upload-modal"
        open={downloadSettingVisible}
        onOk={() => {
          downloadSetting(checkedList.map(item => ({ contentId: item.contentId_, isDownload: downloadCheckd, isFolder: false }))).then(() => {
            message.success(t("设置成功"))
            setDownloadSettingVisible(false)
            getFolderList()
          })
        }}
        onCancel={() => setDownloadSettingVisible(false)}
        width={550}
      >
        <Checkbox checked={downloadCheckd} onChange={(e) => setDownloadCheckd(e.target.checked)}>{t("允许下载")}</Checkbox>
      </Modal>
    }
    <DownloadModal
      modalVisible={downloadModalVisible}
      modalClose={() => {
        setDownloadModalVisible(false)
        getFolderList()
      }}
      downloadlist={isStudent ? downlist.filter((item: any) => item.download !== 0) : downlist}
    />
    <DeleteModal
      modalVisible={deleteModalVisible}
      modalClose={() => setDeleteModalVisible(false)}
      refresh={getFolderList}
      deletelist={checkedList}
    />
  </div>
};

export default CourseData;
