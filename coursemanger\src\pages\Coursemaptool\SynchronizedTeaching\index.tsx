import { CheckService } from '@/api/check';
import ProFormPageSelect from '@/components/ProFormPageSelect';
import useLocale from '@/hooks/useLocale';
import {
  ProForm,
  ProFormInstance,
  ProFormProps,
  ProFormSelect,
} from '@ant-design/pro-components';
import { Empty, Form, Modal, message } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useLocation, useSelector } from 'umi';
import { copyselectmap, syncTeachingPlan } from '@/api/coursemap';

export interface ISyncTeach {
  /** 展示的条件：22 */
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  mapId: string;
  /** 已经绑定的大纲 */
  checkedId: string;
  /** 是否是地图编辑页 */
  isEditMap?: boolean
  // perviewtype: number;
  // mapid: any;
  //回显的对象
  reShowObj?: {
    course?: string
    major?: string
    grade?: string
    majorName?: string
  }
}

const SynchronizedTeaching: React.FC<ISyncTeach> = props => {
  const { visible, mapId, checkedId,isEditMap,reShowObj, onClose, onSuccess } = props;
  const location: any = useLocation();
  const { t } = useLocale();
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const parameterConfig = useSelector<any, any>(
    ({ global }) => global.parameterConfig,
  );
  const { courseDetail } = useSelector<Models.Store, any>(
    (state) => state.moocCourse);

  const handleClose = () => {
    onClose();
    form.resetFields()
  };

  const formRef = useRef<ProFormInstance>(null);
  const [form] = Form.useForm();
  const proformConfig: ProFormProps = {
    layout: 'horizontal',
    formRef,
    form,
    submitter: false,
    onFinish: async () => {
      // setLoading(true);
      // await config.onFinish?.(
      //   formRef.current?.getFieldsValue(true),
      //   async () => {
      //     setConfig(draft => {
      //       draft.open = false;
      //     });
      //     // 添加动画时间, 防止闪烁
      //     await Utils.sleep(100);
      //   },
      //   setLoading,
      // );
      setLoading(false);
    },
    loading,
  };

  const handleConfirm = async (value: any) => {
    try {
      bindCourse();
    } catch (error) {
      message.error('绑定课程大纲失败');
    }
    async function bindCourse() {
      setLoading(true);
      try {
        const { course, pyfaItem, grade } = value;
        const { item } = course;
        const {
          data: { data: courseData, message: msg },
        } = await CheckService.bindOutlineByCourseCode({
          courseCode: item.id,
          couresCversion: item.courseId,
          id: mapId,
          // courseId: isEditMap? '' : location?.query?.id,
          courseId: !!(location?.query?.type) ?location?.query?.id : '',
          courseSemester: location?.query?.sm,
          applicableMajorName: pyfaItem?.majorName,
          applicableMajorCode: pyfaItem?.majorCode,
          // trainingPlanId: pyfaItem?.id,
          grade: grade
        });
        if (courseData) {
          message.success('绑定课程大纲成功');
          // 将大纲中的信息生成地图
          await CheckService.mapBindCourse({courseId: item.id, mapId: mapId, name: item?.courseName})
          onSuccess();
        } else {
          message.error(msg);
        }
      } catch (error) {
        message.error('绑定课程大纲失败');
      }
      setLoading(false);
    }
  };

      //#region 根据大纲id查询培养方案
      const [educationList, setEducationList] = useState<any[]>([])
      const getPyfaDetail = (id: string, itemId?: string) => {
        CheckService.querySyllabusList({courseId: id }).then(res => {
          if (res?.status == 200) {
            setEducationList(res?.data?.data || [])
            if (!!itemId) {
              const item = (res?.data?.data || []).find((cell: any) => cell?.majorCode === itemId)
            setCheckMajor(item)
            form.setFieldValue('pyfaItem', item)
            }
          }
        })
      }
      // 适用专业
      const majorList = useMemo(() => {
        const res: any = {}
        if (!educationList.length) return res
        educationList.forEach(item => {
          res[item.majorCode] = item.majorName
        })
        return res
      }, [educationList])
      //选择的培养方案
      const [checkMajor, setCheckMajor] = useState<any>({})
      // 适用年级
      const gradeList = useMemo(() => {
        const res: any = {}
        if (!checkMajor?.applyGrade) return res
        checkMajor.applyGrade.split(',').forEach((keyStr: string) => {
          res[keyStr] = keyStr
        })
        return res
      }, [checkMajor])
      //#endregion

      //已经绑定的大纲
      const [bindOutline, setBindOutline] = useState<any>({})
      //回显
      useEffect(() => {
        if (!!checkedId) {
          CheckService.learnSearch({pageIndex: 1, pageSize: 1, courseId: checkedId}).then((res) => {
            if (res?.status == 200) {
              if (res?.data?.data?.length > 0) {
                const pyfaItem = res.data.data[0]
                setBindOutline(pyfaItem)
                getPyfaDetail(pyfaItem?.id || '',reShowObj?.major)
                form.setFieldsValue({
                  course: `${pyfaItem?.courseName}(${pyfaItem?.grade})`,
                  major: reShowObj?.majorName,
                  grade: reShowObj?.grade
                })
              }
            }
          })
        }
      }, [checkedId])

      const syncTeachingPlancontent = () => {
        if(courseDetail){
          setConfirmLoading(true)
          const description = courseDetail.entityData.description_.split('_');
          syncTeachingPlan({
            courseId: courseDetail.contentId,
            courseNo: description[0],
            serialNo: description[1],
          }).then((res:any)=>{
            if(res.data.data.courseMapId){
              copyselectmap({
                sourMapId: res.data.data.courseMapId,
                targetMapId: mapId,
              }).then((res2:any)=>{
                if(res2.status == 200){
                  setConfirmLoading(false);
                  message.success('同步成功！')
                  onSuccess();
                } 
              }).catch(()=>{
                message.error('同步失败！')
                setConfirmLoading(false);
              })
            }
          }).catch((err:any)=>{
            message.error('同步失败！')
            setConfirmLoading(false)
          })
        }else{
          message.warn('获取课程信息失败！')
        }        
      }
  
  //公大项目定制需求 
  if(parameterConfig.target_customer == 'ppsuc'){
    return (
      <Modal
        title={t('同步大纲教学内容')}
        open={visible}
        onCancel={handleClose}        
        confirmLoading={confirmLoading}
        onOk={syncTeachingPlancontent}
      >
        <span>同步课程大纲教学内容将覆盖当前图谱数据，是否确认？</span>
      </Modal>)
  }else{
    return (
      <Modal
        title={t('同步大纲教学内容')}
        open={visible}
        onCancel={handleClose}
        onOk={e => {
          const values = formRef.current?.getFieldsValue();
          values.pyfaItem = checkMajor
          // const { course, pyfaItem, grade } = value;
          //如果回显，course为一个字符串
          if (typeof values.course === 'string') {
            values.course = {item: bindOutline}
          }
          if (!!checkedId) {
            Modal.confirm({
              content: '当前地图已绑定大纲，是否更换？',
              onCancel: onClose,
              onOk: () => handleConfirm(values),
            });
            return;
          }
          handleConfirm(values);
        }}
      >
        <ProForm {...proformConfig}>
          <ProFormPageSelect
            label="选择大纲"
            required
            needScroll={false}
            name={'course'}
            showSearch
            compatibleLowerVersions
            fieldProps={{
              filterOption: false,
              labelInValue: true,
            }}
            rules={[{ required: true, message: '请选择大纲' }]}
            request={async ({ pageSize, current, keyWords }) => {
              const {
                data: { data, recordTotal },
              } = await CheckService.learnSearch({
                pageSize,
                pageIndex: current,
                course: keyWords,
              });
              return {
                total: recordTotal,
                data: data.map((item: any) => {
                  return {
                    label: `${item.courseName}(${item.grade})`,
                    value: item.id,
                    item,
                  };
                }),
                success: true,
              };
            }}
            onChange={(value:any) => {
              getPyfaDetail(value?.value || '')
              //重置下面的选择框
              setCheckMajor({})
              form.resetFields(['major', 'grade'])
            }}
          />
          <ProFormSelect
          label="适用专业"
          name="major"
          valueEnum={majorList}
          onChange={(value: any) => {
            const item = educationList.find(cell => cell?.majorCode === value)
            setCheckMajor(item)
            form.resetFields(['grade'])
          }}
          fieldProps={{
            notFoundContent: <Empty description='请确认相关培养方案已发布' />
          }}
        />
          <ProFormSelect
          label="适用年级"
          name="grade"
          valueEnum={gradeList}
        />
        </ProForm>
      </Modal>
    );
  }
  
};

export default SynchronizedTeaching;
