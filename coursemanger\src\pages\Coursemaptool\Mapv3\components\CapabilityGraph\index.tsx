import React, { useState,useEffect, useRef } from 'react';
import './index.less';
import { Button, Checkbox, Form, Input, message, Select, Tooltip } from 'antd';
import { ReloadOutlined,SearchOutlined,InfoCircleOutlined, PlusOutlined, MinusOutlined } from '@ant-design/icons';
import { getAbilityMap } from '@/api/coursemap'
import jsplumb from 'jsplumb';
import { useLocation } from 'umi';
import useLocale from '@/hooks/useLocale';
const { Option } = Select;

const CapabilityGraph: React.FC = () => {
  // 获取url参数
  const { query }: any = useLocation();
  // 连线的对象
  const jsPlumbInstance = useRef<any>(jsplumb.jsPlumb.getInstance());

  const [searchform] = Form.useForm();
  const [alldata,setalldata] = useState<any>(null);
  const [graduate,setgraduate] = useState<any>([]);
  //所有的培养目标
  const [trainingTarget,settrainingtarget] = useState<any>([]);
  // 所有的课程
  const [course,setcourse] = useState<any>([]);
  // 所有的课程目标
  const [courseTarget,setcoursetarget] = useState<any>([]);
  // 所有的知识点
  const [courseTeachingContent,setcourseteachingcontent] = useState<any>([]);
  //所有连线的元数据
  const [orangeLinedata,setOrangeLinedata] = useState<any>([]);
  // 所有连线数据
  const [lineData,setlinedata] = useState<any>([]);
  // 所有的达成度
  const [allachievementData,setallachievementDate] = useState<any>([]);
  // 是否显示无数据
  const [nodata,setnodata] = useState<boolean>(false);
  //显示的连线id数组
  const [showlineids,setshowlineids] = useState<any>([]);
  //显示的连线数据
  const [showline,setshowline] = useState<any>([]);
  //当前选中的节点
  const [currentNode,setcurrentNode] = useState<any>(null);
  //当前要展示的板块
  const [currentShowNode,setcurrentShowNode] = useState<any>(['TrainingTargets','graduateRequires','trainingPlanCourseRelations','courseTargets','CourseTeachingContent']);
  //搜索节点的id集合
  const [searchids,setSearchids] = useState<any>([]);
  // 当前搜索结果展示
  const [searchresult,setSearchResult] = useState<any>('0');
  //暂时存放原来的数据的
  const originaldata = useRef<any>({});
  const { t } = useLocale();
  const [zoom,setZoom] = useState<number>(1);
  // 添加这一行来监听 filterType 的值
  const filterType = Form.useWatch('filterType', searchform);
  const selectType = Form.useWatch('selectType', searchform);
  // 所有的年级
  const [allgrade,setallgrade] = useState<any>([]);
  // 当前选择的年级
  const [currentGrade,setcurrentGrade] = useState<any>(null);



  const resize = () => {
    jsPlumbInstance.current.repaintEverything();
  };

  useEffect(() => {
    if(query.applyGrade){        
        let newarr = query.applyGrade.split(',') || [];
        if(newarr.length){
            setcurrentGrade(newarr[0]);        
            setallgrade(newarr.map((item:any)=>{
                return {
                    label:item,
                    value:item
                }
            }));
        }
    }
    window.addEventListener("resize", resize);
    return () => {
        window.removeEventListener("resize", resize);
    };
 }, []);

// 数据变化才去渲染连线
 useEffect(() => {
    if(showline.length){
        renderLine(showline);
    }else{
        jsPlumbInstance.current?.reset();
    }
 } , [showline]);

// 勾选的节点发生变化后自动重新渲染
 useEffect(()=>{
    if(currentNode){
        if(currentShowNode.includes(currentNode.type)){
            getlinedata(currentNode,currentNode.type);
        }else{
            // 取消当前列显示
            if(trainingTarget?.length){
                getlinedata(trainingTarget[0],'TrainingTargets');
            }
        }
    }
 },[currentShowNode])


// 数据渲染完毕了才去选择第一个节点
useEffect(() => {
    if(trainingTarget?.length && orangeLinedata.length){
        if(query.couresSyllabusCode){ 
            const findex = course.findIndex((item:any)=>item.courseId == query.couresSyllabusCode) || 0;      
            getlinedata(course[findex].course,'trainingPlanCourseRelations');
            setTimeout(() => {
                onmousemethod(course[findex].course,'trainingPlanCourseRelations');
            }, 500);
        }else{
            getlinedata(trainingTarget[0],'trainingTarget');
        }
    }
} , [orangeLinedata]);

    useEffect(() => {
        if(currentGrade){
            init();
        }
    }, [currentGrade]);

  const init = () =>{
    getAbilityMap({
        grade: currentGrade,
        majorCode: query.majorCode,
        graph: 0,
        symbol: 0,
        type: 0
    }).then((res)=>{
        if(res.status == 200){
            if(res.data.trainingPlan == null){
                setnodata(true);
                return;
            }
            // 找到所有的培养目标
            const trainingTargetarr = res.data.trainingPlan.trainingTargets;
            settrainingtarget(trainingTargetarr);
            // 处理毕业要求和毕业要求指标点
            let graduatearr = res.data.trainingPlan.graduateRequires
            // .filter((item:any)=>item.parentId == null);
            // let newgraduate =  graduatearr.map((item:any)=>{
            //     let newarr = res.data.trainingPlan.graduateRequires.filter((item2:any)=>item2.parentId == item.id);
            //     return {
            //         ...item,
            //         children:newarr
            //     }
            // })
            setgraduate(graduatearr);
            // 找到所有的课程
            let coursearr = res.data.trainingPlan.trainingPlanCourseRelations;
            setcourse(coursearr);
            // 找到所有的课程目标
            let courseTargetarr:any = [];
            res.data.trainingPlan.trainingPlanCourseRelations.forEach((element:any) => {
                courseTargetarr = [...courseTargetarr,...element.course.courseTargets]
            });
            setcoursetarget(courseTargetarr);
            // 找到所有的知识点
            let courseTeachingContentarr:any = []
            res.data.trainingPlan.trainingPlanCourseRelations.forEach((element:any) => {
                courseTeachingContentarr = [...courseTeachingContentarr,...element.course.courseTeachingContents];
            });
            setcourseteachingcontent(courseTeachingContentarr);
            // 单独存一份数据
            originaldata.current = {
                trainingTarget: trainingTargetarr,
                graduateRequire: graduatearr,
                course: coursearr,
                courseTarget: courseTargetarr,
                courseTeachingContent: courseTeachingContentarr
            }

            // 所有连线关系
            let alllinedata:any = [];
            // 所有的达成度
            let allachievement:any = {};

            // 读取所有节点的统计数据
            res.data.targetIdAchievementDegreeDTOList.forEach((element:any) => {
                allachievement[element.targetId] = Number(element.targetAchievementDegree) || 0;
            });
            // 培养目标到毕业要求
            res.data.trainingTargetToGraduateRequireSupport?.forEach((element:any) => {
                // allachievement[element.sourceId] = Number(element.sourceAchievementDegree) || 0;
                // allachievement[element.targetId] = Number(element.targetAchievementDegree) || 0;

                alllinedata.push({
                    source:element.sourceId,
                    target:element.targetId,
                    data:{
                        ...element,
                        type:'trainingTargetToGraduateRequireSupport'
                    },
                    connector: ["Flowchart",{
                        cornerRadius: 10,
                        alwaysRespectStubs: false,
                        // midpoint:1,
                        // gap: 5,
                    }]
                })
            });
            // 毕业要求及指标点到课程
            res.data.graduateRequireToCourse.forEach((element:any) => {
                // allachievement[element.sourceId] = Number(element.sourceAchievementDegree) || 0;
                // allachievement[element.targetId] = Number(element.targetAchievementDegree) || 0;

                alllinedata.push({
                    source:element.sourceId,
                    target:element.targetId,
                    data:{
                        ...element,
                        type:'graduateRequireToCourse',
                        courseGraduateRequiresWeight: res.data.trainingPlan.courseGraduateRequiresWeight
                    },
                    connector: ["Flowchart",{
                        cornerRadius: 10,
                        alwaysRespectStubs: false,
                        midpoint:0,
                        gap: 5,
                        // stub: [25, 0],
                    }],
                    // overlays: overlays
                })
            });
            // 课程到课程目标
            res.data.courseToCourseTarget.forEach((element:any) => {
                // allachievement[element.sourceId] = Number(element.sourceAchievementDegree) || 0;
                // allachievement[element.targetId] = Number(element.targetAchievementDegree) || 0;
                alllinedata.push({
                    source:element.sourceId,
                    target:element.targetId,
                    data:{
                        ...element,
                        type:'courseToCourseTarget'
                    },
                    connector: ["Flowchart",{
                        cornerRadius: 10,
                        alwaysRespectStubs: false,
                        midpoint:1,
                        gap: 5,
                    }]
                })
            });
            // 课程目标到知识点
            res.data.courseTargetToCourseTeachingContent.forEach((element:any) => {
                // allachievement[element.sourceId] = Number(element.sourceAchievementDegree) || 0;
                // allachievement[element.targetId] = Number(element.targetAchievementDegree) || 0;
                alllinedata.push({
                    source:element.sourceId,
                    target:element.targetId,
                    data:{
                        ...element,
                        type:'courseTargetToCourseTeachingContent'
                    },
                    connector: ["Flowchart",{
                        cornerRadius: 10, //连接线转折点的圆角
                        alwaysRespectStubs: false, //表示即使源元素和目标元素非常接近，也总是绘制所需长度的存根。
                        midpoint:0, //用作源和目标之间中点的点。默认为 0.5。
                        gap: 5, // 开始和结束 锚点的距离
                    }]
                })
            });
            // 毕业要求到课程目标
            res.data.graduateRequireToCourseTarget.forEach((element:any) => {
                alllinedata.push({
                    source:element.sourceId,
                    target:element.targetId,
                    data:{
                        ...element,
                        type:'graduateRequireToCourseTarget'
                    },
                    connector: ["Flowchart",{
                        cornerRadius: 10, //连接线转折点的圆角
                        alwaysRespectStubs: false, //表示即使源元素和目标元素非常接近，也总是绘制所需长度的存根。
                        midpoint:0, //用作源和目标之间中点的点。默认为 0.5。
                        gap: 5, // 开始和结束 锚点的距离
                    }]
                })
            });

            // 课程到知识点
            res.data.courseToCourseTeachingContent.forEach((element:any) => {
                alllinedata.push({
                    source:element.sourceId,
                    target:element.targetId,
                    data:{
                        ...element,
                        type:'courseToCourseTeachingContent'
                    },
                    connector: ["Flowchart",{
                        cornerRadius: 10, //连接线转折点的圆角
                        alwaysRespectStubs: true, //表示即使源元素和目标元素非常接近，也总是绘制所需长度的存根。
                        midpoint:0.6, //用作源和目标之间中点的点。默认为 0.5。
                        gap: 5, // 开始和结束 锚点的距离
                    }]
                })
            });

            console.log('allachievement',allachievement);
            setlinedata(alllinedata);
            setOrangeLinedata(alllinedata);
            setallachievementDate(allachievement);
            setalldata(res.data);
        }else{
            message.error(res.message);
        }
    })
  }

  const initjsPlumb = () => {
    jsPlumbInstance.current.ready(function () {
        jsPlumbInstance.current.setContainer("container");
        jsPlumbInstance.current.importDefaults({
            Connector: ["Flowchart"], //设置连线为直线   Straight 线条  Flowchart 流程图 StateMachine 状态机 Bezier 贝塞尔曲线
            ConnectionsDetachable: false, //是否可以用鼠标拖动使其断开
            Anchors: ["Right", "Left"], //设置连接点位置
            // anchor:"Continuous",
            PaintStyle: { stroke: "#A2B7D5", strokeWidth: 2 ,dashstyle: '2' }, //连线颜色  dashstyle 虚线
            Endpoint: "Blank", //设置连接点形状为圆形
            EndpointStyle: { fill: "#fff" }, //设置连接点的颜色
            HoverPaintStyle: { stroke: "#ff0000" }, //鼠标移上去颜色
            EndpointHoverStyle: { fill: "#ff0000" }, //鼠标移上去连接点的颜色
            ConnectionOverlays: [
                ["Arrow", { width: 10, length: 10, location: 1 }], //箭头的形状
            ],
        });
    });
    jsPlumbInstance.current.batch(function () {
        // renderLine();
    });
    // console.log('mapdata',mapinfo);
}

  // 渲染连线
  const renderLine = (linearr:any) =>{
    let ids:any = [];  //所有的id
    jsPlumbInstance.current.reset();
    linearr.forEach((item:any) => {
        let flag = true;
        // 如果勾选了课程
        if(currentShowNode.includes('trainingPlanCourseRelations') && item.data.type == 'graduateRequireToCourseTarget'){
            flag = false;
        }
        // 如果勾选了课程目标
        if(currentShowNode.includes('courseTargets') && item.data.type == 'courseToCourseTeachingContent'){
            flag = false;
        }

        // 连线上面的标签动态显示
        let overlays:any[] = [];
        // if(item.data.type == 'graduateRequireToCourse'){
        //     if(item.data.courseGraduateRequiresWeight == 0){
        //         overlays = [
        //             [ "Label",
        //                 {
        //                     label:item.data.percentage + '%',
        //                     location: (currentNode.type == 'graduateRequireToCourse'|| currentNode.type == 'TrainingTargets') ? 0.98 : 0.1,
        //                     id:"label",
        //                     cssClass: (currentNode.type == 'graduateRequireToCourse'|| currentNode.type == 'TrainingTargets') ? 'rigth_label_tag' : "left_label_tag"
        //                 }
        //             ]
        //         ]
        //     }else if(item.data.courseGraduateRequiresWeight == 2){
        //         overlays = [
        //             [ "Label",
        //                 {
        //                     label:item.data.lmh,
        //                     location: (currentNode.type == 'graduateRequireToCourse'|| currentNode.type == 'TrainingTargets') ? 0.98 : 0.1,
        //                     id:"label",
        //                     cssClass: (currentNode.type == 'graduateRequireToCourse'|| currentNode.type == 'TrainingTargets') ? 'rigth_label_tag' : "left_label_tag"
        //                 }
        //             ]
        //         ]
        //     }
        //     item.overlays = overlays;
        // }

        if(flag){
            ids.push(item.source);
            ids.push(item.target);
            jsPlumbInstance.current.connect(item);
        }
    })
    setshowlineids(ids);
  }

  // 页面渲染完毕后执行
  useEffect(()=>{
    if(lineData.length){
        initjsPlumb();
    }
  },[lineData])

//   useEffect(()=>{
//     init();
//   },[])

  //获取当前点击的节点的连线数据
  const getlinedata = (node:any,type:string='',isparent=false) =>{
    // 重置搜索显示
    setSearchResult(null);
    setcurrentNode({
        ...node,
        type:type
    });
    let ids:any = [node.id];  //所有的id
    let linearr:any = []
    // 递归向上查找
    const findparentChild = (target:string) =>{
        let arr = orangeLinedata.filter((item:any)=>item.target == target)
        if(arr.length){
            arr.forEach((item:any)=>{
                let flag = true;
                // 如果勾选了课程
                if(currentShowNode.includes('trainingPlanCourseRelations')){
                    if(item.data.type == 'graduateRequireToCourseTarget'){
                        flag = false;
                    }
                }else{
                    if(item.data.type == 'graduateRequireToCourse'){
                        flag = false;
                    }
                }
                // 如果勾选了课程目标
                if(currentShowNode.includes('courseTargets')){
                    if(item.data.type == 'courseToCourseTeachingContent'){
                        flag = false;
                    }
                }else{
                    if(item.data.type == 'courseTargetToCourseTeachingContent'){
                        flag = false;
                    }
                }
                if(flag){
                    ids.push(item.source);
                    ids.push(item.target);
                    linearr.push(item)
                    findparentChild(item.source);
                }
            })
        }
    }
    // 递归向下查找
    const findchildren = (source:string) =>{
        let arr = orangeLinedata.filter((item:any)=>item.source == source)
        if(arr.length){
            arr.forEach((item:any)=>{
                let flag = true;
                // 如果勾选了课程
                if(currentShowNode.includes('trainingPlanCourseRelations')){
                    if(item.data.type == 'graduateRequireToCourseTarget'){
                        flag = false;
                    }
                }else{
                    if(item.data.type == 'graduateRequireToCourse'){
                        flag = false;
                    }
                }
                // 如果勾选了课程目标
                if(currentShowNode.includes('courseTargets')){
                    if(item.data.type == 'courseToCourseTeachingContent'){
                        flag = false;
                    }
                }else{
                    if(item.data.type == 'courseTargetToCourseTeachingContent'){
                        flag = false;
                    }
                }
                if(flag){
                    ids.push(item.source);
                    ids.push(item.target);
                    linearr.push(item)
                    findchildren(item.target);
                }
            })
        }
    }

    if(isparent){
        // 找到当前子节点的所有父节点和子节点        
        let childgraduate = graduate.filter((item:any)=>item.parentId == node.id);
        childgraduate.forEach((element:any) => {
            ids.push(element.id);
            findparentChild(element.id);
            findchildren(element.id);
            setTimeout(() => {
                onmousemethod(element,'graduateRequireToCourse'); 
            }, 500);
        });
        setshowlineids(ids);
        console.log('ids',ids)
    }else{
        findparentChild(node.id);
        findchildren(node.id);
        setshowlineids(ids);
        console.log('ids',ids)
    }
    setshowline(linearr);
  }


  let courseindex  = 0;
// 获取左边距离
  const getleft = () =>{
    if(courseindex == 0){
        courseindex = 1;
        return  '40%'
    }else if(courseindex == 1){
        courseindex = 2;
        return  '10%'
    }else if(courseindex == 2){
        courseindex = 0;
        return  '74%'
    }
  }
  // 动态设置选中 一定要加上空格 注意注意注意！！！
  const getdomclass = (node:any,orgclass:string,type:string = '') =>{
    let newclass = orgclass;
    if(node.id == currentNode?.id){
        if(type == 'courseTeachingContent' || type == 'course'){
            newclass = newclass + ' courseteachingcontent_box'
        }else{
            newclass = newclass +  ' select_box'
        }
    }else if(showlineids.includes(node.id)){
        if(type == 'courseTeachingContent' || type == 'course'){
            newclass = newclass +  ' courseteachingcontent_box'
        }else{
            newclass = newclass + ' active_box'
        }
    }else{
        newclass = newclass + ' hide_box';
    }
    return newclass
  }

  //搜索节点
  const searchnode = (e:any) =>{
    jsPlumbInstance.current?.reset();
    if(e.selectType == '0'){
        searchfun(trainingTarget,e)
    }else if(e.selectType == '1'){
        searchfun(graduate,e)
    }else if(e.selectType == '2'){
        searchfun(course,e)
    }else if(e.selectType == '3'){
        searchfun(courseTarget,e)
    }else if(e.selectType == '4'){
        searchfun(courseTeachingContent,e)
    }
  }
  //统一检索方法  避免重复代码
  const searchfun = (data:any[],e:any) =>{
    let arr:any =[];
    // 根据名字筛选
    if(e.name){
       if(e.selectType == 2){
            arr = data.filter((item:any)=>item.course.courseName.includes(e.name));
        }else if(e.selectType == 4){
            arr = data.filter((item:any)=>item.content.includes(e.name));
        }else{
            arr = data.filter((item:any)=>item.desc.includes(e.name));
        }
    }  
    if(filterType == '1'){
        // 根据参数筛选
        if(e.value){
            // 根据条件筛选
            if(!e.conditions){
                message.info('请选择达成条件！');
                return;
            }
    
            //先根据条件查询
            arr = data.filter((item:any)=>{
                let numbuerdata = allachievementData[item.id] || 0;
                if(e.selectType == '2'){
                    numbuerdata = allachievementData[item.courseId] || 0;
                }
                if(e.conditions == '>='){
                    return numbuerdata >= Number(e.value);
                }else if(e.conditions == '='){
                    return numbuerdata == Number(e.value);
                }else if(e.conditions == '<='){
                    return numbuerdata <= Number(e.value);
                }
            })
        }
    }else{
        let filarr:any = [];
        orangeLinedata.forEach((item:any)=>{           
            if(item.data.lmh == e.typetag){
                if(e.selectType == '1'){
                    filarr.push(item.source)                    
                }else if(e.selectType == '2'){
                    filarr.push(item.target)                    
                }
            }
        })
        
        arr = data.filter((item:any)=>{
            if(e.selectType == 2){
                return filarr.includes(item.courseId);
            }else{
                return filarr.includes(item.id);
            }
        })
    } 
    if(arr.length==0){
        message.info('搜索结果为空');
    }else{
        setSearchResult(e.selectType);
        let newarr = [];
        newarr = arr.map((item:any)=>{
            if(e.selectType == 2){
                return item.courseId;
            }else{
                return item.id;
            }
        })
        
        if(e.selectType == '0'){
            settrainingtarget(arr);
        }else if(e.selectType == '1'){
            setgraduate(arr)
        }else if(e.selectType == '2'){
            setcourse(arr);
        }else if(e.selectType == '3'){
            setcoursetarget(arr);
        }else if(e.selectType == '4'){
            setcourseteachingcontent(arr);
        }
        console.log('搜索结果',newarr)
        setSearchids(newarr);
        setshowlineids([]);
        setcurrentNode(null);
    }
  }

  //监听鼠标移入移出事件
  const onmousemethod = (node:any,type:string,isout:boolean=false) =>{
    // if(showlineids.includes(node.id)){
        const allConnections = jsPlumbInstance.current.getAllConnections();
        const newarr = allConnections.filter((item:any)=>{
            if(type == 'graduateRequireToCourse'){
                return item.sourceId == node.id
            }else{
                return item.targetId == node.id
            }
        })
        newarr.forEach((item:any)=>{
            const data = item.getData();
            if(isout){
                // item.setLabel('');
                // item.removeClass('select_node_item')
                // item.setPaintStyle({ stroke: '#A2B7D5' })
            }else{
                item.setPaintStyle({ stroke: '#ff0000' })
                item.addClass('select_node_item')
                item.setLabel({
                    label: data.courseGraduateRequiresWeight == 0 ? data.percentage + '%' : data.lmh,
                    location: type == 'graduateRequireToCourse' ? 0.98 : 0.1,
                    cssClass: type == 'graduateRequireToCourse' ? 'rigth_label_tag' : "left_label_tag"
                })
            }
         })
    // }
  }

  // 精确到小数点后2位
  const getnumber = (num:any) =>{
    return Number(Number(num).toFixed(1));
  }


  const zoomdom = (type:string) =>{
    const viewBox = document.querySelector('.view_box') as HTMLElement;
    const newZoom = type === 'enlarge' ? zoom + 0.1 : zoom - 0.1;
    
    // 限制缩放范围
    if (newZoom >= 0.5 && newZoom <= 2) {
        setZoom(newZoom);
        viewBox.style.setProperty('--zoom-level', newZoom.toString());
    }
  }

  return (
    <div className="capabilityGraph_view">
        <div className='zoom_view'>
            <Tooltip title={t('放大')} placement="right">
                <Button
                    icon={<PlusOutlined />}
                    title={t('放大')}
                    onClick={() => zoomdom('enlarge')}
                />
                </Tooltip>
                <Tooltip title={t('缩小')} placement="right">
                <Button
                    icon={<MinusOutlined />}
                    title={t('缩小')}
                    style={{ marginTop: '10px' }}
                    onClick={() => zoomdom('narrow')}
                />
            </Tooltip>
        </div>
        <div className='view_box' >
            <div className="search_view">
                <Checkbox.Group className='checkbox_group'
                value={currentShowNode}
                onChange={e => {
                    if(e.includes('trainingPlanCourseRelations') || e.includes('courseTargets')){
                        setcurrentShowNode(e);
                    }else{
                        message.info('课程和课程目标至少选择其中一个')
                    }
                }}
                >
                    <Checkbox value='TrainingTargets' disabled={true} style={{display:'none'}}>培养目标</Checkbox>
                    <Checkbox value='graduateRequires' disabled={true} style={{display:'none'}}>毕业要求及指标点</Checkbox>
                    <Checkbox value='trainingPlanCourseRelations'>课程</Checkbox>
                    <Checkbox value='courseTargets'>课程目标</Checkbox>
                    <Checkbox value='CourseTeachingContent'>知识点</Checkbox>
                </Checkbox.Group>
                <div className='right_view'>
                <Form form={searchform} name="search_form" layout="inline" onFinish={searchnode}>
                    {/* 选择年级 */}
                    <Form.Item  name="grade">
                        <Select
                            value={currentGrade}
                            defaultValue={currentGrade}
                            style={{ width: 100 }}
                            options={allgrade}
                            onChange={(e)=>{
                                setcurrentGrade(e);
                            }}
                        />
                    </Form.Item>
                    <Form.Item name="selectType" initialValue={'0'}></Form.Item>
                    <Form.Item name="name">
                        <Input allowClear addonBefore={(
                            <Select
                                placeholder="请选择类型"  style={{width:'100px'}}
                                defaultValue="0"
                                onChange={(e)=>{
                                    // setSearchResult(e);
                                    // setSearchids([]);
                                    searchform.setFieldValue('selectType',e);
                                }}
                                options={[
                                    {
                                        value: '0',
                                        label: '培养目标',
                                    },
                                    {
                                        value: '1',
                                        label: '毕业要求',
                                    },
                                    {
                                        value: '2',
                                        label: '课程',
                                    },
                                    {
                                        value: '3',
                                        label: '课程目标',
                                    },
                                    {
                                        value: '4',
                                        label: '知识点',
                                    },
                                ]}
                            />
                        )} placeholder="请输入名称" />
                    </Form.Item>
                    { selectType && <Form.Item name="filterType" initialValue={"1"}>
                        <Select
                            placeholder="请选择达成条件"
                            style={{ width: 150 }}
                            onChange={(value) => {
                                if(value === '2') {
                                    searchform.setFieldsValue({
                                        conditions: undefined,
                                        value: undefined
                                    });
                                }
                            }}
                            options={[
                                {
                                    value: '1',
                                    label: '达成条件',
                                },
                                {
                                    value: '2', 
                                    label: '权重关系',
                                    disabled:!(selectType == '1' || selectType == '2'),                                    
                                }
                            ]}
                        />
                    </Form.Item>}

                    {/* 使用 filterType 变量来控制显示 */}
                    {filterType === '1' && (
                        <>
                            <Form.Item name="conditions">
                                <Select
                                    placeholder="请选择符号"
                                    allowClear
                                    style={{ width: 150 }}
                                    options={[
                                        {
                                            value: '>=',
                                            label: '>=',
                                        },
                                        {
                                            value: '=',
                                            label: '=', 
                                        },
                                        {
                                            value: '<=',
                                            label: '<=',
                                        }
                                    ]}
                                />
                            </Form.Item>
                            <Form.Item name="value">
                                <Input 
                                    allowClear 
                                    addonAfter={<span style={{color: '#565656'}}>%</span>} 
                                    style={{ width: 150 }} 
                                    placeholder="请输入达成度" 
                                />
                            </Form.Item>
                        </>
                    )}

                    {filterType === '2' && (
                        <Form.Item name="typetag" initialValue="L">
                            <Select
                                placeholder="请选择符号"
                                style={{ width: 150 }}
                                options={[
                                    {
                                        value: 'L',
                                        label: 'L',
                                    },
                                    {
                                        value: 'M',
                                        label: 'M', 
                                    },
                                    {
                                        value: 'H',
                                        label: 'H',
                                    }
                                ]}
                            />
                        </Form.Item>
                    )}

                    {/* 重置 */}
                    <Form.Item>
                        <Button type="text" onClick={()=>{
                            jsPlumbInstance.current?.reset();
                            settrainingtarget(originaldata.current.trainingTarget);
                            setgraduate(originaldata.current.graduateRequire)
                            setcourse(originaldata.current.course);
                            setcoursetarget(originaldata.current.courseTarget);
                            setcourseteachingcontent(originaldata.current.courseTeachingContent);
                            // 清空当前选择的状态
                            setSearchResult(null);
                            // 清空选中
                            setcurrentNode(null);
                            // 清除搜索结果
                            setSearchids([]);
                            // 清除符合条件的筛选节点
                            setshowlineids([]);
                            searchform.resetFields();
                            // 默认选择第一项
                            if(trainingTarget.length){
                                getlinedata(trainingTarget[0],'trainingTarget');
                            }

                        }}>清空<ReloadOutlined /></Button>
                    </Form.Item>
                    {/* 搜索 */}
                    <Form.Item>
                        <Button type="primary" htmlType="submit">搜索<SearchOutlined /></Button>
                    </Form.Item>
                </Form>
                </div>
            </div>
            <div className='detail_content_view'>
                <div className='table_heard'>
                    { currentShowNode.includes('TrainingTargets') && <div className={`title_box length${currentShowNode.length}`}>
                        <span>培养目标</span>
                        <Tooltip title="毕业要求达成度按支撑权重取和">
                            <InfoCircleOutlined style={{fontSize:'14px',marginLeft:'5px'}} />
                        </Tooltip></div>
                    }
                    {currentShowNode.includes('graduateRequires') && <div className={`title_box length${currentShowNode.length}`}>
                        <span>毕业要求及指标点</span>
                        <Tooltip title="关联的课程目标达成度按支撑权重取和，再按课程支撑权重取和">
                            <InfoCircleOutlined className='tip' />
                        </Tooltip>
                    </div>}
                    {currentShowNode.includes('trainingPlanCourseRelations') && <div className={`title_box length${currentShowNode.length}`}>
                        <span>课程</span>
                        <Tooltip title="关联的课程目标完成情况的平均值">
                            <InfoCircleOutlined className='tip' />
                        </Tooltip>
                    </div>}
                    {currentShowNode.includes('courseTargets') && <div className={`title_box length${currentShowNode.length}`}>
                        <span>课程目标</span>
                        <Tooltip title="课程目标考核项完成情况的加权平均值">
                            <InfoCircleOutlined className='tip' />
                        </Tooltip>
                    </div>}
                    {currentShowNode.includes('CourseTeachingContent') && <div className={`title_box length${currentShowNode.length}`}>
                        <span>知识点</span>
                        <Tooltip title="教学大纲中知识点绑定资源、试题或其他教学活动完成情况按考核占比权重取和">
                            <InfoCircleOutlined className='tip' />
                        </Tooltip>
                    </div>}
                </div>
                {
                   nodata ?
                    <div className='nodata'>
                        <span>暂无能力图谱数据</span>
                    </div>:
                    <div className='ovflowbox'>
                        <div className={`table_content_view  ${searchids.length>0 ? 'nosearch'+searchresult :''}`} id="container">
                            {/* 培养目标 */}
                            {currentShowNode.includes('TrainingTargets')  && <div className={`cell_item length${currentShowNode.length}`}>
                                <div className='course_target_view'>
                                    {
                                        trainingTarget?.map((item:any, index:number) => {
                                            return (
                                                <div id={item.id} className={getdomclass(item,'item_target_view','TrainingTargets')} key={index} onClick={()=>getlinedata(item,'TrainingTargets')}>
                                                    <div className='jindu'>
                                                        <div className='both'></div>
                                                        <span>{getnumber(allachievementData[item.id]) || 0}%</span>
                                                    </div>
                                                    <span className='name' title={item.desc}>{item.desc}</span>
                                                    {showlineids.includes(item.id) && <div className='pile_end'></div>}
                                                </div>
                                            )
                                        })
                                    }
                                </div>
                            </div>}
                            {/* 毕业要求和毕业指标点 */}
                            { currentShowNode.includes('graduateRequires') && <div className={`cell_item length${currentShowNode.length}`}>
                                {
                                    graduate.map((item:any, index:number) => {
                                        // if(item.children?.length){
                                        if(item.parentId ==  null){
                                            return (
                                                <div id={item.id} className='item_view' key={index}  onClick={()=>{
                                                    getlinedata(item,'graduateRequireToCourse',true);
                                                    setTimeout(() => {
                                                        onmousemethod(item,'graduateRequireToCourse');
                                                    }, 100);
                                                }} onMouseOver={()=>onmousemethod(item,'graduateRequireToCourse')} onMouseOut={()=>onmousemethod(item,'graduateRequireToCourse',true)}>
                                                    <div className={getdomclass(item,'left_view','graduateRequires')} >
                                                        <span className='name'>{item.name}</span>
                                                        <div className='jindu'>
                                                            <div className='both'></div>
                                                            <span>{getnumber(allachievementData[item.id]) || 0}%</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            )
                                        }else{
                                            return (
                                                <div className='item_view' key={index} >
                                                    <div className='right_view'>
                                                        <div id={item.id} className={getdomclass(item,'zhibiaodian_view','graduateRequires')} onClick={()=>{
                                                            getlinedata(item,'graduateRequireToCourse');
                                                            setTimeout(() => {
                                                                onmousemethod(item,'graduateRequireToCourse');
                                                            }, 100);
                                                        }} onMouseOver={()=>onmousemethod(item,'graduateRequireToCourse')} onMouseOut={()=>onmousemethod(item,'graduateRequireToCourse',true)}>
                                                            <div className='jindu'>
                                                                    <div className='both'></div>
                                                                    <span>{getnumber(allachievementData[item.id]) || 0}%</span>
                                                            </div>
                                                            <span className='name'>{item.desc}</span>
                                                            <div  className='pile'></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            )
                                        }
                                    })
                                }
                            </div>}
                            {/* 课程 */}
                            { currentShowNode.includes('trainingPlanCourseRelations') && <div className={`cell_item length${currentShowNode.length}`}>
                                <div className='course_view'>
                                    {
                                        course.map((item:any, index:number) => {
                                            return (
                                                <div id={item.course.id} className={getdomclass(item.course,'course_item','course')} key={index} style={{marginLeft:getleft()}} onClick={()=>{
                                                    getlinedata(item.course,'trainingPlanCourseRelations');
                                                    setTimeout(() => {
                                                        onmousemethod(item.course,'trainingPlanCourseRelations');
                                                    }, 100);
                                                }}
                                                onMouseOver={()=>onmousemethod(item.course,'trainingPlanCourseRelations')} onMouseOut={()=>onmousemethod(item.course,'trainingPlanCourseRelations',true)}>
                                                    <div id={item.course.id} className='img_box'>
                                                        { (!allachievementData[item.course.id] || (allachievementData[item.course.id]>= 0 && allachievementData[item.course.id]< 30)) && <img src={require('./img/huang1.png')} />}
                                                        { allachievementData[item.course.id]>= 30 && allachievementData[item.course.id]< 70 && <img src={require('./img/lan1.png')} />}
                                                        { allachievementData[item.course.id]>= 70 && <img src={require('./img/green1.png')} />}
                                                        <span>{getnumber(allachievementData[item.course.id]) || 0}%</span>
                                                    </div>
                                                    <span className='name' title={item.course.courseName}>{item.course.courseName}</span>
                                                </div>
                                            )
                                        })
                                    }
                                </div>
                            </div>}
                            {/* 课程目标 */}
                            {currentShowNode.includes('courseTargets') && <div className={`cell_item length${currentShowNode.length}`}>
                                <div className='course_target_view'>
                                    {
                                        courseTarget.map((item:any, index:number) => {
                                            if(showlineids.includes(item.id) || searchresult!=null){
                                                return (
                                                    <div id={item.id} className={getdomclass(item,'item_target_view','courseTargets')} key={index} onClick={()=>getlinedata(item,'courseTargets')}>
                                                        <div  className='pile_start'></div>
                                                        <div className='jindu'>
                                                            <div className='both'></div>
                                                            <span>{getnumber(allachievementData[item.id]) || 0}%</span>
                                                        </div>
                                                        <span className='name' title={item.desc}>{item.desc}</span>
                                                        <div className='pile_end'></div>
                                                    </div>
                                                )
                                            }
                                        })
                                    }
                                </div>
                            </div>}
                            {/* 知识点 */}
                            {currentShowNode.includes('CourseTeachingContent') && <div className={`cell_item length${currentShowNode.length}`}>
                                <div className='courseTeachingContent_view'>
                                    {
                                        courseTeachingContent.map((item:any, index:number) => {
                                            if(showlineids.includes(item.id) || searchresult!=null){
                                                return (
                                                    <div className={getdomclass(item,'course_item','courseTeachingContent')} key={index} style={{marginLeft:getleft()}} onClick={()=>getlinedata(item,'CourseTeachingContent')}>
                                                        <div id={item.id} className='img_box'>
                                                            { allachievementData[item.id]>= 0 && allachievementData[item.id]< 30 && <img src={require('./img/huang1.png')} />}
                                                            { allachievementData[item.id]>= 30 && allachievementData[item.id]< 70 && <img src={require('./img/lan1.png')} />}
                                                            { allachievementData[item.id]>= 70 && <img src={require('./img/green1.png')} />}
                                                            <span>{allachievementData[item.id] || 0}%</span>
                                                        </div>
                                                        <span className='name' title={item.content}>{item.content}</span>
                                                    </div>
                                                )
                                            }
                                        })
                                    }
                                </div>
                            </div>}
                        </div>
                    </div>

                }
                {/* 底部提醒 */}
                <div className='bottom_tip'>
                        <div className='item_box'>
                            <div className='both'></div>
                            <span>100%-70%</span>
                        </div>

                        <div className='item_box'>
                            <div className='both'></div>
                            <span>70%-30%</span>
                        </div>

                        <div className='item_box'>
                            <div className='both'></div>
                            <span>30%-0%</span>
                        </div>
                </div>
            </div>
        </div>
    </div>
  );
};

export default CapabilityGraph;
