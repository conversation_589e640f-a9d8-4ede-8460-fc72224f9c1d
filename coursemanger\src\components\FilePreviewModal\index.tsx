import "./index.less";
import React, { FC } from 'react';
import { Modal } from 'antd';
import Entity from "@/components/entity/entity";
// import {use} from "umi" 


interface fileModalProp {
  visible: boolean;
  onClose: () => void;
  width?: number;
  file: any;
  fileType: "video" | "audio" | "picture" | "document";
}


const FilePreviewModal: FC<fileModalProp> = ({
  visible,
  onClose,
  width,
  file,
  fileType
}) => {
  // const location = useLocation()
  return <Modal destroyOnClose width={width || "80vw"} title={file.extraData} visible={visible} footer={null} onCancel={onClose}>
    <div className={`preview-wrap ${fileType}-wrp`}>
      <Entity type={fileType} src={file.filePath?.replace("/mstorage", "")} />
    </div>
  </Modal>;
};

export default FilePreviewModal;