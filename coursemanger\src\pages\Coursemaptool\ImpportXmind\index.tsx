import React, { FC, useRef, useState } from 'react';
import { Modal, Upload, Button, message, Select } from 'antd';
import Icon, { DeleteOutlined } from '@ant-design/icons';
import { ReactComponent as plus_icon } from "@/assets/imgs/icon/plus_icon.svg";
import chapterApis from "@/api/chapter";
import { importxmind, unlockmap } from '@/api/coursemap';
import { useLocation, useSelector } from "umi";
import "./index.less";
import useLocale from '@/hooks/useLocale';

const { Dragger } = Upload;
const { confirm } = Modal;
interface IWordImport {
  visible: number;
  onClose: () => void;
  onSuccess: () => void;
  perviewtype: number;
  mapid: any;
}

const PlusIcon = (props: any) => <Icon component={plus_icon} {...props} />;

const ImpportXmind: FC<IWordImport> = ({ visible, onClose, onSuccess, perviewtype, mapid }) => {
  const { t } = useLocale();
  const [fileData, setFileData] = useState<any>(null);
  //  文件后缀
  const [fileType, setFileType] = useState<string>("");
  const { query }: any = useLocation();
  const [loading, setLoading] = useState<boolean>(false);
  const [generationmode, setGenerationmode] = useState<string>('1');
  const parameterConfig = useSelector<any, any>(
    ({ global }) => global.parameterConfig,
  );

  const props = {
    name: 'file',
    accept:'.xmind',
    showUploadList: false,
    beforeUpload(file: any) {
      const nameArr = file.name.split('.');
      let suffix = nameArr[nameArr.length - 1].toLowerCase();
      if (suffix === 'xmind') {
        
        if (parameterConfig?.target_customer === 'sjtu') {
          handleImport(file,suffix);
          return false;
        }else{
          setFileData(file);
          setFileType(suffix);
        }
      } else {
        message.warning(t("只能上传xmind文件！"));
      }
      return false;
    },
    onDrop(e: any) {
      console.log('Dropped files', e.dataTransfer.files);
    }
  };

  const handleImport = (file: any=null,suffix: string='') => {
    confirm({
      title: t(`导入数据会覆盖当前编辑内容，是否确定？`),
      onOk() {
        executeImport(file,suffix);
      },
      onCancel() { }
    });
  };

  const executeImport = (file: any=null,suffix: string='') => {
    // if (!fileData.name && !file) {
    //   message.warning(t("请先上传文件再导入！"));
    //   return;
    // }
    setLoading(true);
    const formData = new FormData();
    formData.append("file", fileData || file);
    let courseId = null;
    if (perviewtype == 1) {
      courseId = query.id;
    }
    if (fileType == 'xmind' || suffix == 'xmind') {
      importxmind(mapid, 'Xmind2map', generationmode, formData).then((res: any) => {
        if (res.success) {
          unlockmap({
            mapId: mapid,
            type: 0
          }).then((res2) => {
            console.log('地图加锁',res2);
            onSuccess();
            handleClose();
          });
        } else {
          message.error(t("导入失败：") + res.message);
        }
      }).finally(() => {
        setLoading(false);
      });
    } else {
      message.warning(t("只能上传xmind文件！"));
      setLoading(false);
    }
  };

  function base64ToExcel(base64Data: any, fileName: String) {
    // 解码 Base64 字符串为二进制数据
    const binaryString = atob(base64Data);
    // 将二进制数据转换为字节数组
    const byteNumbers = new Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      byteNumbers[i] = binaryString.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);

    // 创建 Blob 对象
    const blobData = new Blob([byteArray], { type: 'application/vnd.ms-excel' });

    // 创建 data URI
    const url = URL.createObjectURL(blobData);

    // 设置下载链接的 href 属性为 data URI
    const downloadLink: any = document.createElement('a');
    downloadLink.href = url;
    downloadLink.download = fileName;
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
  }

  const handleClose = () => {
    setFileData(null);
    onClose();
  };
  const handleDelete = () => {
    setFileData(null);
  };

  return <Modal title={t("导入xmind")} open={visible == 15} footer={null} onCancel={handleClose}>
    <div className="import-xmind-map-chapter">
      {
        fileData?.name ? <div className='file-name'>
          <span>选择文件：</span>
          {fileData?.name}<DeleteOutlined onClick={handleDelete} /></div> : 
        <Dragger {...props}>
          <p className="ant-upload-drag-icon">
            <PlusIcon />
          </p>
          <p className="ant-upload-text">{t("将文件拖拽至此处，或")}<a>{t("点击上传")}</a></p>
          {/* <p className="ant-upload-text">上传成功的文件可在个人资源-课程上传资源文件夹里找到</p> */}
        </Dragger>}
      {
        fileData?.name ? <>
          <span>转换模式：</span>
          <Select
            defaultValue="auto"
            value={generationmode}
            style={{ width: 120 }}
            onChange={(e) => setGenerationmode(e)}
            options={[
              {
                value: '1',
                label: t("分类节点")
              },
              {
                value: '2',
                label: t("知识节点")
          }]} />
          <p style={{ fontSize: '14px', color: 'rgba(0, 0, 0, 0.65)',marginTop:'10px' }}>{t('分类节点')}：{t("文件中的节点全部转换为分类节点")}</p>
          <p style={{ fontSize: '14px', color: 'rgba(0, 0, 0, 0.65)' }}>{t('知识节点')}：{t("文件中的节点全部转换为知识节点")}</p>
        </>: null
      }
      <div className="btn-group">
        <Button type="primary" loading={loading} disabled={!fileData} onClick={handleImport}>{t("确认导入")}</Button>
      </div>
    </div>

  </Modal>;
};

export default ImpportXmind;