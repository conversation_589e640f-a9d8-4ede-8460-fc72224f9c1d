import React, { useState, useEffect, FC, useRef, useCallback } from "react";
import './index.less';
import Header from "../../../Mapv4/components/heard";
import MapX6 from "../mapX6";
import { useLocation, useSelector, useDispatch } from 'umi';
import { getCourseByMajorCode, getMapByCourseNumber, querymapbyid } from '@/api/coursemap';
import { createNodeData, MapConfig, defaultNodeData } from '../util';
import { defaultNode } from '../../../Editmap/util';
import { Drawer, Button, message } from 'antd';
// 搜索的组件
import Search from '../Search';
// 详情组件
import Rdrawer from '../../../Rdrawer';
// 课程详情
import CourseInfo from '../CourseInfo';
// 展示跨课关系
import RelationMap from '../../../components/RelationMap';
import debounce from "lodash/debounce";
// 知识点达成度
import Achievementdegree from '../../../Achievementdegree';
import useLocale from "@/hooks/useLocale";
import close_icon1 from '@/assets/imgs/coursemap/v3/close.png';
import close_icon2 from '@/assets/imgs/coursemap/v3/close2.png';
import title_bg1 from '@/assets/imgs/coursemap/v3/title_bg.png';
import title_bg2 from '@/assets/imgs/coursemap/v3/title_bg_dark.png';
interface IAtlaspatternProps {
  init?: any;
}

const Atlaspattern: FC<IAtlaspatternProps> = ({init}) => {

  const { t } = useLocale();
  const [mapinfo, setMapInfo] = useState<any>(null);
  const [graph, setGraph] = useState<any>(null);
  // 搜索组件的ref 里面暴露了方法出来
  const searchref = useRef<any>();
  // 0是关闭状态  1显示知识节点弹窗  2显示对比辨析弹窗  3关联节点   4知识节点对比辨析  5绑定管理  6搜素   7 excel word 导入  8 对比辨析详情页面  9课程大钢  10课程地图按课程资源生成  11 地图保存记录 12 知识点达成度 13选择添加的课程  14课程详情  
  // 15是导入xmind  16是引用地图  17是添加地图关系  18是展示跨课关系
  const [visible, setVisible] = useState<number>(0);
  // 查询类型 0是全部  1是子节点  2是知识节点
  const [querytype, setQuerytype] = useState<string>('0');
  //  查询节点的类型  疑难点  对比辨析节点  
  const [selecttype, setSelecttype] = useState<string>('0');
  // 搜索框的参数
  const [inputtext, setInputtext] = useState<string>('');
  // 详情的参数
  const [drawerdata, setDrawerdata] = useState<any>(null);
  // 当前选择的课程id
  const [selectcourseid, setSelectcourseid] = useState<any>(null);
  // 当前的状态
  const perviewtype: number = 3; //0是工具端  1是老师端打开 2是学生端 3是专业路径
  // 当前选择的地图id
  const [selectmapid, setSelectmapid] = useState<any>(null);
  // 获取url参数
  const { query }: any = useLocation();
  // 当前是专业地图还是课程地图
  const [maptype, setMaptype] = useState<number>(1); //1是专业地图  2是课程地图
  // 异常情况展示
  const [errorinfo, setErrorinfo] = useState<any>(0); //0是正常  1参数错误  2当前专业暂无课程数据 3暂无专业图谱数据  4当前课程地图已下架  5当前课程地图已被删除
  // 当前选择的节点
  const [selectnode, setSelectnode] = useState<any>(null);
  // 当前布局模式
  const [layouttype, setLayouttype] = useState<number>(1);
  // 当前皮肤模式
  const [skin, setSkin] = useState<string>('2');

  useEffect(() => {
    if (query.majorCode) {
      initmarjormap(query.majorCode,query.grade);
      setMaptype(1);
    } else if (query.mapid) {
      initcoursemap(query.mapid);
      setMaptype(2);
    } else {
      console.log('没有参数');
      setErrorinfo(1);
    }

    return () => {
      if (graph) {
        graph.current.dispose();
        graph.current = null;
      }
    };
  }, []);

  const initmarjormap = (majorCode: string = '',grade:string='') => {
    getCourseByMajorCode({ majorCode: majorCode,grade:grade }).then((res) => {
      if (res.data) {
        if(init){
          init(res.data);
        }
        let mapdata = createNodeData(res.data);
        console.log(mapdata, 'mapdata');
        if (mapdata.courseNumber.length == 0) {
          setErrorinfo(2);
          // message.info('当前专业暂无课程数据');
          return;
        }
        getMapByCourseNumber(mapdata.courseNumber).then((res2: any) => {
          if (res2.data.length) {
            res2.data.forEach((element: any) => {
              let rootid = '';
              if (element.nodeInfo.nodesVos.length) {
                rootid = element.nodeInfo.nodesVos[0].nodeId;
                // 删除第一个节点
                element.nodeInfo.nodesVos.shift();
                // 存进去
                mapdata.nodes = [...mapdata.nodes, ...element.nodeInfo.nodesVos];
              }
              if (element.nodeInfo.relationVos.length) {
                element.nodeInfo.relationVos.forEach((element2: any) => {
                  if (element2.source == rootid) {
                    mapdata.edges.push({
                      ...element2,
                      source: mapdata.mapcourseobj[element.courseNumber]
                    });
                  } else {
                    mapdata.edges.push(element2);
                  }
                });
              }
            });
            let newmapdata = makedata(mapdata.nodes, mapdata.edges);
            setMapInfo(newmapdata);
            console.log(newmapdata);
          } else {
            let newmapdata = makedata(mapdata.nodes, mapdata.edges);
            setMapInfo(newmapdata);
          }
        });

      } else {
        setErrorinfo(3);
        // message.info('暂无专业图谱数据');
      }
    });
  };

  const initcoursemap = (mapid: string = '') => {
    querymapbyid({
      mapId: mapid
    }).then((res: any) => {
      if (res.status == 200) {
        let newmapdata: any = null;
        // 如果地图有数据
        if (res.data.nodesVos.length) {
          newmapdata = makedata(res.data.nodesVos, res.data.relationVos);
        } else {
          // 如果没有数据 就显示默认的节点
          const defaultNodes: any = defaultNode();
          newmapdata = makedata(defaultNodes.nodesVos, defaultNodes.relationVos);
        }
        setMapInfo(newmapdata);
        console.log(newmapdata);
      } else if (res.status == 400) {
        // 已经删除
        setErrorinfo(5);
        // message.info('当前课程地图已被删除！')
      } else {
        setErrorinfo(6);
        message.error(res.message);
      }
    });
  };

  const makedata = (nodesVos: any, relationVos: []) => {
    let linkmapid: any = [];
    let newnodes = nodesVos.map((item: any) => {
      let data = JSON.parse(item.valueMap);
      let obj = {
        ...item,
        id: item.nodeId,
        data: {
          ...defaultNodeData,
          ...data.data,
          mapId: item.mapId || null
        },
        zIndex: 3,
        shape: 'react-shape',
        component: 'react-compont-v3',
        type: data.data.type,
        visible: data.data.visible == undefined ? true : data.data.visible
      };
      // 把跨界的节点存起来 下面建立连线
      if (obj.data.linkmapid.length) {
        obj.data.linkmapid.forEach((item2: any) => {
          linkmapid.push({
            ...item2,
            thisobj: obj
          });
        });
      }
      if (data.data.type == 4) {
        obj.size = [MapConfig.marjor.size[0], MapConfig.marjor.size[1]];
        obj.width = MapConfig.marjor.size[0];
        obj.height = MapConfig.marjor.size[1];
      } else if (data.data.type == 3) {
        obj.size = [MapConfig.course.size[0], MapConfig.course.size[1]];
        obj.width = MapConfig.course.size[0];
        obj.height = MapConfig.course.size[1];
      } else if (data.data.type == 1) {
        if (data.data.isroot) {
          obj.size = [MapConfig.course.size[0], MapConfig.course.size[1]];
          obj.width = MapConfig.course.size[0];
          obj.height = MapConfig.course.size[1];
        } else {
          obj.size = [MapConfig.fenlei.size[0], MapConfig.fenlei.size[1]];
          obj.width = MapConfig.fenlei.size[0];
          obj.height = MapConfig.fenlei.size[1];
        }
      } else if (data.data.type == 2) {
        obj.size = [MapConfig.knowledge.size[0], MapConfig.knowledge.size[1]];
        obj.width = MapConfig.knowledge.size[0];
        obj.height = MapConfig.knowledge.size[1];
      } else if(data.data.type == 5){
        obj.size = [MapConfig.teachingmodule.size[0], MapConfig.teachingmodule.size[1]];
          obj.width = MapConfig.teachingmodule.size[0];
          obj.height = MapConfig.teachingmodule.size[1];
      }else{
        console.log('未知类型');
      }
      return obj;
    });

    let newedges = relationVos.map((item: any) => {
      const data = JSON.parse(item.data);
      //每个边的初始化数据
      const obj = {
        ...item,
        data: {
          ...data,
          newadd: data.isnew || false
        },
        visible: data.visible == undefined ? true : data.visible
      };
      if (data.type == 1) {//包含
        obj.attrs = {
          line: {
            stroke: "#B3BAC4", // 指定 path 元素的填充色
            strokeWidth: 1,
            targetMarker: {
              args: { size: 8 },
              name: 'classic'
            }
          }
        };
      } else if (data.type == 2) {//等价
        obj.attrs = {
          line: {
            stroke: "#B3BAC4", // 指定 path 元素的填充色
            strokeWidth: 1,
            targetMarker: null,
            strokeDasharray: 5
          }
        };
      } else if (data.type == 3) {//后续
        obj.attrs = {
          line: {
            stroke: "#B3BAC4", // 指定 path 元素的填充色
            strokeWidth: 1,
            // targetMarker: null
            // #333333
            strokeDasharray: 5,
            targetMarker: {
              args: { size: 8 },
              name: 'classic'
            }
          }
        };
      } else if (data.type == 4) {//关联
        obj.attrs = {
          line: {
            stroke: "#B3BAC4", // 指定 path 元素的填充色
            strokeWidth: 1,
            // targetMarker: null
            // #333333
            strokeDasharray: 5,
            targetMarker: {
              args: { size: 8 },
              name: 'classic'
            }
          }
        };
      }else if (data.type == 5) {//关联
        obj.attrs = {
          line: {
            stroke: "#B3BAC4", // 指定 path 元素的填充色
            strokeWidth: 1,
            // targetMarker: null
            // #333333
            strokeDasharray: 5,
            sourceMarker: {
              args: { size: 8 },
              name: 'classic'
            }
          }
        };
      }else if (data.type == 6) {//依赖
        obj.attrs = {
          line: {
            stroke: "#B3BAC4", // 指定 path 元素的填充色
            strokeWidth: 1,
            targetMarker: {
              args: { size: 8 },
              name: 'classic'
            }
          }
        };
      }else if (data.type == 7) {//递进
        obj.attrs = {
          line: {
            stroke: "#B3BAC4", // 指定 path 元素的填充色
            strokeWidth: 1,
            targetMarker: {
              args: { size: 8 },
              name: 'classic'
            }
          }
        };
      }else if (data.type == 8) {//辩证
        obj.attrs = {
          line: {
            stroke: "#B3BAC4", // 指定 path 元素的填充色
            strokeWidth: 1,
            targetMarker: {
              args: { size: 8 },
              name: 'classic'
            }
          }
        };
      }
      return obj;
    });

    // 如果有跨课的节点  就建立连线
    if (linkmapid.length) {
      linkmapid.forEach((item: any) => {
        //先查找有没有这个节点
        let getnode = newnodes.find((item2: any) => item2.id == item.nodeId);
        if (getnode) {
          const obj: any = {
            data: { visible: true, type: item.type, isnew: true },
            source: item.thisobj.id,
            target: item.nodeId,
            type: item.type
          };
          if (item.type == 1) {//包含
            obj.attrs = {
              line: {
                stroke: "#B3BAC4", // 指定 path 元素的填充色
                strokeWidth: 1,
                targetMarker: {
                  args: { size: 8 },
                  name: 'classic'
                }
              }
            };
          } else if (item.type == 2) {//等价
            obj.attrs = {
              line: {
                stroke: "#B3BAC4", // 指定 path 元素的填充色
                strokeWidth: 1,
                targetMarker: null,
                strokeDasharray: 5
              }
            };
          } else if (item.type == 3) {//后续
            obj.attrs = {
              line: {
                stroke: "#B3BAC4", // 指定 path 元素的填充色
                strokeWidth: 1,
                // targetMarker: null
                // #333333
                strokeDasharray: 5,
                targetMarker: {
                  args: { size: 8 },
                  name: 'classic'
                }
              }
            };
          } else if (item.type == 4) {//后续
            obj.attrs = {
              line: {
                stroke: "#B3BAC4", // 指定 path 元素的填充色
                strokeWidth: 1,
                // targetMarker: null
                // #333333
                strokeDasharray: 5,
                targetMarker: {
                  args: { size: 8 },
                  name: 'classic'
                }
              }
            };
          }
          newedges.push(obj);
        }
      });
    }

    return {
      nodes: newnodes,
      edges: newedges
    };
  };

  // 更新节点的对比辨析字段
  const updatanodecompare = (compare: any) => {
    const allnodes = graph.getNodes();
    // 先把所有的都改成false
    allnodes.forEach((element: any) => {
      let data = element.getData();
      element.updateData({
        ...data,
        isDiscriminate: false
      });
    });
    // 再更新节点
    compare.forEach((id: any) => {
      const node = graph.getCellById(id);
      if (node) {
        let data = node.getData();
        node.updateData({
          ...data,
          isDiscriminate: true
        });
      }
    });
  };

  // 更新节点的data
  const updatanode = (id: string, data: any) => {
    const node = graph.getCellById(id);
    // console.log('更新节点的data', node.getData());
    let nodedata = node.getData();
    let newdata = {
      ...nodedata,
      ...data
    };
    node.updateData(newdata);
  };

  // 居中节点
  const centerednode = (id: string) => {
    let cell = graph.getCellById(id);
    graph.getNodes().forEach((item: any) => {
      // 设置节点透明
      item.attr('foreignObject/opacity', 0.2);
    });
    graph.getEdges().forEach((item: any) => {
      // 设置节点透明
      item.attr('line/stroke', '#333333');
    });
    // 设置节点高亮
    cell.attr('foreignObject/opacity', 1);
    graph.centerCell(cell, { animation: { duration: 400 } });
  };


  // 设置状态 这里用防抖是因为 点击跨课 标签的点击事件 和 图谱 节点点击事件 会同时触发 取最后一次的
  const setVisiblestate = debounce((e: any) => {
    setVisible(e);
  }, 100);

  return (
    <div className={skin == '1' ? "Atlaspattern_view" : "Atlaspattern_view_dark"}>
      {/*  1参数错误  2当前专业暂无课程数据 3暂无专业图谱数据  4当前课程地图已下架  5当前课程地图已被删除  6其他异常 */}
      {
        errorinfo !== 0 &&
        <div className="message_view">
          {errorinfo == 1 && <span>{t("参数错误")}</span>}
          {errorinfo == 2 && <span>{t("当前专业暂无课程数据")}</span>}
          {errorinfo == 3 && <span>{t("暂无专业图谱数据")}</span>}
          {errorinfo == 4 && <span>{t("当前课程地图已下架")}</span>}
          {errorinfo == 5 && <span>{t("当前课程地图已被删除")}</span>}
          {errorinfo == 6 && <span>{t("其他异常")}</span>}
        </div>}



      <Header skin={skin} mapdata={mapinfo} graph={graph} maptype={maptype} perviewtype={query.perviewtype || 3} querytype={querytype} inputtext={inputtext}
        setInputtext={(e: any) => setInputtext(e.target.value)}
        setSelecttype={setSelecttype}
        typeonselect={(LabeledValue: any) => {
          setQuerytype(LabeledValue);
          setVisible(6);
          setInputtext('');
          searchref.current.querynode();
        }}
        search={(e: any) => {
          setVisible(6);
          setQuerytype('0');
          searchref.current.querynode();
        }}
        updataskin={setSkin}
        setVisible={setVisible}  setLayouttype={setLayouttype} >
      </Header>
      <div className="map_content_view">
        <MapX6 skin={skin} mapdata={mapinfo} scale={maptype == 1} showzoombtn={true} maptype={maptype} showunfold={true} layouttype={layouttype}
          nodeClick={(node: any) => {
            const data = node.getData();
            if (data.type == 2) {
              setDrawerdata(node);
              setSelectmapid(data.mapId);
              setVisiblestate(1);
            } else if (data.type == 3) {
              setSelectcourseid(data.course.courseId);
              setVisiblestate(14);
            } else {
              console.log('其他类型的节点不处理');
            }
          }}
          initover={(e: any) => {
            setGraph(e);
          }}
          clicktag={(e: any) => {
            setSelectnode(e);
            setVisiblestate(18);
          }}>
        </MapX6>
        {/* 搜索管理 */}
        <Search ref={searchref} graph={graph} querytype={querytype} centerednode={(e: any) => { }} selecttype={selecttype} inputtext={inputtext} visible={visible} setVisible={(e: number) => setVisible(e)}></Search>
        {/* 知识点详情 */}
        {/* 节点详情 */}
        <Drawer
          placement="right"
          mask={false}
          title={
            <div style={{ position: 'relative', width: '100%', height: '100%' }}>
              <img style={{ width: '100%', height: 'auto' }} src={skin == '1'? title_bg1:title_bg2 } />
              <img onClick={() => setVisible(0)} style={{ position: 'absolute', right: '20px', width: '15px', top: '22px' }} src={skin == '1' ? close_icon1: close_icon2} alt="" />
            </div>}

          closable={false}
          onClose={() => {
            setVisible(0);
            setDrawerdata(null);
          }}
          visible={visible == 1}
          getContainer={false}
          style={{ position: 'absolute' }}
          width="600px"
          className="node_detail_view">

          <Rdrawer graph={graph} x6node={drawerdata} visible={visible} onback={() => setDrawerdata(null)}
            updatanodecompare={updatanodecompare}
            centerednode={centerednode}
            updatanode={updatanode}
            perviewtype={query.perviewtype || 3}
            mapid={selectmapid}
            courseid={query.courseid}
            coursename={null}
            isedit={false}
            isv3={true}
            setVisible={(e: number) => setVisible(e)}>
          </Rdrawer>
        </Drawer>
        {/* 课程详情 */}
        <CourseInfo visible={visible} courseid={selectcourseid} onCancel={() => setVisible(0)}></CourseInfo>
        {/* 展示跨课关系 */}
        {
          maptype == 2 && visible == 18 &&
          <RelationMap mapid={query.mapid} graph={graph} selectnode={selectnode} visible={visible} onClose={() => setVisible(0)}></RelationMap>}

        {/* 知识点达成度 */}
        {
          maptype == 2 && visible == 12 &&
          <Achievementdegree visible={visible} centerednode={centerednode} courseid={query.courseid} isv3={true} mapid={query.mapid} perviewtype={query.perviewtype || perviewtype} setVisible={setVisible} onCancel={() => setVisible(0)}></Achievementdegree>}


      </div>
    </div>);

};

export default Atlaspattern;