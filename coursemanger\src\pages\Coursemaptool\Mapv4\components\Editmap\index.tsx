import baseInfo from '@/api/baseInfo';
import { IconFont } from '@/components/iconFont';
import {
  attrs,
  beforeunload,
  createguid,
  defaultMarjorNode,
  defaultMicromajorNode,
  defaultNode,
  defaultNodeData,
  defaultEdgeData,
  getNodewidth,
  getTextSize,
  mapv4colors,
  ports,
  tranListToTreeData,
  tranTreeDataToList,
  getKnowledgeBymouldes,
  checkshowmessage,
  computingKnowledge,
  buildKnowledgedata,
  hasRichTextContent
} from '@/pages/Coursemaptool/Editmap/util';
import {
  BarChartOutlined,
  CloseOutlined,
  EllipsisOutlined,
  LeftOutlined,
  LockOutlined,
  MinusCircleOutlined,
  MinusOutlined,
  OrderedListOutlined,
  PlusCircleOutlined,
  PlusOutlined,
  SaveOutlined,
  SyncOutlined,
  ZoomInOutlined,
  ZoomOutOutlined
} from '@ant-design/icons';
import Hierarchy from '@antv/hierarchy';
import { DataUri, Graph, Markup, Model } from '@antv/x6';
import {
  Button,
  Checkbox,
  Drawer,
  Dropdown,
  Input,
  Menu,
  Modal,
  Popover,
  Select,
  Tabs,
  Tag,
  Tooltip,
  message,
  notification
} from 'antd';
import React, { FC, useEffect, useRef, useState } from 'react';
import { useDispatch, useLocation, useSelector } from 'umi';
import './index.less';
// 这个是自动布局的插件
// import {
//   GridLayout,
//   RandomLayout,
//   GForceLayout,
//   ForceLayout,
//   CircularLayout,
//   DagreLayout,
//   DagreCompoundLayout,
//   RadialLayout,
//   ConcentricLayout,
//   MDSLayout,
//   FruchtermanLayout,
//   FruchtermanGPULayout,
//   GForceGPULayout,
//   ComboForceLayout,
//   ComboCombinedLayout,
//   ForceAtlas2Layout,
//   ERLayout,
// } from '@antv/layout';
// 引入react插件
import '@antv/x6-react-shape';
// 引入 ui 组件
import { Toolbar } from '@antv/x6-react-components';
import '@antv/x6-react-components/es/dropdown/style/index.css';
import '@antv/x6-react-components/es/menu/style/index.css';
import '@antv/x6-react-components/es/toolbar/style/index.css';
import { snapdom } from '@zumer/snapdom';
import ReactDOM from 'react-dom';
import { demojson } from './demo'
// 详情组件
import Rdrawer from '../../../Rdrawer';
// 绑定管理组件
import Bindmange from '../../../Bindmange';
// 导入组件
import ImportModal from '../../../ImportModal';
// 对比辨析组件
import Comparativeanalysis from '../../../Comparativeanalysis';
// 搜索的组件
import Search from '../../../Search';
// 大纲
import Outline from '../../../outline';
// 上传课件转换
import UploadModal from '../../../UploadModal';
// 上传课件转换
import Conversion from '../../../Conversion';
// 地图保存记录
import Maprecord from '../../../Maprecord';
// 知识点达成度
import Achievementdegree from '../../../Achievementdegree';
// 选择课程
import Selectcourse from '../../../components/SelectCourse';
// 课程详情
import CourseInfo from '../../../CourseInfo';
// 导入xmind
import ImpportXmind from '../../../ImpportXmind';
// 选择课程地图弹窗
import SelectMap from '../../../components/SelectMap';
// 添加地图关系
import LinkMap from '../../../components/LinkMap';
// 展示跨课关系
import RelationMap from '../../../components/RelationMap';
// 导入大纲
import ImpportOutline from '../../../ImpportOutline';
// 添加教学模块
import Teachingmodule from '@/pages/Coursemaptool/components/Teachingmodule';
// 添加其他地图
import Addothermap from '@/pages/Coursemaptool/components/Addothermap';
/** 同步大纲教学内容 */
import SynchronizedTeaching from '@/pages/Coursemaptool/SynchronizedTeaching';
// 一键绑定
import ResourceBinding from '@/pages/Coursemaptool/components/ResourceBinding';

import {
  exportexcel,
  getmapedit,
  lockmap,
  queryKnowledgeDetailById,
  querymapbyid,
  recordCurrentNode,
  redmessage,
  savemap,
  updatemapinfo,
  uploadFile,
  getModulePermission,
  getCompareAnalyze
} from '@/api/coursemap';
// import { uploadFile } from '@/api/homework';
import WholeRdrawer from '../../../WholeDrawer';

import debounce from 'lodash/debounce';
import { useBus, useListener } from 'react-bus';
const { Option } = Select;
const { TabPane } = Tabs;
const { confirm } = Modal;
const Item = Toolbar.Item; // eslint-disable-line
const Group = Toolbar.Group; // eslint-disable-line
// 当前选择新建的关系
let selectrelation: any = 0;
// const [selectrelation, setSelectrelation] = useState<number>(0); //0 是未选中 1是包含 2是等价 3后续
// 当前连线起点
let startnode: any = null;
// const [startnode, setStartnode] = useState<any>(null);
import { CheckService } from '@/api/check';
import statisticsApi from '@/api/statistics';
import useLocale from '@/hooks/useLocale';
import { getSensitiveWord } from '@/utils';
import { Utils } from '@/utils/utils';
import ProgressBox from '../ProgressBox';
import Statistics from '@/pages/Coursemaptool/components/Statistics';

import MapToolbar from '../MapToolbar';
import MapStatistics from '../MapStatistics';
import MapLockStatus from '../MapLockStatus';
import Mapoperation from '../MapStatistics';
import SearchBox from '../SearchBox';
import { getLabels } from '@/api/labelConfig';
import SelectResources from '@/pages/Coursemaptool/components/SelectResourcesFile';
import SelectRecording from '@/pages/Coursemaptool/components/SelectRecording';


interface IMap {
  mapid?: any;
  perviewtype?: number; //0是工具端  1是老师端打开 2是学生端 3是专业路径 4是图谱课 5是微专业
  courseid?: string;
  coursename?: string;
  showback?: boolean;
  centerid?: any;
  showrete?: boolean;
  ismicromajor?: any; //是否是微专业
  couresSyllabusCode?: string // 绑定的大纲查询code
  /** 是否是教师端 */
  isMain?: boolean
  //是否是校验权限  false 不校验  true 校验
  checkRole?: boolean;
  showsave?: boolean; //是否显示保存按钮
}

const Editmap: FC<IMap> = ({
                             mapid,
                             perviewtype = 0,
                             courseid,
                             coursename,
                             showback = false,
                             centerid = null,
                             showrete = false,
                             ismicromajor = false,
                             couresSyllabusCode,
                             isMain = false,
                             checkRole = true,
                             showsave = false
                           }) => {
  const { t } = useLocale();
  const dispatch = useDispatch();
  const graphRef: any = React.useRef(null);
  let graph: any = graphRef.current; //全局方法
  const thismapid = useRef<any>(null);
  let contnum: number = 1; //1到5之间  轮流取值
  let colormodel: any = useRef<any>({}); //颜色字典表

  const bus = useBus();
  const ref = useRef<any>(null); //dom节点
  const searchref = useRef<any>();
  const TeachingmoduleRef = useRef<any>(null);
  // const fontSize = 14; //统一字体大小
  // 节点字体大小
  const NODE_SIZE = 18;
  const PEM = 5;
  // 节点的高度
  const NODE_HEIGHT = 53;
  // 教学模块的高度
  const MODULE_HEIGHT = 60;

  const TIMEOUT = 10000; //30秒
  // 获取url参数
  const { query, pathname }: any = useLocation();
  const [selecttype, setSelecttype] = useState<string>('0'); //选择的筛选节点类型
  const [inputtext, setInputtext] = useState<string>(''); //搜索框内容
  const [selectnode, setSelectnode] = useState<any>([]); //选中的节点
  const [selectedge, setSelectedge] = useState<any>([]); //选中的边
  // 按钮配置
  const [kuangxuan, setKuangxuan] = useState<any>(false);
  // 0是关闭状态  1显示知识节点弹窗  2显示对比辨析弹窗  3关联节点   4知识节点对比辨析  5绑定管理  6搜素   7 excel word 导入  8 对比辨析详情页面  9课程大钢  10课程地图按课件生成  11 地图保存记录 12 知识点达成度 13选择添加的课程  14课程详情
  // 15是导入xmind  16是引用地图  17是添加地图关系  18是展示跨课关系  19是导入大纲  20是添加和修改教学模块  21 添加其他地图  22 同步大纲教学内容  23 一键绑定
  const [visible, setVisible] = useState<number>(0);
  const [drawerdata, setDrawerdata] = useState<any>(null);
  const [graphmap, setGraphmap] = useState<any>(null);
  const [querytype, setQuerytype] = useState<string>('0');
  const [x6treedata, setX6treedata] = useState<any>({
    nodes: [],
    edges: [],
  });
  // 保存原始节点信息
  // const [nodeListInfo, setNodeListInfo] = useState<any>()
  const showSearch = query.showSearch !== '0'
  const nodeListInfo = useRef<any>([])
  // 选中的节点的详细信息
  // const [checkedNodeDetail, setCheckedNodeDetail] = useState<any>()
  const [wholeDrawer, setWholeDrawer] = useState<boolean>(false);
  // 是否格式化
  const [isFormat, setIsFormat] = useState<boolean>(true);

  // 是否是有锁
  // const [lock, setLock] = useState<boolean>(false);
  const lock = useRef(false);
  // 是否正在流程中
  const process = useRef(false);

  // 是否是有锁
  const [islock, setIslock] = useState<boolean>(false);

  // 是否是流程中
  const [isprocess, setIsprocess] = useState<boolean>(false);

  // 当前编辑人
  const [editUser, setEditUser] = useState<any>(null);

  const time1: any = useRef(null);

  // 更新节点
  const [temptime, setTemptime] = useState<number>(0);

  // 统计更新
  const [statisticsTime, setStatisticsTime] = useState<number>(0);

  // 当前需要展开的节点
  const [expandnode, setExpandnode] = useState<any>(null);

  // 批量添加节点
  const [batchaddnode, setBatchaddnode] = useState<any>(false);

  // 批量添加的类型
  const [batchaddtype, setBatchaddtype] = useState<any>(1);
  // 绑定大纲后的大纲id
  const [bindCourseCode, setBindCourseCode] = useState('');
  // 批量添加的数据
  const [batchadddata, setBatchadddata] = useState<any>([]);

  const mapinfo: any = useSelector<any, any>(
    (state: { coursemap: { mapinfo: any } }) => state.coursemap.mapinfo,
  );

  // 选择课程弹窗
  const [conversionVisible, setConversionVisible] = useState(false);
  // 当前是什么布局
  const layoutname = useRef<number>(1); //1是树形  2是辐射(已废弃 且移除)
  // 是否已下架
  const [hasRole, setHasRole] = useState<boolean>(true);
  // 是否已经被删除
  const [isdelete, setIsdelete] = useState<boolean>(false);
  // 当前是从哪里跳转过来的
  const [orgfrom, setOrgFrom] = useState<number>(0);
  // 不同的入口打开这个地方的id不一样
  let newmapid: string = '';
  if (perviewtype == 0) {
    if (query.id) {
      newmapid = query.id;
    } else {
      newmapid = mapid;
    }
  } else {
    newmapid = mapid;
  }
  const [totalTime, setTotalTime] = useState<number>(0);
  const [currentTime, setCurrentTime] = useState<number>(0);
  // 已经绑定的课程id
  const [bindCourseId, setBindCourseId] = useState('');
  // 当前粘贴板原始数据
  const [clipboardData, setClipboardData] = useState<any>(null);
  //绑定的大纲后的信息
  const [bindOutlineInfo, setBindOutlineInfo] = useState<any>({})
  // 在组件中新增一个状态用于存储删除的节点ID
  const [deletedNodes, setDeletedNodes] = useState<string[]>([]);
  // 所有公共标签
  const [allLabels, setAllLabels] = React.useState<any[]>([]);

  async function getInfo() {
    const [err, res] = await Utils.tryitRequestDefaultFn(
      CheckService.infoInstalments,
    )({
      mapId: newmapid,
    });
    if (err) {
      // message.error(err.message ?? '获取信息失败');
    } else {
      setBindCourseId(res?.data?.data?.courseCode || '');
      setBindOutlineInfo(res?.data?.data)
    }
  }

  /**
   * 获取所有标签
   */
  const getalllabels = () => {
    getLabels({
      type: 0,
      page: 1,
      size:1000
    }).then((res:any) => {
      if(res.results?.length>0){
        setAllLabels(res.results);
      }
    })
  };

  useEffect(() => {
    // 图谱的编辑页面和课程里的图谱编辑
    if (!newmapid) return
    getInfo()
  }, [newmapid])
  // 当前选择的课程id
  const [selectcourseid, setSelectcourseid] = useState<string>('');
  const { userInfo } = useSelector<any, any>(state => state.global);
  const [userroles, setUserroles] = useState<any>([]); //用户角色
  const [loading, setLoading] = useState<boolean>(false); //保存loading
  // 根据次数判断是否有操作课程地图
  const clicknum = useRef<any>(0); //点击次数
  const parameterConfig = useSelector<any, any>(
    ({ global }) => global.parameterConfig,
  );

  // 图片的整体 完成率 和 掌握率
  const [noderate, setNoderate] = useState<any>(null);
  // 当前的所有模块
  const [allmodule, setAllmodule] = useState<any>([]);
  // 当前选择的模块
  const [selectmodule, setSelectmodule] = useState<any>('all');
  // 当前正在编辑的节点
  let editnodes: any = useRef<any>([]);
  // 当前图谱是否有数据 默认使用的是初始化的数据 会导致某些地方报错
  const [hasdata, sethasdata] = useState<boolean>(false);
  // 模块权限
  const modelpermission = useRef<any>(null);
  // 模块下所有知识点信息 包含  所有后修连线
  const moudelNodedata = useRef<any>({
    allhouxiuedegs:[],
    modulenodeList:{},
    notNeedToStudy:[]
  });
  // 当前节点是否要记录学习进度
  const [notrecord, setNotrecord] = useState<boolean>(false);
  // 当前要置灰的点
  const [grayedoutarr, setGrayedoutarr] = useState<any>([]);
  // 关系数组
  const [relationarr, setRelationarr] = useState<any>([
    { key: 1, name: '包含' , show: true },
    { key: 2, name: '等价' , show: true },
    { key: 3, name: '后续' , show: true },
    { key: 4, name: '关联' , show: true },
    { key: 5, name: '后修' , show: query.type == 'micromajor' || query.type == 'microMajor'},
    { key: 6, name: '依赖' , show: true },
    { key: 7, name: '递进' , show: true },
    { key: 8, name: '辩证' , show: true }
  ]);
  const [previewEntity, setPreviewEntity] = useState<any>({});
  // 标识是否是恢复的历史记录
  const [restore, setRestore] = useState<boolean>(false);
  const [knowledgeDiscriminateList, setKnowledgeDiscriminateList] = useState<any>([]);
  // 判断添加节点的入口
  const [addtype, setAddtype] = useState<number>(0); // 0 是导入   1是从当前节点添加

  // 更新权限
  useEffect(() => {
    if (userInfo?.roles && userInfo?.roles.length && showSearch) {
      setUserroles(userInfo?.roles.map((item: any) => item.roleCode));
    }
  }, [userInfo?.roles]);
  useListener('nodelearning', debounce(({ id, finishRate, masterRate }) => {
    if (showrete) {
      refresh();
    }
  }, 1000))

  // 刷新页面统计
  const refresh = () => {
    getTeacherOrStudentRate(isMain ? 0 : 1)
  }

  //总线通信
  useListener('saveMap', (val: any) => {
    saveContent(val);
  });

  //监听预览保存
  useListener('saveMapbyperview', () => {
    //第一个参数 判断要不要弹窗提醒  第二个参数判断是不是预览保存
    saveContent(false, true);
  });

  // 监听保存封面
  useListener('saveCover', () => {
    console.log('saveCover');
    saveCover();
  });

  // 监听数据更新
  useListener('updatanode', ({id,data}:any) => {
    updatanode(id,data)
  });

  //总线通信
  useListener('refreshMap', (val: any) => {
    initdata();
  });

  useEffect(() => {
    if (perviewtype && graph) {
      // 判断当前地图是否有数据 如果没有数据需要把右侧详情弹窗关闭一下 因为每次重新渲染的预置节点 nodeid 都不一样
      if(hasdata){
        initdata();
      }else{
        initdata();
        setVisible(0);
      }
      if (perviewtype == 0 || perviewtype == 1 || perviewtype == 3) {
        // 获取当前地图的状态 是否有人正在编辑
        getflushmap(newmapid);
      }
    }
  }, [perviewtype]);

  useEffect(() => {
    if (mapinfo?.type == 8) {
      notification.error({
        message: t(`按课件生成地图失败，请重新发起！`),
        placement: 'top',

        onClose: () => {
          redmessage(mapinfo.id).then(res => { });
        },
      });
    }
  }, [mapinfo]);

  useEffect(() => {
    if (query.nodeId) {
      setSelectmodule(query.nodeId);
    }
    // 学生端 切换浏览器不保存
    if (perviewtype != 2 && !lock.current && !process.current) {
      // 直接关闭浏览器提示保存
      window.addEventListener('beforeunload', beforeunload);
    }

    return () => {
      // 有锁状态 和 转换状态 切换不自动保存
      if (perviewtype != 2 && !lock.current && !process.current) {
        // 切换路由自动保存
        window.removeEventListener('beforeunload', beforeunload);
        if (clicknum.current > 0) {
          console.log('切换路由自动保存', mapid);
          saveContent();
        }
      }
    };
  }, []);

  // 初始化数据
  useEffect(() => {
    thismapid.current = newmapid;
    getalllabels();
    initdata();
    if (perviewtype == 0 || perviewtype == 1 || perviewtype == 3) {
      // 获取当前地图的状态 是否有人正在编辑
      getflushmap(newmapid);
    }

    return () => {
      if (time1?.current) {
        clearInterval(time1.current);
      }
    };
  }, [mapid]);

  // 监听批量添加弹窗
  useEffect(() => {
    if (batchaddnode) {
      navigator.clipboard?.readText().then(text => {
        // 去除开头和结尾空格
        text = text.trim();
        // 判断是否包含换行符
        if (text.indexOf('\r\n') != -1) {
          // 弹窗提示
          Modal.confirm({
            title: t('是否使用粘贴板数据自动填入？'),
            centered: true,
            onOk: () => {
              // 确认添加
              formatPaste(text);
              setClipboardData(text);
            },
            onCancel: () => {
              setBatchadddata([]);
            },
          });
        }
      });
    }
  }, [batchaddnode]);


  // 获取知识点掌握率
  const getTeacherOrStudentRate = (type: number) => {
    statisticsApi.getTeacherOrStudentAllRate({
      courseId: courseid,
      mapId: newmapid,
      /** 0 教师端, 1 学生端 */
      type: type
    }).then((res: any) => {
      if (res.data.status == 200) {
        setNoderate({
          achievingRateTotal: res.data.data.nodeAttainment || 0,
          finishRateTotal: res.data.data.totalFinishRate || 0,
          masterRateTotal: res.data.data.totalMasterRate || 0,
        });
      }
    });
  };

  // 格式化粘贴板内容
  const formatPaste = (text: string, isfilter: boolean = false) => {
    // 获取系统粘贴板的数据
    if (text) {
      // 换行符号
      text = text.replace(/[\r\n]/g, '/');
      // 删除最后一个斜杠
      // text = text.substring(0, text.length - 2);
      let arr = text.split('//'); //字符串转数组
      if (isfilter) {
        //匹配  第二章、  第二节.  这种文本
        const regex1 = /第[\u4e00-\u9fa5]+(章|节)[、.]/;
        // 匹配  第1章、  第2节.  这种文本
        const regex2 = /第\d+(章|节)[、.]/;
        // 匹配  1.2这种文本
        const regex3 = /\b\d+\.\d+\b/;
        // 匹配  一、1.这种文本
        const regex4 = /([一二三四五六七八九十]+、|[1-9]+[、.])/;  //
        let newarr: any = arr.map((item: string) => {
          let newstr = item.replace(regex1, '');
          newstr = newstr.replace(regex2, '');
          newstr = newstr.replace(regex3, '');
          newstr = newstr.replace(regex4, '');
          newstr = newstr.trim();
          return newstr
        });
        // 数组去重
        newarr = newarr.filter((item: any, index: number) => newarr.indexOf(item) === index);
        setBatchadddata(newarr);
        console.log(newarr);
      } else {
        setBatchadddata(arr);
        console.log(arr);
      }
    }
  };

  // 设置当前节点颜色字典
  const setcolormodel = (towedges: any) => {
    colormodel.current = {};
    towedges.forEach((item: any) => {
      // 只有5个图标来随机 1到5之间
      if (contnum == 6) {
        contnum = 1;
      }
      // 这里为了方便子集 取用父级的颜色 存了一个字典表
      colormodel.current[item.target] = contnum;
      contnum++;
    });
  };

  // 获取图谱当前状态
  const getflushmap = (id: string) => {
    getmapedit({
      mapId: id,
    }).then(res => {
      // 有人正在编辑
      if (res.data.editing) {
        lock.current = true;
        setIslock(true);
        setEditUser(res.data.user);
        // 每30 再去查询一下
        if (time1.current) {
          clearInterval(time1.current);
        }
        time1.current = setInterval(() => {
          getflushmap(newmapid);
        }, TIMEOUT);
        setVisible(0);
      } else if (res.data.process) {
        // 正在转换流程中
        process.current = true;
        setIsprocess(true);
        // 每30 再去查询一下
        if (time1.current) {
          clearInterval(time1.current);
        }
        time1.current = setInterval(() => {
          getflushmap(newmapid);
        }, TIMEOUT);
        setVisible(0);
      } else {
        // 其他人编辑完毕  或者流程解锁了
        if (lock.current || process.current) {
          // 查询最新的数据
          initdata();
          // 然后把锁解开
          lock.current = false;
          process.current = false;
          setIslock(false);
          setIsprocess(false);
          // 每30 再去查询一下
          if (time1.current) {
            clearInterval(time1.current);
          }
          time1.current = setInterval(() => {
            getflushmap(newmapid);
          }, TIMEOUT);
        } else {
          lock.current = false;
          process.current = false;
          setIslock(false);
          setIsprocess(false);
          // 如果是没有人在编辑 就去获取锁
          lockmapbyid();
          if (time1.current) {
            clearInterval(time1.current);
          }
          // 加锁
          time1.current = setInterval(() => {
            lockmapbyid();
          }, TIMEOUT);
        }
      }
    });
  };

  // 给地图加锁
  const lockmapbyid = () => {
    lockmap({ mapId: newmapid });
  };

  const getBaseInfoMap = () => {
    // 更新dva的store
    baseInfo
      .getCourseDetails(query?.id, query?.sm ?? 1)
      .then(res => {
        if (res && res.message === 'OK') {
          // console.info('获取详情信息', res.data)
          dispatch({
            type: 'moocCourse/updateState',
            payload: {
              courseDetail: res.data,
            },
          });
        }
      });
  }
  //刷新地图数据
  const handleRefreshMapInfo = () => {
    dispatch({
      type: 'coursemap/fetchMapInfo',
      payload: {
        params: {
          mapId: newmapid,
        },
      },
    });
  };

  // 获取图谱数据
  const initdata = async () => {
    // 查询当前人有那些模块的权限
    if(query.type == 'microMajor'){
      const resdata = await getModulePermission({
        courseId:query.id
      });
      modelpermission.current = resdata.data;
    }

    if(newmapid === 'custommapid'){
      const res = demojson;
      if(res.status == 200){
        // 已经下架
        if(res.data.hasRole){
          setHasRole(res.data.hasRole);
        }
        iflayout(res);
      }else if(res.status == 400){
        // 已经被删除
        setIsdelete(true);
        message.error(res.message);
      }else{
        message.error(res.message);
      }
    }else{
      querymapbyid({
        mapId: newmapid,
        courseId: courseid,
      }).then((res: any) => {
        if (res.status == 200) {
          let { nodesVos, relationVos,knowledgeDiscriminateList } = res.data;
          // 获取当前所有的对比辨析
          setKnowledgeDiscriminateList(knowledgeDiscriminateList || []);
          // 获取当前的统计数据
          if (showrete) {
            getTeacherOrStudentRate(isMain ? 0 : 1)
          }
          if(relationVos.length){
            sethasdata(true);
            // 获取第二层的节点
            let towedges = relationVos.filter((item: any) => {
              let data = JSON.parse(item.data);
              data = {
                ...defaultEdgeData,
                ...data,
              }
              if (item.source == nodesVos[0].nodeId && data.isnew == false) {
                return true;
              } else {
                return false;
              }
            });
            // // 给二级节点设置颜色
            setcolormodel(towedges);
          }else{
            sethasdata(false);
          }

          // 已经下架
          if (res.data.hasRole) {
            setHasRole(res.data.hasRole);
          }
          iflayout(res);
        } else if (res.status == 400) {
          // 已经被删除
          setIsdelete(true);
          message.error(res.message);
        } else {
          message.error(res.message);
        }
      });
    }
  };
  //获取知谱空间的数据
  const getMapDataByGroupId = (groupId: string) => {
    queryKnowledgeDetailById({
      graph_id: groupId,
      map_id: thismapid.current,
    }).then((res: any) => {
      if (!!res) {
        const cloneData = {
          data: {
            nodesVos: res?.extend_message?.nodesVos,
            relationVos: res?.extend_message?.relationVos,
          },
        };
        iflayout(cloneData);
      }
    });
  };

  // 判断当前的布局
  const iflayout = (res: any) => {
    layoutname.current = 1;
    let newdata = formMapdata(res);
    setX6treedata(newdata);
    nodeListInfo.current = res?.data?.nodesVos || []
  };

  // 转换数据
  const formMapdata = (res: any) => {
    let newdata: any = {
      nodes: [],
      edges: [],
    };
    let allllmodulearr: any = [];
    // 1是树形布局的数据格式化
    let nodesVos: any = [];
    let relationVos: any = [];
    if (res.data.nodesVos.length) {
      nodesVos = res.data.nodesVos;
      relationVos = res.data.relationVos;
    } else {
      if (mapinfo.type == 7) {
        let defaultNodearr = defaultMarjorNode(mapinfo.mapName);
        nodesVos = defaultNodearr.nodesVos;
        relationVos = defaultNodearr.relationVos;
      } else {
        let defaultNodearr = query.type == 'micromajor' ? defaultMicromajorNode() : defaultNode();
        nodesVos = defaultNodearr.nodesVos;
        relationVos = defaultNodearr.relationVos;
        // 给二级节点设置颜色
        setcolormodel(relationVos);
      }
    }
    // 所有的知识点
    let knowledgeList: any = []
    nodesVos.forEach((element: any) => {
      let data = JSON.parse(element.valueMap);
      // 如果是根节点
      if (data.data.isroot) {
        // 如果有坐标信息就不格式化
        if (data.data.x && data.data.y) {
          setIsFormat(false);
        } else {
          // 如果没有就格式化
          setIsFormat(true);
        }
      }
      if(data.data.type == 2){
        knowledgeList.push(element);
      }
      let nodeheight = NODE_HEIGHT;
      if (data.data.type == 5) {
        nodeheight  =  MODULE_HEIGHT
        allllmodulearr.push({ label: data.data.label, value: element.nodeId });
        // 判断管理员权限  和 单独模块权限的处理
        // if(modelpermission.data.isManager){
        //   allllmodulearr.push({ label: data.data.label, value: element.nodeId });
        // }else{
        //   allllmodulearr.push({ label: data.data.label, value: element.nodeId });
        // }
      }
      newdata.nodes.push({
        id: element.nodeId, // String，可选，节点的唯一标识
        // width: data.data.width,   // Number，可选，节点大小的 width 值
        width: getNodewidth(
          element.nodeId,
          data.data,
          relationVos,
          NODE_SIZE
        ),
        height: nodeheight, // Number，可选，节点大小的 height 值
        label: data.data.label,
        data: {
          ...defaultNodeData,
          ...data.data,
          serialNumber: element.serialNumber || null,
        }, //注册的组件 通过 data 获取参数
        shape: 'react-shape',
        component: 'react-compont',
        magnet: true,
        ports: {
          groups: ports.groups,
        },
        position: {
          x: data.data.x,
          y: data.data.y,
        },
        visible: data.data.visible == undefined ? true : data.data.visible,
      });
    });
    // 添加边
    relationVos.forEach((element: any) => {
      let data = JSON.parse(element.data);
      if (!data.isnew) {
        newdata.edges.push({
          source: {
            cell: element.source,
            anchor: 'right', //midSide
          }, // String，必须，起始节点 id
          target: {
            cell: element.target,
            anchor: 'left',
          }, // String，必须，目标节点 id
          connector: {
            name: 'smooth',
            args: {
              direction: 'H',
            },
          },
          visible: data.visible == undefined ? true : data.visible,
          data: {
            type: element.type, //1包含 2等价 3后续 4关联
            isnew: false, //是否是用户新建的边
          },
          attrs: {
            line: {
              stroke: '#a3b1bf', // 指定 path 元素的填充色
              targetMarker: null,
            },
          },
        });
      } else {
        let defaultLabel:any = {
          markup: Markup.getForeignObjectMarkup(),
          attrs: {
            fo: {
              width: 40,
              height: 30,
              x: -20,
              y: -15,
            },
          },
        };

        let label = t('包含');
        if (element.type == 2) {
          label = t('等价');
        } else if (element.type == 3) {
          label = t('后续');
        } else if (element.type == 4) {
          label = t('关联');
        }else if (element.type == 5) {
          label = t('后修');
          defaultLabel = null;
        }else if (element.type == 6) {
          label = t('依赖');
        }else if (element.type == 7) {
          label = t('递进');
        }else if (element.type == 8) {
          label = t('辩证');
        }
        newdata.edges.push({
          source: {
            cell: element.source,
            anchor: 'right', //midSide
          }, // String，必须，起始节点 id
          target: {
            cell: element.target,
            anchor: 'right', //midSide
          }, // String，必须，目标节点 id
          connector: { name: 'rounded' },
          router: {
            name: 'oneSide',
            args: { side: 'right' },
          },
          defaultLabel: defaultLabel,
          label: {
            text: label,
            position: 0.6,
          },
          visible: data.visible,
          data: {
            type: element.type, //1包含 2等价 3后续 4关联
            isnew: true, //是否是用户新建的边
          },
          attrs: attrs,
        });
      }
    });
    if(query.type == 'microMajor'){
      moudelNodedata.current = getKnowledgeBymouldes(allllmodulearr, newdata);
      console.log('moudelNodedata',moudelNodedata.current);
      // 计算出来要置灰的点
      const grayedoutarr = computingKnowledge(moudelNodedata.current,knowledgeList);
      setGrayedoutarr(grayedoutarr);
    }else{
      // 教学模块详情要查看先修和后修的节点
      const alledegs = newdata.edges.filter((edg:any)=>edg.data.type == 5);
      moudelNodedata.current = {
        allhouxiuedegs:alledegs,
        modulenodeList:{},
        notNeedToStudy:[]
      }
    }
    setAllmodule(allllmodulearr);
    return newdata;
  };

  // 根据 节点是不是  0 是查询全部  1是查询重难点  2是查询有详解的节点
  const typeonselect = (LabeledValue: any) => {
    setQuerytype(LabeledValue);
    setGraphmap(graph);
    setVisible(6);
    searchref.current.querynode();
  };

  // 删除选择的节点
  const deletenode = (node?: any) => {
    // 开始事务 批量删除  用来撤销
    graph.startBatch('Batchdeletion');
    // 先获取到所有的父节点
    let pre: any = [];
    if (node) {
      // 获取是不是根节点
      pre = graph.getPredecessors(node, { distance: 1 });
      if (node.store.data.data.isroot) {
        message.info(t('根节点不能删除！'));
        return;
      }

      //获取当前节点的所有子节点
      let next = graph.getSuccessors(node);
      if (next.length) {
        let nextarr = next.map((item: any) => item.id);
        // 这里是把 用户自己连线 的节点 不受展开收起按钮控制
        graph.getEdges().forEach((item: any) => {
          if (item.store.data.data.isnew) {
            // 删除节点有2种情况要区分  1.同树下相连多个子节点  2 不同树下相连多个子节点
            if (
              nextarr.includes(item.store.data.source.cell) ||
              nextarr.includes(item.store.data.target.cell)
            ) {
              // 1.同树下相连多个子节点 这种情况下边肯定是先删除的
              graph.removeCell(item);
            } else {
              // 不进行处理
            }
          }
        });
        next = graph.getSuccessors(node);
      }

      // 点选的批量删除
      graph.removeCells([node, ...next]);
      deletecompare([node, ...next].map((item2:any)=>item2.id));
      batchupdatecompare([node, ...next]);
      // 更新父节点的状态
      if (pre.length) {
        pre.forEach((args: any) => {
          let data = args.getData();
          args.updateData({
            ...data,
            temp: new Date().getTime(),
          });
        });
      }
      // 结束事务
      graph.stopBatch('Batchdeletion');
      setVisible(0);
    } else {
      // 框选的删除
      selectnode.forEach((args: any) => {
        deletenode(args);
      });
      // 结束事务
      graph.stopBatch('Batchdeletion');
    }
    // 重置选择的节点
    setSelectnode([]);
  };

  // 删除对比辨析
  const deletecompare = (nodeid:string[]) => {
    // 使用函数式更新确保获取最新状态
    setKnowledgeDiscriminateList((prevList:any) => {
      return prevList.filter((item:any) =>
        !nodeid.includes(item.firstKnowledge) &&
        !nodeid.includes(item.secondKnowledge) &&
        !nodeid.includes(item.thirdKnowledge)
      );
    });
  }

  // 批量更新对比辨析
  const batchupdatecompare = (nodeid:string[]) => {
    if(nodeid){
      const newnodeid = nodeid.map((item:any)=>item.id);
      getCompareAnalyze(newmapid,newnodeid).then((res:any)=>{
        if(res.data.data.length){
          res.data.data.forEach((item:any)=>{
            updatanode(item.nodeId,{
              isDiscriminate:item.isDiscriminate
            });
          })
        }
      })
    }
  }

  // 添加节点  isbatch 是否是批量  batchadddata 批量添加的名称数据
  const addChild = (
    source: any,
    type: number,
    nodelabel: string = '',
    newdata: any = {}
  ) => {
    // 防止数据回填问题 新建节点的时候自动关闭详情页
    setVisible(0);
    // 动态获取高度
    let nodeheight:number = NODE_HEIGHT;
    graph.startBatch('Batchaddnode');
    // 获取有多少个子节点
    const succ = graph.getSuccessors(source, { distance: 1 });
    // 判断当前节点类型
    let label = '';
    if (type == 1) {
      label = `${t('分类节点')}${succ.length + 1}`;
    } else if (type == 2) {
      label = `${t('知识节点')}${succ.length + 1}`;
    } else if (type == 3) {
      label = `${t('课程节点')}${succ.length + 1}`;
    } else if (type == 5) {
      label = `${t('教学模块')}${succ.length + 1}`;
      nodeheight = MODULE_HEIGHT;
    }

    // 如果有名称传入 就用传入的名称
    if (nodelabel != '') {
      label = nodelabel;
    }
    let targetid = createguid();
    if(source.data.isroot == false || source.data.isroot == undefined){
      colormodel.current[targetid] = colormodel.current[source.id];
    }else{
      // 随机 1 到 5
      colormodel.current[targetid] = Math.floor(Math.random() * 5 + 1);
    }
    const newobj = {
      type: type, // 1子节点 2知识节点 3课程节点
      label: label,
      ...defaultNodeData,
      isCollapsed: false, //是否展开
      isedit: type == 5 ? false : true, //是否编辑
      createuser:userInfo.nickName,
      ...newdata, // 传入的数据
    }
    // 教学模块的唯一id
    if(type == 5){
      newobj.nochangedId = createguid();
    }
    // 调用工具api添加节点
    let target = graph.addNode(
      graph.createNode({
        id: targetid, // String，可选，节点的唯一标识
        width:  getNodewidth(targetid,newobj,[],NODE_SIZE), //150, // Number，可选，节点大小的 width 值
        height: nodeheight, // Number，可选，节点大小的 height 值
        label: label,
        // position: {
        //   x: source.store.data.position.x + 280,
        //   y: source.store.data.position.y + (succ.length * 80)  //用子节点 x 每个子节点高度 得到 y轴距离
        // },
        data: newobj, //注册的组件 通过 data 获取参数
        shape: 'react-shape',
        component: 'react-compont',
        visible: true, //是否显示
        magnet: true, //是否可以从节点连线
        ports: {
          groups: ports.groups,
        },
      }),
    );
    if(type != 5){
      restedit();
      // 添加到当前编辑节点中
      editnodes.current.push(target);
    }
    // 添加边
    graph.addEdge(
      graph.createEdge({
        source: {
          cell: source.id,
          anchor: 'right',
        }, // String，必须，起始节点 id
        target: {
          cell: target.id,
          anchor: 'left',
        }, // String，必须，目标节点 id
        connector: {
          name: 'smooth',
          args: {
            direction: 'H',
          },
        },
        visible: true, //是否显示
        data: {
          type: 1, //1包含 2等价 3后续 4关联
          isnew: false, //是否是用户新建的边
        },
        attrs: {
          line: {
            stroke: '#a3b1bf', // 指定 path 元素的填充色
            targetMarker: null,
          },
        },
      }),
    );
    // 重置选择的节点
    const data: any = source.getData();
    // 修改数据 让节点重新渲染
    source.updateData({
      ...data,
      isCollapsed: true,
    });
    // updatasize(source, data, data.label);
    // 添加节点和边 自动布局
    maplayout();
    graph.select(target);
    setSelectnode([target]);
    graph.stopBatch('Batchaddnode');
  };

  //修改边的类型
  const updataedge = (edge: any, type: number) => {
    let data = edge.getData();
    edge.updateData({
      ...data,
      type: type,
    });

    let label = t('包含');
    if (type == 2) {
      label = t('等价');
    } else if (type == 3) {
      label = t('后续');
    }else if (type == 4) {
      label = t('关联');
    }else if (type == 5) {
      label = t('后修');
    }else if(type == 6) {
      label = t('依赖');
    }else if (type == 7) {
      label = t('递进');
    }else if (type == 8) {
      label = t('辩证');
    }

    edge.setLabels({
      position: 0.5,
      text: label,
    });
  };

  // 反向边
  const reverseedge = (edge: any) => {
    let source = edge.getSource();
    let target = edge.getTarget();
    edge.setSource(target);
    edge.setTarget(source);
  };

  // 边
  const edgeMenu = (edge: any) => (
    <Menu>
      <Menu.Item onClick={() => updataedge(edge, 1)}>{t('包含')}</Menu.Item>
      <Menu.Item onClick={() => updataedge(edge, 2)}>{t('等价')}</Menu.Item>
      <Menu.Item onClick={() => updataedge(edge, 3)}>{t('后续')}</Menu.Item>
      <Menu.Item onClick={() => updataedge(edge, 4)}>{t('关联')}</Menu.Item>
      {
        query.type == 'micromajor' || query.type == 'microMajor' && <Menu.Item onClick={() => updataedge(edge, 5)}>{t('后修')}</Menu.Item>
      }
      <Menu.Item onClick={() => updataedge(edge, 6)}>{t('依赖')}</Menu.Item>
      <Menu.Item onClick={() => updataedge(edge, 7)}>{t('递进')}</Menu.Item>
      <Menu.Item onClick={() => updataedge(edge, 8)}>{t('辩证')}</Menu.Item>
    </Menu>
  );

  // 放大缩小画布   code 有三个参数  init 是初始化   enlarge 是放大   narrow是缩小
  const zoomdom = (code: string) => {
    if (code == 'enlarge') {
      const zoom = parseFloat(graph.zoom().toFixed(2));
      graph.zoomTo(zoom + 0.1);
    } else if (code == 'narrow') {
      const zoom = parseFloat(graph.zoom().toFixed(2));
      graph.zoomTo(zoom - 0.1);
    }
  };

  // 展开所有
  const expandall = (flage: boolean) => {
    // 获取所有节点
    const nodes = graph.getNodes();
    let rootnodes = graph.getRootNodes();
    let successors = [];
    if (rootnodes.length > 0) {
      successors = graph.getNeighbors(rootnodes[0], {incoming:false, outgoing: true }).map((item: any) => item.id);
    } else {
      successors = graph.getNeighbors(nodes[0], {incoming:false,outgoing: true }).map((item: any) => item.id);
    }

    nodes.forEach((node: any) => {
      const data = node.getData();
      const isroot = graph.isRootNode(node);
      // 如果不是根节点和二级菜单
      if (!isroot) {
        // 设置展开状态
        node.updateData({
          ...data,
          isCollapsed: flage,
        });
      }
      // 显示的节点为根节点或者二级节点  或者 教学模块
      if (isroot || successors.includes(node.id)) {
        node.setVisible(true);
      } else {
        node.setVisible(flage);
      }
      // if (!data.isroot && !successors.includes(node.id)) {
      //   node.setVisible(flage);
      // }
    });
    maplayout(true);
    // 如果课程大纲打开了  需要更新数据重新渲染
    if (visible == 9) {
      setExpandnode(null);
      setGraphmap(graph);
      setTemptime(new Date().getTime());
    }
  };

  // 点击框选的代码
  const multipleSelection = (flag: boolean) => {
    setKuangxuan(!flag);
    // 启用关闭选择
    graph.toggleSelection(!flag);
    // 切换选中节点/边是否可以被移动
    graph.toggleSelectionMovable(!flag);
    // 启用关闭 框选
    graph.toggleRubberband(!flag);
    // 启用关闭 点选
    graph.toggleMultipleSelection(!flag);
    // 启用关闭 画布平移
    graph.togglePanning(flag);
  };

  const canChangeToClassNode = (graph: any, node: any) => {
    // 获取父节点
    const parent = graph.getPredecessors(node, { distance: 1 })[0];
    if (!parent) return true; // 如果没有父节点，允许修改
    const parentData = parent.getData();
    // 只有父节点是分类节点时才允许修改为分类节点
    return Number(parentData.type) === 1;
  };

  // 修改节点类型
  const updatType = (node: any, type: any) => {
    if (type === 1 && !canChangeToClassNode(graph, node)) {
      message.error(t('不能将分类节点作为知识点的下级节点'));
      return;
    }
    const data: any = node.getData();
    const newdata = {
      ...data,
      type: type,
    }
    if(type== 2){
      newdata.createuser = userInfo.nickName
    }
    node.updateData(newdata);
    updatasize(node, newdata, data.label);
    // 获取所有子节点
    let children = graph.getSuccessors(node, { distance: 1 });
    if (type == 2 && children.length) {
      children.forEach((item: any) => {
        updatType(item, 2);
      });
    }
  };

  // 修改重难点
  const uodatayinandian = (node?: any, flag?: boolean) => {
    // 单个重难点设置
    if (node) {
      const data: any = node.getData();
      node.setData({
        ...data,
        isyinandian: !data.isyinandian,
      });
      updatasize(
        node,
        {
          ...data,
          isyinandian: !data.isyinandian,
        },
        data.label,
      );
    } else {
      // 批量重难点设置
      selectnode.forEach((element: any) => {
        const data: any = element.getData();
        if (data.type == 2) {
          element.updateData({
            ...data,
            isyinandian: flag,
          });
          updatasize(
            element,
            {
              ...data,
              isyinandian: !data.isyinandian,
            },
            data.label,
          );
        }
      });
    }
  };

  // 修改根据key 修改节点
  const uodatabykey = (node?: any, flag?: boolean, objkey: string = '') => {
    // 单个重难点设置
    if (node) {
      const data: any = node.getData();
      let obj = {
        ...data,
      };
      // 根据key 修改value
      obj[objkey] = !data[objkey];
      node.setData(obj);
      updatasize(node, obj, data.label);
    } else {
      // 批量重难点设置
      selectnode.forEach((element: any) => {
        const data: any = element.getData();
        if (data.type == 2) {
          let obj = { ...data };
          obj[objkey] = flag;
          element.updateData(obj);
          updatasize(element, obj, data.label);
        }
      });
    }
  };

  const updatalabel = (item: any,flage: boolean,node:any=null) => {
    if(node){
      const data: any = node.getData();
        if (data.type == 2) {
          if(flage){
            // 判断是否已经存在
            if (data.labelsconfig.find((item2: any) => item2.id == item.id)) {
              message.error(t('标签已经存在'));
              return;
            }
            // 更新标签
            let newlabelsconfig = [...data.labelsconfig, item];
            node.updateData({
              ...data,
              labelsconfig: newlabelsconfig
            });
          }else{
            // 删除标签
            let newlabelsconfig = data.labelsconfig.filter((item2: any) => item2.id != item.id);
            node.updateData({
             ...data,
              labelsconfig: newlabelsconfig
            });
          }
        }
    }else if (selectnode.length) {
      selectnode.forEach((element: any) => {
        const data: any = element.getData();
        if (data.type == 2) {
          if(flage){
            // 判断是否已经存在
            if (data.labelsconfig.find((item2: any) => item2.id == item.id)) {
              message.error(t('标签已经存在'));
              return;
            }
            // 更新标签
            let newlabelsconfig = [...data.labelsconfig, item];
            element.updateData({
              ...data,
              labelsconfig: newlabelsconfig
            });
          }else{
            // 删除标签
            let newlabelsconfig = data.labelsconfig.filter((item2: any) => item2.id != item.id);
            element.updateData({
             ...data,
              labelsconfig: newlabelsconfig
            });
          }
        }
      });
    } else {
      message.error(t('请选择一个节点'));
    }
  };

  // 修改节点名称
  const updatlabel = (node: any, data: any, e: any) => {
    node.updateData({
      ...data,
      label: e.target.value,
      isedit: true,
    });
    updatasize(node, data, e.target.value);
    if (visible != 0) {
      setGraphmap(() => {
        return graph;
      });
    }
  };

  // 修改节点大小
  const updatasize = (
    node: any,
    data: any,
    value: string,
    ischange: boolean = false,
  ) => {
    let childlength = graph.getSuccessors(node, { distance: 1 });
    let divwidth = getTextSize(data.label, NODE_SIZE);

    // if (ischange) {
    //   divwidth = divwidth + 40;
    // }

    if (data.type == 1 || data.type == 5) {
      if (childlength.length) {
        divwidth = divwidth + 105;
      } else {
        divwidth = divwidth + 85;
      }
      if (divwidth < 150) {
        divwidth = 150
      }

    } else if (data.type == 2) {
      // 如果是有子节点
      if (childlength.length) {
        divwidth = divwidth + 85;
      } else {
        divwidth = divwidth + 55;
      }
      // 设置最小宽度 防止没有输入的时候有问题
      if (divwidth < 150) {
        divwidth = 150
      }
    }
    let nodeheight = NODE_HEIGHT;
    if(data.type == 5){
      if (childlength.length) {
        divwidth = divwidth + 120;
      }else{
        divwidth = divwidth + 70;
      }
      nodeheight = MODULE_HEIGHT;
    }

    node.resize(divwidth, nodeheight);
  };

  const restedit = () =>{
    if (editnodes.current.length > 0) {
      editnodes.current.forEach((element: any) => {
        let data = element.getData();
        // 先把宽度更新了 不然会出现bug
        updatasize(element, data, data.label);
        element.updateData({
          ...data,
          isedit: false
        })
      });
      editnodes.current = [];
    }
  }

  // 注册自定义组件
  const registerstyle = () => {
    try {
      // 注册返回 React 组件的函数
      Graph.registerReactComponent('react-compont', node => {
        const data: any = node.getData();
        // 判断当前节点是否选中了
        let isselect = graph.isSelected(node);
        // 获取当前下面有没有子节点
        let childlength = graph.getSuccessors(node, { distance: 1 });
        // 获取当前节点的所有边  过滤掉用户自己连的边  用户自己链接的边  不出现展开收起按钮
        let arrEdges = graph.getEdges().filter((item: any) => {
          if (
            node.id == item.store.data.source.cell &&
            !item.store.data.data.isnew
          ) {
            return true;
          }
        });
        // 当前随机出来的图标
        let iconnum = 1;
        if (node.data.isroot == false || node.data.isroot == undefined) {
          if (colormodel.current[node.id]) {
            iconnum = colormodel.current[node.id];
            // // 获取所有的子节点 给所有子节点加上标识
            let arredges = x6treedata.edges.filter((item: any) => {
              if (item.source.cell == node.id && item.data.isnew == false) {
                return true;
              }
            });
            arredges.forEach((item: any) => {
              colormodel.current[item.target.cell] = iconnum;
            });
          }
        } else {
          iconnum = 0;
        }
        // 判断节点类型 不同的类型是不同的样式
        if (data.type == 1) {
          // 节点单击右键的菜单  1是分类节点  2 是知识节点
          const contextMenu = (node: any): any => (
            <Menu>
              <Menu.SubMenu title={t('添加节点')}>
                <Menu.Item onClick={() => addChild(node, 1)}>
                  {t('分类节点')}
                </Menu.Item>
                <Menu.Item onClick={() => addChild(node, 2)}>
                  {t('知识节点')}
                </Menu.Item>
                {data.isroot == true && (query.type == 'micromajor' || query.type == 'microMajor') && (
                  <Menu.Item onClick={() => {
                    setVisible(20);
                    if (TeachingmoduleRef.current) {
                      TeachingmoduleRef.current.resetForm();
                    }
                  }}>
                    {t('教学模块')}
                  </Menu.Item>
                )}
                <Menu.Item
                  onClick={() => {
                    setSelectnode(node);
                    setVisible(21);
                  }}
                >
                  {t('从其他地图添加')}
                </Menu.Item>
                {mapinfo.type == 7 && data.isroot && (
                  <Menu.Item
                    onClick={() => {
                      setSelectnode(node);
                      setVisible(13);
                    }}
                  >
                    {t('课程')}
                  </Menu.Item>
                )}
                <Menu.Item
                  onClick={() => {
                    setBatchaddtype(1);
                    setBatchaddnode(true);
                  }}
                >
                  {t('批量添加')}
                </Menu.Item>
              </Menu.SubMenu>
              <Menu.Item
                onClick={() => {
                  setExpandnode(node.id);
                  setVisible(9);
                  setTemptime(new Date().getTime());
                }}
              >
                {t('修改顺序')}
              </Menu.Item>
              {!data.isroot && (
                <Menu.SubMenu title={t('修改类型')}>
                  <Menu.Item onClick={() => updatType(node, 1)}>
                    {t('分类节点')}
                  </Menu.Item>
                  <Menu.Item onClick={() => updatType(node, 2)}>
                    {t('知识节点')}
                  </Menu.Item>
                  {query.type == 'micromajor' || query.type == 'microMajor' && <Menu.Item onClick={() => updatType(node, 5)}>
                    {t('教学模块')}
                  </Menu.Item>}
                </Menu.SubMenu>
              )}
              {/* 删除会抛出一个异常 不知道为啥 但是不影响业务 */}
              <Menu.Item
                onClick={() => deletenode(node)}
                disabled={data.isroot}
              >
                {t('删除节点')}
              </Menu.Item>
            </Menu>
          );

          return (
            <Dropdown
              overlay={() => contextMenu(node)}
              trigger={['contextMenu']}
              disabled={perviewtype == 2}
            >
              <div
                style={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  flexWrap:'nowrap',
                  alignItems: 'center',
                  justifyContent: 'center',
                  background: `linear-gradient( 90deg, rgba(${mapv4colors[iconnum].start},1) 0%, rgba(${mapv4colors[iconnum].end},1) 100%)`,
                  borderRadius: '27px',
                  border: isselect ? '3px solid var(--primary-color)' : 'none',
                  filter: data.showgrey ? 'grayscale(100%)':''
                }}
                onClick={() => {
                  if (perviewtype == 2 && arrEdges.length) {
                    graph.select(node);
                    if (data.isCollapsed) {
                      x6collapsed(node, childlength, false);
                    } else {
                      x6collapsed(node, childlength, true);
                    }
                  } else {
                    graph.select(node);
                  }
                }}
                onMouseDown={(e: any) => {
                  if (e.button == 2) {
                    graph.select(node);
                  }
                }}
              >
                {data.isedit ? (
                  <Input
                    maxLength={60}
                    showCount
                    style={{
                      flex: 1,
                      height: '100%',
                      fontSize: '14px',
                      padding: '0 !important',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      textAlign: 'center',
                    }}
                    onPressEnter={(e: any) =>{
                      updatlabel(node, data, e);
                      restnodeedit();
                    }}
                    onChange={(e: any) => {
                      // updatasize(node, data, e.target.value, true);
                      updatlabel(node, data, e)
                    }}
                    defaultValue={data.label}
                    bordered={false}
                    autoFocus
                    // onBlurCapture={(e: any) => updatlabel(node, data, e)}
                    // onFocus={(e: any) => {
                    //   e.target.select();
                    // }}
                  />
                ) : (
                  <span
                    style={{
                      minWidth: '50px',
                      fontSize: '18px',
                      // flex: 1,
                      color: '#fff',
                      height: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                    onDoubleClick={() => {
                      if (perviewtype != 2) {
                        restedit();
                        editnodes.current.push(node);
                        node.updateData({
                          ...data,
                          isedit: true,
                        });
                        updatasize(node, data, data.label, true);
                      }
                    }}
                    title={data.label}
                  >
                      {data.label.length > 60 ? data.label.substring(0, 60)+'...' : data.label}
                    </span>
                )}
                {arrEdges.length > 0 && !data.isedit && (
                  <div
                    onClick={() => {
                      graph.select(node);
                      if (arrEdges.length) {
                        if (data.isCollapsed) {
                          x6collapsed(node, childlength, false);
                        } else {
                          x6collapsed(node, childlength, true);
                        }
                      }
                    }}
                    style={{
                      width: '20px',
                      height: '20px',
                      background: 'rgba(255,255,255,0.3)',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginLeft: '10px',
                      border: '1px solid rgba(255,255,255,0.7)',
                    }}
                  >
                    {data.isCollapsed ? (
                      <MinusOutlined
                        style={{ color: '#FFF', fontSize: '10px' }}
                      />
                    ) : (
                      <span style={{ color: '#FFF', fontSize: '11px' }}>
                          {arrEdges.length}
                        </span>
                    )}
                  </div>
                )}
              </div>
            </Dropdown>
          );
        }else if(data.type == 5){
          // 节点单击右键的菜单  1是子节点  2 是知识节点
          const contextMenu = (node: any): any => (
            <Menu>
              <Menu.Item
                onClick={() => {
                  setVisible(20);
                  if (TeachingmoduleRef.current) {
                    TeachingmoduleRef.current.setaddForm({
                      nodeId: node.id,
                      ...data
                    })
                  }
                }}
              >
                {t('查看详情')}
              </Menu.Item>
              <Menu.SubMenu title={t('添加节点')}>
                <Menu.Item onClick={() => addChild(node, 1)}>
                  {t('分类节点')}
                </Menu.Item>
                <Menu.Item onClick={() => addChild(node, 2)}>
                  {t('知识节点')}
                </Menu.Item>
                <Menu.Item
                  onClick={() => {
                    setSelectnode(node);
                    setVisible(21);
                  }}
                >
                  {t('从其他地图添加')}
                </Menu.Item>
                {mapinfo.type == 7 && data.isroot && (
                  <Menu.Item
                    onClick={() => {
                      setSelectnode(node);
                      setVisible(13);
                    }}
                  >
                    {t('课程')}
                  </Menu.Item>
                )}
                <Menu.Item
                  onClick={() => {
                    setBatchaddtype(1);
                    setBatchaddnode(true);
                  }}
                >
                  {t('批量添加')}
                </Menu.Item>
              </Menu.SubMenu>
              <Menu.Item
                onClick={() => {
                  setExpandnode(node.id);
                  setVisible(9);
                  setTemptime(new Date().getTime());
                }}
              >
                {t('修改顺序')}
              </Menu.Item>
              {!data.isroot && (
                <Menu.SubMenu title={t('修改类型')}>
                  <Menu.Item onClick={() => updatType(node, 1)}>
                    {t('分类节点')}
                  </Menu.Item>
                  <Menu.Item onClick={() => updatType(node, 2)}>
                    {t('知识节点')}
                  </Menu.Item>
                  {query.type == 'micromajor' || query.type == 'microMajor' && <Menu.Item onClick={() => updatType(node, 5)}>
                    {t('教学模块')}
                  </Menu.Item>}
                </Menu.SubMenu>
              )}
              {/* 删除会抛出一个异常 不知道为啥 但是不影响业务 */}
              <Menu.Item
                onClick={() => deletenode(node)}
                disabled={data.isroot}
              >
                {t('删除节点')}
              </Menu.Item>
            </Menu>
          );

          return (
            <Dropdown
              overlay={() => contextMenu(node)}
              trigger={['contextMenu']}
              disabled={perviewtype == 2}
            >
              <div
                style={{
                  width: '100%',
                  height: '100%',
                  // display: 'flex',
                  // alignItems: 'center',
                  // justifyContent: 'center',
                  background: `linear-gradient( 90deg, rgba(${mapv4colors[iconnum].start},1) 0%, rgba(${mapv4colors[iconnum].end},1) 100%)`,
                  borderRadius: '33px',
                  border: isselect ? '3px solid var(--primary-color)' : 'none',
                  filter: data.showgrey ? 'grayscale(100%)':''
                }}
                onClick={() => {
                  if (perviewtype == 2) {
                    graph.select(node);
                    setVisible(20);
                    TeachingmoduleRef.current.setaddForm({
                      nodeId: node.id,
                      ...data
                    })
                  }else{
                    graph.select(node);
                  }
                }}
                onMouseDown={(e: any) => {
                  if (e.button == 2) {
                    graph.select(node);
                  }
                }}
              >
                    <span
                      style={{
                        minWidth: '50px',
                        fontSize: '18px',
                        // flex: 1,
                        color: '#fff',
                        position: 'absolute',
                        left:'18px',
                        top:'5px',
                      }}
                    >
                        {data.label}
                      </span>
                <span style={{
                  position: 'absolute',
                  right: arrEdges.length>0 ? '60px' :'15px',
                  top: '8px',
                  fontSize: '13px',
                  color: '#fff'
                }}>{data.score || '-'}分</span>
                <span style={{
                  position: 'absolute',
                  left: '18px',
                  bottom: '8px',
                  fontSize: '12px',
                  color: '#fff'
                }}>学习时间：{data.studytime[0]?.replace('-','.')}~{data.studytime[1]?.replace('-','.')}</span>
                {arrEdges.length > 0 && (
                  <div
                    onClick={(e) => {
                      graph.select(node);
                      if (arrEdges.length) {
                        if (data.isCollapsed) {
                          x6collapsed(node, childlength, false);
                        } else {
                          x6collapsed(node, childlength, true);
                        }
                        e.stopPropagation();
                      }
                    }}
                    style={{
                      position: 'absolute',
                      right: '20px',
                      top:'22px',
                      width: '20px',
                      height: '20px',
                      background: 'rgba(255,255,255,0.3)',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginLeft: '10px',
                      border: '1px solid rgba(255,255,255,0.7)',
                      cursor: 'pointer'
                    }}
                  >
                    {data.isCollapsed ? (
                      <MinusOutlined
                        style={{ color: '#FFF', fontSize: '10px' }}
                      />
                    ) : (
                      <span style={{ color: '#FFF', fontSize: '11px' }}>
                            {arrEdges.length}
                          </span>
                    )}
                  </div>
                )}
              </div>
            </Dropdown>
          );

        } else if (data.type == 2) {
          // 节点单击右键的菜单  1是子节点  2 是知识节点
          const contextMenu = (node: any): any => (
            <Menu>
              <Menu.Item
                onClick={() => {
                  setVisible(1);
                  setDrawerdata(node);
                  setGraphmap(graph);
                  // const curCheckedNode = nodeListInfo.current?.find((item: any) => item?.nodeId === node?.id)
                  // setCheckedNodeDetail(curCheckedNode)
                }}
              >
                {t('查看详情')}
              </Menu.Item>
              <Menu.SubMenu title={t('添加节点')}>
                <Menu.Item disabled onClick={() => addChild(node, 1)}>
                  {t('分类节点')}
                </Menu.Item>
                <Menu.Item onClick={() => addChild(node, 2)}>
                  {t('知识节点')}
                </Menu.Item>
                <Menu.Item
                  onClick={() => {
                    setBatchaddtype(2);
                    setBatchaddnode(true);
                  }}
                >
                  {t('批量添加')}
                </Menu.Item>
                <Menu.Item
                  onClick={() => {
                    setSelectnode(node);
                    setVisible(21);
                  }}
                >
                  {t('从其他地图添加')}
                </Menu.Item>
                <Menu.Item
                  onClick={() => {
                    setAddtype(1);
                    setSelectnode(node);
                    setVisible(24);
                  }}
                >
                  {t('从视频添加')}
                </Menu.Item>
                <Menu.Item
                  onClick={() => {
                    setAddtype(1);
                    setSelectnode(node);
                    setVisible(25);
                  }}
                >
                  {t('从在线课堂添加')}
                </Menu.Item>
              </Menu.SubMenu>
              <Menu.Item
                onClick={() => {
                  setExpandnode(node.id);
                  setVisible(17);
                  setSelectnode(node);
                  setGraphmap(graph);
                }}
              >
                {t('知识点关联')}
              </Menu.Item>
              <Menu.Item
                onClick={() => {
                  setExpandnode(node.id);
                  setVisible(9);
                  setTemptime(new Date().getTime());
                }}
              >
                {t('修改顺序')}
              </Menu.Item>
              {data.isroot ? (
                ''
              ) : (
                <Menu.SubMenu title={t('修改类型')}>
                  <Menu.Item onClick={() => updatType(node, 1)}>
                    {t('分类节点')}
                  </Menu.Item>
                  <Menu.Item onClick={() => updatType(node, 2)}>
                    {t('知识节点')}
                  </Menu.Item>
                  {query.type == 'micromajor' || query.type == 'microMajor' && <Menu.Item onClick={() => updatType(node, 5)}>
                    {t('教学模块')}
                  </Menu.Item>}
                </Menu.SubMenu>
              )}

              <Menu.SubMenu title={t('对比辨析')}>
                <Menu.Item
                  disabled={!data.isDiscriminate}
                  onClick={() => {
                    setDrawerdata(node);
                    setGraphmap(graph);
                    setVisible(4);
                  }}
                >
                  {t('查看')}
                </Menu.Item>
                <Menu.Item
                  onClick={() => {
                    setDrawerdata(node);
                    setGraphmap(() => {
                      return graph;
                    });
                    // saveContent(false);
                    setVisible(8);
                  }}
                >
                  {t('添加')}
                </Menu.Item>
              </Menu.SubMenu>
              <Menu.SubMenu title={t('设置标签')}>
                <Menu.Item onClick={() => uodatayinandian(node)}>
                  {data.isyinandian ? t('取消重难点') : t('设为重难点')}
                </Menu.Item>
                <Menu.Item
                  onClick={() =>
                    uodatabykey(node, !data.iscoreknowledge, 'iscoreknowledge')
                  }
                >
                  {data.iscoreknowledge
                    ? t('取消核心知识点')
                    : t('设为核心知识点')}
                </Menu.Item>
                <Menu.Item
                  onClick={() =>
                    uodatabykey(
                      node,
                      !data.isexpandknowledge,
                      'isexpandknowledge',
                    )
                  }
                >
                  {data.isexpandknowledge
                    ? t('取消拓展知识点')
                    : t('设为拓展知识点')}
                </Menu.Item>
                <Menu.Item
                  onClick={() => uodatabykey(node, !data.iscase, 'iscase')}
                >
                  {data.iscase ? t('取消案例') : t('设为案例')}
                </Menu.Item>
                <Menu.Item
                  onClick={() =>
                    uodatabykey(node, !data.isexperiment, 'isexperiment')
                  }
                >
                  {data.isexperiment ? t('取消实验') : t('设为实验')}
                </Menu.Item>
                <Menu.SubMenu title={t("自定义标签")}>
                  {
                    allLabels.map((item: any) => {
                      let flageset = data.labelsconfig.some((label1: any) => label1.id === item.id)
                      return <>
                              <Menu.Item onClick={() => updatalabel(item,!flageset,node)}>
                                {flageset ? '取消' :'设为'}{item.name}
                              </Menu.Item>                              
                            </>
                    })
                  }
                </Menu.SubMenu>
                
              </Menu.SubMenu>
              {/* 删除会抛出一个一场 不知道为啥 但是不影响业务 */}
              {data.isroot ? (
                ''
              ) : (
                <Menu.Item onClick={() => deletenode(node)}>
                  {t('删除节点')}
                </Menu.Item>
              )}
            </Menu>
          );
          let flag = childlength.length > 0 || data.isyinandian;
          let hashlink =
            isselect ||
            data.bindresource.length ||
            data.explanation ||
            data.homework?.length ||
            data.bindlink?.length ||
            data.isDiscriminate ||
            data.referenceMaterials?.length;
          let resourcelength =
            (data.bindresource?.length || 0) +
            (data.homework?.length || 0) +
            (data.bindlink?.length || 0) +
            (data.referenceMaterials?.length || 0);
          if(hasRichTextContent(data.explanation)){
            resourcelength += 1;
          }
          return (
            <Dropdown
              overlay={() => contextMenu(node)}
              trigger={['contextMenu']}
              disabled={perviewtype == 2}
              onOpenChange={(open) => {
                if(open){
                  restnodeedit();
                }
              }}
            >
              <div
                style={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  filter: data.showgrey ? 'grayscale(100%)':''
                  // overflow: 'hidden'
                }}
                onClick={() => {
                  graph.select(node);
                  // 在学生端的情况下 点击节点打开详解
                  if (perviewtype == 2) {
                    setDrawerdata(node);
                    setGraphmap(graph);
                    if(query.type == 'microMajor'){
                      checkshowmessage(node,moudelNodedata.current,query.id,newmapid,query.sm).then((res:any)=>{
                        if(!res){
                          message.warning('现在学习该节点将不计掌握率完成率，请先完成先修模块的学习！')
                          setNotrecord(true);
                        }
                        setVisible(1);
                      })
                    }else{
                      setVisible(1);
                    }
                  }else{
                    if(!data.isedit){
                      // 重置输入框状态
                      restnodeedit();
                    }
                  }
                }}
                onMouseDown={(e: any) => {
                  if (e.button == 2) {
                    graph.select(node);
                  }
                }}
              >
                <div
                  style={{
                    position: 'relative',
                    flex: '1',
                    height: '40px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: flag ? 'center' : 'center',
                    background: `linear-gradient( 90deg, rgba(${mapv4colors[iconnum].start},0.1) 0%, rgba(${mapv4colors[iconnum].end},0.3) 100%)`,
                    borderRadius: '27px',
                    border: isselect
                      ? '3px solid var(--primary-color)'
                      : 'none',
                  }}
                >
                  {!data.isedit &&<div
                    style={{
                      marginRight: '10px',
                      width: '10px',
                      height: '10px',
                      borderRadius: '50%',
                      border: `1px solid rgb(${mapv4colors[iconnum].end})`,
                      background:
                        resourcelength > 0
                          ? `rgb(${mapv4colors[iconnum].end})`
                          : '#fff',
                    }}
                  ></div>}
                  {data.isedit ? (
                    // 如果有展开 按钮 和 重难点 宽度就要收紧一点 反之  100%
                    <Input
                      maxLength={60}
                      showCount
                      style={{
                        // width: (flag ? nodewidth + 'px' : '100%'),
                        fontSize: '14px',
                        padding: '0 !important',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        textAlign: 'center',
                      }}
                      onPressEnter={(e: any) =>{
                        updatlabel(node, data, e);
                        restnodeedit();
                      }}
                      onChange={(e: any) =>
                        updatasize(node, data, e.target.value, true)
                      }
                      defaultValue={data.label}
                      bordered={false}
                      autoFocus
                      onBlurCapture={(e: any) => {
                        updatlabel(node, data, e);
                      }}
                      // onFocus={(e: any) => {
                      //   e.target.select();
                      // }}
                    />
                  ) : (
                    <span
                      style={{
                        fontSize: '16px',
                        minWidth: '50px',
                        // width: flag ? nodewidth + 'px' : '100%',
                        height: '100%',
                        color: `rgb(${mapv4colors[iconnum].end})`,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                      onDoubleClick={() => {
                        if (perviewtype != 2) {
                          restedit();
                          editnodes.current.push(node);
                          node.updateData({
                            ...data,
                            isedit: true,
                          });
                          updatasize(node, data, data.label, true);
                        }
                      }}
                      title={data.label}
                    >
                        {data.label.length > 60 ? data.label.substring(0, 60)+'...' : data.label}
                      </span>
                  )}
                  {arrEdges.length > 0 && !data.isedit && (
                    <div
                      style={{
                        width: '18px',
                        height: '18px',
                        marginLeft: '10px',
                        background: `rgb(${mapv4colors[iconnum].end})`,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                      onClick={e => {
                        e.stopPropagation();
                        if (data.isCollapsed) {
                          x6collapsed(node, childlength, false);
                        } else {
                          x6collapsed(node, childlength, true);
                        }
                      }}
                    >
                        <span style={{ fontSize: '12px', color: '#fff' }}>
                          {data.isCollapsed ? <MinusOutlined /> : arrEdges.length}
                        </span>
                    </div>
                  )}
                </div>
                <div
                  style={{
                    position: 'absolute',
                    left: '7.5px',
                    bottom: '-12px',
                    width: 'calc(100% - 7.5px)',
                    height: '17px',
                    display: 'flex',
                    alignItems: 'flex-end',
                  }}
                >
                  {data.isyinandian ? (
                    <div
                      style={{
                        width: '23px',
                        height: '15px',
                        backgroundColor: '#FF5854',
                        borderRadius: '3px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '8px',
                        color: '#fff',
                        marginRight: '5px',
                      }}
                    >
                      {t('难')}
                    </div>
                  ) : null}
                  {data.iscoreknowledge ? (
                    <div
                      style={{
                        width: '23px',
                        height: '15px',
                        backgroundColor: '#FD8059',
                        borderRadius: '3px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '8px',
                        color: '#fff',
                        marginRight: '5px',
                      }}
                    >
                      {t('核')}
                    </div>
                  ) : null}
                  {data.isexpandknowledge ? (
                    <div
                      style={{
                        width: '23px',
                        height: '15px',
                        backgroundColor: '#FCBD6F',
                        borderRadius: '3px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '8px',
                        color: '#fff',
                        marginRight: '5px',
                      }}
                    >
                      {t('拓')}
                    </div>
                  ) : null}
                  {data.isDiscriminate ? (
                    <div
                      style={{
                        width: '23px',
                        height: '15px',
                        backgroundColor: '#34BAD1',
                        borderRadius: '3px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '8px',
                        color: '#fff',
                        marginRight: '5px',
                      }}
                    >
                      {t('辩')}
                    </div>
                  ) : null}
                  {data.isexperiment ? (
                    <div
                      style={{
                        width: '23px',
                        height: '15px',
                        backgroundColor: '#6CCC85',
                        borderRadius: '3px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '8px',
                        color: '#fff',
                        marginRight: '5px',
                      }}
                    >
                      {t('实')}
                    </div>
                  ) : null}
                  {data.iscase ? (
                    <div
                      style={{
                        width: '23px',
                        height: '15px',
                        backgroundColor: '#8471FF',
                        borderRadius: '3px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '8px',
                        color: '#fff',
                        marginRight: '5px',
                      }}
                    >
                      {t('案')}
                    </div>
                  ) : null}
                  {data.linkmapid?.length > 0 && (
                    <div
                      style={{
                        width: '23px',
                        height: '15px',
                        backgroundColor: '#549CFF',
                        borderRadius: '3px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '8px',
                        color: '#fff',
                        marginRight: '5px',
                        cursor: 'pointer',
                      }}
                      onClick={(e: any) => {
                        setVisible(18);
                        setSelectnode(node);
                        setGraphmap(graph);
                        // 阻止事件冒泡
                        e.stopPropagation();
                      }}
                    >
                      {t('跨')}
                    </div>
                  )}
                  {
                    // 自定义标签
                    data.labelsconfig?.length > 0 && data.labelsconfig.map((labelitem: any) => {
                      return (
                        <div
                          key={labelitem.id}
                          style={{
                            width: '23px',
                            height: '15px',
                            backgroundColor: labelitem.backgroundColor,
                            borderRadius: '3px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '8px',
                            color: labelitem.font,
                            marginRight: '5px',
                            cursor: 'pointer',
                          }}                         
                        >
                          {labelitem.displayName}
                        </div>
                      )
                    })
                  }
                </div>
              </div>
            </Dropdown>
          );
        } else {
          // 节点单击右键的菜单  1是子节点  2 是知识节点  3是课程节点
          const contextMenu = (node: any): any => (
            <Menu>
              <Menu.Item
                onClick={() => {
                  setVisible(14);
                  setSelectcourseid(data.courseId);
                  setGraphmap(graph);
                }}
              >
                {t('查看详情')}
              </Menu.Item>
              <Menu.SubMenu title={t('添加节点')}>
                <Menu.Item onClick={() => addChild(node, 1)}>
                  {t('分类节点')}
                </Menu.Item>
                <Menu.Item onClick={() => addChild(node, 2)}>
                  {t('知识节点')}
                </Menu.Item>
                <Menu.Item
                  onClick={() => {
                    setBatchaddtype(1);
                    setBatchaddnode(true);
                  }}
                >
                  {t('批量添加')}
                </Menu.Item>
              </Menu.SubMenu>
              <Menu.Item
                onClick={() => {
                  setExpandnode(node.id);
                  setVisible(9);
                  setTemptime(new Date().getTime());
                }}
              >
                {t('修改顺序')}
              </Menu.Item>
              {data.isroot ? (
                ''
              ) : (
                <Menu.SubMenu title={t('修改类型')}>
                  <Menu.Item onClick={() => updatType(node, 1)}>
                    {t('分类节点')}
                  </Menu.Item>
                  <Menu.Item onClick={() => updatType(node, 2)}>
                    {t('知识节点')}
                  </Menu.Item>
                  {query.type == 'micromajor' || query.type == 'microMajor' && <Menu.Item onClick={() => updatType(node, 5)}>
                    {t('教学模块')}
                  </Menu.Item>}
                </Menu.SubMenu>
              )}

              {/* 删除会抛出一个异常 不知道为啥 但是不影响业务 */}
              <Menu.Item
                onClick={() => deletenode(node)}
                disabled={data.isroot}
              >
                {t('删除节点')}
              </Menu.Item>
            </Menu>
          );

          return (
            <Dropdown
              overlay={() => contextMenu(node)}
              trigger={['contextMenu']}
              disabled={perviewtype == 2}
            >
              <div
                style={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: isselect ? '#FFF0DE' : '#F1F1F1',
                  borderLeft: isselect
                    ? '3px solid #FFA873'
                    : '3px solid #B3B3B3',
                }}
                onClick={() => graph.select(node)}
                onMouseDown={(e: any) => {
                  if (e.button == 2) {
                    graph.select(node);
                  }
                }}
              >
                {data.isedit ? (
                  <Input
                    maxLength={60}
                    showCount
                    style={{
                      // width: '100%',
                      flex: 1,
                      height: '100%',
                      fontSize: '14px',
                      padding: '0 !important',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      textAlign: 'center',
                    }}
                    // onPressEnter={(e: any) => updatlabel(node, data, e)}
                    onChange={(e: any) =>
                      updatasize(node, data, e.target.value, true)
                    }
                    defaultValue={data.label}
                    bordered={false}
                    autoFocus
                    onBlurCapture={(e: any) => updatlabel(node, data, e)}
                    // onFocus={(e: any) => {
                    //   e.target.select();
                    // }}
                  />
                ) : (
                  <span
                    style={{
                      fontSize: '14px',
                      // width: '75%',
                      flex: 1,
                      height: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                    onDoubleClick={() => {
                      if (perviewtype != 2) {
                        restedit();
                        editnodes.current.push(node);
                        node.updateData({
                          ...data,
                          isedit: true,
                        });
                        updatasize(node, data, data.label, true);
                      }
                    }}
                    title={data.label}
                  >
                      {data.label.length > 60 ? data.label.substring(0, 60)+'...' : data.label}
                    </span>
                )}
                {arrEdges.length ? (
                  data.isCollapsed ? (
                    <MinusCircleOutlined
                      style={{
                        fontSize: '18px',
                        backgroundColor: '#FF9351',
                        color: '#fff',
                        cursor: 'pointer',
                        borderRadius: '50%',
                        overflow: 'hidden',
                        marginRight: '8px',
                      }}
                      onClick={e => x6collapsed(node, childlength, false)}
                    />
                  ) : (
                    <PlusCircleOutlined
                      style={{
                        fontSize: '18px',
                        backgroundColor: '#FF9351',
                        color: '#fff',
                        cursor: 'pointer',
                        borderRadius: '50%',
                        overflow: 'hidden',
                        marginRight: '8px',
                      }}
                      onClick={() => x6collapsed(node, childlength, true)}
                    />
                  )
                ) : (
                  ''
                )}
              </div>
            </Dropdown>
          );
        }
      });
    } catch (error) {
      // console.log('注册组件报错！',error);
    }
  };

  // 展开收起所有子节点
  const x6collapsed = (node: any, childarr: any, collapsed: boolean) => {
    const data: any = node.getData();
    // 修改数据 让节点重新渲染
    node.updateData({
      ...data,
      isCollapsed: collapsed,
    });
    // 判断是否把节点居中
    let iscenter = false;
    // 递归数据
    const run = (pre: any) => {
      // let succ = graph.getSuccessors(pre, { distance: 1 });
      let succ = graph.getNeighbors(pre, { outgoing: true });
      // 这里是把 用户自己连线 的节点 不受展开收起按钮控制
      graph.getEdges().forEach((item: any) => {
        if (item.store.data.data.isnew) {
          // 根据id删除succ里面的
          succ = succ.filter((item2: any) => {
            if (
              item.store.data.source.cell == pre.id &&
              item.store.data.target.cell == item2.id
            ) {
              return false;
            } else {
              return true;
            }
          });
        }
      });

      // 判断子节点是不是大于2个
      if (succ.length >= 2) {
        const container: any = ReactDOM.findDOMNode(ref.current);
        // 判断下面的节点是否超出了容器
        if (
          succ[succ.length - 1].store.data.position.y -
          succ[0].store.data.position.y >=
          container.clientHeight
        ) {
          iscenter = true;
        }
      }
      if (succ) {
        succ.forEach((node2: any) => {
          const data2: any = node2.getData();
          // 不显示当前节点和线
          // node2.toggleVisible(collapsed);
          node2.setVisible(collapsed);
          // 判断下面还有没有子节点  collapsed 是控制只展开下一级
          if (childarr.length && !collapsed) {
            // 修改数据 让节点重新渲染
            node2.updateData({
              ...data2,
              isCollapsed: collapsed,
            });
            run(node2);
          }
        });
      }
    };
    run(node);
    if (node.data.isroot) {
      // 如果是点击的根节点 就自动布局
      maplayout(true);
    } else {
      // 如果超出了容器就当前节点居中
      if (iscenter) {
        maplayout();
        // 当前节点居中
        graph.centerCell(node);
      } else {
        maplayout();
      }
    }
  };

  // 初始化
  useEffect(() => {
    if (x6treedata.nodes.length) {
      initmap();
      if([1,2].includes(perviewtype)){
        getBaseInfoMap();
      }
      return () => {
        Graph.unregisterReactComponent('react-compont');
        graph.dispose();
        // graph = null;
        // graphRef.current = null;
      };
    }
  }, [x6treedata]);

  // 保存图谱 添加防抖 避免多次变更 调用接口 debounce(
  const saveContent = async (
    showmessage: boolean = true,
    isperview: boolean = false,
  ) => {
    if (islock) {
      // message.error('当前地图其他人正在编辑，无法保存！');
      return;
    }
    if (!hasRole) {
      message.error(t('当前地图已下架，无法保存！'));
      return;
    }

    if (isdelete) {
      message.error(t('当前地图已删除，无法保存！'));
      return;
    }

    if (process.current) {
      // message.error('当前地图正在转换中，无法保存！');
      return;
    }

    if (graphRef.current == null) {
      return;
    }
    let obj = getmapdatainfo();
    // 检测敏感词
    getSensitiveWord(
      JSON.stringify(obj.nodesVos.map((item: any) => item.entity)),
      '课程地图节点',
      async () => {
        obj.checkRole = checkRole;
        let res = await savemap(obj);
        // 判断是不是预览的保存
        if (isperview) {
          bus.emit('perviewsaveover', false);
        } else {
          bus.emit('mapsaveover', false);
          // 这里保存后会退出编辑模式
          bus.emit('saveMapout', showmessage);
        }
        if (res.status == 200) {
          message.success(t('保存成功！'));
          setRestore(false);
          // if (showmessage) {
          // }
        } else {
          message.info(t('保存失败！') + res.message);
        }
      },
      () => {
        bus.emit('saveMapout');
        // if (isperview) {
        //   bus.emit('perviewsaveover', false);
        // } else {
        //   bus.emit('saveCoverover', false);
        //   bus.emit('saveMapout');
        // }
      },
    );
  };

  const restnodeedit = () => {
    // setSelectnode([]);
    if (editnodes.current.length > 0) {
      editnodes.current.forEach((element: any) => {
        let data = element.getData();
        // 先把宽度更新了 不然会出现bug
        updatasize(element, data, data.label);
        element.updateData({
          ...data,
          isedit: false
        })
      });
      editnodes.current = [];
    }
  }

  // 获取图谱当前的数据
  const getmapdatainfo = () => {
    // thismapid.current 这里为什么要用ref 来实现 是因为切换路由 卸载了当前的组件后这个id会是初始值 会导致把其他地图覆盖掉
    let obj: any = {
      mapId: null,
      courseId: query.micromajorid || query.id, //教室界面的图谱保存需要传课程的id  后端要判断教学团队权限 其他情况不传
      nodesVos: [],
      relationVos: [],
      teachingModuleTeacherInfos: [],
      knowledgeDiscriminateList: knowledgeDiscriminateList.map((item: any) =>{
        let newobj ={
          ...item
        }
        delete newobj.id;
        return newobj;
      })
    };

    if(restore){
      obj.restore = restore;
      obj.knowledgeDiscriminateList = knowledgeDiscriminateList;
    }

    if (perviewtype == 0) {
      if (query.id) {
        obj.mapId = query.id;
      } else {
        obj.mapId = query.mapId;
      }
    } else {
      if (thismapid.current) {
        obj.mapId = thismapid.current;
      } else {
        obj.mapId = query.mapId;
      }
    }

    const allnodes = graphRef.current?.getNodes();
    const alledges = graphRef.current?.getEdges();
    // 获取根节点
    const rootnode = allnodes[0];
    const modelQuestion = buildKnowledgedata(rootnode,allnodes,alledges);
    console.log('modelQuestion', modelQuestion);
    // 保存当前的位置
    allnodes[0].data.translate = graphRef.current?.translate();

    // if(query.type == 'micromajor'){
    //   obj.microProfessionId = query.micromajorid || null;
    // }

    if(query.type == 'microMajor'){
      obj.courseId =  query.id || null;
    }

    allnodes.forEach((node: any) => {
      const strvalue = {
        ...node.store.data.data,
        id: node.store.data.id || node.id,
        width: node.store.data.size.width,
        height: node.store.data.size.height,
        x: node.store.data.position.x,
        y: node.store.data.position.y,
        visible: node.store.data.visible,
        layoutname: 1,
        isedit: false
      };

      if (node.data.type == 5) {
        node.data.teachingModuleManager.forEach((element: any) => {
          obj.teachingModuleTeacherInfos.push({
            teachingModuleUserCode: element.value,
            teachingModuleUserName: element.label[0]
          })
        })
      }


      obj.nodesVos.push({
        entity: strvalue.label,
        nodeId: node.store.data.id,
        value: {
          data: JSON.stringify(strvalue),
        },
        valueMap: JSON.stringify({
          data: strvalue,
        }),
      });
    });
    // type 1包含 2等价 3后续
    alledges.forEach((edge: any) => {
      const strvalue = {
        visible:
          edge.store.data.visible == undefined ? true : edge.store.data.visible,
        type: edge.store.data.data.type,
        isnew: edge.store.data.data.isnew,
      };

      obj.relationVos.push({
        source: edge.store.data.source.cell,
        target: edge.store.data.target.cell,
        type: edge.store.data.data.type,
        data: JSON.stringify(strvalue),
      });
    });
    obj.modelQuestion = modelQuestion;
    return obj;
  };

  // 初始化编辑器
  const initmap = () => {
    const container: any = ReactDOM.findDOMNode(ref.current);
    const width = container.scrollWidth;
    // 如果画布存在就先清除 然后销毁
    if (graph) {
      try {
        graph.dispose();
        Graph.unregisterReactComponent('react-compont');
      } catch (error) {
        console.log(error);
      }
    }
    // X6 注册react 组件
    registerstyle();

    try {
      // 实例初始化
      graph = new Graph({
        container: ref.current,
        autoResize: true,
        background: {
          color: '#fff', // 设置画布背景颜色
        },
        grid: false,
        // scroller: {
        //   enabled: true,
        //   pageVisible: false,
        //   pageBreak: false,
        //   pannable: true,
        // },
        // 线
        // connecting: {
        //   connector: 'rounded', //线的形状
        //   snap: true, //自动吸附
        //   allowBlank: false,//是否允许连接到画布空白位置的点
        //   allowLoop: false,//是否允许创建循环连线
        //   allowMulti: false,//是否允许在相同的起始节点和终止之间创建多条边
        //   allowNode: true,
        //   allowEdge: false, //是否允许边链接到另一个边
        //   highlight: true, //拖动边时，是否高亮显示所有可用的连接桩或节点，
        //   anchor: {
        //     name: 'midSide',  // 设置线连接到节点的那个位置
        //   },
        //   // 判断是否新增边，触发时机是 magnet 被按下，如果返回 false，则没有任何反应，如果返回 true，会在当前 magnet 创建一条新的边。
        //   validateMagnet({ e, magnet, view, cell }) {
        //     return true
        //   },
        //   //
        //   validateEdge({ edge, type, previous }:any) {
        //     return true;
        //   }
        // },
        selecting: {
          enabled: false,
          rubberband: false, // 启用框选
          movable: false, //拖动选框框选的节点一起移动
          multiple: false, // 启用点选
          showNodeSelectionBox: false, //是否显示节点的选中样式
          showEdgeSelectionBox: false, //是否显示边的选中样式
        },
        preventDefaultBlankAction: false, //在画布空白位置响应鼠标事件时，是否禁用鼠标默认行为
        width,
        // height,
        // snapline: true, //对齐线
        // scroller:true,
        //普通画布(未开启 scroller 模式)通过开启 panning 选项来支持拖拽平移。
        panning: {
          enabled: true,
          // eventTypes: ['rightMouseDown', 'mouseWheel']
        },
        // 滚轮放大缩小
        mousewheel: {
          enabled: true,
          modifiers: [], //配置快捷键 触发
          minScale: 0.5,
        },
        //撤销和重做
        history: {
          enabled: true,
          beforeAddCommand(event: any, args: any) {
            // console.log('history',event,args)
            let arr = ['cell:added', 'cell:removed'];
            if (arr.includes(event)) {
              return true;
            } else if (args.key == 'data') {
              if (args.current.label != args.previous.label) {
                return true;
              } else {
                return false;
              }
            } else {
              return false;
            }
          },
        },
        // 调整节点大小
        resizing: {
          enabled: false,
        },
        // 调整节点旋转角度
        // rotating: {
        //     enabled: true,
        //   },
        // 快捷键
        keyboard: {
          enabled: true,
        },
        // minimap: {
        //   enabled: true,
        //   container: container,
        //   scalable:true
        // },
        // 自定义标签
        onEdgeLabelRendered: (args: any) => {
          const { selectors } = args;
          const content = selectors.foContent as HTMLDivElement;
          if (content) {
            ReactDOM.render(
              <Dropdown overlay={edgeMenu(args.edge)} trigger={['contextMenu']}>
                <Tag>{args.label.text || args.label.attrs.label.text}</Tag>
              </Dropdown>,
              content,
            );
          }
        },
        // 自定义交互行为
        interacting: function (cellView: any) {
          if (cellView.cell.isNode() || perviewtype == 2) {
            if (cellView.cell.getData()?.isedit || perviewtype == 2) {
              return { nodeMovable: false };
            }
          }
          // 禁止边移动
          return {
            edgeLabelMovable: false,
            nodeMovable: false,
          };
        },
      });
      graphRef.current = graph;

      graph.fromJSON(x6treedata);
      if (isFormat) {
        // // 初始化布局算法插件
        // const dagreLayout = new DagreLayout({
        //   type: 'dagre',
        //   rankdir: 'LR',
        //   // align: 'DL',
        //   ranksep: 40,
        //   nodesep: 15,
        // })
        // // 布局算法
        // const model = dagreLayout.layout(x6treedata);
        // // 把布局完毕的数据渲染上去
        // graph.fromJSON(model);
        setTimeout(() => {
          maplayout(true);
        }, 500);
      }
      // 判断是不是从首页进来的带有keyword参数
      keywordchange();
      // 判断是不是从编辑模块进来的
      if(query.nodeId){
        if(query.type == 'micromajor' || query.type == 'microMajor'){
          showmodule(query.nodeId);
        }
        // 非管理员  以及 教学模块是否有数据 并且是微专业 和 微专业课里面才有这个东西
      }else if(modelpermission.current && !modelpermission.current.isManager && modelpermission.current.moduleInfos.length && query.type == 'microMajor'){
        setSelectmodule(modelpermission.current.moduleInfos[0].nodeId);
        showmodule(modelpermission.current.moduleInfos[0].nodeId);
        // 这个是重绘上次保存的位置
      }else if (x6treedata.nodes[0]?.data.translate) {
        graph.translate(
          x6treedata.nodes[0].data.translate.tx,
          x6treedata.nodes[0].data.translate.ty,
        );
      } else {
        // 视图居中对齐
        graph.centerContent();
      }

      // 动态添加工具
      graph.on('edge:click', ({ edge }: any) => {
        // 学生端预览情况下不允许拖拽移动
        if (perviewtype == 2) {
          // 判断是否是后续关系,如果是则修改文本为前序并反转箭头
          if(edge.store.data.data?.type === 3) {
            edge.setLabels([{
              text: edge.labels[0].text == '后续' ? t('前序') : '后续',
              position: 0.5
            }]);
            // 获取当前的起点和终点
            const source = edge.getSource();
            const target = edge.getTarget();
            // 反转箭头方向
            edge.setSource(target);
            edge.setTarget(source);
          }
          return;
        }

        let tools = edge.getTools();
        // 系统默认连线
        if (edge.store.data.data?.isnew) {
          if (tools) {
            edge.removeTools();
          } else {
            graph.resetSelection(edge);
            edge.addTools([
              // 'vertices',  //修改线
              'source-arrowhead', //修改箭头指向
              {
                name: 'target-arrowhead',
                args: {
                  attrs: {
                    fill: 'red',
                  },
                },
              },
              // 只有用户连的线才允许删除
              {
                name: 'button-remove',
                args: {
                  distance: -20,
                },
              },
            ]);
          }
        } else {
          if (tools) {
            edge.removeTools();
          } else {
            graph.resetSelection(edge);
            edge.addTools([
              // 'vertices',  //修改线
              'source-arrowhead', //修改箭头指向
              {
                name: 'target-arrowhead',
                args: {
                  attrs: {
                    fill: 'red',
                  },
                },
              },
            ]);
          }
        }
      });

      // 监听鼠标进入事件
      graph.on('edge:mouseenter', ({ edge }: any) => {
        if (edge.data.isnew) {
          edge.attr({
            line: {
              style: {
                animation: 'ant-line 30s infinite linear',
              },
            },

          });

          if(edge.data.type == 5){
            // 重设标签
            edge.setLabels([
              {
                attrs: { label: { text: '后修',position: 0.6 },
                  fo: {
                    width: 40,
                    height: 30,
                    x: -20,
                    y: -15,
                  }},
                markup: Markup.getForeignObjectMarkup(),
              }
            ])
          }
        }
      });
      // 监听鼠标离开边事件
      graph.on('edge:mouseleave', ({ edge }: any) => {
        if (edge.data.isnew) {
          edge.attr({
            line: {
              style: {
                animation: 'none',
              },
            },
          });
        }
        if(edge.data.type == 5){
          // 重设标签
          edge.setLabels([
            {
              attrs: {},
              markup: null,
            }
          ])
        }
      });

      //监听鼠标点击节点事件
      graph.on('node:click', ({ e, x, y, node, view }: any) => {
        setSelectnode([node]);
        // 展开了详情弹窗的时候 点击节点自动切换
        // hooks 并不会立即更新 visible 状态 在这地方 获取最新的状态
        let flag;
        setVisible((pre: any) => {
          flag = pre;
          return pre;
        });
        // 点击普通的子节点 不显示查看详情
        if (flag == 1 && node.getData().type == 2) {
          setDrawerdata(node);
          setGraphmap(graph);
          if (perviewtype == 2) {
            recordCurrentNode({
              courseId: courseid || query.id,
              mapId: newmapid,
              nodeId: node.id,
              semester: query.sm,
            });
          }
        }

        // 如果当前是连线状态
        if (selectrelation != 0 && startnode && startnode.id != node.id) {
          // 子节点不能连线到分类节点
          if (startnode.getData().type == 2 && node.getData().type == 1) {
            message.error(t('子节点不能连线到分类节点'));
            return;
          }
          if(node.data.type != 5 && selectrelation == 5){
            message.error(t('后修关系只能在教学模块之间存在'));
            return;
          }
          let startid = startnode.id;
          let endid = node.id;

          let defaultLabel:any = {
            markup: Markup.getForeignObjectMarkup(),
            attrs: {
              fo: {
                width: 40,
                height: 30,
                x: -20,
                y: -15,
              },
            },
          }
          let label = t('包含');
          if (selectrelation == 2) {
            label = t('等价');
          } else if (selectrelation == 3) {
            label = t('后续');
          } else if (selectrelation == 4) {
            label = t('关联');
          } else if (selectrelation == 5) {
            label = t('后修');
            defaultLabel = null;
          }else if (selectrelation == 6) {
            label = t('依赖');
          }else if (selectrelation == 7) {
            label = t('递进');
          }else if (selectrelation == 8) {
            label = t('辩证');
          }

          // 添加边
          graph.addEdge(
            graph.createEdge({
              source: {
                cell: startid,
                anchor: 'right', //midSide
              }, // String，必须，起始节点 id
              target: {
                cell: endid,
                anchor: 'right', //midSide
              }, // String，必须，目标节点 id
              connector: { name: 'rounded' },
              router: {
                name: 'oneSide',
                args: { side: 'right' },
              },
              visible: true, //是否显示
              defaultLabel: defaultLabel,
              data: {
                type: selectrelation, //1包含 2等价 3后续 4关联  5后修
                isnew: true, //是否是用户自己新建的边
              },
              label: {
                text: label,
                position: {
                  distance: 0.6,
                  options: {
                    // keepGradient: true,
                  },
                },
              },
              attrs: attrs,
            }),
          );
          selectrelation = 0;
          startnode = null;
        }

        if (startnode && startnode?.id == node.id) {
          message.info(t('不能选择同一节点作为连线终点,请重新选择'));
        }
      });

      // 节点选中的时候 去触发重新渲染
      graph.on('node:selected', ({ node, cell, options }: any) => {
        let data = node.getData();
        node.updateData({
          ...data,
          temp: new Date().getTime(),
        });
      });
      // 节点取消选中的时候 去触发重新渲染
      graph.on('node:unselected', ({ node, cell, options }: any) => {
        let data = node.getData();
        node.updateData({
          ...data,
          temp: new Date().getTime(),
        });
      });

      // 监听鼠标选中边事件
      graph.on('edge:selected', ({ edge, cell, options }: any) => {
        // 选中边
        setSelectedge(edge);
        // 取消节点的选中
        setSelectnode([]);
      });

      // 监听鼠标取消选中边事件
      graph.on('edge:unselected', ({ edge, cell, options }: any) => {
        let tools = edge.getTools();
        if (tools) {
          edge.removeTools();
        }
        // 取消选中边
        setSelectedge([]);
      });

      // 监听鼠标点击空白画布事件
      graph.on('blank:click', ({ e, x, y, node, view }: any) => {
        restnodeedit();
        setSelectnode([]);
      });

      // 监听框选
      graph.on(
        'cell:selected',
        (args: { cell: any; options: Model.SetOptions }) => {
          // 判断当前是不是打开了框选
          if (graph.isSelectionEnabled()) {
            setSelectnode((pre: any) => {
              return [...pre, args.cell];
            });
          }
        },
      );

      // 监听取消框选
      graph.on(
        'cell:unselected',
        (args: { cell: any; options: Model.SetOptions }) => {
          // 判断当前是不是打开了框选
          if (graph.isSelectionEnabled()) {
            setSelectnode(() => {
              return [];
            });
          }
        },
      );

      // 监听线的链接
      graph.on('edge:connected', ({ isnew, edge, type, previousCell }: any) => {
        if (!isnew) {
          // 子节点不能连线到分类节点 type: 1,  //1包含 2等价 3后续 4关联
          if (
            edge.getSourceCell().getData().type == 2 &&
            edge.getTargetCell().getData().type == 1
          ) {
            message.error(t('子节点不能连线到分类节点'));
            // 判断是连线的起点还是终点  修改回原来的样子
            if (type == 'source') {
              edge.setSource({
                cell: previousCell.id,
                anchor: 'right',
              });
            } else {
              edge.setTarget({
                cell: previousCell.id,
                anchor: 'left',
              });
            }
            // 删除连线的工具
            edge.removeTools();
          } else if (edge.getData().isnew) {
            // 这里是防止修改的连线的节点后  没有在右侧的正中间
            edge.setSource({
              cell: edge.store.data.source.cell,
              anchor: 'right', //midSide
            });
            edge.setTarget({
              cell: edge.store.data.target.cell,
              anchor: 'right', //midSide
            });
            // 删除连线的工具
            edge.removeTools();
          } else {


            // 判断是连线的起点还是终点  修改回原来的样子
            if (type == 'source') {
              let sourcenode = edge.getSourceCell();
              let data = sourcenode.getData();
              updatasize(sourcenode, data, data.label);
              edge.setSource({
                cell: sourcenode.id,
                anchor: 'right',
              });
            } else {
              let targetnode = edge.getTargetCell();
              let data = targetnode.getData();
              updatasize(targetnode, data, data.label);
              edge.setTarget({
                cell: targetnode.id,
                anchor: 'left',
              });
            }

            // 删除连线的工具
            edge.removeTools();
            maplayout();
            // 判断连线后是否展示课程大纲的功能 有没有打开
            let Showoutline =
              localStorage.getItem('Showoutline') == 'true' ? true : false;
            if (Showoutline) {
              setVisible(9);
            }
          }
        }
      });

      // 学生端没有监听事件
      if (perviewtype != 2) {
        // 快捷键 tab
        graph.bindKey('tab', (e: any) => {
          let flag: any = [];
          setSelectnode((pre: any) => {
            flag = pre;
            return pre;
          });
          if (flag.length > 0) {
            let data = flag[0].getData();
            if (data.type == 2) {
              addChild(flag[0], 2);
            } else {
              addChild(flag[0], 1);
            }
          }
        });

        // 快捷键enter
        graph.bindKey('enter', (e: any) => {
          let flag: any = [];
          setSelectnode((pre: any) => {
            flag = pre;
            return pre;
          });
          if (flag.length > 0) {
            // if(flag[0].data.isedit){
            //   setSelectnode(flag[0]);
            //   return;
            // }

            // 获取前序节点
            let parent = graph.getPredecessors(flag[0], { distance: 1 });
            if (parent.length) {
              // 用当前选择的节点进行判断
              let data = flag[0].getData();
              if (data.type == 2) {
                addChild(parent[0], 2);
              } else {
                addChild(parent[0], 1);
              }
            }
          }
        });

        // 快捷键delete
        graph.bindKey('backspace', (e: any) => {
          let select = graph.getSelectedCells();
          if (select.length > 0) {
            select.forEach((element: any) => {
              if (element.store.data.shape == 'edge') {
                // 如果删除的是自定义的边允许删除
                if (element.store.data.data.isnew) {
                  graph.removeCells([element]);
                  batchupdatecompare([element]);
                }
              } else {
                deletenode(element);
              }
            });
          }
        });

        // 快捷键delete
        graph.bindKey('delete', (e: any) => {
          let select = graph.getSelectedCells();
          if (select.length > 0) {
            select.forEach((element: any) => {
              if (element.store.data.shape == 'edge') {
                // 如果删除的是自定义的边允许删除
                if (element.store.data.data.isnew) {
                  graph.removeCells([element]);
                  batchupdatecompare([element]);
                }
              } else {
                deletenode(element);
              }
            });
          }
        });
      }

      // 监听图谱变化 加入防抖 500ms内只执行一次
      graph.on(
        'cell:changed',
        debounce(({ cell, options }: any) => {
          let flag = 0;
          setVisible((pre: any) => {
            flag = pre;
            return pre;
          });
          if (flag == 9) {
            setTemptime(new Date().getTime());
          }
        }),
        500,
      );

      // 监听撤销后 自动布局
      graph.history.on('undo', (args: { cmds: any; options: any }) => {
        graph.cleanSelection();
        // 在数据进行转换的时候是否排序
        localStorage.setItem('isSort', 'true');
        // 执行自动布局
        maplayout();
      });

      // 监听重做后 自动布局
      graph.history.on('redo', (args: { cmds: any; options: any }) => {
        // 在数据进行转换的时候是否排序
        localStorage.setItem('isSort', 'true');
        // 执行自动布局
        maplayout();
      });
    } catch (error) {
      console.log(error);
    }

    // 学生端展示自动收起到第二级
    if (perviewtype == 2) {
      if (centerid == null) {
        expandall(false);
      } else {
        centerednode(centerid);
        let cell = graph.getCellById(centerid);
        graph.select(cell);
        // 在学生端的情况下 直接打开详解
        if (perviewtype == 2) {
          setVisible(1);
          setDrawerdata(cell);
          setGraphmap(graph);
        }
      }

      if(grayedoutarr.length){
        grayedoutarr.forEach((item:any) => {
          let element = graphRef.current.getCellById(item);
          if(element){
            const data: any = element.getData();
            element.updateData({
              ...data,
              showgrey: true
            });
          }
        });
      }
    }
    // 外部跳转选中节点打开右侧菜单
    if(query.selectnodeid){
      let findnum = 0;
      const  interval1 = setInterval(() => {
        if(findnum == 20){
          clearInterval(interval1);
          console.log('找不到节点');
        }
        const selectnode = graphRef.current.getCellById(query.selectnodeid);
        console.log('selectnode', selectnode);
        if(selectnode){
          if(selectnode.data.type == 2){
            graphRef.current.select(selectnode);
            setSelectnode([selectnode]);
            const data: any = selectnode.getData();
            selectnode.updateData({
              ...data,
              temp: new Date().getTime(),
            });
            centerednode(query.selectnodeid);
            setDrawerdata(selectnode);
            setGraphmap(graph);
            setVisible(1);
          }
          // 清理定时器
          clearInterval(interval1);
        }else{
          findnum ++
        }
      }, 500);
    }
    // 外部跳转选中节点打开右侧菜单
    if(query.selectnodeid){
      let findnum = 0;
      const  interval1 = setInterval(() => {
        if(findnum == 20){
          clearInterval(interval1);
          console.log('找不到节点');
        }
        const selectnode = graphRef.current.getCellById(query.selectnodeid);
        console.log('selectnode', selectnode);
        if(selectnode){
          if(selectnode.data.type == 2){
            graphRef.current.select(selectnode);
            setSelectnode([selectnode]);
            const data: any = selectnode.getData();
            selectnode.updateData({
              ...data,
              temp: new Date().getTime(),
            });
            centerednode(query.selectnodeid);
            setDrawerdata(selectnode);
            setGraphmap(graph);
            setVisible(1);
          }
          // 清理定时器
          clearInterval(interval1);
        }else{
          findnum ++
        }
      }, 500);
    }
  };

  // 更新节点的data
  const updatanode = (id: string, data: any) => {
    const node = graphRef.current.getCellById(id);
    if(node){
      // console.log('更新节点的data', node.getData());
      let nodedata = node.getData();
      let newdata = {
        ...nodedata,
        ...data,
      };
      node.updateData(newdata);
      updatasize(node, newdata, newdata.label);
      // saveContent(false); //自动保存
    }else{
      console.log('更新节点失败');
    }

  };

  // 更新节点的对比辨析字段
  const updatanodecompare = (compare: any) => {
    const allnodes = graphRef.current.getNodes();
    // 先把所有的都改成false
    allnodes.forEach((element: any) => {
      let data = element.getData();
      element.updateData({
        ...data,
        isDiscriminate: false,
      });
    });
    // 再更新节点
    compare.forEach((id: any) => {
      const node = graphRef.current.getCellById(id);
      if (node) {
        let data = node.getData();
        node.updateData({
          ...data,
          isDiscriminate: true,
        });
      }
    });
  };

  // 节点居中
  const centerednode = (id?: string) => {
    if (id) {
      let cell = graph.getCellById(id);
      if (cell) {
        // 这里是解决知识节点是收起不显示状态下的居中
        if (!cell.store.data.visible) {
          // 获取当前节点的所有父节点
          let parent = graph.getPredecessors(cell);
          if (parent.length) {
            parent.forEach((element: any) => {
              let childlength = graph.getSuccessors(element);
              // 把所有的父级展开
              x6collapsed(element, childlength, true);
            });
          }
          //展开所有的父级会有时间延迟根据浏览器不同决定
          setTimeout(() => {
            graph.centerCell(cell, { animation: { duration: 400 } });
          }, 1000);
        } else {
          graph.centerCell(cell, { animation: { duration: 400 } });
        }
      }
    } else {
      let select: any = [];
      setSelectnode((pre: any) => {
        select = pre;
        return pre;
      });
      if (select.length) {
        graph.centerCell(select[0], { animation: { duration: 400 } });
      } else {
        graph.centerContent({ animation: { duration: 400 } });
      }
    }
  };

  // 根据当前的布局来自动执行
  const maplayout = (flage?: Boolean) => {
    graph.cleanSelection();
    if (layoutname.current == 1) {
      maptreelayout(flage);
    }
  };

  // 自动布局
  const maptreelayout = (flage?: Boolean) => {
    if (layoutname.current == 1) {
      let json = graph.toJSON();
      let visiblenode: any = [];
      let useredge: any = [];
      let allmoedarr: any = [];
      // 获取画布的数据
      const mapdata = {
        nodes: json.cells.filter((node: any) => {
          if (node.data.type == 5) {
            allmoedarr.push({ label: node.data.label, value: node.id })
          }
          if (
            (node.shape == 'react-shape' && node.visible) ||
            node.data.isroot
          ) {
            return true;
          } else {
            visiblenode.push(node);
            return false;
          }
        }),
        edges: json.cells.filter((edge: any) => {
          if (edge.shape == 'edge' && !edge.data.isnew && edge.visible) {
            return true;
          } else {
            useredge.push(edge);
            return false;
          }
        }),
      };
      setAllmodule(allmoedarr);
      // 布局算法
      let treearr = tranListToTreeData(mapdata.nodes, mapdata.edges);
      const result = Hierarchy.mindmap(treearr[0], {
        direction: 'H',
        getHeight() {
          return NODE_HEIGHT;
        },
        getWidth(info: any) {
          return info.size.width;
        },
        getHGap(info: any) {
          return Number(info?.size?.width || 0) / 3;
        },
        getVGap() {
          return 15;
        },
        getSide: () => {
          return 'right';
        },
      });
      // 树形布局转成数组  下面的注释算法不要删  以后可能会用到
      let listarr = tranTreeDataToList(result);
      listarr.forEach((element: any) => {
        let node1 = graph.getCellById(element.id);
        if (
          node1.store.data.position.x != element.x ||
          node1.store.data.position.y != element.y
        ) {
          node1.position(element.x, element.y);
          // node1.translate(tx, ty,{
          //         transition:{
          //           duration: 300,
          //         }
          //       });
        }
      });
    }
    // 视图居中对齐
    if (flage) {
      setTimeout(() => {
        graph.centerContent();
      }, 400);
    } else {
      // setTimeout(() => {
      //   centerednode();
      // }, 400);
    } //设置布局标识
    layoutname.current = 1;
  };

  // 修改大纲的顺序从新布局
  const updateindex = (datalist: any) => {
    // 重新渲染图谱
    const result = Hierarchy.mindmap(datalist[0], {
      direction: 'H',
      getHeight() {
        return NODE_HEIGHT;
      },
      getWidth(info: any) {
        return info.size.width;
      },
      getHGap(info: any) {
        return info.size.width / 3;
      },
      getVGap() {
        return 15;
      },
      getSide: () => {
        return 'right';
      },
    });
    let listarr = tranTreeDataToList(result);
    listarr.forEach((element: any) => {
      let node1 = graph.getCellById(element.id);
      if (
        node1.store.data.position.x != element.x ||
        node1.store.data.position.y != element.y
      ) {
        node1.position(element.x, element.y);
      }
    });
  };

  // 批量添加节点
  const batchaddnodefun = () => {
    if (batchadddata.length) {
      graph.batchUpdate('BatchAdd', () => {
        // 获取选中的节点
        let SelectedCells = graph.getSelectedCells();
        batchadddata.forEach((element: any) => {
          addChild(SelectedCells[0], batchaddtype, element);
        });
        setBatchaddnode(false);
        setBatchadddata([]);
      });
    } else {
      message.info(t('请先输入节点名称'));
    }
  };

  // 设置封面
  const saveCover = () => {
    // 框选会锁定画布导致获取不到当前屏幕截图
    if(kuangxuan){
      bus.emit('saveCoverover', false);
      setLoading(false);
    }else{
      const el:any = document.getElementsByClassName('map_box x6-graph x6-graph-pannable')[0];
      snapdom.toPng(el, {
        scale: 1, // 缩放比例
        compress: true, // 压缩
        fast: true, // 快速模式
        embedFonts: false, // 是否嵌入字体
      }).then((base64: any) => {
          // 上传图片不入库
          uploadFile({
            imageBase64: base64.src,
            relatedId: '',
          }).then((res: any) => {
            updatemapinfo({
              ...mapinfo,
              mapCover: res.data,
            }).then(res => {
              bus.emit('saveCoverover', false);
              if (res.status == 200) {
                // console.log('课程地图设置封面成功！');
              }
              setLoading(false);
            });
          });
        })
        .catch((err: any) => {
          setLoading(false);
          console.log(t('生成课程地图封面失败！') + err);
        });
    }

  };

  // 图谱渲染完毕后 如果是从首页跳转过来的自动打开搜索页面
  const keywordchange = () => {
    if (query.keyword && query.keyword != '') {
      setInputtext(query.keyword);
      setGraphmap(graph);
      setVisible(6);
      setQuerytype('0');
      searchref.current.querynode();
    }
  };

  // 导出excel
  const exportmaptoexcel = async () => {
    let msg = message.loading(t('正在导出excel...'));
    await saveContent(false);
    exportexcel(newmapid).then((res: any) => {
      msg();
      const blobURL = window.URL.createObjectURL(res);
      // 创建a标签，用于跳转至下载链接
      const tempLink = document.createElement('a');
      tempLink.style.display = 'none';
      tempLink.href = blobURL;
      tempLink.setAttribute('download', `${mapinfo.mapName}.xlsx`);
      // 兼容：某些浏览器不支持HTML5的download属性
      if (typeof tempLink.download === 'undefined') {
        tempLink.setAttribute('target', '_blank');
      }
      // 挂载a标签
      document.body.appendChild(tempLink);
      tempLink.click();
      document.body.removeChild(tempLink);
      // 释放blob URL地址
      window.URL.revokeObjectURL(blobURL);
    });
  };

  // 展示当前的模块视图
  const showmodule = (id: string) => {
    setSelectmodule(id);
    if (id == 'all') {
      let allnode = graph.getNodes();
      graph.centerCell(allnode[0]);
      allnode.forEach((element: any) => {
        element.attr('foreignObject/opacity', 1);
      });
      let alledges = graph.getEdges();
      alledges.forEach((element: any) => {
        element.attr('line/opacity', 1);
      });
    } else {
      let node = graph.getCellById(id);
      graph.centerCell(node);
      // 获取后续节点
      let nextnode = graph.getSuccessors(node);
      let shownode = [node, ...nextnode];
      let shownodeids = shownode.map((item) => item.id);
      let allnode = graph.getNodes();
      allnode.forEach((element: any) => {
        if (shownodeids.includes(element.id)) {
          // 设置节点透明
          element.attr('foreignObject/opacity', 1);
        } else {
          element.attr('foreignObject/opacity', 0.2);
        }
      });
      let alledges = graph.getEdges();
      alledges.forEach((element: any) => {
        if (shownodeids.includes(element.source.cell)) {
          // 设置节点透明
          element.attr('line/opacity', 1);
        } else {
          element.attr('line/opacity', 0.2);
        }
      });

    }
  }

  const addlink = (item: any) => {
    if(item.key == 5){
      if(selectnode.length == 1 && selectnode[0].getData().type == 5){
        selectrelation = item.key;
        startnode = selectnode[0];
        message.info(t('请再选择一个教学模块作为终点'));
      }else{
        message.info(t('请先选择一个教学模块作为起点'));
      }
    }else{
      if(selectnode.length >= 1 && selectnode[0].getData().type == 2){
        selectrelation = item.key;
        startnode = selectnode[0];
        message.info(t('请再选择一个知识节点作为终点'));
      }else{
        message.error(t('请先选择一个知识节点作为起点'));
      }
    }
  }

  const handleSearch = () => {
    setGraphmap(graph);
    setVisible(6);
    setQuerytype('0');
    searchref.current.querynode();
  };

  const handleSave = () => {
    saveContent(false);
    saveCover();
    setLoading(true);
  };

  return (
    <div
      className="editmap_box"
      onClick={() => {
        clicknum.current = clicknum.current + 1;
      }}
    >
      {showrete && (
        <div className="jindu_box">
          <div style={{ width: '200px', height: '100%' }}>
            <ProgressBox
              label="目标达成度"
              data={noderate?.achievingRateTotal}
              color="#549CFF"
              tip="资源、试题或其他教学活动完成情况按考核占比权重取和"
            ></ProgressBox>
          </div>
          <div style={{ width: '200px', height: '100%' }}>
            <ProgressBox
              label="完成率"
              data={noderate?.finishRateTotal}
              color="#44D6E5"
              tip="资源完成数与总数占比"
            ></ProgressBox>
          </div>
          <div style={{ width: '200px', height: '100%' }}>
            <ProgressBox
              label="掌握率"
              data={noderate?.masterRateTotal}
              color="#F3B764"
              tip="试题按难度、认知层次计算的正确率"
            ></ProgressBox>
          </div>
        </div>
      )}

      {/* 状态锁 */}
      <MapLockStatus
        islock={islock}
        hasRole={hasRole}
        isdelete={isdelete}
        isprocess={isprocess}
        editUser={editUser}
      />

      {/* <div className="divider"></div> */}
      <div
        className="mapview"
        style={(() => {
          //0是工具端  1是老师端打开  2是学生端
          if (perviewtype == 0) {
            return {
              height: '100%',
            };
          } else if (perviewtype == 1) {
            return {
              height: '100%',
            };
          } else if (perviewtype == 2) {
            // 判断是不是预览页面
            if (pathname == '/perviewemap') {
              return {
                height: showSearch ? 'calc(100% - 85px)': 'calc(100% - 30px)'
              };
            } else {
              return {
                height: '100%',
              };
            }
          } else if (perviewtype == 3) {
            return {
              // height: 'calc(100% - 70px)',
              height:'100%'
            };
          } else {
            return {
              height: 'calc(100% - 20px)',
            };
          }
        })()}
      >
        <div className="mapdetail">
          <div ref={ref} className="map_box"></div>
          {/* 搜索栏 */}
          <SearchBox
            perviewtype={perviewtype}
            query={query}
            selectmodule={selectmodule}
            allmodule={allmodule}
            selecttype={selecttype}
            inputtext={inputtext}
            querytype={querytype}
            showsave={showsave}
            loading={loading}
            userInfo={userInfo}
            userroles={userroles}
            setSelecttype={setSelecttype}
            setInputtext={setInputtext}
            showmodule={showmodule}
            typeonselect={typeonselect}
            handleSearch={handleSearch}
            handleSave={handleSave}
            allLabels={allLabels}
          />
          {/* 左侧放大缩小重新布局统计等按钮 */}
          <Mapoperation
            graph={graph}
            setStatisticsTime={setStatisticsTime}
            statisticsTime={statisticsTime}
            zoomdom={zoomdom}
            expandall={expandall}
            maplayout={maplayout}
          />
          {/* 底部工具栏 */}
          {(perviewtype == 0 || perviewtype == 1 || perviewtype == 3) && (
            <MapToolbar
              key="maptoolbar"
              perviewtype={perviewtype}
              selectnode={selectnode}
              parameterConfig={parameterConfig}
              userroles={userroles}
              query={query}
              graph={graph}
              restore={restore}
              newmapid={newmapid}
              kuangxuan={kuangxuan}
              setVisible={setVisible}
              setConversionVisible={setConversionVisible}
              addChild={addChild}
              updatType={updatType}
              addlink={addlink}
              uodatayinandian={uodatayinandian}
              uodatabykey={uodatabykey}
              deletenode={deletenode}
              multipleSelection={multipleSelection}
              setGraphmap={()=>{
                setGraphmap(graph);
              }}
              setDrawerdata={setDrawerdata}
              setExpandnode={setExpandnode}
              setTemptime={setTemptime}
              exportmaptoexcel={exportmaptoexcel}
              relationarr={relationarr}
              updatalabel={updatalabel}
              allLabels={allLabels}
              setAddtype={setAddtype}
            />
          )}
        </div>
        {/* 节点详情 */}
        <Drawer
          placement="right"
          mask={false}
          title={
            <div
              style={{
                width: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
            >
              {/* 如果是从知识点达成度跳转过来的就显示返回按钮 */}
              {orgfrom == 12 ? (
                <Button
                  type="link"
                  style={{ color: '#549CFF' }}
                  icon={<LeftOutlined />}
                  onClick={() => {
                    setVisible(12);
                    setOrgFrom(0);
                  }}
                >
                  {t('返回')}
                </Button>
              ) : (
                <div></div>
              )}

              <CloseOutlined
                style={{ cursor: 'pointer', color: '#838a9d' }}
                onClick={() => {
                  setVisible(0);
                  setDrawerdata(null);
                  setOrgFrom(0);
                  setPreviewEntity({});
                  setSelectnode([]);
                }}
              />
            </div>
          }
          closable={false}
          onClose={() => {
            setVisible(0);
            setDrawerdata(null);
          }}
          visible={visible == 1}
          getContainer={false}
          style={{ position: 'absolute' }}
          width={wholeDrawer ? '100%' : '600px'}
          className={wholeDrawer ? 'custom_drawer' : 'drawer'}
        >

          {wholeDrawer ? (
            <WholeRdrawer
              graph={graph}
              key={drawerdata?.id}
              x6node={drawerdata}
              setDrawerdata={setDrawerdata}
              previewEntity={previewEntity}
              setTotalTime={setTotalTime}
              totalTime={totalTime}
              setCurrentTime={setCurrentTime}
              currentTime={currentTime}
              setPreviewEntity={setPreviewEntity}
              visible={visible}
              temptime={temptime}
              expandnode={expandnode}
              selectNode={selectnode}
              setSelectNode={setSelectnode}
              onback={() => setDrawerdata(null)}
              updatanodecompare={updatanodecompare}
              centerednode={(e: any) => centerednode(e)}
              updatanode={updatanode}
              perviewtype={perviewtype}
              bindCourseCode={bindCourseCode}
              mapid={newmapid}
              courseid={perviewtype == 1 || perviewtype == 2 ? courseid : null}
              coursename={coursename}
              courseCode={couresSyllabusCode || bindCourseId || ''}
              isedit={perviewtype == 1 || perviewtype == 0}
              setVisible={(e: number) => setVisible(e)}
              isMicroMajor={query.type == 'micromajor' || query.type == 'microMajor'}
            ></WholeRdrawer>
          ) : (
            <Rdrawer
              deletedNodes={deletedNodes}
              graph={graph}
              key={drawerdata?.id}
              x6node={drawerdata}
              visible={visible}
              setTotalTime={setTotalTime}
              totalTime={totalTime}
              setCurrentTime={setCurrentTime}
              currentTime={currentTime}
              onback={() => setDrawerdata(null)}
              updatanodecompare={updatanodecompare}
              centerednode={(e: any) => centerednode(e)}
              previewEntity={previewEntity}
              setPreviewEntity={setPreviewEntity}
              updatanode={updatanode}
              notrecord={notrecord}
              perviewtype={perviewtype}
              mapid={newmapid}
              bindCourseCode={bindCourseCode}
              setBindCourseCode={setBindCourseCode}
              courseid={perviewtype == 1 || perviewtype == 2 ? courseid : null}
              coursename={coursename}
              isedit={perviewtype == 1 || perviewtype == 0}
              setVisible={(e: number) => setVisible(e)}
              courseCode={couresSyllabusCode || bindCourseId || ''}
              // checkNode={checkedNodeDetail}
              nodeList={nodeListInfo.current}
              isMain={isMain}
              isEditMap={true}
              isMicroMajor={query.type == 'micromajor' || query.type == 'microMajor'}
              knowledgeDiscriminateList={knowledgeDiscriminateList}
              discriminateListChange={(e:any)=>{
                setKnowledgeDiscriminateList(e)
              }}
            ></Rdrawer>
          )}
          {wholeDrawer ? (
            <img
              onClick={() => setWholeDrawer(false)}
              className="arrow"
              src={require('@/assets/imgs/coursemap/right_arrow.png')}
            />
          ) : (
            <img
              onClick={() => {
                setWholeDrawer(true)
                setTemptime(new Date().getTime());
              }}
              className="arrow"
              src={require('@/assets/imgs/coursemap/left_arrow.png')}
            />
          )}
        </Drawer>

        {/* 对比辨析是2个不同的组件 但是组件里面的内容是一样的 */}
        <Drawer
          placement="right"
          mask={false}
          closable={true}
          onClose={() => {
            setVisible(0);
            setDrawerdata(null);
          }}
          title={false}
          visible={visible == 4 || visible == 8}
          getContainer={false}
          style={{ position: 'absolute' }}
          width="600px"
        >
          {/* 不管外面怎么变化这里始终是传的mapid  后端要求在 教师编辑课程地图的时候传递 课程的id  在里面通过场景值 perviewtype 来判断 具体参数参考上面 */}
          {showSearch &&<Comparativeanalysis
            key="comparativeanalysis"
            graph={graph}
            selectnode={drawerdata}
            visible={visible}
            mapid={newmapid}
            perviewtype={perviewtype}
            updatanodecompare={updatanodecompare}
            knowledgeDiscriminateList={knowledgeDiscriminateList}
            onChange={(e)=>{
              setKnowledgeDiscriminateList(e)
            }}
          />}
        </Drawer>

        {/* 绑定管理 */}
        <Bindmange
          key="bingdmange"
          graph={graphmap}
          mapid={newmapid}
          courseid={courseid}
          coursename={coursename}
          updatanode={updatanode}
          visible={visible}
          setVisible={(e: number) => {
            setVisible(e);
            // 关闭绑定管理触发绑定事件
            if(e == 0){
              saveContent(false);
            }
          }}
        ></Bindmange>

        {/* 搜索管理 */}
        <Search
          ref={searchref}
          graph={graph}
          querytype={querytype}
          centerednode={(e: any) => centerednode(e)}
          selecttype={selecttype}
          inputtext={inputtext}
          visible={visible}
          setVisible={(e: number) => setVisible(e)}
        ></Search>

        {/* 不管外面怎么变化这里始终是传的mapid  后端要求在 教师编辑课程地图的时候传递 课程的id  在里面通过场景值 perviewtype 来判断 具体参数参考上面 */}
        {/* 导入excel word  perviewtype == 0 是在课程地图工具端 创建地图进行编辑  1 是在编辑课程里面进行地图编辑  2是学生端不做处理 */}
        <ImportModal
          perviewtype={perviewtype}
          mapid={newmapid}
          visible={visible}
          onClose={() => setVisible(0)}
          onSuccess={() => {
            initdata();
            getflushmap(newmapid);
          }}
        ></ImportModal>

        {/* 根据资源转换成课程地图 */}
        <UploadModal
          perviewtype={perviewtype}
          mapid={newmapid}
          visible={visible}
          onClose={() => setVisible(0)}
          courseid={courseid || ''}
          coursename={coursename || ''}
          onSuccess={() => {
            initdata();
            getflushmap(newmapid);
          }}
        ></UploadModal>

        {/* 课程转换弹窗 */}
        {conversionVisible ? (
          <Conversion
            perviewtype={perviewtype}
            mapid={newmapid}
            graph={graph}
            onCancel={() => {
              setConversionVisible(false);
              initdata();
            }}
          ></Conversion>
        ) : null}

        {/* 地图大纲 */}
        <Outline
          graph={graph}
          temptime={temptime}
          expandnode={expandnode}
          visible={visible}
          setVisible={() => {
            setVisible(0);
            setTemptime(0);
            setGraphmap(null);
          }}
          centernode={(info: any) => {
            centerednode(info);
            // 根据id获取节点
            const node = graph.getCell(info);
            // 设置选中
            graph.select(node);
          }}
          updateindex={updateindex}
          selectnode={selectnode}
        ></Outline>

        {/* 批量添加 */}
        <Modal
          title={t('批量添加节点')}
          open={batchaddnode}
          onOk={() => {
            batchaddnodefun();
          }}
          onCancel={() => setBatchaddnode(false)}
          centered
        >
          <Select
            placeholder={t('请选择批量添加的类型')}
            value={batchaddtype}
            style={{ width: '100%' }}
            onChange={e => setBatchaddtype(e)}
          >
            <Option
              value={1}
              disabled={(() => {
                if (selectnode.length > 0) {
                  return selectnode[0].store.data.data.type == 2 ? true : false;
                } else {
                  return false;
                }
              })()}
            >
              {t('分类节点')}
            </Option>
            <Option value={2}>{t('知识节点')}</Option>
          </Select>
          <Select
            mode="tags"
            value={batchadddata}
            style={{ width: '100%', marginTop: '20px' }}
            placeholder={t(
              '按回车隔开各节点名称，若需粘贴多个节点，需复制后再打开此窗口',
            )}
            onChange={e => {
              setBatchadddata(e)
            }}
            open={false}
          />
          <Checkbox style={{ marginTop: '20px' }} onChange={(e) => {
            formatPaste(clipboardData, e.target.checked);
          }}>自动去除章节编号</Checkbox>
        </Modal>

        {/* 地图保存记录 */}
        <Maprecord
          visible={visible}
          mapid={newmapid}
          onCancel={() => {
            setVisible(0);
          }}
          onupdata={(res: any) => {
            setRestore(true);
            setKnowledgeDiscriminateList(res.knowledgeDiscriminateList);
            iflayout(res);
          }}
        ></Maprecord>

        {/* 知识点达成度 */}
        <Achievementdegree
          visible={visible}
          centerednode={(id: any) => {
            centerednode(id);
            setVisible(1);
            // 根据id获取节点
            setDrawerdata(graph.getCell(id));
            setGraphmap(graph);
            setOrgFrom(12);
          }}
          courseid={courseid}
          mapid={newmapid}
          perviewtype={perviewtype}
          onCancel={() => setVisible(0)}
        ></Achievementdegree>

        {/* 选择课程 */}
        {perviewtype != 2 && visible == 13 && (
          <Selectcourse
            visible={visible}
            onCancel={() => setVisible(0)}
            onOk={(coursearr: any) => {
              coursearr.forEach((element: any) => {
                addChild(selectnode, 3, element.courseName, {
                  courseId: element.courseId,
                });
              });
              setVisible(0);
            }}
          ></Selectcourse>
        )}

        {/* 课程详情 */}
        <CourseInfo
          visible={visible}
          courseid={selectcourseid}
          onCancel={() => setVisible(0)}
        ></CourseInfo>
        {/* 导入xmind  perviewtype == 0 是在课程地图工具端 创建地图进行编辑  1 是在编辑课程里面进行地图编辑  2是学生端不做处理 */}
        <ImpportXmind
          perviewtype={perviewtype}
          mapid={newmapid}
          visible={visible}
          onClose={() => setVisible(0)}
          onSuccess={() => {
            initdata();
            getflushmap(newmapid);
          }}
        ></ImpportXmind>
        {/* 引用地图弹窗 */}
        <SelectMap
          courseid={courseid}
          perviewtype={perviewtype}
          mapid={newmapid}
          visible={visible}
          onClose={() => setVisible(0)}
          onSuccess={(value?: any) => {
            console.log(value, 'value');
            if (!!value) {
              // 手动查询知谱空间的地图数据并绑定
              getMapDataByGroupId(value);
              getflushmap(newmapid);
              return;
            }
            initdata();
            getflushmap(newmapid);
          }}
        ></SelectMap>
        {/* 添加关系弹窗 */}
        <LinkMap
          mapid={newmapid}
          graph={graph}
          selectnode={selectnode}
          updatanode={updatanode}
          visible={visible}
          onClose={() => setVisible(0)}
          onSuccess={() => { }}
        ></LinkMap>
        {/* 展示跨课关系 */}
        {visible == 18 && (
          <RelationMap
            mapid={newmapid}
            graph={graph}
            selectnode={selectnode}
            visible={visible}
            onClose={() => setVisible(0)}
          ></RelationMap>
        )}

        {/* 导入xmind  perviewtype == 0 是在课程地图工具端 创建地图进行编辑  1 是在编辑课程里面进行地图编辑  2是学生端不做处理 */}
        {visible == 19 && (
          <ImpportOutline
            perviewtype={perviewtype}
            mapid={newmapid}
            visible={visible}
            onClose={() => setVisible(0)}
            onSuccess={() => {
              initdata();
              getflushmap(newmapid);
            }}
          ></ImpportOutline>
        )}

        {/* 添加教学模块 */}
        <Teachingmodule
          visible={visible}
          setVisible={setVisible}
          closable={true}
          ref={TeachingmoduleRef}
          moudelNodedata={moudelNodedata.current}
          graph={graph}
          isedit={perviewtype == 1 || perviewtype == 0}
          onFinish={(e: any) => {
            let node = graph.getCellById(x6treedata.nodes[0].id);
            addChild(node, 5, e.name, {
              type: 5,
              teachingModuleManager: e.teacher,
              score:e.score,
              studytime:e.time?.length ?  [e.time[0].format('YYYY-MM-DD'),e.time[1].format('YYYY-MM-DD')] : []
            });
            setVisible(0);
          }}
          onUpdata={(id: any, e: any) => {
            updatanode(id, {
              label: e.name,
              teachingModuleManager: e.teacher,
              score:e.score,
              studytime:e.time?.length ?  [e.time[0].format('YYYY-MM-DD'),e.time[1].format('YYYY-MM-DD')] : []
            });
            setVisible(0);
          }}>
        </Teachingmodule>
        {/* 添加其他地图 */}
        {visible == 21 && (
          <Addothermap
            selectNode={selectnode}
            mapid={newmapid}
            courseId={courseid}
            setVisible={setVisible}
            graph={graph}
            onSuccess={({ nodes, edges }: any) => {
              let newnodes: any = [];
              let newedegs: any = [];

              nodes.forEach((element: any) => {
                let node1 = graph.createNode(element);
                newnodes.push(node1);
              });

              edges.forEach((element: any) => {
                let edeg1 = graph.createEdge(element);
                newedegs.push(edeg1);
              });
              graph.startBatch('Addothermap');
              graph.addNodes(newnodes);
              graph.addEdges(newedegs);
              maplayout();
              graph.stopBatch('Addothermap');
            }}
          ></Addothermap>
        )}
        {/* 同步大纲教学内容 */}
        {visible == 22 && (
          <SynchronizedTeaching
            visible={visible == 22}
            onSuccess={() => {
              setVisible(0);
              initdata();
              getflushmap(newmapid);
              handleRefreshMapInfo();
            }}
            onClose={() => setVisible(0)}
            mapId={newmapid}
            checkedId={couresSyllabusCode || bindCourseId || ''}
            isEditMap={true}
            reShowObj={{
              grade: bindOutlineInfo?.trainingGrade,
              major: bindOutlineInfo?.applicableMajorCode,
              majorName: bindOutlineInfo?.applicableMajorName
            }}
          />
        )}

        {/* 一键绑定 */}
        {visible == 23 && (
          <ResourceBinding
            visible={visible == 23}
            graph={graph}
            onSuccess={() => {
              setVisible(0);
              // 打开绑定管理
              setVisible(5);
            }}
            onClose={() => {
              setVisible(0);
            }}
            updatanode={updatanode}
          />
        )}

        {/* 按照视频生成 */}
        {visible == 24 && (
          <SelectResources visible={true} selectnode={selectnode} addtype={addtype} graph={graph} onCancel={()=>setVisible(0)} onOk={(newmapdata:any)=>{
            if(addtype === 0){
              iflayout(newmapdata);              
            }else{
              const { nodes, edges } = newmapdata;
              let newnodes: any = [];
              let newedegs: any = [];

              nodes.forEach((element: any) => {
                let node1 = graph.createNode(element);
                newnodes.push(node1);
              });

              edges.forEach((element: any) => {
                let edeg1 = graph.createEdge(element);
                newedegs.push(edeg1);
              });
              graph.startBatch('Addtorecourse');
              graph.addNodes(newnodes);
              graph.addEdges(newedegs);
              maplayout();
              graph.stopBatch('Addtorecourse');
            }
            setVisible(0);
          }} />
        )}
        {/* 按照在线课堂生成 */}
        {visible == 25 && (
          <SelectRecording addtype={addtype} selectnode={selectnode} graph={graph} onCancel={()=>setVisible(0)} onOk={(newmapdata:any)=>{
            if(addtype === 0){
              iflayout(newmapdata);              
            }else{
              const { nodes, edges } = newmapdata;
              let newnodes: any = [];
              let newedegs: any = [];

              nodes.forEach((element: any) => {
                let node1 = graph.createNode(element);
                newnodes.push(node1);
              });

              edges.forEach((element: any) => {
                let edeg1 = graph.createEdge(element);
                newedegs.push(edeg1);
              });
              graph.startBatch('Addtorecourse');
              graph.addNodes(newnodes);
              graph.addEdges(newedegs);
              maplayout();
              graph.stopBatch('Addtorecourse');
            }
            setVisible(0);
          }} />
        )}
      </div>
    </div>
  );
};

export default Editmap;
