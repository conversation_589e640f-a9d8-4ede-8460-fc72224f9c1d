import React, { FC, useState, useEffect } from 'react';
import './index.less';
import {
  getteacherlist,
  addteacher,
  updateteacher,
  deleteteacher,
  userOriginal
} from
  '@/api/teacher';
import { querymapteacher, deletemapteacher, updatemapinfo, addmapteacher } from '@/api/coursemap';
import { useSelector, useLocation, useDispatch, useHistory } from 'umi';
import {
  Select,
  Table,
  Button,
  Modal,
  Form,
  Checkbox,
  message,
  Input,
  Avatar,
  Space
} from
  'antd';
import { FormOutlined, DeleteOutlined, UserOutlined } from '@ant-design/icons';
import { IconFont } from '@/components/iconFont';
import AddTeacherModalV2Spoc from '@/components/AddTeacherModalV2Spoc';
import useLocale from '@/hooks/useLocale';
import main from 'sha1';
// import AddTeacherModal from '@/components/AddTeacherModal'
const { Option } = Select;
const { Search } = Input;
const TeachingTeam: FC<{}> = () => {
  const { t } = useLocale();
  const { query: queryParams }: any = useLocation();
  const history: any = useHistory();
  const [editform] = Form.useForm();
  const [deleteVis, setDeleteVis] = useState<boolean>(false);
  const [editVis, setEditVis] = useState<boolean>(false);
  const [addTeacherVis, setAddTeacherVis] = useState<boolean>(false);
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false);
  const [addLoading, setAddLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<Array<ITeacher.IupdataTeacherParams>>([]);
  const [operationData, setOperationData] = useState<any>([]);
  const [addOrEdit, setAddOrEdit] = useState<string>('add');
  const [batchOrAlone, setBatchOrAlone] = useState<string>('alone');
  const [newSelectedRowKeys, setNewSelectedRowKeys] = useState<Array<any>>([]);
  const [teachSearchLoad, setTeachSearchLoad] = useState<boolean>(false);
  const [addTeacher, setAddTeacher] = useState<any>([]);
  const [keyword, setKeyword] = useState('');
  const [query, setQuery] = useState<any>({
    page: 1,
    size: 10,
    include_roles: true
  });
  const dispatch = useDispatch();
  const [dataList, setDataList] = useState([]);
  // 共享权限范围
  const [shareRange, setShareRange] = useState<any[]>([]);
  const [total, setTotal] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [step, setStep] = useState(1);
  const mapinfo: any = useSelector<any, any>(
    (state: { coursemap: { mapinfo: any } }) => state.coursemap.mapinfo,
  );
  const options = [
    { label: t("修改基本信息"), value: 'modifyBasicInfo' },
    { label: t("修改章节"), value: 'chapterRevision' },
    { label: t("修改课程公告"), value: 'modifyCourseNotice' },
    { label: t("学生管理"), value: 'studentManagement' },
    { label: t("教学团队管理"), value: 'teachingTeamManage' },
    { label: t("修改学习设置"), value: 'modifySettings' }];

  useEffect(() => {
    getTeacher();
  }, []);

  useEffect(() => {
    const defaultShareRange = mapinfo?.releaseScope?.includes(',') ? mapinfo?.releaseScope?.split(',') : [];
    // console.info('mapinfo', mapinfo, defaultShareRange);
    setShareRange(defaultShareRange.map((item: any) => Number(item)));
  }, [mapinfo])

  const getTeacher = () => {
    // 查询所有的管理员
    querymapteacher({
      mapId: history.location.query.id
    }).then((res: any) => {
      if (res && res.status === 200) {
        setDataSource(res.data);
      }
    });

    // getteacherlist(history.location.query.id).then((res: any) => {
    //   if (res && res.status === 200) {
    //     setDataSource(res.data);
    //   }
    // });
  };
  const columns: any = [
    {
      title: t("头像"),
      dataIndex: 'photo',
      key: 'photo',
      align: 'center',
      width: 100,
      render: (text: any, record: any) => {
        if (record.avatarUrl) {
          return <Avatar shape="square" size={64} src={record.avatarUrl} icon={<UserOutlined />} />;
        } else {
          return <Avatar shape="square" size={64} icon={<UserOutlined />} />;
        }
      }
    },
    {
      title: t("姓名"),
      dataIndex: 'userName',
      key: 'userName',
      align: 'center'
    },
    {
      title: t("学号/工号"),
      dataIndex: 'userCode',
      key: 'userCode',
      align: 'center'
    },
    {
      title: t("学院"),
      dataIndex: 'college',
      key: 'college',
      align: 'center'
    },
    // {
    //   title: '性别',
    //   dataIndex: 'sex',
    //   key: 'sex',
    //   align: 'center',
    // },

    {
      title: t("课程角色"),
      dataIndex: 'role',
      key: 'role',
      align: 'center',
      render: (text: number) => {
        if (text == 1) {
          return t("管理员");
        } else if (text == 2) {
          return t("编辑者");
        }
      }
    },
    {
      title: t("操作"),
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      render: (text: any, record: any) =>
        <div className="action">
          <IconFont type='iconedit' onClick={() => openEditModal(record)} />
          <IconFont type='icondelete'
            onClick={() => {
              setDeleteVis(true);
              setOperationData(record);
              setBatchOrAlone('alone');
            }} />

        </div>

    }];

  const rowSelection = {
    onChange: (newSelectedRowKeys: Array<any>, selectedRows: any) => {
      setNewSelectedRowKeys(newSelectedRowKeys);
    }
  };
  const batchDelete = () => {
    if (newSelectedRowKeys.length) {
      setDeleteVis(true);
      setBatchOrAlone('batch');
    } else {
      message.info(t('请选择要删除的教师'));
    }
  };
  const handleDeleteOk = () => {
    let data: string[] = [];
    if (batchOrAlone === 'alone') {
      data = [operationData.id];
    } else {
      data = [...newSelectedRowKeys];
    }

    deletemapteacher(data).then((res: any) => {
      if (res && res.status === 200) {
        message.success(t('删除成功'));
      }
      setDeleteVis(false);
      getTeacher();
      setNewSelectedRowKeys([]);
    });
    // deleteteacher(history.location.query.id, data).then((res: any) => {
    //   if (res && res.status === 200) {
    //     message.success('删除成功');
    //   }
    //   setDeleteVis(false);
    //   getTeacher();
    //   setNewSelectedRowKeys([]);
    // });

  };
  const openEditModal = (record: any) => {
    setAddOrEdit('edit');
    // let data: string[] = [];
    setEditVis(true);
    setOperationData(record);
    // Object.keys(record.authority).forEach(item => {
    //   if (record.authority[item] === 1) {
    //     data.push(item);
    //   }
    // });
    editform.setFieldsValue({
      category: record.role
      // jurisdiction: data,
    });
  };
  const handleEditOk = () => {
    editform.validateFields().then((item) => {
      if (addOrEdit === 'add') {
        if (addTeacher.length) {
          let data: any = {
            modifyBasicInfo: 0,
            chapterRevision: 0,
            modifyCourseNotice: 0,
            studentManagement: 0,
            teachingTeamManage: 0,
            modifySettings: 0
          };
          item.jurisdiction &&
            item.jurisdiction.forEach((item: string) => {
              data[item] = 1;
            });
          addteacher(history.location.query.id, [
            {
              userCode: addTeacher[0].userCode,
              name: addTeacher[0].nickName,
              jobNumber: addTeacher[0].account,
              sex: addTeacher[0].sex,
              avatarUrl: addTeacher[0].avatarUrl,
              type: item.category,
              authority: data,
              position: addTeacher[0].position
            }]).
            then((res: any) => {
              if (res && res.status === 200) {
                message.success(t('添加成功'));
              } else if (res && res.status === 400) {
                message.error(res.message);
              } else {
                message.error(t('添加失败'));
              }
              closeaddmodal();
              getTeacher();
            });
        } else {
          message.error(t('请输入正确的工号/学号'));
        }
      } else {
        let editdatd = editform.getFieldsValue();
        // let data: any = {
        //   modifyBasicInfo: 0,
        //   chapterRevision: 0,
        //   modifyCourseNotice: 0,
        //   studentManagement: 0,
        //   teachingTeamManage: 0,
        //   modifySettings: 0,
        // };
        // editdatd.jurisdiction.forEach((item: string) => {
        //   data[item] = 1;
        // });
        // const isEdit = Object.keys(operationData?.authority ?? {}).some(item => data[item] !== operationData?.authority?.[item]) || operationData?.type !== editdatd.category
        const isEdit = operationData?.type !== editdatd.category;
        if (!isEdit) {
          setEditVis(false);
          message.success(t('修改成功'));
          return;
        }
        addmapteacher([
          {
            ...operationData,
            role: editdatd.category
          }]).
          then((res: any) => {
            if (res && res.message === 'OK') {
              message.success(t('修改成功'));
            } else {
              message.error(t('添加失败'));
            }
            setEditVis(false);
            getTeacher();
          });

        // updateteacher(history.location.query.id, {
        //   userCode: operationData?.userCode,
        //   name: operationData?.name,
        //   jobNumber: operationData?.jobNumber,
        //   sex: operationData?.sex,
        //   id: operationData?.id,
        //   avatarUrl: operationData?.avatarUrl,
        //   type: editdatd.category,
        //   // authority: data,
        //   position: operationData?.position,
        // }).then((res: any) => {
        //   if (res && res.message === 'OK') {
        //     message.success('修改成功');
        //   } else {
        //     message.error('添加失败');
        //   }
        //   setEditVis(false);
        //   getTeacher();
        // });
      }
    });
  };
  const fetchDataList = () => {
    setTeachSearchLoad(true);
    userOriginal({
      ...query,
      keyword
    }).then((res: any) => {
      setTeachSearchLoad(false);
      if (res && res.errorCode === 'course_0000_0000') {
        setDataList(res.extendMessage.results);
        setTotal(res.extendMessage.recordTotal);
      }
    });
  };
  const closeaddmodal = () => {
    setEditVis(false);
    setAddTeacher([]);
  };
  const handleNextStep = () => {
    if (addTeacher.length === 0) {
      message.error(t('请选择教师'));
      return;
    }
    setStep(2);
  };

  //刷新地图数据
  const handleRefreshMapInfo = () => {
    dispatch({
      type: 'coursemap/fetchMapInfo',
      payload: {
        params: {
          mapId: queryParams.id,
        },
      },
    });
  };

  const changeShareParams = (value: any[]) => {
    let checkValue = [];

    if (value[value.length - 1] === 2) {
      checkValue = value.filter(a => a !== 3); 
    } else if (value[value.length - 1] === 3) {
      checkValue = value.filter(a => a !== 2); 
    } else {
      checkValue = value
    }
    setShareRange(checkValue);
    updatemapinfo({ id: queryParams.id, releaseScope: checkValue.join(',') }).then((res: any) => {
      if (res?.message === 'OK') {
        handleRefreshMapInfo();
      }
    });
  }

  return (
    <div className="teaching-team">
      {mapinfo?.isShow === 2 && <div className='share-primess-style'>
        <div className='title'>共建权限</div>
        <div className='check-content'>
          <Checkbox.Group onChange={changeShareParams} value={shareRange}>
            <Checkbox value={0}>{t('仅门户查看')}</Checkbox>
            <Checkbox value={1}>{t('可引用编辑')}</Checkbox>
            <Checkbox value={2}>{t('修改同步至发布对象')}</Checkbox>
            <Checkbox value={3}>{t('不修改同步至发布对象')}</Checkbox>
          </Checkbox.Group>
        </div>
      </div>}
      <div className="top-bottom">
        {/* <Button
           type="primary"
           style={{ width: 100 }}
           onClick={() => {
             setEditVis(true);
             setAddOrEdit('add');
             editform.resetFields();
           }}
          >
           添加人员
          </Button> */}
        {mapinfo?.isShow === 2 && <div className='title'>共建权限</div>}
        <div className='btns-style'>
          <Button
            type="primary"
            style={{ width: 100 }}
            onClick={() => {
              setAddTeacherVis(true);
              editform.resetFields();
            }}>
            {t("添加人员")}

          </Button>
          <Button style={{ width: 100 }} onClick={batchDelete}>{t("删除")}</Button>
        </div>
      </div>
      <div className='create-common-style' style={{ height: mapinfo?.isShow === 2 ? 'calc(100% - 180px)' : 'calc(100% - 60px)'}}>
        <Table
          style={{ width: '100%', height: '100%' }}
          dataSource={dataSource}
          columns={columns}
          rowKey="id"
          pagination={false}
          rowSelection={{
            ...rowSelection
          }}
          scroll={{ y: 'calc(100% - 60px)' }} />
      </div>

      <Modal
        title={t("警告?")}
        visible={deleteVis}
        confirmLoading={deleteLoading}
        onOk={() => handleDeleteOk()}
        onCancel={() => setDeleteVis(false)}>

        <div className="deleteprompt">
          <div>{t("确定是否移除该教师？")}</div>
        </div>
      </Modal>
      <Modal
        width={400}
        title={t("编辑")}
        visible={editVis}
        confirmLoading={addLoading}
        onOk={() => handleEditOk()}
        onCancel={closeaddmodal}
        footer={
          <div style={{ textAlign: 'center' }}>
            <Space>
              <Button onClick={handleEditOk} type="primary">{t("确定")}

              </Button>
            </Space>
          </div>}>


        <Form labelCol={{ span: 7 }} form={editform}>
          <Form.Item
            label={t("角色")}
            name="category"
            rules={[{ required: true, message: t("请选择角色") }]}>

            <Select style={{ width: 200 }} placeholder={t("请选择角色")}>
              <Option value={1}>{t("管理员")}</Option>
              <Option value={2}>{t("编辑者")}</Option>
            </Select>
          </Form.Item>
          {/* <Form.Item
             label="权限"
             name="jurisdiction"
             rules={[{ required: true, message: '请至少选择一个权限' }]}
            >
             <Checkbox.Group
               options={options}
               // defaultValue={['Pear']}
             />
            </Form.Item> */}
        </Form>
      </Modal>
      <AddTeacherModalV2Spoc
        addOrEdit="add"
        modalVisible={addTeacherVis}
        modalClose={() => setAddTeacherVis(false)}
        relationPeople={dataSource}
        ismapa={true}
        refresh={() => {
          getTeacher();
        }} />

    </div>);

};
export default TeachingTeam;