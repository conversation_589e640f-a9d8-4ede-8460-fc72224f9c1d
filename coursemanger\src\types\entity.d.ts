declare namespace Entity {
  interface IBaseEntity {
    src: string;
    onError: () => void;
    id?: string;
    isAutoplay?: boolean;
    pip?: boolean;
    centerednode?: (id: string) => void;
    knowledge?: any;
    onListener?:any;
    onUpdate?: any;
    cover?: string;
    selectNode?: any;
    finishStatus?: boolean;
    currentTime?: number;
  }

  interface IEntityProps {
    type: 'video' | 'audio' | 'picture' | 'document' | 'point' | null;
    src: string;
    id?: string;
    selectNode?: any;
    centerednode?: (id: string) => void;
    isAutoplay?: boolean;
    pip?: boolean;
    knowledge?: any;
    onListener?:any;
    onUpdate?: any;
    cover?: string;
    finishStatus?: boolean;
    injectData?: any;
  }
}
