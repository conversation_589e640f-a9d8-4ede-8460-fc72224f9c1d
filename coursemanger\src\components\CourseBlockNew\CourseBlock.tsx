import {
  CheckCircleOutlined,
  EllipsisOutlined,
  MinusCircleOutlined,
} from '@ant-design/icons';
import { Checkbox, Popover } from 'antd';
import React, { FC, useState } from 'react';

import useLocale from '@/hooks/useLocale';
import usePermission from '@/hooks/usePermission';
import { CUSTOMER_NPU } from '@/permission/moduleCfg';
import moment from 'moment';
import { history, useSelector } from 'umi';
import { IconFont } from '../iconFont';
import './CourseBolck.less';

interface ICourseBlock {
  item: any;
  isRejected?: boolean;
  onPreview?: () => void;
  onData?: () => void;
  onEdit?: () => void;
  onPublish?: () => void;
  onUnPublish?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
  onLog?: () => void;
}

const hasReview = (courseType: 0 | 1 | 2, parameterConfig: any) => {
  if (
    courseType === 0 &&
    parameterConfig?.microcourse_course_release_review === 'true'
  ) {
    return true;
  } else if (
    courseType === 1 &&
    parameterConfig?.mooc_course_release_review === 'true'
  ) {
    return true;
  } else {
    return false;
  }
};

const CourseBlock: FC<ICourseBlock> = ({
  item,
  isRejected,
  onPreview,
  onEdit,
  onPublish,
  onData,
  onUnPublish,
  onDelete,
  onCopy,
  onLog,
}) => {
  const { t } = useLocale();
  const { location } = history;
  const courseTypeText = [
    t('微课'),
    t('公开课'),
    t('班级课'),
    t('培训课'),
    t('图谱课'),
  ];
  const courseTypeEn = ['micro', 'mooc', 'spoc', 'training', 'map'];
  const { parameterConfig } = useSelector<
    { global: any },
    { buttonPermission: string[]; parameterConfig: any; permission: any }
  >(state => state.global);

  const { mobileFlag } = useSelector<{ config: any }, any>(
    ({ config }) => config,
  );

  const jurisdictionList = useSelector<{ jurisdiction: any }, any>(
    ({ jurisdiction }) => {
      return jurisdiction.jurisdictionList;
    },
  );

  const [moreMenuVisible, setMoreMenuVisible] = useState<boolean>(false);
  const { getOptAuth, getPermission } = usePermission();
  // const [menuVisible,setMenuVisible] = useState(false)
  const isMircromajor = location.pathname.includes('/micromajor/course'); // 微专业课程放开权限
  let ifEdit = getOptAuth('edit', item.entityData?.courseType) || isMircromajor;
  const getMenu = (item: any) => {
    let ifShowPublish = getOptAuth('publish', item.entityData?.courseType);
    let ifShowDelete = getOptAuth('delete', item.entityData?.courseType);
    let ifShowCopy = getOptAuth('copy', item.entityData?.courseType, [
      1,
      2,
      3,
      4,
    ]);
    const btnList = [
      {
        name: t('预览'),
        func: onPreview,
        icon: <IconFont type="iconpreview_icon" />,
        show: true,
      },
      {
        name: t('数据'),
        func: onData,
        icon: <IconFont type="icondata" />,
        show:
          (item.entityData?.courseType === 0 &&
            item.entityData?.publishStatus == 1) ||
          isMircromajor,
      },
      {
        name: t('编辑'),
        func: onEdit,
        icon: <IconFont type="iconedit" />,
        show:
          (ifEdit &&
            (item.entityData?.type_ !== 'biz_sobey_course' ||
              item.entityData?.publishStatus === 0)) ||
          item.entityData?.type_ === 'biz_sobey_video_entity' ||
          isMircromajor,
      },
      {
        name: item.entityData?.publishStatus === 2 ? t('通过') : t('发布'),
        func: onPublish,
        icon: <IconFont type="iconrelease" />,
        show:
          (isRejected && item.entityData?.publishStatus === 2) ||
          ((ifShowPublish ||
            item.entityData?.type_ === 'biz_sobey_video_entity') &&
            item.entityData?.publishStatus == 0) ||
          isMircromajor,
      },
      {
        name: t('下架'),
        func: onUnPublish,
        icon: <IconFont type="iconoffShelf" />,
        show:
          ((ifShowPublish ||
            item.entityData?.type_ === 'biz_sobey_video_entity') &&
            item.entityData?.publishStatus == 1 &&
            !isRejected) ||
          isMircromajor,
      },
      {
        name: isRejected ? t('驳回') : t('撤回'),
        func: onUnPublish,
        icon: <IconFont type="iconrecall" />,
        show:
          (ifShowPublish && item.entityData?.publishStatus == 2) ||
          isMircromajor,
      },
      {
        name: t('复制'),
        func: onCopy,
        icon: <IconFont type="iconcopy" />,
        show: (ifShowCopy && !isRejected) || isMircromajor,
      },
      {
        name: t('删除'),
        func: onDelete,
        icon: <IconFont type="icondelete" />,
        show:
          (ifShowDelete && item.entityData?.publishStatus == 0) ||
          item.entityData?.type_ === 'biz_sobey_video_entity' ||
          isMircromajor,
      },
      {
        name: t('日志'),
        func: onLog,
        icon: <IconFont type="iconlog_icon" />,
        show:
          (isRejected &&
            getPermission(
              ['micro', 'spoc', 'mooc', 'training', 'map'],
              '_course_release_review',
              true,
              courseTypeEn[item.entityData?.courseType],
            )) ||
          isMircromajor,
      },
    ].filter(item => item.show);
    const length = btnList.length;
    const showLength = length > 4 ? 3 : 4;

    return (
      <div
        className="bottom_btn"
        onClick={(e: any) => {
          e.stopPropagation();
          setMoreMenuVisible(false);
        }}
      >
        {btnList.slice(0, showLength).map(item_ => (
          <div onClick={item_.func} key={item_.name}>
            {item_.icon}
            <span className="btn-name">{t(item_.name)}</span>
          </div>
        ))}
        {length > 4 && (
          <Popover
            placement="bottom"
            overlayClassName="course-card-other-btn-wrp"
            content={
              <div>
                {btnList.slice(showLength, length).map(item_ => (
                  <div
                    key={item_.name}
                    className="other-btn-wrp"
                    onClick={item_.func}
                  >
                    <span>{item_.icon}</span>
                    <span className="other-btn-name">{t(item_.name)}</span>
                  </div>
                ))}
              </div>
            }
          >
            <div>
              <EllipsisOutlined />
              <span className="btn-name">{t('更多')}</span>
            </div>
          </Popover>
        )}
      </div>
    );
  };
  const popover = (
    <Popover
      // getPopupContainer={(e:any) => e.parentElement}
      open={moreMenuVisible}
      onOpenChange={(newOpen: boolean) => setMoreMenuVisible(newOpen)}
      content={() => getMenu(item)}
    >
      <EllipsisOutlined
        className="mobile_single_btns"
        title={t('更多操作')}
        onClick={(e: any) => {
          e.preventDefault();
          e.stopPropagation();
          setMoreMenuVisible(!moreMenuVisible);
        }}
      />
    </Popover>
  );
  return (
    <div
      className={`course_block_item ${
        item.entityData?.courseType === 2 ? 'course_spoc_item' : ''
      } ${parameterConfig.target_customer === CUSTOMER_NPU && 'npu'}`}
      // onMouseOver={()=>{
      //   setMenuVisible(true)
      // }}
      // onMouseLeave={()=>{
      //   setMenuVisible(false)
      // }}
      key={item.id}
      onClick={() => {
        if (
          ifEdit &&
          (item.entityData?.type_ !== 'biz_sobey_course' ||
            item.entityData?.publishStatus === 0)
        ) {
          onEdit && onEdit();
        }
      }}
    >
      <div className="img_box">
        <Checkbox
          value={item.entityData?.contentId_}
          onClick={e => e.stopPropagation()}
        />
        <img src={item.entityData?.cover} />
        {parameterConfig.target_customer !== CUSTOMER_NPU && (
          <div className="icon-wrp">
            <div className="left">
              {item.entityData?.live_flag && (
                <div className="icon">
                  <IconFont type="iconplay_back_icon" />
                  <span>课堂回看</span>
                </div>
              )}
              {item.entityData?.recording_flag && (
                <div
                  className="icon"
                  onClick={(e: any) => {
                    e.stopPropagation();
                    window.open(
                      `#/editcourse/coursereview?id=${item.entityData?.contentId_}&type=spoc&${item.entityData?.course_semester_id}`,
                    );
                  }}
                >
                  <IconFont type="iconlive_icon" />
                  <span>课堂直播</span>
                </div>
              )}
            </div>
            {item.entityData?.courseType !== 0 && (
              <div className="right">
                <div className="hits">
                  <IconFont type="iconparticipation" />
                  <span>{item.entityData?.join}</span>
                </div>
              </div>
            )}
          </div>
        )}
        <div
          className="action_icon"
          onClick={e => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          {/* <Dropdown overlay={getMenu(item)} overlayClassName="overlaystyal">
               <EllipsisOutlined />
             </Dropdown> */}
          {!mobileFlag && getMenu(item)}
        </div>
        {item.entityData?.publishStatus == 0 ? (
          item.entityData?.approvalStatus === 1 ? (
            <div className="label_item label_item4">{t('已驳回')}</div>
          ) : (
            <div className="label_item">{t('未发布')}</div>
          )
        ) : item.entityData?.publishStatus == 1 ? (
          <div className="label_item label_item2">{t('已发布')}</div>
        ) : (
          <div className="label_item label_item3">{t('待审核')}</div>
        )}
        {item.entityData?.inside_isbn &&
          ((item.entityData?.courseType == 1 &&
            parameterConfig?.mooc_certification_display == 'true') ||
            (item.entityData?.courseType == 2 &&
              parameterConfig?.spoc_certification_display == 'true')) && (
            <div className="certification">
              <IconFont type="icontongyirenzheng_1" />
              <div>{item.entityData?.certification_type}</div>
            </div>
          )}
      </div>
      <div
        className={`${
          item.entityData?.courseType === 0
            ? 'detail_box_for_micro'
            : 'detail_box'
        } ${item.entityData?.courseType === 2 ? 'spoc_box' : ''}`}
      >
        <div className="title_box">
          {isRejected && (
            <span className={`type-tag`}>
              {courseTypeText[item.entityData?.courseType]}
            </span>
          )}
          <span
            className="title"
            title={`${item.entityData?.name_}${
              item.entityData?.classTeaching
                ? `（${item.entityData?.classTeaching}）`
                : ''
            }`}
          >
            {item.entityData?.name_}
            {item.entityData?.classTeaching &&
              `（${item.entityData?.classTeaching}）`}
          </span>
        </div>
        {parameterConfig.target_customer === CUSTOMER_NPU &&
          item.entityData?.courseType === 1 &&
          item.entityData?.courseStatus != null && (
            <div
              className={`progress-time-wrp ${
                item.entityData?.courseStatus === 1
                  ? 'not-start'
                  : item.entityData?.courseStatus === 2
                  ? 'running'
                  : 'end'
              }`}
            >
              <div className="progress-wrp">
                {item.entityData?.courseStatus === 1
                  ? t('未开始')
                  : item.entityData?.courseStatus === 2
                  ? t('进行中')
                  : t('已结束')}
              </div>
              <div className="date-range-wrp">
                {moment(item.entityData?.start_time).format('YYYY.MM.DD') +
                  '-' +
                  moment(item.entityData?.end_time).format('YYYY.MM.DD')}
              </div>
            </div>
          )}

        <div className="teacher">
          <span
            title={
              item.entityData?.teacher_names?.join(',') ??
              item.entityData?.teacher_names ??
              item.entityData?.importuser
            }
          >
            {item.entityData?.teacher_names?.join(',') ??
              item.entityData?.teacher_names ??
              item.entityData?.importuser}
          </span>
          {item.entityData?.publishType && (
            <span>
              {item.entityData?.publishType === 1 ? t('公开课') : t('班级课')}
            </span>
          )}
          {!isRejected && parameterConfig.target_customer === CUSTOMER_NPU && (
            <span>
              {item.entityData?.courseType === 0 ? (
                <div className="hits">
                  <IconFont type="iconviews" />
                  <span>{item.entityData?.hits || 0}</span>
                </div>
              ) : (
                item.entityData?.courseType === 1 && (
                  <div
                    className="hits"
                    title={`${t('参与人数：')}${item.entityData?.joinUserSum ||
                      0}`}
                  >
                    <IconFont type="iconparticipation" />
                    <span>
                      {item.entityData?.joinUserSum}
                      {t('人参加')}
                    </span>
                  </div>
                )
              )}
            </span>
          )}
          {(item.entityData?.courseType === 0 ||
            (item.entityData?.courseType === 1 && isRejected)) &&
            mobileFlag &&
            popover}
        </div>
        {parameterConfig.target_customer !== CUSTOMER_NPU &&
          item.entityData?.courseType !== 0 &&
          item.entityData?.type_ !== 'biz_sobey_video_entity' &&
          !isRejected && (
            <div className="statusCount">
              {!mobileFlag && (
                <>
                  <div
                    className={`status ${
                      item.entityData?.courseStatus === 1
                        ? 'nostarted'
                        : item.entityData?.courseStatus === 2
                        ? 'starting'
                        : 'finished'
                    }`}
                  >
                    {/* <IconFont type='icondate1' /> */}
                    <span>
                      {item.entityData?.courseStatus === 1
                        ? t('未开始')
                        : item.entityData?.courseStatus === 2
                        ? t('进行中')
                        : t('已结束')}
                    </span>
                  </div>
                  {/* {<div className='hits'><IconFont type="iconparticipation" /><span>{item.entityData?.joinUserSum}</span></div>} */}
                  <div className="date">
                    {moment(item.entityData?.start_time).format('YYYY/MM/DD') +
                      '-' +
                      moment(item.entityData?.end_time).format('YYYY/MM/DD')}
                  </div>
                </>
              )}
              {mobileFlag && popover}
            </div>
          )}

        {parameterConfig.target_customer === CUSTOMER_NPU &&
          item.entityData?.courseType === 2 && (
            <div className="spoc_bottom">
              <Popover
                placement="bottom"
                content={
                  <div>
                    <div>
                      {t('本课程')}
                      {item.entityData?.recording_flag ? t('有') : t('暂无')}
                      {t('课堂实录；')}
                      {item.entityData?.live_flag ? t('已') : t('未')}
                      {t('开启课程直播')}
                    </div>
                    {item.entityData?.recording_flag && (
                      <a
                        onClick={(e: any) => e.stopPropagation()}
                        href={`#/editcourse/coursereview?id=${item.entityData?.contentId_}&type=spoc&reviewid=undefined&${item.entityData?.course_semester_id}`}
                        target="_black"
                      >
                        {t('管理') + t("在线课堂")}
                      </a>
                    )}
                  </div>
                }
              >
                <div className="spoc_bottom_wrp">
                  <span
                    className={`${
                      item.entityData?.recording_flag ? 'active' : ''
                    }`}
                  >
                    {item.entityData?.recording_flag ? (
                      <CheckCircleOutlined />
                    ) : (
                      <MinusCircleOutlined />
                    )}
                    {mobileFlag ? '' : t('课堂')}
                    {t('实录')}
                  </span>
                  <span
                    className={`${item.entityData?.live_flag ? 'active' : ''}`}
                  >
                    {item.entityData?.live_flag ? (
                      <CheckCircleOutlined />
                    ) : (
                      <MinusCircleOutlined />
                    )}
                    {mobileFlag ? '' : t('课程')}
                    {t('直播')}
                  </span>
                </div>
              </Popover>
              {mobileFlag && popover}
              {/* {
                isRejected && <div>{item.entityData?.collegeName?.join("，")}</div>
              } */}
            </div>
          )}
      </div>
    </div>
  );
};
export default CourseBlock;
