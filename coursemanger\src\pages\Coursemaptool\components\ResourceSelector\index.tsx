import React, { useState, useEffect } from 'react';
import { Checkbox, Tree, Space, Empty } from 'antd';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import type { DataNode } from 'antd/es/tree';
import { getResouceTree } from '@/api/home';
import './index.less';

type ResourceType = 'resource' | 'knowledge' | 'question' | 'documentknowledge';

interface ResourceTreeItem {
  id: string;
  name: string;
  value: string;
  path: string;
  parentId: string | null;
  parentPath: string;
  layer: number;  
  children?: ResourceTreeItem[];
}

interface ResourceSelectorProps {
  onChange?: (selectedKeys: React.Key[], types: ResourceType[]) => void;
}

const ResourceSelector: React.FC<ResourceSelectorProps> = ({ onChange }) => {
  const [selectedTypes, setSelectedTypes] = useState<ResourceType[]>(() => {
    const savedTypes = localStorage.getItem('resourceSelectorTypes');
    return savedTypes ? JSON.parse(savedTypes) : ['resource', 'knowledge'];
  });
  
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  
  const [treeData, setTreeData] = useState<DataNode[]>([]);

  // 将API返回的数据转换为Tree组件需要的格式
  const transformTreeData = (data: ResourceTreeItem[]): DataNode[] => {
    
    const transformNode = (item: ResourceTreeItem): DataNode => ({
      title: item.name,
      key: item.path,
      children: item.children ? item.children.map(child => transformNode(child)) : undefined
    });

    // 处理公共资源和个人资源
    const result: DataNode[] = [];
    data.forEach(item => {
      if (item.name === '个人资源') {
        result.push(transformNode(item));
      } else if (item.name === '公共资源' && item.children) {
        // 将公共资源的子节点提升到顶层
        result.push(...(item.children.map(child => transformNode(child))));
      }
    });
    
    return result;
  };

  useEffect(() => {
    const loadTreeData = async () => {
      if (selectedTypes.length === 1 && selectedTypes.includes('question')) {
        setTreeData([]);
        return;
      }

      try {
        const response = await getResouceTree();
        if (response?.success && response.data) {
          const transformedData = transformTreeData(response.data);
          setTreeData(transformedData);
          // 在数据加载完成后，设置上次保存的选中状态
          const savedKeys = localStorage.getItem('resourceSelectorKeys');
          if (savedKeys) {
            const parsedKeys = JSON.parse(savedKeys);
            setSelectedKeys(parsedKeys);
            onChange?.(parsedKeys, selectedTypes);
          }
        }
      } catch (error) {
        console.error('加载资源目录失败:', error);
        setTreeData([]);
      }
    };

    loadTreeData();
  }, [selectedTypes]);

  const handleTypeChange = (type: ResourceType) => (e: CheckboxChangeEvent) => {
    const newTypes = e.target.checked
      ? [...selectedTypes, type]
      : selectedTypes.filter(t => t !== type);
    
    setSelectedTypes(newTypes);
    localStorage.setItem('resourceSelectorTypes', JSON.stringify(newTypes));
    onChange?.(selectedKeys, newTypes);
  };

  const handleTreeSelect = (checked: React.Key[] | { checked: React.Key[]; halfChecked: React.Key[] }) => {
    const keys = Array.isArray(checked) ? checked : checked.checked;
    setSelectedKeys(keys);
    localStorage.setItem('resourceSelectorKeys', JSON.stringify(keys));
    onChange?.(keys, selectedTypes);
  };

  return (
    <div className="resource-selector">
      <div className="resource-selector-header">
        <Space size={16}>
          <Checkbox
            checked={selectedTypes.includes('resource')}
            onChange={handleTypeChange('resource')}
          >
            资源
          </Checkbox>
          <Checkbox
            checked={selectedTypes.includes('knowledge')}
            onChange={handleTypeChange('knowledge')}
          >
            知识点
          </Checkbox>
          {/* <Checkbox
            checked={selectedTypes.includes('documentknowledge')}
            onChange={handleTypeChange('documentknowledge')}
          >
            文档知识点
          </Checkbox> */}
          <Checkbox
            checked={selectedTypes.includes('question')}
            onChange={handleTypeChange('question')}
          >
            试题
          </Checkbox>
        </Space>
      </div>
      
      <div className="resource-selector-content">
        {selectedTypes.length === 1 && selectedTypes.includes('question') ? (
          <Empty description="试题类型无需选择目录" />
        ) : (
          <Tree
            checkable
            multiple
            defaultExpandAll
            treeData={treeData}
            checkedKeys={selectedKeys}
            onCheck={handleTreeSelect}
          />
        )}
      </div>
    </div>
  );
};

export default ResourceSelector; 