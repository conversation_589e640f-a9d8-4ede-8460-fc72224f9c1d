import React, { FC, useEffect, useState, useRef } from 'react';
import {
  Input,
  Button,
  Table,
  Pagination,
  Popconfirm,
  Tag
} from
  'antd';
import './Achievementdegree.less';
import { useHistory, history, useLocation, useSelector } from 'umi';
import statisticsApi from '@/api/statistics';
import { IconFont } from '@/components/iconFont';
import useLocale from '@/hooks/useLocale';
const { Search } = Input;

const Achievementdegree: FC<any> = ({ sesdetail }) => {
  const { t } = useLocale();
  const location: any = useLocation();
  // 当前的课程id
  const courseId = location.query.id;
  const [data, setData] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const courseName = useRef<string>('');
  const isQuestion = useRef<any>(null); // 是否提问
  const nodeName = useRef<string>(''); // 知识点名称
  const [pageinfo, setPageinfo] = useState<any>({
    pageIndex: 1,
    pageSize: 6,
    total: 0,
    // sortOrder: '',
    sortField: '',
    isDesc: true //是否逆序
  }); // 分页信息


  useEffect(() => {
    initData(
      pageinfo.pageIndex,
      pageinfo.pageSize,
      pageinfo.sortField,
      pageinfo.isDesc);

  }, []);

  // 查询列表
  const initData = (
    page: number,
    size: number,
    sortField: string,
    isDesc: boolean) => {
    setLoading(true);
    let obj = {
      courseId: courseId,
      courseName: courseName.current,
      nodeName: nodeName.current,
      page: page,
      size: size,
      isDesc: isDesc,
      field: sortField,
      isQuestion: isQuestion.current //是否提问： false true
    };
    statisticsApi.getKnowledgestatistics(obj).then((res: any) => {
      if (res.status == 200) {
        setPageinfo({
          ...pageinfo,
          pageIndex: res.data.data.page,
          pageSize: res.data.data.size,
          total: res.data.data.total
        });
        setData(res.data.data.results);
      } else {
        setPageinfo({
          ...pageinfo,
          pageIndex: res.data.data.page,
          pageSize: res.data.data.size,
          total: res.data.data.total
        });
        setData([]);
      }
      setLoading(false);
    });
  };

  // 导出excel
  const exportExcel = () => {
    window.open(`/learn/map/resource/study/export/knowledge/learning?courseId=${courseId}&courseSemester=${location.query.sm}`);
  };

  // 知识点
  const columns: any = [
    {
      title: t("知识点"),
      dataIndex: 'nodeName',
      key: 'nodeName',
      align: 'center',
      width: 500,
      ellipsis: true
    },
    {
      title: t("上级节点"),
      dataIndex: 'parentNodeName',
      key: 'parentNodeName',
      align: 'center',
      width: 500,
      ellipsis: true
    },
    // {
    //   title: '完成进度',
    //   dataIndex: 'finishRate',
    //   key: 'finishRate',
    //   align: 'center',
    //   render(text: any, record: any) {
    //     return (
    //       <span>
    //         {Number(record.studyResourceCount)}/{Number(record.resourceTotal)}
    //       </span>
    //     );
    //   },
    // },
    {
      title: t("完成率"),
      dataIndex: 'finishRate',
      key: 'finishRate',
      align: 'center',
      sorter: true,
      render(text: any, record: any) {
        if (text == '-') {
          return '-';
        } else {
          return Number(text) + '%';
        }
      }
    },
    {
      title: t("掌握率"),
      dataIndex: 'masterRate',
      key: 'masterRate',
      align: 'center',
      sorter: true,
      render(text: any, record: any) {
        if (text == '-') {
          return '-';
        } else {
          return Number(text) + '%';
        }
      }
    },
    {
      title: t("提问数"),
      dataIndex: 'questionCount',
      key: 'questionCount',
      align: 'center',
      render: (text: any, record: any) => {
        return <span>{text || 0}</span>;
      }
    },
    {
      title: t("操作"),
      key: 'action',
      align: 'center',
      render: (_: any, record: any) => <Button type="link" onClick={() => {
        sesdetail(record);
      }}>{t("查看详情")}</Button>
    }];

  return (
    <div className="Achievementdegree">
      <div className="search_box">
        <Search
          allowClear
          style={{ width: 220 }}
          placeholder={t("请输入知识点名称")}
          onSearch={(e: any) => {
            nodeName.current = e;
            initData(pageinfo.pageIndex, pageinfo.pageSize, pageinfo.sortField, pageinfo.isDesc);
          }} />

        <Popconfirm
          title={t("确认导出知识点学习情况?")}
          onConfirm={exportExcel}
          okText={t("导出")}
          cancelText={t("取消")}>

          <Button type="primary" icon={<IconFont type='iconexport'></IconFont>}>{t("导出Excel")}</Button>
        </Popconfirm>
      </div>
      <Table
        className="statistics_table"
        columns={columns}
        dataSource={data}
        pagination={false}
        // scroll={{ x: true }}
        loading={loading}
        rowKey={'nodeId'}
        onChange={(page: any, filters: any, sorter: any) => {
          let isDesc: any = sorter.order ?
            sorter.order == 'descend' ?
              true :
              false :
            null;
          let sortField: any = isDesc != null ? sorter.field : null;
          setPageinfo({
            ...pageinfo,
            sortField: sortField,
            isDesc: isDesc
          });
          initData(pageinfo.pageIndex, pageinfo.pageSize, sortField, isDesc);
        }} />

      <div className="cd_course_pagination">
        <Pagination
          size="small"
          showQuickJumper
          current={pageinfo.pageIndex}
          pageSize={pageinfo.pageSize}
          total={pageinfo.total}
          showSizeChanger={true}
          pageSizeOptions={[6, 12, 24, 48, 60, 90]}
          showTotal={(total) => t("共{name}条", String(total))}
          onChange={(pageIndex, pageSize: any) => {
            initData(pageIndex, pageSize, pageinfo.sortField, pageinfo.isDesc);
          }} />

      </div>
    </div>);

};

export default Achievementdegree;