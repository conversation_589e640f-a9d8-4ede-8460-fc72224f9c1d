.progress_box{
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    

    .top_view{
        width: 90%;
        margin-left: 5%;
        height: 60%;
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        .name{
            font-weight: 400;
            font-size: 14px;
            line-height: 30px;
            color: #333333;
        }

        .value{
            font-family: <PERSON><PERSON>, Arial;
                font-weight: normal;
                font-size: 20px;
                color: #549CFF;
        }

        // span{
        //     &:first-child{
        //         font-weight: 400;
        //         font-size: 14px;
        //         line-height: 30px;
        //         color: #333333;
        //     }

        //     &:last-child{
        //         font-family: <PERSON><PERSON>, <PERSON>l;
        //         font-weight: normal;
        //         font-size: 20px;
        //         color: #549CFF;
        //     }
        // }
    }

    .bottom_view{
        width: 90%;
        margin-left: 5%;
        height: 40%;
        display: flex;
        align-items: flex-start;
        justify-content: center;
    }
}