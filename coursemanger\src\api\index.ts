import { typeEn } from '@/permission/moduleCfg';
import { matchChecked } from '@/utils';
import axios from 'axios';
import { getDvaApp } from 'umi';
let configSafe: any = null;
async function getconfig() {
  if ((window as any).configSafe) {
    return (window as any).configSafe;
  } else {
    const res = await fetch('safeMethodConfig.json');
    const result = await res.json();
    (window as any).configSafe = result;
    return result;
  }
}

const HTTP = axios.create({
  // baseURL: 'http://rman.**************.xip.io',
  timeout: 20000,
  withCredentials: true,
});
// const SAFE_MODE = false;
// 请求的数量
let requsetCount: number = 0;
let queryObj: any = {};
const params = window.location.href.split('?')?.[1]?.split('&');
params?.map((item: any) => (queryObj[item.split('=')[0]] = item.split('=')[1]));
HTTP.defaults.headers['Content-Type'] = 'application/json';
const appToken =
  localStorage.getItem('x-cas-token-app') ?? queryObj['x-cas-token'];
if (appToken && /Mobi|Android|iPhone/i.test(navigator.userAgent)) {
  HTTP.defaults.headers['x-cas-token'] = appToken;
}
// 添加请求拦截器
HTTP.interceptors.request.use(
  async (config: any) => {
    // configSafe = await getconfig();

    configSafe = (window as any).sessionStorage.getItem('configSafe');
    if (!configSafe) {
      configSafe = await getconfig();
    }
    const { _store } = getDvaApp();
    _store.dispatch({
      type: 'config/changeShowLoading',
      payload: {
        value: true,
      },
    });
    requsetCount++;
    const orignalMethod: any = config.method;
    if (
      matchChecked(
        config.url as string,
        ['/exam', '/learn', 'forumservice', '/rman/v1/question'],
        [
          '/exam-api/examination',
          '/exam-api/paper',
          '/exam-api/resource/homework/batch-score',
          '/learn/v1/teaching/import/catalogue',
          'upload',
          '/course/template',
          'learn/v1/teaching/course/add/student/',
        ],
      )
    ) {
      const propertyName = config.url.includes('/rman/v1/question')
        ? 'semesterId'
        : config.url.includes('forumservice')
        ? 'course_semester'
        : 'courseSemester';

      if (
        !config.params?.[propertyName] &&
        !config.url.includes(propertyName) &&
        queryObj?.sm && !config.url.includes('learn/m1/knowledge/course/statistics/node/info')
      ) {
        config.params = {
          ...config.params,
          [propertyName]: Number(queryObj.sm),
        };
      }
      if (
        !config.params?.courseType &&
        !config.url.includes('courseType') &&
        queryObj.type && !config.url.includes('learn/m1/knowledge/course/statistics/node/info')
      ) {
        config.params = {
          ...config.params,
          courseType: typeEn[queryObj.type] ?? null,
        };
      }
      if (config.data && !(config.data instanceof FormData)) {
        const temp =
          config.data instanceof Object ? config.data : JSON.parse(config.data);
        if (
          !temp?.[propertyName] &&
          !temp?.courseSemesterId &&
          queryObj?.sm &&
          !Array.isArray(temp)
        ) {
          const _data = { ...temp, [propertyName]: Number(queryObj.sm) };
          config.data =
            config.data instanceof Object ? _data : JSON.stringify(_data);
        }
      }
    }
    // 在发送请求之前做些什么
    if (
      configSafe?.SAFE_MODE &&
      configSafe._jsonMethod.includes(config.method.toLowerCase())
    ) {
      config.method = configSafe._safeMethod;
      let orignalUrl = config.url;
      config.url =
        config.url
          .split('/')
          .slice(0, 2)
          .join('/') + `/safeproxy`;
      config.headers = {
        'Content-Type': 'application/json',
        realurl: orignalUrl,
        realmethod: orignalMethod,
        ...config.headers,
      };
    }
    return config;
  },
  error => {
    // 对请求错误做些什么
    return Promise.reject(error);
  },
);
let flag = true;

// 添加响应拦截器
function sleep(ns: number) {
  return new Promise(resolve =>
    setTimeout(() => {
      resolve(null);
    }, ns),
  );
}

// fetch('safeMethodConfig.json').then(res=>res.json()).then((ress)=>{
//   console.log(ress)
// })

HTTP.interceptors.response.use(
  async response => {
    requsetCount--;
    if (requsetCount === 0) {
      const { _store } = getDvaApp();
      _store.dispatch({
        type: 'config/changeShowLoading',
        payload: {
          value: false,
        },
      });
    }
    // console.log(response.config.url,);
    if (
      (response.data?.errorCode === 'course_0000_0002' ||
        response.data?.errorCode === 'notlogged' ||
        response.data?.error_code === 'forumservice.0000.0401') &&
      !window.location.href.includes('/#/perviewemap') &&
      !window.location.href.includes('/micro/resource/search')
    ) {
      if (flag) {
        flag = false;
        // alert('未登录或登录过期！');
        window.parent.postMessage(
          JSON.stringify({ action: 'login' }),
          window.location.origin,
        );
        window.location.replace(
          `/unifiedlogin/v1/loginmanage/login/direction?redirect_url=${encodeURIComponent(
            window.location.href,
          )}`,
        );
      }
    } else {
      flag = true;
    }
    if (
      response.data.status === 401 ||
      response.data.errorCode === '401' ||
      response.data.error_code === '401'
    ) {
      // console.log('进入了401拦截器',response.data);
      const showreplace = localStorage.getItem('showreplace') || false;
      if (showreplace) {
        console.log(`未登录或登录过期！, ${response.config.url}, ${response.data.error_code}, ${response.data.status}`);
      }else{
        window.location.replace('/unifiedplatform/#/not_authority');
      }
    } else {
      return response;
    }
  },
  error => {
    requsetCount--;
    if (requsetCount === 0) {
      const { _store } = getDvaApp();
      _store.dispatch({
        type: 'config/changeShowLoading',
        payload: {
          value: false,
        },
      });
    }
    //出版社登录拦截
    if (error.response.data.error?.code === '401' || error.response.data.status == 401) {
      // alert('未登录或登录过期！');
      window.parent.postMessage(
        JSON.stringify({ action: 'login' }),
        window.location.origin,
      );
      window.location.replace(
        `/unifiedlogin/v1/loginmanage/login/direction?redirect_url=${encodeURIComponent(
          window.location.href,
        )}`,
      );
    }else {
      console.log('进了异常处理 但是没有判断code');
      // 对响应错误做点什么
      return Promise.reject(error);
    }
  },
);

export default HTTP;
