import chapterApis from '@/api/chapter';
import {
  examinationAdd,
} from '@/api/coursemap';
import Entity from '@/components/entity/entity';
import { IconFont } from '@/components/iconFont';
import { IGlobal, IGlobalModelState } from '@/models/global';
import CourseQAdetail from '@/pages/CourseQA/detail';
import CourseQA from '@/pages/CourseQA/index';
import type { MenuProps } from 'antd';
import {
  Button,
  Dropdown,
  Empty,
  List,
  Modal,
  Spin,
  Tabs,
  Tag,
  Input,
  message,
  Select
} from 'antd';
import React, { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { useLocation, useSelector } from 'umi';
import Askquestions from '../askquestions';
import './index.less';

import Icon, {
  CheckCircleOutlined,
  CloseCircleOutlined,
  CloseOutlined,
  DownOutlined,
  EditOutlined,
  UpOutlined,
} from '@ant-design/icons';
const background_img = require('@/assets/imgs/icon/knowledge_bg.png');
const { TabPane } = Tabs;
const { confirm } = Modal;
// 对比辨析组件
// 引用选择作业的弹窗
// 作业组件
//从题库自动添加弹窗
import AutomaticProblem from '@/pages/Coursemaptool/components/AutomaticProblem';
import HomeworkSubItem from '@/pages/HomeworkManagement/components/HomeworkSubItem';
import RelationMap from './RelationMap';
import Outline from './outline';
// 引用svg
import { getdiscriminate } from '@/api/coursemap';

import { addlog, getTreebylevel } from '@/api/addCourse';
import { getSmartExplain } from '@/api/chatgpt';
import { bindQuestionToNode, checkNumber, deleteQuestionToNode, deleteResourceStudyRecord, finishKnowledge, getStudyResourceList, getknowledgelist, knowledrecommend, questionsanswers, recommendanswers, updateNumber } from '@/api/coursemap';
import statisticsApi from "@/api/statistics";
import useLocale from '@/hooks/useLocale';
import { cloneDeep } from 'lodash';
import { useBus } from 'react-bus';
import { createguid, defaultNodeData } from '../Editmap/util';
import ProgressBox from './ProgressBox';
import TextMore from './Textmore';
import { CheckService } from '@/api/check';
import { handleDownload } from '@/pages/HomeworkManagement/utils/tools';
import MicroMajorGoals from './MicroMajorGoals';
import TagSpan from '../components/TagSpan'
const WholeDrawer: FC<any> = ({
  setVisible,
  x6node,
  isedit,
  expandnode,
  temptime,
  updatanode,
  graph,
  centerednode,
  visible,
  previewEntity,
  setPreviewEntity,
  onback,
  mapid,
  setTotalTime,
  setCurrentTime,
  currentTime,
  bindCourseCode,
  courseCode,
  perviewtype,
  updatanodecompare,
  courseid,
  coursename,
  isv3 = false,
  isMicroMajor,
  handleResource,
  setDrawerdata
}) => {
  const { t } = useLocale();
  const bus = useBus();
  //添加试题下拉选项
  const items: MenuProps['items'] = [
    {
      key: '1',
      label: (
        <a target="_blank" rel="noopener noreferrer" onClick={e => setAddtopicvisible(true)}>
          {t('从题库')}
        </a>
      ),
    },
    // {
    //   key: '2',
    //   label: (
    //     <a target="_blank" rel="noopener noreferrer" onClick={e => setAddtopicvisibleAi(true)}>
    //       {t('自动出题')}
    //     </a>
    //   ),
    // },
  ];

  const finishresourceRef = useRef<any>({});

  const location: any = useLocation();
  const parameterConfig = useSelector<any, any>(
    ({ global }) => global.parameterConfig);
  const { query, pathname }: any = useLocation();
  const newmapid = perviewtype == 0 ? query.id : mapid;

  const { parameterConfigObj, buttonPermission } = useSelector<
    { global: IGlobalModelState; },
    IGlobalModelState>(
      (state) => state.global);
  const mapinfo: any = useSelector<any, any>(
    (state: { coursemap: { mapinfo: any; }; }) => state.coursemap.mapinfo);

  const { userInfo } = useSelector<any, IGlobal>((state) => state.global);
  const microCourseInfo: any = useSelector<any, any>(state => state.moocCourse?.courseDetail)
  const [selectNode, setSelectNode] = useState<any>(); //选中的节点

  const [nodeinfo, setNodeinfo] = useState<any>(null); //选中的节点
  // const [courseknowledge, setCourseknowledge] = useState<any>([]);
  // const [knowledgeindex, setKnowledgeindex] = useState<number>(0);
  // 添加题目的开关
  const [addtopicvisibleAi, setAddtopicvisibleAi] = useState<boolean>(false);
  const container = useRef<any>(null);
  const graphRef = useRef<any>(null); //全局方法
  const [perviewvisit, setPerviewvisit] = useState<boolean>(false);
  const [perviewsrc, setPerviewsrc] = useState<string>(''); //预览路径
  // 引用的知识点
  const [referencedknowledge, setReferencedknowledge] = useState<any>([]);
  let colormodel: any = {}; //颜色字典表
  const [entityLoading, setEntityLoading] = useState<boolean>(false);
  const [recommendId, setRecommendId] = useState<string>(''); //解析

  // 选择资源弹窗的数据
  const [modalTreeData, setModalTreeData] = useState<ResourceModal.treeItem[]>(
    []);

  //   选择资源弹窗的开关
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  // 资源预览相关state
  const [entityModalVisible, setEntityModalVisible] = useState(false);
  const [entityPreview, setEntityPreview] = useState<any>(null);

  const [modalLoading, setModalLoading] = useState<boolean>(false); //选择资源弹窗控制
  // 父级节点
  const [parentnode, setParentnode] = useState<any>([]);
  const [preorder, setPreorder] = useState<any>([]); //前序知识点
  const [nextorder, setNextorder] = useState<any>([]); //后序知识点
  const [tableData, setTableData] = useState<any>([]);

  const [seecontentid, setSeecontentid] = useState<string>(''); //查看内容id
  const [questionvisible, setQuestionvisible] = useState<number>(1); //1 是展示列表  2是添加评论  3是展示详情
  const [todetail, setTodetail] = useState<any>(null); //跳转到评论详情的 参数
  // 参考资料
  const [reference, setReference] = useState<any>([]);
  // 作业
  const [homework, setHomework] = useState<any>([]);
  // 添加题目的开关
  const [addtopicvisible, setAddtopicvisible] = useState<boolean>(false);
  // 当前是添加绑定资源还是添加参考资料
  const [addtype, setAddtype] = useState<string>('bindresource');
  //当前用户选择的所有答案
  const [useranswer, setUseranswer] = useState<any>([]);
  // 是否查看答案
  const [isseeanswer, setIsseeanswer] = useState<boolean>(false);
  //当前推荐的所有答案
  const [recommendanswer, setRecommendanswer] = useState<any>([]);
  // 是否查看推荐答案
  const [isseerecommendanswer, setIsseerecommendanswer] = useState<boolean>(false);
  // 当前查看视频的窗口是在哪里
  const [currentvideotype, setCurrentvideotype] = useState<number>(1); //顶部

  const [explanation, setExplanation] = useState<string>(''); //解析

  // 智能解析loading
  const [intelligentloading, setIntelligentloading] = useState<boolean>(false);

  const [recommendloading, setRecommendloading] = useState<boolean>(false);
  // 当前新绑定的资源
  const newBindResource = useRef<any>([]);

  // 当前已经学习完毕的资源
  const [finishresource, setFinishresource] = useState<any>([]);
  // 获取新建的ID
  const [getNodeinfoId, setNodeinfoId] = useState<number>(0);
  // 掌握率  完成率
  const [rate, setRate] = useState<any>({
    finishRate: 0,
    masterRate: 0,
    nodeAttainment: 0
  });
  // 知识点编号编辑状态
  const [editlabel, setEditlabel] = useState<boolean>(false);
  // 知识点编号
  const [serialNumber, setSerialNumber] = useState<string>('');
  const showSearch = location.query.showSearch !== '0'
  const [resourceIndex, setResourceIndex] = useState<number>(999);
    const couresSyllabusCode: string = useMemo(() => {
      return bindCourseCode || courseCode;
    }, [bindCourseCode, courseCode]);
  // 目标列表
  const [goalList, setGoalList] = useState<any[]>([]);
  // 根据地图id查询课程目标
  const getGoalList = useCallback(() => {
    if (!couresSyllabusCode || !mapid || !x6node?.id) {
      return;
    }
    setModalLoading(true)
    CheckService.queryGoalByMapNode({
      courseCode: couresSyllabusCode,
      courseId: '',
      mapId: mapid,
      nodeId: x6node.id,
      courseSemester: '1',
    }).then(res => {
      if (res?.status == 200) {
        const { data } = res;
        setGoalList(data?.data || []);
      }
    }).finally(() => {
      setModalLoading(false)
    })
  }, [couresSyllabusCode, mapid, x6node?.id]);

  useEffect(() => {
    getGoalList();
  }, [getGoalList]);
  useEffect(() => {
    init()
  }, [x6node]);

  // useEffect(() => {
  //   if(selectNode){
  //     init();
  //   }
  // }, [selectNode]);
  
  const init = async () => {
    if (x6node) {
      const data = x6node.getData();
      if (data.bindresource && data.bindresource.length && typeof (data.bindresource[0]) == 'string') {
        await chapterApis.resourceDetailNew(data.bindresource).then((res: any) => {
          if (res.status != 200) return data.bindresource = []
          data.bindresource = data.bindresource.map((contentId: any, index: number) => ({
            name: res.data[index]?.name,
            contentId,
            contentId_: contentId,
            type: res.data[index]?.type,
            keyframe_: res.data[index]?.cover
          }))
          console.log('data.bindresource', data.bindresource)
        })
      }
      setSelectNode((pre: any) => {
        return {
          id: x6node.id,
          ...defaultNodeData,
          ...data,
          temp: new Date().getTime()
        };
      });
      // 获取所有绑定的资源 并且 如果第一个是视频  默认播放
      if (data.bindresource.length) {
        // previewEntity.name ? previewEntity:
        let item = data.bindresource[0];
        let type = item.type.split('_');
        if (item.type == 'biz_sobey_point' || item.recousetype == 'point') {
          onEntityPreview(item.parentcontentId, {
            name: item.name,
            type: 'video',
            knowledge: {
              inpoint: item.inpoint,
              outpoint: item.outpoint,
            },
          }, item);
          setSeecontentid(item.tempid);
        } else if (type[type.length - 1] == 'video' || type[type.length - 1] == 'picture' || type[type.length - 1] == 'document') {
          onEntityPreview(item.contentId, {
            name: item.name,
            type: type[type.length - 1],
          }, item);
          setSeecontentid(item.tempid);
        }else{
          // 清空previewEntity
          setPreviewEntity({});
        }
      }else{
        // 清空previewEntity
        setPreviewEntity({});
      }
      let referencedarr = [];
      // 获取所有的引用知识点
      if (data.quoteKnowledge?.length) {
        let nodearr = graph.
          getNodes().
          filter((item: any) => item.store.data.data.type == '2');
        if (nodearr.length) {
          let arr = nodearr.filter((item: any) =>
            data.quoteKnowledge.includes(item.id));

          setReferencedknowledge(arr);
          referencedarr = arr;
        }
      } else {
        setReferencedknowledge([]);
      }

      if (referencedarr.length) {
        let str = data.explanation;
        referencedarr.forEach((element: any) => {
          // 替换匹配到的字符串
          str = str.replace(element.store.data.data.label, `<span style="color:#549CFF;cursor: pointer;">${element.store.data.data.label}</span>`);
        });
        setExplanation(str);
      } else {
        setExplanation(data.explanation);
      }


      // // 获取所有的参考资料
      // if(data.referenceMaterial?.length){
      //   setReference(data.referenceMaterial);
      // }

      // // 获取所有的
      // if (data.homework?.length) {
      //   setUseranswer(
      //     data.homework.map((item: any) => item.questions_answers || []),
      //   );
      // }

      // 获取所有的出点
      let pre = graph.getOutgoingEdges(x6node);
      // 入点
      let next = graph.getIncomingEdges(x6node);
      // 1连接到2   1就是2的前序  2就是1的后序
      if (pre && pre.length) {
        let node: any = [];
        pre.forEach((element: any) => {
          if (element.store.data.data.type == 3) {
            let cell = graph.getCellById(element.store.data.target.cell);
            node.push(cell);
          }
        });
        setNextorder(node);
      } else {
        setNextorder([]);
      }

      if (next && next.length) {
        let parent: any = [];
        let node: any = [];
        // type 1包含 2等价 3后续
        next.forEach((element: any) => {
          if (element.store.data.data.type == 3) {
            let cell = graph.getCellById(element.store.data.source.cell);
            node.push(cell);
          }

          if (element.store.data.data.type == 1) {
            let cell = graph.getCellById(element.store.data.source.cell);
            parent.push(cell);
          }
        });
        setPreorder(node);
        setParentnode(parent);
      } else {
        setPreorder([]);
        setParentnode([]);
      }
      // setPreviewEntity(null);
      showSearch && getlist();
      // showSearch && getAnalysisList()
      showSearch && getknowledrecommend()
      getresourcelearning();
      getRate();
      setUseranswer([]);
      setIsseeanswer(false);
    }
  }
  useEffect(() => {
      setSeecontentid(previewEntity.tempid);
    }, [previewEntity])

  /**
   * 查询资源预览网址，显示资源详情modal
   *
   * @param {string} id
   * @param {*} { name, type }
   */
  const onEntityPreview = (id: string, { name, type, knowledge }: any, info?: any) => {
    setEntityLoading(true);
    chapterApis.
      resourceDetailNew([id]).
      then((res) => {
        if (res.status == 200) {
          if (res.data.length) {
            const data = {
              src: res.data[0].filePath,
              cover: res.data[0].cover,
              finishStatus: Array.isArray(finishresourceRef.current)
                ? finishresourceRef.current.includes(info?.tempid)
                : true,
              contentId: id,
              name,
              type,
              tempid: info?.tempid,
              knowledge: {
                ...(knowledge
                  ? knowledge
                  : {
                      inpoint: 0,
                    }),
                  contentId: id,
              },
            };
            if(id !== previewEntity?.contentId){
              setPreviewEntity(data);
            }
          }
        }
      }).
      finally(() => setEntityLoading(false));
  };


  // 取消知识点关联题目
  const removeQuestion = (e: any, item: any, index: number) => {
    e.stopPropagation();
    confirm({
      title: t(`确认删除题目吗？`),
      onOk() {
        let deletearr: any = [];
        // 根据下标删除数组中的元素
        let data = selectNode.homework.filter((item: any, index2: number) => {
          if (index2 != index) {
            return true;
          } else {
            deletearr.push(item);
            return false;
          }
        });
        setSelectNode({
          ...selectNode,
          homework: data,
          temp: new Date().getTime()
        });
        updatanode(x6node.id, {
          ...selectNode,
          homework: data
        });
        deletequestion(deletearr);
      },
      onCancel() { }
    });
  };

  // 动态渲染文件类型
  const getTag = (item: any) => {
    if (item.type == 'biz_sobey_point' || item.recousetype == 'point') {
      return <Tag color="green">{t("视频知识点")}</Tag>;
    } else if (item.type == 'biz_sobey_video') {
      return <Tag color="green">{t("视频")}</Tag>;
    } else if (item.type == 'biz_sobey_audio') {
      return <Tag color="blue">{t("音频")}</Tag>;
    } else if (item.type == 'biz_sobey_picture') {
      return <Tag color="orange">{t("图片")}</Tag>;
    } else if (item.type == 'biz_sobey_document') {
      return <Tag color="purple">{t("文档")}</Tag>;
    } else if (typeof item === 'string') {
      return <Tag color="cyan">{t("链接")}</Tag>;
    } else {
      return <Tag color="purple">{t("其他")}</Tag>;
    }
  };

 
  const addlogs = () => {
    if (newBindResource.current.length) {
      addlog(mapinfo.mapName, newBindResource.current).then((res) => {
        if (res.success) {
          newBindResource.current = [];
          console.log('添加资源日志记录成功！');
        } else {
          console.log('添加资源日志记录失败！');
        }
      });
    }
  };

  const createlogdata = (node: any, arr: any, type: number) => {
    arr.forEach((item: any) => {
      newBindResource.current.push({
        "contentId": item.contentId, //资源id
        "name": item.name, //资源名称
        "courseKnowledgeName": node.label, //知识节点名称
        "knowledgeNames": type == 2 ? [item.name] : []
      });
    });
  };

  //当前试题批改结果
  const [correctionMap, setCorrecttionMap] = useState<any>({})
// 获取当前节点的所有试题
const getlist = () => {
  // getknowledgelist({
  //   "mapId": mapid,
  //   "courseId": courseid,
  //   // "mapName": mapinfo.mapName,
  //   "nodeId": x6node.id,
  //   "stuCode": userInfo.userCode
  // }).then((res: any) => {
  //   if (res.status == 200 && res.data) {
  //     //保存批改结果
  //     if (res.data?.answersExtends) {
  //       setCorrecttionMap(res.data.answersExtends)
  //     }
  //     setNodeinfo(res.data);
  //     let newuseranswer: any = [];
  //     let newhomework = res.data.questions || [];
  //     newhomework.forEach((item: any, index: number) => {
  //       newuseranswer.push(item.student_answers || []);
  //     });
  //     setUseranswer(newuseranswer);
  //     let newdata = null;
  //     setSelectNode((pre: any) => {
  //       newdata = {
  //         ...pre,
  //         homework: newhomework,
  //         temp: new Date().getTime()
  //       };
  //       return newdata;
  //     });
  //     if (perviewtype == 0 || perviewtype == 1) {
  //       updatanode(x6node.id, newdata);
  //     }
  //   }
  // });
};

  const getknowledrecommend = () => {
    setRecommendloading(true)
    knowledrecommend({
      courseSemester: 1,
      entity: x6node.getData().label, //知识点名称
      sourceQuestion: 2,
      mapId: mapid,
      courseId: courseid,
      nodeId: x6node.id,
      stuCode: userInfo.userCode,
    }).then(res => {
      setRecommendloading(false)
      if (res.status == 200 && res.data) {
        let newuseranswer: any = [];
        setRecommendId(res.data.id)
        let newhomework = res.data.questions || [];
        newhomework.forEach((item: any, index: number) => {
          newuseranswer.push(item.student_answers || []);
        });
        setRecommendanswer(newuseranswer);
        let newdata = null;
        setSelectNode((pre: any) => {
          newdata = {
            ...pre,
            recommend: newhomework,
            temp: new Date().getTime(),
          };
          return newdata;
        });
        // 这里只更新当前节点的试题  不要把data全部更新了 会导致数据同步问题
        // if (perviewtype == 0 || perviewtype == 1) {
        //   updatanode(x6node.id, {
        //     homework: newhomework,
        //   });
        // }
      }
    })
  }
  // 调用试题的新增接口
  const addquestion = (arr: any = []) => {
    let data: any = {
      entity: selectNode.label, //知识点名称
      // entity_id: mapinfo.id,  //知识点id
      courseId: courseid, //课程id
      courseName: coursename, //课程名称
      mapId: mapid, //地图id
      mapName: mapinfo.name, //地图名称
      nodeId: selectNode.id, //知识点id
      question_ids: arr.map((item: any) => item.id),
      questions: arr, //试题数组
      stuCode: userInfo.userCode, //学生code
      stuName: userInfo.nickName //学生名称
    };
    if (arr.length) {
      data.id = getNodeinfoId > 0 ? getNodeinfoId : nodeinfo.id;
    }

    bindQuestionToNode(data).then((res: any) => {
      if (res.status == 200) {
        setNodeinfoId(res.data)
        message.success(t('添加试题成功'));
      } else {
        message.error(t('添加试题失败'));
      }
    });
  };

  //删除试题
  const deletequestion = (item: any) => {
    deleteQuestionToNode({
      // id:nodehomewordid.current,
      knowledgeTestQuestions: item,
      courseId: courseid,
      mapId: mapid,
      nodeId: x6node.id //知识点id
    }).then((res: any) => {
      if (res.status == 200 && res.data) {
        message.success(t('删除试题成功'));
      } else {
        message.error(t('删除试题失败'));
      }
      getlist();
    });
  };
  // 提交推荐答案
  const submitrecommendanswer = () => {
    confirm({
      title: t(`查看答案`),
      content: t(`若此刻提交，未做的题目将被记为0分，是否查看？`),
      onOk() {
        setIsseerecommendanswer(!isseerecommendanswer);
        let newhomework = selectNode.recommend.map(
          (item: any, index: number) => {
            return {
              ...item,
              student_answers: recommendanswer[index] || [],
            };
          },
        );
        let pre = graph.getPredecessors(x6node, { distance: 1 });
        let parent = pre.find(item => item.store.data.data.type == 1)
        let next = graph.getSuccessors(parent);

        // 用当前选择的节点进行判断
        recommendanswers(mapid, courseid, recommendId,
          {
            predecessorKnowledgePoints: pre.map(item => item.id),
            parentKnowledgePoints: [pre.find(item => item.store.data.data.type == 1).id],
            siblingKnowledgePoints: next.filter(item => item.id !== x6node.id).map(item => item.id),
            knowledgeTestQuestion: newhomework
          }).then(
            (res: any) => {
              if (res.status == 200 && res.data) {
                message.success(t('提交成功'));
                // getlist();
              } else {
                message.error(t('提交失败'));
              }
            },
          );
      },
      onCancel() { },
    });
  };
  // 提交答案
  const submitanswer = () => {
    confirm({
      title: t(`查看答案`),
      content: t(`若此刻提交，未做的题目将被记为0分，是否查看？`),
      onOk() {
        setIsseeanswer(!isseeanswer);
        let newhomework = selectNode.homework.map((item: any, index: number) => {
          return {
            ...item,
            student_answers: useranswer[index] || []
          };
        });
        questionsanswers(nodeinfo.id, courseid, newhomework).then((res: any) => {
          if (res.status == 200 && res.data) {
            message.success(t('提交成功'));
            // getlist();
          } else {
            message.error(t('提交失败'));
          }
        });
      },
      onCancel() { }
    });
  };

  // 获取资源学习记录
  const getresourcelearning = () => {
    if (perviewtype != 0 && courseid && location.pathname != '/perviewemap') {
      getStudyResourceList({
        courseId: courseid,
        mapId: mapid,
        nodeId: x6node.id
      }).then((res: any) => {
        if (res.status == 200) {
          setFinishresource(res.data);
          finishresourceRef.current = res.data;
        } else {
          message.error(t('获取资源学习记录失败'));
        }
      });
    }
  };

  // 添加资源学习
  const addResourcelearning = (resourceId: any = null) => {
    let flage: any = finishresource;
    // 判断是否已经学习过了
    if (flage.includes(seecontentid)) {
      return;
    }

    const learnFlage: any[] = [
      ...flage,
      resourceId ? resourceId : seecontentid,
    ];

    let type = 0; //0 视频 1文档 2 图片 3音频 4 作业 5 问答  课程地图目前没有 作业 和 问答
    if (resourceId) {
      type = 1;
    } else {
      if (previewEntity.type == 'biz_sobey_video' || previewEntity.type == 'video') {
        type = 0;
      } else if (previewEntity.type == 'biz_sobey_audio' || previewEntity.type == 'audio') {
        type = 3;
      } else if (previewEntity.type == 'biz_sobey_picture' || previewEntity.type == 'picture') {
        type = 2;
      } else if (previewEntity.type == 'biz_sobey_document' || previewEntity.type == 'document') {
        type = 1;
        console.log('添加文档学习记录');
      }
    }

    let data = [{
      "courseId": courseid,
      "courseName": coursename,
      "mapId": mapid,
      "nodeId": x6node.id,
      "resourceId": resourceId ? resourceId : seecontentid,
      "resourceType": type,
      "userCode": userInfo.userCode
    }];
    finishKnowledge(data).then((res: any) => {
      if (res.status == 200) {

        finishresourceRef.current = learnFlage;
        setFinishresource(learnFlage);
        // message.success('添加成功');
      } else {
        // message.error(t('添加学习记录失败！'));
        console.log('添加学习记录失败！', res.message);
      }
      getresourcelearning();
      getRate();
    });
  };

  // 查询当前知识点 完成率 掌握率
  const getRate = () => {
    if (perviewtype != 0 && courseid && location.pathname != '/perviewemap') {
      statisticsApi.getTeacherOrStudentRate({
        "courseId": courseid,
        "mapId": mapid,
        "nodeId": x6node.id,
        "type": perviewtype == 1 ? 0 : 1 //教师端 1  学生端 0
      }).then((res: any) => {
        if (res.data.status == 200) {
          setRate({
            finishRate: res.data.data.finishRate,
            masterRate: res.data.data.masterRate,
            nodeAttainment: Number(res.data.data.nodeAttainment)
          });

        } else {
          message.error(t('获取完成率失败！'));
        }

      });
    }
  };

  useEffect(() => {
    // 触发学习记录事件
    if (rate.finishRate > 0 || rate.masterRate > 0 || rate.nodeAttainment > 0) {
      bus.emit('nodelearning', {
        id: x6node.id,
        finishRate: Number(rate.finishRate),
        masterRate: Number(rate.masterRate),
        nodeAttainment: Number(rate.nodeAttainment)
      });
    }
  }, [rate])

  // 修改知识点编号
  const verificationSerialNum = () => {
    checkNumber({ serialNum: serialNumber }).then((res: any) => {
      if (res.data) {
        updateNumber({
          mapId: mapid,
          Id: selectNode.id,
          serialNum: serialNumber
        }).then((res2: any) => {
          if (res2.status == 200) {
            updatanode(x6node.id, { serialNumber: serialNumber });
            setSelectNode({
              ...selectNode,
              serialNumber: serialNumber,
              temp: new Date().getTime()
            });
            message.success(t('修改成功！'));
            setEditlabel(false);
          } else {
            message.error(t('修改失败！'));
          }
        });
      } else {
        // 编号不符合规则
        message.error(t('编号不符合规则，请重新输入！'));
      }
    });
  }


  return (
    <div className='whole_drawer'>
      <div className='teaching_syllabus'>
        <Outline
            graph={graph}
            temptime={temptime}
            expandnode={expandnode}
            visible={visible}
            setSelectNode={(e:any)=>{
              setDrawerdata(e);
            }}
            centernode={(info: any) => {
              centerednode(info);
              // 根据id获取节点
              const node = graph.getCell(info);
              // 设置选中
              graph.select(node);
            }}
            selectnode={[selectNode]}
          ></Outline>
      </div>
      <div className="left_page">
        <div className="content_box">
          <div className="title">
            <div className="text" title={selectNode ? selectNode.label : t("暂无名称")}>
              {selectNode ? selectNode.label : t("暂无名称")}
            </div>
            {/* <div className='number_view'>
              <span>{t("编号：")}</span>
              <>
                <span style={{ color: '#000000' }}>{selectNode?.serialNumber || ''}</span>
              </>
            </div> */}
          </div>
          <div className="number_view">
                  <div className="left_view">
                    <span>{t('编号：')}</span>
                    {editlabel ? (
                      <>
                        <Input
                          style={{ width: '60%' }}
                          value={serialNumber}
                          defaultValue={selectNode?.serialNumber || ''}
                          placeholder={t('请输入编号')}
                          onChange={(e: any) => setSerialNumber(e.target.value)}
                        />
                        <CheckCircleOutlined
                          style={{ fontSize: '22px', marginLeft: '10px' }}
                          onClick={verificationSerialNum}
                        />
                        <CloseCircleOutlined
                          style={{ fontSize: '22px', marginLeft: '10px' }}
                          onClick={() => setEditlabel(false)}
                        />
                      </>
                    ) : (
                      <>
                        <span style={{ color: '#000000' }}>
                          {selectNode?.serialNumber || ''}
                        </span>
                        {/* 修改按钮 */}
                        {isedit && (
                          <EditOutlined
                            style={{ fontSize: '18px', marginLeft: '10px' }}
                            onClick={() => {
                              setEditlabel(true);
                              setSerialNumber(selectNode?.serialNumber || '');
                            }}
                          />
                        )}
                      </>
                    )}
                  </div>
                  <span className='node_createuser'>{selectNode?.createuser!='' ? `创建人：${selectNode?.createuser}`:''}</span>
                </div>
                <div className='number_view'>
                    <div className='left_view'>
                      <span>属性：</span>
                       <span>{selectNode?.attribute || ''}</span>
                    </div>
                    <span className='node_status'>状态：{selectNode?.status == 0 ? '未编辑' : '已编辑'}</span>
                </div>
                <TagSpan key={selectNode?.id} nodedata={selectNode} />
          {explanation && <div className="detail">
            <TextMore text={explanation}></TextMore>
          </div>}
        </div>
        <div className="content_box">
          <div className="title">
            <div className="text">关联节点
            </div>
          </div>
          <RelationMap
            mapid={newmapid}
            graph={graph}
            openlink={false}
            selectnode={x6node}
            visible={18}
            onClose={() => setVisible(0)}
          ></RelationMap>
        </div>
        {isMicroMajor && <MicroMajorGoals sm={location.query?.sm} courseId={location.query?.id} planId={location.query?.planId || microCourseInfo?.planId} mapId={mapid} nodeId={x6node?.id} />}
        {!isMicroMajor && <div className="content_box" style={{ flex: 1 }}>
          <div className="title">
            <div className="text">课程目标
            </div>
          </div>
          <div className="target_list">
              {goalList?.map((item) => (
                <div className="target_item">
                  <div className="text">{item?.targetName || '目标'}</div>
                    <div className="desc">
                      <TextMore rows={1} text={item?.description}></TextMore>
                    </div>
                </div>
              ))}
          </div>
        </div>}
      </div>
      <div className="middle_page">
        <div className="content_box">
          <div className="title">
            <div className="text">资源
            </div>
          </div>
          <Spin spinning={entityLoading}>
            {visible == 1 && (selectNode?.bindresource?.concat(selectNode?.referenceMaterials))?.length && currentvideotype == 1 ?
              <div className="entity-preview"  style={{ width: '100%', position: 'relative' }}>
                <div className="video-wrap">
                 { previewEntity.src && <Entity
                    type={previewEntity.type}
                    src={previewEntity.src}
                    currentTime={currentTime}
                    onUpdate={(currentTime: number, totalTime?: number) => {
                      setCurrentTime?.(currentTime);
                      if (totalTime) setTotalTime?.(totalTime);
                    }}
                    finishStatus={previewEntity.finishStatus}
                    id={'previewVideo'}
                    isAutoplay={false}
                    pip={false}
                    knowledge={previewEntity.knowledge}
                    onListener={(e: string) => {
                      // 只有在学生端才会有学习记录
                      if (e == 'ended' && perviewtype == 2 && location.query.isJoin == 'true') {
                        addResourcelearning();
                      }
                    }} />}

                </div>
                <div
                  className='resource_list' style={resourceIndex > 1 ? { height: '100%' } : {}}>
                  <List
                    bordered
                    locale={{ emptyText: t("暂无绑定资源") }}
                    dataSource={[...selectNode?.bindresource,...selectNode?.referenceMaterials, ...selectNode?.bindlink].slice(0, resourceIndex)}
                    renderItem={(item: any, index: number) =>
                      <List.Item
                        style={{ cursor: 'pointer' }}
                        key={index}
                        onClick={() => {
                          setCurrentTime?.(0)
                          if (
                            item.type == 'biz_sobey_point' ||
                            item.recousetype == 'point'
                          ) {
                            onEntityPreview(item.parentcontentId, {
                              name: item.name,
                              type: 'video',
                              knowledge: {
                                inpoint: item.inpoint,
                                outpoint: item.outpoint,
                              },
                            }, item);
                          } else if (
                            item.type == 'biz_sobey_document_point' ||
                            item.recousetype == 'document_point'
                          ) {
                            window.open(
                              `/rman/#/basic/rmanDetail/${item.parentcontentId}?showArrow=true&guid_=${item.contentId}`,
                            );
                          } else if(item.type == 'canvas_resource'){
                            window.open(item.url);
                          }else{
                            let type = item.type.split('_');
                            onEntityPreview(item.contentId, {
                              name: item.name,
                              type: type[type.length - 1],
                            }, item);
                          }
                          setSeecontentid(item.tempid);
                          setCurrentvideotype(1);
                          if (
                            item.type == 'biz_sobey_document' &&
                            perviewtype == 2 &&
                            !finishresource.includes(item.tempid) &&
                            location.query.isJoin == 'true'
                          ) {
                            addResourcelearning(item.tempid);
                          }
                        }}
                        >

                        <div className='content_item'>
                          {getTag(item)}
                          <div
                            style={{
                              color:
                                seecontentid == item.tempid ?
                                  '#1890ff' :
                                  '#d9d9d9'
                            }}
                            className='content_name' title={item.name}>

                            {typeof item === 'string' ? item : item.name}
                          </div>
                          {index === 0 && resourceIndex === 1  && [...selectNode?.bindresource,...selectNode?.referenceMaterials, ...selectNode?.bindlink].length>1 && <DownOutlined style={{color: '#fff'}} onClick={(e) => {
                            e.stopPropagation()
                            setResourceIndex([...selectNode?.bindresource,...selectNode?.referenceMaterials, ...selectNode?.bindlink].length)
                          }}  />}
                          {index === 0 && resourceIndex > 1 && [...selectNode?.bindresource,...selectNode?.referenceMaterials, ...selectNode?.bindlink].length>1 && <UpOutlined style={{color: '#fff'}} onClick={(e) => {
                            e.stopPropagation()
                            setResourceIndex(1)
                          }}  />}
                        </div>
                      </List.Item>} />
                </div>
              </div> :
              <Empty description={t("暂无资源")} />}
          </Spin>
        </div>
        {tableData.length > 0 && <div className="content_box">
          <div className="title">
            <div className="text">对比辨析
            </div>
          </div>
          <div className='list'>
            {tableData?.map((item: any) => (
              <div className="list_item">
                <div className='dots'>
                  <span className='dot'>对比节点：</span>
                  <span className='value'>{item.secondKnowledgeName}：{item.thirdKnowledgeName}</span>
                </div>
                <div className="desc">{item.text}</div>
              </div>
            )
            )}
          </div>
        </div>}
      </div>
      <div className="right_page">
        <div className='rate_box'>
          <div className='left_view'>
            <ProgressBox label="目标达成度" data={Number(rate.nodeAttainment) > 100 ? '100' : Number(rate.nodeAttainment)} color="#549CFF"></ProgressBox>
          </div>
          <div className='left_view'>
            <ProgressBox label="完成率" data={Number(rate.finishRate) > 100 ? '100' : Number(rate.finishRate)} color="#44D6E5"></ProgressBox>
          </div>
          <div className='left_view'>
            <ProgressBox label="掌握率" data={Number(rate.masterRate) > 100 ? '100' : Number(rate.masterRate)} color="#F3B764"></ProgressBox>
          </div>
        </div>   

            <Tabs>
              <TabPane tab={t("试题")} key="1">
                <div className='rdrawer'>
                  <div className='drawer_view'>
                    <div className="video_view" >
                      {/* <div className="redio_view"></div> */}
                      {/* <span className="title_span">试题</span> */}
                      <div style={{ flex: 1, textAlign: 'right' }}>
                        {selectNode?.homework?.length ?
                          <Button
                            size="small"
                            type="primary"
                            onClick={(e) => {
                              if (!isseeanswer) {
                                // 学生端 并且 不是管理员角色
                                // if (perviewtype == 2 && location.query.isJoin == 'true') {
                                //   submitanswer();
                                // } else {
                                //   setIsseeanswer(!isseeanswer);
                                // }
                                //修改原因： 填空题新增了范围填空，需要后端进行校验
                                submitanswer();
                              } else {
                                setIsseeanswer(!isseeanswer);
                              }
                            }}
                            style={{ marginRight: '10px' }}>

                            {isseeanswer ? t("重新答题") : t("查看答案")}
                          </Button> :
                          null}
                        {isedit ? (
                          <Dropdown menu={{ items }} placement="bottomRight">
                            <Button
                              size="small"
                              type="primary"
                            >
                              {t('添加')}
                            </Button>
                          </Dropdown>
                        ) : null}
                        <AutomaticProblem
                          currentname={selectNode ? selectNode.label : ''}
                          selectKeys={homework}
                          visible={addtopicvisibleAi}
                          disabled={[3]}
                          onConfirm={(e, x) => {
                            let arr = selectNode.homework || [];
                            let param: any = []
                            e.map((item: any) => {
                              let arr: any = {
                                "knowledge_points": [
                                  {
                                    "entity": nodeinfo.entity,
                                    "entity_id": nodeinfo.entity_id,
                                    "mapId": nodeinfo.mapId,
                                    "mapName": nodeinfo.mapName,
                                    "nodeId": nodeinfo.nodeId,
                                    "parentNode": nodeinfo.parentNode,
                                    "propertyValue": nodeinfo.propertyValue
                                  }
                                ],
                                "hasAttachment": [],
                                "questions_type": x,
                                "questions_analysis": `<p>${item.analysis}</p>`,
                                "questions_answers": [
                                  x == 4 ? item.answer[0] ? 'A' : 'B' : item.answer[0]
                                ],
                                "questions_content": `<p>${item.content}</p>`,
                                "labels": [],
                                "fileList": []
                              }
                              if (x == 1 || x == 0) {
                                param.push({
                                  ...arr,
                                  "questions_options": item.options.map((i: any, index: number) => {
                                    return {
                                      "seq": index + 1,
                                      "content": `<p>${i.slice(2)}</p>`
                                    }
                                  })
                                })
                              } else if (x == 2) {
                                param.push({
                                  ...arr,
                                  "questions_options": item.answer.map((i: any, index: number) => {
                                    return {
                                      "seq": index + 1,
                                      "content": `<p>${i.content}</p>`,
                                      "answerType": 1,
                                      "answerRange": null,
                                      "answerMax": null,
                                      "answerMin": null
                                    }
                                  })

                                })
                              } else if (x == 3) {
                                param.push({
                                  ...arr,
                                  "questions_options": [],
                                })
                              } else {
                                param.push({
                                  ...arr,
                                  "questions_options": [
                                    {
                                      "seq": 1,
                                      "content": "正确"
                                    },
                                    {
                                      "seq": 2,
                                      "content": "错误"
                                    }
                                  ],

                                })
                              }
                            })
                            examinationAdd(param).then((res: any) => {
                              if (arr && arr.length) {
                                // 去重
                                let newarr = res.data.filter((item: any) => {
                                  let flage = true;
                                  arr.forEach((item2: any) => {
                                    if (item.id == item2.id) {
                                      flage = false;
                                    }
                                  });
                                  return flage;
                                });
                                arr = [...arr, ...newarr];
                                if (newarr.length == 0) {
                                  message.warning(t('试题重复添加！'));
                                  return;
                                }
                              } else {
                                arr = res.data;
                              }
                              setSelectNode({
                                ...selectNode,
                                homework: arr,
                                temp: new Date().getTime(),
                              });
                              updatanode(x6node.id, {
                                ...selectNode,
                                homework: arr,
                              });
                              setAddtopicvisibleAi(false);
                              addquestion(arr);
                            })
                          }}
                          onclose={() => setAddtopicvisibleAi(false)}
                        />
                      </div>
                    </div>
                    {selectNode?.homework?.map((item: any, index: number) => {
                      return (
                        <div className="topic_box" key={index}>
                          {
                            isedit ?
                              <IconFont
                                title={t("删除题目")}
                                className="remove_topic"
                                onClick={(e) => removeQuestion(e, item, index)}
                                style={{
                                  color: 'var(--primary-color)',
                                  fontSize: '16px'
                                }}
                                type="iconhuishouzhan-huise" /> :
                              null}

                          <div style={{ width: '100%' }}>
                            <HomeworkSubItem
                              score={0}
                              showscore={false}
                              key={index}
                              isEnd={isseeanswer}
                              files={null}
                              canEdit={true}
                              openParse={true}
                              index={index + 1}
                              data={item}
                              answer={useranswer[index]}
                              isCorrect={correctionMap?.[item.id]?.map((cell: any) => cell?.correct)}
                              onChange={(e) => {
                                let arr = cloneDeep(useranswer);
                                arr[index] = e;
                                setUseranswer(() => {
                                  return [...arr];
                                });
                              }} />
                          </div>
                        </div>);

                    })}
                    {
                      selectNode?.homework?.length == 0 && <Empty style={{ marginTop: 50 }} description={t("暂无试题")}></Empty>}

                  </div>
                </div>
              </TabPane>
              {parameterConfig.forum_display == 'true' && perviewtype != 0 && courseid && location.pathname != '/perviewemap' ?
                <TabPane tab={t("问答")} key="2">
                  <div className="rdrawer">
                    {questionvisible == 1 ?
                      <div className="drawer_view">
                        <div className="options_view">
                          <Button
                            type="primary"
                            icon={<IconFont type="iconbiji" />}
                            onClick={() => setQuestionvisible(2)}>
                            {t("发起提问")}

                          </Button>
                        </div>
                        <div className="detail_view">
                          <CourseQA
                            ismap={true}
                            extend_link_id={selectNode?.id}
                            extend_type={location.query.type == '1' || location.query.type == 'mooc' ? '03' : '01'}
                            showdetail={(e: any) => {
                              setTodetail(e);
                              setQuestionvisible(3);
                            }}>
                          </CourseQA>
                        </div>
                      </div> :

                      ''}

                    {questionvisible == 2 ?
                      <div className="drawer_view">
                        <Askquestions
                          mapid={mapid}
                          courseid={courseid}
                          perviewtype={perviewtype}
                          selectNode={selectNode}
                          coursename={coursename}
                          onback={() => {
                            setQuestionvisible(1);
                          }}>
                        </Askquestions>
                      </div> :

                      ''}

                    {questionvisible == 3 ?
                      <div className="drawer_view">
                        <CourseQAdetail
                          ismap={true}
                          topicid={todetail.topicid}
                          onback={() => {
                            setQuestionvisible(1);
                          }}>
                        </CourseQAdetail>
                      </div> :

                      ''}

                  </div>
                </TabPane> :

                ''}
            </Tabs>

        {parameterConfig.recommend_display == 'true' && <div className="content_box">
          <div className='divider'></div>
          <div className="title">
            <div className="text">推荐
            </div>
          </div>
          <div className="rdrawer">
            <div className="drawer_view">
              {recommendloading && (
                <Spin
                  style={{
                    width: '100%',
                    height: '300px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  size="large"
                />
              )}
              <div className="video_view" style={{ marginTop: '-10px' }}>
                <div style={{ flex: 1, textAlign: 'right' }}>
                  {!recommendloading && selectNode?.recommend?.length ? (
                    <Button
                      size="small"
                      type="primary"
                      onClick={e => {
                        if (!isseerecommendanswer) {
                          // 学生端 并且 不是管理员角色
                          if (
                            perviewtype == 2 &&
                            location.query.isJoin == 'true'
                          ) {
                            submitrecommendanswer();
                          } else {
                            setIsseerecommendanswer(!isseerecommendanswer);
                          }
                        } else {
                          setIsseerecommendanswer(!isseerecommendanswer);
                        }
                      }}
                      style={{ marginRight: '10px' }}
                    >
                      {isseerecommendanswer ? t('重新答题') : t('查看答案')}
                    </Button>
                  ) : null}
                </div>
              </div>
              {!recommendloading &&
                selectNode?.recommend?.map((item: any, index: number) => {
                  return (
                    <div className="topic_box" key={index}>                    
                      <div style={{ width: '95%' }}>
                        <HomeworkSubItem
                          score={0}
                          showscore={false}
                          key={index}
                          isEnd={isseerecommendanswer}
                          files={null}
                          canEdit={true}
                          openParse={true}
                          index={index + 1}
                          data={item}
                          fromType=""
                          answer={recommendanswer[index]}
                          isCorrect={correctionMap?.[item.id]?.map((cell: any) => cell?.correct)}
                          onChange={e => {
                            let arr = cloneDeep(recommendanswer);
                            arr[index] = e;
                            setRecommendanswer(() => {
                              return [...arr];
                            });
                          }}
                          comment=""
                          onPreview={() => { }}
                          onDownload={handleDownload}
                          onImagePreview={() => { }}
                        />
                      </div>
                    </div>
                  );
                })}
              {selectNode?.recommend?.length == 0 && (
                <Empty
                  style={{ marginTop: 50 }}
                  description={t("暂无试题")}
                ></Empty>
              )}
            </div>
          </div>
        </div>}
      </div>
      <Modal
        width={'70%'}
        title={t("文档预览")}
        visible={perviewvisit}
        className="document_modal_wrap"
        onCancel={() => setPerviewvisit(false)}
        footer={null}>

        <div style={{ height: '65vh' }}>
          {
            perviewsrc != "" && perviewsrc ? <Entity src={perviewsrc} type="document"
              onListener={(e: string) => {
                // 只有在学生端才会有学习记录
                if (e == 'ended' && perviewtype == 2 && location.query.isJoin == 'true') {
                  addResourcelearning();
                }
              }}></Entity> : null}
        </div>

      </Modal>
    </div>
  );

};

export default WholeDrawer;
