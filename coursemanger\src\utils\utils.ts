/*
 * @Author: 冉志诚
 * @Date: 2024-04-03 12:42:59
 * @LastEditTime: 2025-01-02 14:50:16
 * @FilePath: \coursemanger\src\utils\utils.ts
 * @Description: 
 */
import type {
  ProColumns,
  ProSchemaValueEnumType,
} from '@ant-design/pro-components';
import { ColumnsType, ColumnType } from 'antd/es/table';
import dayjs from 'dayjs';

/**
 * @description 工具库
 */
export namespace Utils {
  /**
   * @description:  返回是否为develop模式, preview模式也算
   */
  export const isDevelopment =
    process.env.NODE_ENV === 'development' ||
    Number(process.env.IS_UMI_PREVIEW) === 1;

  /**
   * @description search 解析为对象
   */
  export const parseSearch = <
    T extends Record<string, any>,
  >(
    search: string,
  ): T => {
    const searchParams = new URLSearchParams(search);
    const obj: any = {};
    searchParams.forEach((value, key) => {
      obj[key] = value;
    });
    return obj;
  };
  /**
   * @description: 对象转query string
   * @param o Object
   * @param  needMask 是否需要query问号
   */
  export const objToQueryString = (
    o: Record<string, any>,
    needMask = true,
  ) => {
    return `${needMask ? '?' : ''}${Object.entries(o)
      .reduce(
        (searchParams, [name, value]) => (
          searchParams.append(name, String(value)),
          searchParams
        ),
        new URLSearchParams(),
      )
      .toString()}`;
  };

  /** query转换*/
  export function queryConvert(query: Record<string, any>) {
    const queryArr = Object.keys(query);
    const obj: Record<string, any> = Object.create(null);
    queryArr.forEach((queryItem) => {
      const map: Record<string, any> = new Proxy(
        {
          false: false,
          true: true,
          // null: null, null不应该做处理可能会有异常
          // undefined: undefined,
        },
        {
          get(target, key) {
            return (
              Reflect.get(target, key) ?? query[queryItem]
            );
          },
        },
      );
      obj[queryItem] = map[query[queryItem]];
    });
    return obj;
  }

  /**
   * @description: query对象解码
   * @param query
   */
  export function queryObjDecode(
    query: Record<string, string>,
  ) {
    const obj: Record<string, any> = {};
    const queryArr = Object.keys(query);
    queryArr.forEach((queryItem) => {
      obj[queryItem] = tryDecodeURLComponent(
        query[queryItem],
      );
    });
    return obj;
  }

  /**
   * @description: 解码
   * @param str: 编码字符串。
   * @param maxInterations：尝试解码的最大递归迭代次数str（默认值30） 。
   * @param iterations：标记计数器迭代。
   */
  export function tryDecodeURLComponent(
    str: string,
    maxInterations = 30,
    iterations = 0,
  ): string {
    if (iterations >= maxInterations) {
      return str;
    } else if (
      typeof str === 'string' &&
      (str.indexOf('%3D') !== -1 ||
        str.indexOf('%25') !== -1)
    ) {
      return tryDecodeURLComponent(
        decodeURIComponent(str),
        maxInterations,
        iterations + 1,
      );
    }

    return decodeURIComponent(str);
  }
  /**
   *
   * @param cssVars 传入的css变量 key是驼峰 value是css变量 会自动进行转换
   * 当第一个值未定义，回退值生效。color: var(--text-color, black); /* 此处 color 正常取值 --text-color
   *
   */
  export function setCssVariableInStyle(
    cssVars: Record<string, string>,
  ): void {
    const root = document.querySelector(':root') as any;
    Object.entries(cssVars).forEach(([key, value]) => {
      root.style.setProperty(
        `--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`,
        value,
      );
    });
  }
  // 使用对象生成css变量 同时插入样式中而不是style
  export function setCssVariable(
    cssVars: Record<string, string>,
    id = 'global-css-variable',
  ): void {
    const oldElement = document.getElementById(id);
    const style =
      oldElement ?? document.createElement('style');
    style.id = id;
    let cssText = ':root{';
    Object.entries(cssVars).forEach(([key, value]) => {
      cssText += `--${key
        .replace(/([A-Z])/g, '-$1')
        .toLowerCase()}:${value};`;
    });
    cssText += '}';
    // 清空之前的样式 保证只有一次
    style.innerHTML = cssText;
    if (!oldElement) {
      document.head.appendChild(style);
    }
  }

  /**
   * @description 对象字段去undefined null
   */
  export const removeEmpty = <T extends any>(obj: T) => {
    const newObj: any = obj;
    Object.keys(newObj).forEach((key) => {
      if (
        newObj[key] === undefined ||
        newObj[key] === null ||
        newObj[key] === ''
      ) {
        delete newObj[key];
      }
    });
    return newObj as RequiredObject<
      Required<NonNullable<T>>
    >;
  };
  export type NonNullable<T> = T extends null | undefined
    ? never
    : T;
  export type RequiredObject<T> = {
    [K in keyof T]-?: Exclude<T[K], undefined>;
  };
  /**
   * 获取路径最后一级
   */
  export const getLastPath = (path: string) => {
    return path.split('/').pop();
  };
  /**
   * @description  生成国际化配置对象
   */
  export const generateI18nConfig = <T extends string>(
    keys: T[],
    beforePrefix = '',
    afterPrefix = '',
  ) => {
    const i18nConfig: Record<T, string> = {} as any;
    keys.forEach((key) => {
      i18nConfig[
        key
      ] = `${beforePrefix}${key}${afterPrefix}`;
    });
    return i18nConfig;
  };
  /**
   * 驼峰命名转指定字符分割
   */
  export const camelCaseToOther = (
    str: string,
    other = '_',
  ) => {
    return str
      .replace(/([A-Z])/g, `${other}$1`)
      .toLowerCase();
  };

  /**
   * @description: 展平带children的数组
   */
  export function flattenChildren(ary: FlattenChildren[]) {
    const newAry: FlattenChildren[] = [...ary];
    ary.forEach((item) => {
      if (item.children) {
        newAry.push(...flattenChildren(item.children));
      }
    });
    return newAry;
  }

  interface FlattenChildren {
    children?: FlattenChildren[];
    [x: string]: any;
  }
  export function generateId() {
    return `${Math.random()
      .toString(36)
      .slice(2)}${Date.now()
      .toString()
      .slice(6)}${Math.random().toString(36).slice(2)}`;
  }
  export function getTextWidthAndHeight(
    text: string,
    fontSize = 14,
    fontFamily = 'sans-serif',
    angle = 0,
  ) {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d')!;
    context.font = `${fontSize}px ${fontFamily}`;

    const width = context.measureText(text).width;

    return getRotateLeftAndTopRect(
      width * (1 + 0.3),
      // +4为间距
      fontSize * (1 + 0.22),
      angle,
    );
  }
  // 计算矩形以左上顶点为原点顺时针旋转后的宽高
  export function getRotateLeftAndTopRect(
    width: number,
    height: number,
    angle: number,
  ) {
    return {
      width: Math.sqrt(
        Math.pow(width, 2) + Math.pow(height, 2),
      ),
      height:
        Math.abs(width * Math.sin(angle)) +
        Math.abs(height * Math.cos(angle)),
    };
  }
  /**
   * @description: 取对象值为指定类型的value,并对key进行处理
   */
  export function pickValueByType(
    obj: Record<string, any>,
    type?: string,
    translateKey?: (key: string) => string,
  ): Record<string, any> {
    const newObj: any = {};
    const transform = (str: string) => {
      if (translateKey) {
        return translateKey(str);
      }
      return str;
    };
    Object.keys(obj).forEach((key) => {
      if (!type || typeof obj[key] === type) {
        newObj[transform(key)] = obj[key];
      }
    });
    return newObj;
  }
  /**
   * @description: 首字母大写
   */
  export function firstUpperCase(str: string) {
    return str.replace(/^\S/, (s) => s.toUpperCase());
  }
  /**
   * @description: 获取文件名称后缀
   */
  export function getFileSuffix(fileName: string) {
    return fileName.split('.').pop() as string;
  }
  /** 简单值数组去重,能直接===判断的*/
  export function unique<T extends any>(array: T[]) {
    return [...new Set(array)];
  }
  /**
   * @description 复杂数组 里面嵌套的对象数组去重
   */
  export function uniqueBy<T extends any>(
    array: T[],
    isEqual = (a: T, b: T) => {
      return Object.is(a, b);
    },
  ) {
    return array.filter(
      (item, index, arr) =>
        arr.findIndex((v) => isEqual(v, item)) === index,
    );
  }
  /** @description: 生成柔和的随机颜色 */
  export function randomColor() {
    return `hsl(${Math.random() * 360}, 100%, 75%)`;
  }
  /**
   *@description 生成随机数
   * @param min  最小值
   * @param max  最大值
   * @param precision  保留小数
   * @param count 生成个数
   */
  export function getRandomNums(
    min: number,
    max: number,
    count = 1,
    precision = 0,
  ) {
    const arr = Array(count).fill(0);
    return arr.map(() =>
      Number(
        (Math.random() * (max - min) + min).toFixed(
          precision,
        ),
      ),
    );
  }

  /**
   * @description: 计算字符串的unicode和
   */
  export function getUnicodeSum(str: string) {
    let sum = 0;
    for (let i = 0; i < str.length; i++) {
      sum += str.charCodeAt(i);
    }
    return sum;
  }
  export function getTagColorByStrLen(str: string) {
    const presetColor = [
      'blue',
      'purple',
      'cyan',
      'green',
      'magenta',
      'pink',
      'red',
      'orange',
      'yellow',
      'volcano',
      'geekblue',
      'lime',
      'gold',
    ];
    const sum = getUnicodeSum(str) % presetColor.length;
    return presetColor[sum];
  }
  /**
   * @param keys 排除的key
   * @description 排除对象中的某些key
   */
  export function omit<
    T extends Record<string, any>,
    K extends keyof T,
  >(obj: T, keys: K[]) {
    return Object.fromEntries(
      Object.entries(obj).filter(
        ([key]) => !keys.includes(key as K),
      ),
    ) as Omit<T, K>;
  }
  /**
   * @description 对象字段添加默认值
   */
  export const addDefaultValueWhenEmpty = (
    obj: any,
    defaultValue: string,
  ) => {
    Object.keys(obj).forEach((key) => {
      if (obj[key] === undefined || obj[key] === null) {
        obj[key] = defaultValue;
      }
    });
    return obj;
  };
  /**
   * @param keys 取key
   * @description 取对象中的某些key
   */
  export function pick<
    T extends Record<string, any>,
    K extends keyof T,
  >(obj: T, keys: K[]) {
    return Object.fromEntries(
      Object.entries(obj).filter(([key]) =>
        keys.includes(key as K),
      ),
    ) as Pick<T, K>;
  }

  /**
   * @description: 字符串数组生成label value
   */
  export function generateLabelValueByStringArray(
    strArr: string[],
  ) {
    return strArr.map((str) => ({
      label: str,
      value: str,
    }));
  }
  /**
   * @description: 判断是否为undefined null
   */
  export function isNil(value: any) {
    return value === undefined || value === null;
  }
  export function omitObjectNil<T extends Object>(o: T): T {
    return Object.fromEntries(
      Object.entries(o).filter(([key, value]) => {
        return !isNil(value);
      }),
    ) as T;
  }

  /**
   * 生成指定长度随机字符串,默认28位
   */
  export function getRandomStr(len = 28) {
    const chars =
      'ABCDEFGHIJKMNOPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz0123456789';
    const maxPos = chars.length;
    let str = '';
    for (let i = 0; i < len; i++) {
      str += chars.charAt(
        Math.floor(Math.random() * maxPos),
      );
    }
    return str;
  }
  export function openNewPage(
    rootPathname: string,
    pathname: string,
    query?: Record<string, any>,
  ) {
    const url = `${
      location.origin
    }${rootPathname}/#${pathname}${
      query ? objToQueryString(query) : ''
    }`;
    window.open(url);
  }

  export function goPageInModule(
    pathname: string,
    query?: Record<string, any>,
    newPage = false,
  ) {
    const path = pathname.startsWith('/')
      ? pathname
      : `/${pathname}`;
    const url = `${location.origin}${
      location.pathname.endsWith('/')
        ? location.pathname
        : '/'
    }#${path}${query ? objToQueryString(query) : ''}`;
    if (newPage) {
      window.open(url);
    } else {
      window.location.href = url;
    }
  }

  export function ClassNames(...classNames: any[]) {
    return classNames
      .filter((className) => !!className)
      .join(' ');
  }
  export function goLogin() {
    goPageInModule('login', {
      redirect_url: encodeURIComponent(location.href),
    });
  }
  // 深拷贝
  export function deepClone<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj));
  }
  /**
   * @description 深比较
   */
  export function deepEqual(a: any, b: any) {
    if (a === b) {
      return true;
    }
    if (typeof a !== typeof b) {
      return false;
    }
    if (typeof a !== 'object' || typeof b !== 'object') {
      return false;
    }
    if (Array.isArray(a) !== Array.isArray(b)) {
      return false;
    }
    if (Array.isArray(a) && Array.isArray(b)) {
      if (a.length !== b.length) {
        return false;
      }
      for (let i = 0; i < a.length; i++) {
        if (a[i] !== b[i]) {
          return false;
        }
      }
      return true;
    }
    const aKeys = Object.keys(a);
    const bKeys = Object.keys(b);
    if (aKeys.length !== bKeys.length) {
      return false;
    }
    for (const key of aKeys) {
      if (!bKeys.includes(key)) {
        return false;
      }
      if (!deepEqual(a[key], b[key])) {
        return false;
      }
    }
    return true;
  }

  /**
   * @description: 数组展平
   */
  export function flatten<T>(arr: T[]) {
    return arr.flat(Infinity) as ArrayElement<T[]>[];
  }

  /**
   * @description 取数组中的元素类型
   */
  export type ArrayElement<
    ArrayType extends readonly unknown[],
  > = ArrayType extends readonly (infer ElementType)[]
    ? ElementType extends readonly unknown[]
      ? ArrayElement<ElementType>
      : ElementType
    : never;
  /**
   * @description: 表格居中
   */
  export function tableCenter<T extends any>(
    columns: ProColumns<T>[],
  ) {
    columns.forEach((column) => {
      column.align = 'center';
    });
    return columns;
  }
  /**
   * @description: 监听条件是否满足,满足执行回调
   * @param  condition 条件
   * @param  callback 回调
   * @param  time 间隔时间
   */
  export async function ensure(
    condition: () => boolean,
    callback?: Function,
    time = 400,
  ) {
    if (condition()) {
      callback?.();
    } else {
      await sleep(time);
      ensure(condition, callback, time);
    }
  }
  /**
   * @param time 延迟时间
   * @description 睡一会~
   */
  export const sleep = (time: number) => {
    return new Promise((res, rej) => {
      setTimeout(() => {
        res(true);
      }, time);
    });
  };
  /**uuid生成*/
  export function uuid() {
    return (
      'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
        /[xy]/g,
        (c) => {
          const r = (Math.random() * 16) | 0;
          const v = c === 'x' ? r : (r & 0x3) | 0x8;
          return v.toString(16);
        },
      ) + Date.now()
    );
  }
  /** 对象合并 */
  export function merge<T extends any, K extends any>(
    a: T,
    b: K,
  ): T & K {
    return Object.assign({}, a, b);
  }
  /**
   * @description: 对象转formdata
   */
  export function objToFormData(o: Record<string, any>) {
    const keys = Object.keys(o);
    const formData = new FormData();
    keys.forEach((key) => {
      if (o[key] !== undefined && o[key] !== null) {
        formData.append(key, o[key]);
      }
    });
    return formData;
  }
  /** 枚举转map */
  export function enumToMap<T extends Record<string, any>>(
    obj: T,
    extraSchemas: Omit<
      ProSchemaValueEnumType,
      'text'
    >[] = [],
    //! 是否需要翻转 因为enum在值为number的情况下,可以通过value获取key,也可以通过key获取value,但是在值为string的情况下,只能通过key获取value
    isReverse = true,
    /** 取值区间 */
    range?: [number, number] | [number],
  ) {
    const map = new Map<keyof T, ProSchemaValueEnumType>();
    let entities = Object.entries(obj);
    // 只取枚举的key
    entities = entities.slice(
      isReverse ? entities.length / 2 : 0,
    );
    if (range) {
      entities = entities.slice(range[0], range[1]);
    }
    entities //@ts-ignore
      .forEach((entity: [T[keyof T], keyof T], index) => {
        const [value, key] = entity;
        map.set(key, {
          ...extraSchemas[index],
          text: value,
        });
      });
    return map;
  }
  /** label,value 转object */
  export function labelValueToObject<T extends any>(
    arr: { label: string; value: string }[],
  ) {
    return arr.reduce((obj, item) => {
      obj[item.value] = item.label;
      return obj;
    }, {} as Record<string, string>);
  }
  /** 指定元素平滑滚动到顶部 */
  export function scrollToTop(selector: string) {
    const element =
      document.querySelector(selector) ?? document.body;
    element.scrollTo({
      top: 0,
      // behavior: 'smooth',
    });
  }
  /** 将指定传入格式时间,输出指定输出格式 */
  export function formatTime(
    time?: string | number,
    inputFormat = 'YYYY-MM-DD HH:mm:ss',
    outputFormat = 'YYYY-MM-DD HH:mm:ss',
  ) {
    if (!time) return;
    return dayjs(time, inputFormat).format(outputFormat);
  }
  /** 解决splice不能删除0和最后一个 */
  export function spliceDelete<T>(arr?: T[], index = 0) {
    if (!arr) return;
    if (index === 0) {
      arr.shift();
    } else if (index === arr.length - 1) {
      arr.pop();
    } else {
      arr.splice(index, 1);
    }
    return arr;
  }
  /**
   * 对象转base64
   * @param value json安全的值
   */
  export function valueToBase64(
    value: Record<string, any>,
  ) {
    return btoa(JSON.stringify(value));
  }
  export function base64ToValue(str: string) {
    return JSON.parse(atob(str));
  }
  /**
   * @description 类型
   */
  export type Type =
    | 'number'
    | 'string'
    | 'boolean'
    | 'symbol'
    | 'array'
    | 'function'
    | 'error'
    | 'date'
    | 'object'
    | 'regexp'
    | 'blob'
    | 'null';

  /**
   * @description 获取准确类型
   */
  export const getType = (o: any) => {
    return Object.prototype.toString
      .call(o)
      .slice(8, -1)
      .toLowerCase() as unknown as Type;
  };
  /**
   * @description: 二进制流下载
   * @param  fileStream 文件流
   * @param  fileName 文件名
   */
  export async function download(
    fileStream: any | Blob,
    fileName?: string,
  ) {
    const blob =
      getType(fileStream) === 'blob'
        ? fileStream
        : new Blob([fileStream]);
    // 通过fileStream生成url；
    const url = window.URL.createObjectURL(blob);
    // 创建一个a标签；
    const link = document.createElement('a');
    // 编辑a标签的href属性；
    link.href = url;

    // 编辑a标签的download属性(下载文件时生成的默认文件名);
    link.setAttribute(
      'download',
      fileName ? fileName : 'file',
    );
    // 将a标签添加到body中；
    document.body.appendChild(link);
    // 点击事件；
    link.click();
    // 移除a标签；
    document.body.removeChild(link);
    // 释放url；
    window.URL.revokeObjectURL(url);
  }
  export function downloadBase64(
    base64: string,
    fileName?: string,
  ) {
    const a = document.createElement('a');
    a.href = base64;
    a.download = fileName ?? 'file';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  }
  /**
   * 泛型工具 取数组中的值
   */
  export type ArrayElementType<T> = T extends (infer U)[]
    ? U
    : T;
  /**
   * @description 判断表达式(包含加减乘除括号)是否正确
   * 若错误返回错误的位置, 若正确返回-1.以下是错误的情况
   * 1. 括号不匹配
   * 2. 运算符不完全 如 a+ 则为错误
   * 3. 运算符连续 如 a++ 则为错误
   * 4. 运算符位置错误 如 a++b 则为错误
   * 5. 空括号 如 () 则为错误
   */
  export function checkExpression(expression: string) {
    // 同时记录在表达式中的位置
    const stack: {
      /** 入栈的括号 */
      bracket: string;
      /** 在表达式中的位置 */
      originIndex: number;
    }[] = [];
    const left = ['(', '[', '{'];
    const right = [')', ']', '}'];
    const operator = ['+', '-', '*', '/'];
    /** 校验结果 */
    let result = -1;
    for (let i = 0; i < expression.length; i++) {
      const char = expression[i];
      if (left.includes(char)) {
        // 左括号若不为第一个字符,则前一个字符必须为运算符
        if (
          i !== 0 &&
          !operator.includes(expression[i - 1])
        ) {
          result = i;
          break;
        }
        // 左括号入栈
        stack.push({
          bracket: char,
          originIndex: i,
        });
        //
      } else if (right.includes(char)) {
        const element = stack.pop();
        // 栈中没有元素,说明括号不匹配
        if (!element) {
          result = i;
          break;
        }
        const { bracket: leftChar } = element;
        if (
          // 括号不匹配,抛出错误
          left.indexOf(leftChar) !== right.indexOf(char) ||
          // 空括号
          i - element.originIndex === 1
        ) {
          result = i;
          break;
        }
        // 操作符匹配
      } else if (operator.includes(char)) {
        const lastChar = expression[i - 1];
        const nextChar = expression[i + 1];
        if (
          // 前后字符不存在,则为错误
          !lastChar ||
          !nextChar ||
          // 运算符连续 则为错误
          [...operator, ...right].includes(lastChar) ||
          [...operator, ...left].includes(nextChar)
        ) {
          result = i;
          break;
        }
      }
    }
    // 栈中还有元素,说明括号不匹配
    return stack.length > 0 ? stack[0].originIndex : result;
  }
  /**
   * @description: 传入对象和一个默认值,传入对象为null或者其他空值,
   *  则返回默认值,否则返回传入对象然后合并
   */
  export function mergeWithDefault<
    T extends Record<string, any>,
    U extends Partial<T> & Record<string, any>,
  >(obj: T, defaultObj: U) {
    const newObj = obj;
    const keys = Object.keys(obj);
    for (let index = 0; index < keys.length; index++) {
      const key = keys[index];
      if (isNil(obj[key])) {
        // @ts-ignore
        newObj[key] = defaultObj[key];
      }
    }
    return newObj;
  }
  /**
   * @description: 解析json数组,失败返回空数组
   */
  export function parseJsonArray(
    jsonStr?: any,
  ): Array<any> {
    try {
      if (getType(jsonStr) === 'array') return jsonStr;
      if (getType(jsonStr) === 'object') return [jsonStr];
      const value = JSON.parse(jsonStr!) as any[];
      return getType(value) === 'array' ? value : [value];
    } catch (error) {
      return [];
    }
  }
  /**
   * key value 数组转对象
   */
  export function entriesToObject<T extends any>(
    arr: [string, T][],
  ) {
    const obj: Record<string, T> = {};
    arr.forEach(([key, value]) => {
      obj[key] = value;
    });
    return obj;
  }
  /**
   *@description 数组元素从from移动到to
   */
  export function shiftArrayElements<T extends any[]>(
    arr: T,
    from: number,
    to: number,
  ) {
    const actualTo =
      to > arr.length ? arr.length : to < 0 ? 0 : to;
    // 注意不要用splice
    const element = arr[from];
    spliceDelete(arr, from);
    arr.splice(actualTo, 0, element);
    return arr;
  }
  /**
   * base64转blob
   */
  export function base64ToBlob(
    base64: string,
    type = 'image/png',
  ) {
    const bytes = window.atob(base64);
    const bytesCode = new ArrayBuffer(bytes.length);
    const byteArray = new Uint8Array(bytesCode);
    for (let i = 0; i < bytes.length; i++) {
      byteArray[i] = bytes.charCodeAt(i);
    }
    return new Blob([byteArray], { type });
  }
  export type AssertNotPromise<T extends any> =
    T extends Promise<infer U> ? U : T;
  /**
   * @description 对象嵌套mixin
   * @param target 目标对象
   * @param source  源对象
   * @param isEqual  以此算法判断是否碰撞
   * @returns  返回新的混合对象
   * @name 别名 deepMerge
   */
  export function mixin<
    T extends object | Array<any>,
    U extends object | Array<any>,
  >(
    target: T,
    source: U,
    isEqual?: (a: any, b: any) => boolean,
    // 跳过合并的key,以source为准
    skipKeys=[] as string[],
  ): T & U {
    // 防止污染源对象
    let newTarget = deepClone(target) as T & U;
    if (getType(newTarget) !== getType(source)) {
      return newTarget;
    }
    if (getType(newTarget) === 'object') {
      for (const key in source) {
        // 碰撞
        if (newTarget.hasOwnProperty(key) && !skipKeys.includes(key)) {
          if (typeof newTarget[key] === 'object') {
            //@ts-ignore
            newTarget[key] = mixin(
              //@ts-ignore
              newTarget[key],
              source[key],
              isEqual,
              skipKeys
            );
          } else {
            //@ts-ignore
            newTarget[key] = source[key];
          }
        } else {
          // 不碰撞
          //@ts-ignore
          newTarget[key] = source[key];
        }
      }
    } else if (getType(newTarget) === 'array') {
      //@ts-ignore
      if(source.length === 0 ) return []
      for (const value of source as any[]) {
        const type = getType(value);
        if (type !== 'object' && type !== 'array') {
          //@ts-ignore
          newTarget.push(value);
        } else {
          //@ts-ignore
          newTarget.push(
            mixin(type === 'object' ? {} : [], value,isEqual,skipKeys),
          );
        }
      }
      //@ts-ignore
      newTarget = uniqueByEqual(newTarget, isEqual);
    }

    return newTarget;
  }
  /** 以值相等算法去重 */
  export function uniqueByEqual<T extends any>(
    arr: T[],
    isEqualAl = isEqual,
  ) {
    const newAry: T[] = [];
    for (const value of arr) {
      const type = getType(value);
      if (type !== 'object' && type !== 'array') {
        if (!newAry.includes(value)) {
          newAry.push(value);
        }
      } else if (
        //@ts-ignore
        !newAry.find((v) => isEqualAl(v, value))
      ) {
        newAry.push(value);
      }
    }
    return newAry;
  }
  // 判断对象是否相等
  export function isEqual<T extends object | Array<any>>(
    obj1: T,
    obj2: T,
  ) {
    return JSON.stringify(obj1) === JSON.stringify(obj2);
  }
  export function styleObjToString(styleObj: any) {
    return Object.entries(styleObj)
      .map(([key, value]) => `${key}:${value}`)
      .join(';');
  }
  export function styleStringToObj(styleStr: string) {
    try {
      const obj: Record<string, string> = {};
      styleStr.split(';').forEach((item) => {
        const [key, value] = item.split(':');
        obj[key.trim()] = value.trim();
      });
      return obj;
    } catch (error) {
      console.error(error);
      return {};
    }
  }
  /**
   * @description: 取出react的泛型类型
   */
  export type GetGenericType<T> = T extends React.FC<
    infer U
  >
    ? U
    : T;
  /**
   * 去除url的hash
   */
  export function removeHash(url: string) {
    return url.split('#')[0];
  }
  /** 网络url转换为文件流*/
  export async function urlToFileAndBlob(
    url: string,
    fileName: string,
    onProgress = (progress: number) => {
      console.log(progress, 'progress');
    },
  ) {
    try {
      const response = await fetch(url);
      const reader = response?.body?.getReader();
      // 文件总大小
      const contentLength = response.headers.get(
        'content-length',
      );
      let receivedLength = 0;
      const chunks = [];
      while (true) {
        if (!reader) {
          break;
        }
        const { done, value } = await reader.read();
        if (done) {
          break;
        }
        chunks.push(value);
        receivedLength += value.length;
        onProgress(
          Number(
            (
              (receivedLength / Number(contentLength)) *
              100
            ).toFixed(0),
          ),
        );
      }
      // 处理二进制数据
      const arrayBuffer = new Uint8Array(receivedLength);
      let position = 0;
      for (const chunk of chunks) {
        arrayBuffer.set(chunk, position);
        position += chunk.length;
      }
      const blob = new Blob([arrayBuffer]);
      return {
        file: new File([blob], fileName),
        blob,
        arrayBuffer,
      };
    } catch (error: any) {
      throw new Error(error);
    }
  }
  /**
   * @description 来自于radash 中的库, 仅仅为异步服务
   */
  export function tryit<
    T extends (...args: any) => Promise<any>,
    // K = Awaited<ReturnType<T>>,
  >(
    fn: T,
    /**
     * @description  处理结果,返回msg则抛错,没有返回则非错误
     */
    extraResultThrowHandle: (
      result: Awaited<ReturnType<T>>,
    ) => string | undefined | void = () => undefined,
  ) {
    const result: [Error?, Awaited<ReturnType<T>>?] = [];
    return async (...params: Parameters<T>) => {
      try {
        const data = await fn(...(params as any[]));
        const msg = extraResultThrowHandle(data);
        if (msg) {
          throw new Error(msg);
        }
        result[1] = data;
      } catch (error) {
        result[0] = error as Error;
      }
      return result;
    };
  }
  /**
   * @description tryit 的默认判错函数
   */
  export const tryitRequestDefaultThrowHandleFn = (
    wrapper: any,
  ) => {
    if(!wrapper) return "服务器错误"
    const r =wrapper.data
    if (!r?.data) {
      return r?.message ??'服务器错误'
    }
  };
  /**
   * @description 此项目的默认请求tryit
   */
  export const tryitRequestDefaultFn = <
    T extends (...args: any) => Promise<any>,
  >(
    fn: T,
  ) => {
    return tryit(fn, tryitRequestDefaultThrowHandleFn);
  };
  /**
   * @description 安全解析json字符串
   * @param text 字符串
   * @param defaultValue 默认值
   * @param ensureType 确保类型
   */
  export function safeParse<T = any>(
    text: string,
    defaultValue: T = {} as T,
    ensureType: Type = 'object',
  ): T {
    try {
      const val = JSON.parse(text);
      if (getType(val) === ensureType) {
        return val as T;
      } else {
        return defaultValue as T;
      }
    } catch (error) {
      return defaultValue as T;
    }
  }

    /**
   * 从对象中根据给定的键路径获取值
   *
   * @param o 要从中获取值的对象
   * @param keyPath 键路径的字符串数组
   * @returns 返回从对象中根据键路径获取到的值，若无法获取则返回 null
   */
    export function get<T = any>(
      o: Record<string, any>,
      keyPath: (number|string)[] | (number|string),
    ) {
      if(!Array.isArray(keyPath )) {
        return get(o,[keyPath])
      }
      return keyPath.reduce((o, key) => {
        if (isNil(o)) {
          return o;
        } else if (typeof o === 'object') {
          return (o as any)[key];
        } else {
          console.warn('get 参数错误');
          return null;
        }
      }, o) as T;
    }
    /** 百纳秒转秒 */
    export function bn2s(bn: number) {
      return bn / 10000000;
    }
    export function strongColumns(
      columns:(ColumnType<any> &{
        hideInTable?:boolean
      })[]
    ){
      return columns.filter(
        (item) => !item.hideInTable
      )
    }
}
