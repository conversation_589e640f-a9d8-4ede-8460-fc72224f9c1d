/*
 * @Author: lijin
 * @Description: 提问组件
 * @Date: 2022-02-21 11:49:36
 * @LastEditTime: 2022-04-11 15:00:56
 * @LastEditors: 李武林
 * @FilePath: \coursemanger\src\pages\CourseQA\components\topicItem.tsx
 */
import {
  Button,
  Divider,
  List,
  message,
  Popconfirm,
  Space,
  Tag,
  Tooltip,
  Typography
} from
  'antd';
import React, { useState } from 'react';
import { FC } from 'react';
import './topicItem.less';
import {
  MessageOutlined,
  FieldTimeOutlined,
  UserOutlined,
  LockOutlined,
  UnlockOutlined,
  DeleteOutlined
} from
  '@ant-design/icons';
import moment from 'moment';
import QAService from '@/api/qa';
import { IconFont } from '@/components/iconFont';
import { useLocation } from 'umi';
import { PERMISSION_LIST } from '../utils';
import useLocale from '@/hooks/useLocale';
const { Paragraph, Text } = Typography;

interface TopicItemProps {
  topic: QA.Topic;
  ifDelete: boolean; // 教师角色可以删除，学生角色只能删除自己的
  canOpen?: boolean;
  knowledge?: { knowledgeName: string; id: number; } | null;
  isDetail?: boolean;
  onTopicClick?: (topicid: string) => void;
  onTopicDelete?: (topicid: string) => void;
  onPublicClick?: (topicid: string, isPublic: boolean, permissions?: string) => void;
  ismap?: boolean;
}
const TopicItem: FC<TopicItemProps> = ({
  topic,
  knowledge,
  canOpen,
  ifDelete,
  children,
  isDetail = false,
  onTopicClick,
  onPublicClick,
  onTopicDelete,
  ismap
}) => {
  const { t } = useLocale();
  const location: any = useLocation();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const IconText = ({ icon, text }: any) =>
    <Space>
      {React.createElement(icon)}
      {text}
    </Space>;

  /**
   * @description: 删除主题
   * @param {string} topicid
   * @return {*}
   */
  const deleteTopic = async (topicid: string) => {
    setDeleteLoading(true);

    const result = await QAService.deleteTopics([topicid], location.query.id).catch(() => {
      message.error(t('删除失败'));
      setDeleteLoading(false);
    });
    if (result.error_code === 'forumservice.0000.0000') {
      message.success(t('删除成功'));
    }
    setDeleteLoading(false);
    onTopicDelete && onTopicDelete(topicid);
  };
  return (
    <div className={"topic_container"}>
      <div className={`title-wrp ${!isDetail ? 'flex-sb' : ''}`}>
        <Space className={"title_container"}>
          {location.query.type === "mooc" && topic.permissions && topic.permissions != "0" &&
            <Tooltip
              title={!window.location.hash.includes("out") ?
                `${t("已公开")}${t(PERMISSION_LIST.filter((item: any) => item.value === topic.permissions)?.[0]?.label)}` : t("此被老师选为公开问答")}>


              <div className='public-tag'>{t("公开")}</div>
            </Tooltip>}
          <div
            style={onTopicClick ? { cursor: 'pointer' } : {}}
            className={"topic_title"}
            onClick={() => onTopicClick && onTopicClick(topic.topic_id)}>

            <Text ellipsis={{ tooltip: topic.title }}>{topic.title}</Text>
          </div>
          {/* {topic.unread_flag && <div className={Styles.new_tag}>NEW</div>} */}
          {(topic.unread_subtotal || 0) > 0 && !isDetail &&
            <div
              className={"new_reply_tag"}>
              {`${topic.unread_subtotal}${t("条新回复")}`}</div>}

          {topic.unread_id && !isDetail && <div className={"new_topic_tag"}>new</div>}
        </Space>
        <Space
          size={10}
          className={"info_msg"}
          split={<Divider type="vertical" />}>

          {(topic.extend_type == '01' || topic.extend_type == '03' || topic.extend_type === '05' || topic.extend_type === '11' || topic.extend_type === '14') && !ismap &&
            <span
              className={"info_knowledge"}
              onClick={() => message.info('暂不支持知识点跳转')}>
              {t("相关知识点：")}
              {topic.extend_message}
            </span>}

          {(topic.extend_type == '04' || topic.extend_type == "02" || topic.extend_type === '06' ) && !ismap &&
            <span
              className={"info_knowledge"}
              onClick={() => window.open(topic.extend_url)}>
              {t("相关内容：")}
              <a>{topic.extend_message}</a>
            </span>}

          <IconText icon={UserOutlined} text={topic.user_name} />
          <IconText
            icon={FieldTimeOutlined}
            text={
              moment.unix(topic.created).format('YYYY-MM-DD HH:mm:ss') + t("提问")} />


        </Space>
      </div>
      <Paragraph className={"content_info"} ellipsis={!isDetail}>
        {topic.content}
      </Paragraph>
      <div className={"bottom_container"}>
        {!isDetail &&
          <Button
            icon={<MessageOutlined />}
            type="text"
            onClick={() => onTopicClick && onTopicClick(topic.topic_id)}>
            {t("回复")}{topic.children_comment_count? ' (' + topic.children_comment_count + ')': ''}

          </Button>}
        {ifDelete &&
          <Space size={10}>
            <Popconfirm
              title={t("是否删除该提问？")}
              okText={t("确认")}
              cancelText={t("取消")}
              onConfirm={() => deleteTopic(topic.topic_id)}>

              <Button
                // danger
                loading={deleteLoading}
                icon={<DeleteOutlined />}
                type="text">
                {t("删除")}

              </Button>
            </Popconfirm>
          </Space>}

        {canOpen && (topic.permissions && topic.permissions != "0" ?
          <Space>
            <Button type='text' onClick={() => onPublicClick?.(topic.topic_id, true, topic.permissions)} icon={<LockOutlined />}>{t("修改公开类型")}</Button>
            <Button type='text' onClick={() => onPublicClick?.(topic.topic_id, false)} icon={<LockOutlined />}>{t("取消公开")}</Button>
          </Space> :
          <Button type='text' icon={<UnlockOutlined />} onClick={() => onPublicClick?.(topic.topic_id, true)}>{t("设为公开")}</Button>)}
      </div>
      {children && <div className={"comment_wrapper"}>{children}</div>}
    </div>);

};
export default TopicItem;