import React, { useState, useEffect, FC, useRef } from "react";
import ReactDOM from 'react-dom';
import './index.less';
import { Graph } from '@antv/x6';
// 引入react插件
import '@antv/x6-react-shape';
// 引入布局插件
// import { GridLayout, RandomLayout, GForceLayout, ForceLayout, Force2Layout, CircularLayout, DagreLayout, DagreCompoundLayout, RadialLayout, ConcentricLayout, MDSLayout, FruchtermanLayout, FruchtermanGPULayout, GForceGPULayout, ComboForceLayout, ComboCombinedLayout, ForceAtlas2Layout, ERLayout } from '@antv/layout';
import { Force2Layout, DagreLayout } from '@antv/layout';
import { Tooltip, Button } from 'antd';
import {
  PlusOutlined,
  MinusOutlined,
  SyncOutlined,
  ZoomInOutlined,
  ZoomOutOutlined
} from
  '@ant-design/icons';
import { defaultNodeData } from "../util";
import { useListener } from 'react-bus';
import useLocale from "@/hooks/useLocale";
import { imgbase64 } from './imgbase64'
import { IconFont } from '@/components/iconFont';
import packup_png from '@/assets/imgs/coursemap/v3/packup.png'
import unpack_png from '@/assets/imgs/coursemap/v3/unfold.png'


const MapX6: FC<any> = ({ mapdata,skin='1', initover, nodeClick, showmenu = true, scale = false, showzoombtn = false, clicktag, maptype = 1, showunfold = false, layouttype = 1 ,showmindmap=false}) => {
  const { t } = useLocale();
  const container = useRef<any>(null);
  const graph = useRef<any>(null); //全局方法
  const minimap =  useRef<any>(null); //小地图
  let contnum: number = 1; //1到5之间  轮流取值
  let colormodel: any = {}; //颜色字典表
  const [selectnode, setselectnode] = useState<boolean>(false); //当前选中的节点
  const [mapstatus, setmapstatus] = useState<number>(0); //0 未加载 1加载中  2加载完成
  const [dropdownVisible, setDropdownVisible] = useState<boolean>(false);

  //总线通信
  useListener('showcenter', (val: any) => {
    if (val) {
      // 显示根节点
      layout(mapdata,2,layouttype);
    } else {
      let rootnode = graph.current.getRootNodes();
      // 隐藏根节点
      let nodes = mapdata.nodes.filter((item: any) => item.nodeId != rootnode[0].store.data.nodeId);
      let edges = mapdata.edges.filter((item: any) => item.source != rootnode[0].store.data.nodeId);
      setcolormodel(edges);
      if (maptype == 1) {
        layout({ nodes, edges }, 3,layouttype);
      } else {
        layout({ nodes, edges },2,layouttype);
      }
    }
  });

  useEffect(() => { 
    if(mapstatus==2){
      const alledges = graph.current.getEdges();
      alledges.forEach((item:any) => {         
        if(skin == '1'){
          item.attr('line/stroke', '#B3BAC4');     
        }else{
          item.attr('line/stroke', '#B3BAC4');     
        }
      });
    }
    (window as any).skin = skin;
  }, [skin]);

  // 这里为什么没有弄成监听模式  是因为这个是标准组件 用到了其他的模块
  useEffect(() => {
    if(mapstatus == 2){
      console.log('修改了')
      layout(mapdata,2,layouttype);
    }
  }, [layouttype]);


  // 当前显示模式
  const [showmodel, setshowmodel] = useState<number>(0); //0 全部 1 包含  2 等价  3 后续 4关联

  useEffect(() => {
    if (mapdata) {
      // 获取第二层的节点
      let towedges = mapdata.edges.filter((item: any) => item.source == mapdata.nodes[0].id && item.data.newadd == false);
      setcolormodel(towedges);
      initmap();
    }

    return () => {
      contnum = 1;
      // colormodel = {};
    };
  }, [mapdata]);

  // 设置当前节点颜色字典
  const setcolormodel = (towedges: any) => {
    colormodel = {};
    towedges.forEach((item: any) => {
      // 只有5个图标来随机 1到5之间
      if (contnum == 6) {
        contnum = 1;
      }
      // 这里为了方便子集 取用父级的颜色 存了一个字典表
      colormodel[item.target] = contnum;
      contnum++;
    });
  };

  // 展开收起所有子节点
  const x6collapsed = (node: any, childarr: any, collapsed: boolean) => {
    const data: any = node.getData();
    // 修改数据 让节点重新渲染
    node.updateData({
      ...data,
      isCollapsed: collapsed
    });
    // 递归数据
    const run = (pre: any) => {
      pre.setZIndex(9);
      // 获取所有相邻的输出节点
      let succ = graph.current.getNeighbors(pre, { outgoing: true });
      console.log('succ', succ);
      // 这里是把 用户自己连线 的节点 不受展开收起按钮控制
      graph.current.getEdges().forEach((item: any) => {
        if (item.data.newadd) {
          // 根据id删除succ里面的
          succ = succ.filter((item2: any) => {
            if (item.store.data.source.cell == pre.id && item.store.data.target.cell == item2.id) {
              return false;
            } else {
              return true;
            }
          });
        }
      });

      succ.forEach((item: any) => {
        const data2: any = item.getData();
        if (collapsed) {
          item.show();
        } else {
          item.hide();
        }
        // 判断下面还有没有子节点  collapsed 是控制只展开下一级
        if (childarr.length && !collapsed) {
          // 修改数据 让节点重新渲染
          item.updateData({
            ...data2,
            isCollapsed: collapsed
          });
          run(item);
        }
      });

    };
    run(node);
    resetstyle();
  };

  const registerstyle = () => {
    try {
      // 注册返回 React 组件的函数
      Graph.registerReactComponent('react-compont-v3', (node) => {
        // 获取前序节点
        // let prenode = graph.current.getPredecessors(node,{distance:1});
        // 当前随机出来的图标
        let iconnum = 1;
        const nodedata = node.getData();
        const data = {
          ...defaultNodeData,
          ...nodedata
        };
        // 获取当前下面有没有子节点
        // let childlength = graph.current.getSuccessors(node, { distance: 1 });
        // 获取当前节点的所有边  过滤掉用户自己连的边  用户自己链接的边  不出现展开收起按钮
        let arrEdges = graph.current.getEdges().filter((item: any) => {
          if (node.id == item.store.data.source.cell && !item.store.data.data.newadd) {
            return true;
          }
        });
        if (node.data.isroot == false || node.data.isroot == undefined) {
          if (colormodel[node.id]) {
            iconnum = colormodel[node.id];
            // // 获取所有的子节点 给所有子节点加上标识
            let arredges = mapdata.edges.filter((item: any) => {
              if (item.source == node.id && item.data.newadd == false) {
                return true;
              }
            });
            arredges.forEach((item: any) => {
              colormodel[item.target] = iconnum;
            });
          }
        }

        const imgdom = () => {
          if (Number(data.type) == 1 && data.isroot == true) {
            return <img style={{ width: '100%', height: 'auto' }} src={imgbase64[`course${iconnum}`]}></img>;
          } else {            
            return <>
              {data.type == 5 && <img style={{ width: '100%', height: 'auto' }} src={imgbase64[`teachingmodule${iconnum}`]}></img>}
              {data.type == 4 && <img style={{ width: '100%', height: 'auto' }} src={imgbase64[`marjor${iconnum}`]}></img>}
              {data.type == 3 && <img style={{ width: '100%', height: 'auto' }} src={imgbase64[`course${iconnum}`]}></img>}
              {data.type == 2 && <img style={{ width: '100%', height: 'auto' }} src={imgbase64[`knowledge${iconnum}`]}></img>}
              {data.type == 1 && <img style={{ width: '100%', height: 'auto' }} src={imgbase64[`fenlei${iconnum}`]}></img>}
            </>;
          }
        };

        const unfold = () => {
          if (data.isCollapsed) {
            return <div data-event='unfold' data-flage='false' style={{ width: '30px', height: '30px', display: 'inline-block', paddingRight: '5px' }}>
              <img data-event='unfold' data-flage='false' style={{ width: '18px', height: 'auto', marginTop: '3px' }}
                src={packup_png} />
            </div>;
          } else {
            return <div data-event='unfold' data-flage='true' style={{ width: '30px', height: '30px', display: 'inline-block', paddingRight: '5px' }}>
              <img data-event='unfold' data-flage='true' style={{ width: '18px', height: 'auto', marginTop: '3px' }}
                src={unpack_png} />
            </div>;
          }
        };

        const showtag = data.isyinandian || data.isDiscriminate || data.iscoreknowledge || data.isexpandknowledge || data.linkmapid.length > 0 || data.isexperiment || data.iscase;
        let resourcelength = (data.bindresource?.length || 0) + (data.homework?.length || 0) + (data.bindlink?.length || 0) + (data.referenceMaterials?.length || 0);
       
        return (
          <div style={{ width: '100%', height: '100%', borderRadius: '50%', position: 'relative', display: 'flex', justifyContent: 'center', cursor: 'pointer',filter: data.showgrey ? 'grayscale(100%)':''}}>
            <div style={{ width: '100%', height: '100%' }}>
              {imgdom()}
            </div>
            <div style={{ position: 'absolute', top: '100%', width: 'auto', height: '100%', display: 'flex', alignItems: 'center' }}>
              {
                data.isyinandian &&
                <div style={{
                  width: '24px',
                  height: '18px',
                  background: '#FF5854',
                  borderRadius: '4px',
                  textAlign: 'center',
                  lineHeight: '18px',
                  marginLeft: '4px',
                  color: '#fff',
                  fontSize: '12px'
                }}>{t("难")}</div>}

              {
                data.iscoreknowledge &&
                <div style={{
                  width: '24px',
                  height: '18px',
                  background: '#FD8059',
                  borderRadius: '4px',
                  textAlign: 'center',
                  lineHeight: '18px',
                  marginLeft: '4px',
                  color: '#fff',
                  fontSize: '12px'
                }}>{t("核")}</div>}

              {
                data.isexpandknowledge &&
                <div style={{
                  width: '24px',
                  height: '18px',
                  background: '#FCBD6F',
                  borderRadius: '4px',
                  textAlign: 'center',
                  lineHeight: '18px',
                  marginLeft: '4px',
                  color: '#fff',
                  fontSize: '12px'
                }}>{t("拓")}</div>}

              {
                data.isDiscriminate &&
                <div style={{
                  width: '24px',
                  height: '18px',
                  background: '#34BAD1',
                  borderRadius: '4px',
                  textAlign: 'center',
                  lineHeight: '18px',
                  marginLeft: '4px',
                  color: '#fff',
                  fontSize: '12px'
                }}>{t("辨")}</div>}
              
              {
                data.isexperiment &&
                <div style={{
                  width: '24px',
                  height: '18px',
                  background: '#6CCC85',
                  borderRadius: '4px',
                  textAlign: 'center',
                  lineHeight: '18px',
                  marginLeft: '4px',
                  color: '#fff',
                  fontSize: '12px'
                }} data-event='showlinkmap'>{t("实")}</div>}

              {
                data.iscase &&
                <div style={{
                  width: '24px',
                  height: '18px',
                  background: '#8471FF',
                  borderRadius: '4px',
                  textAlign: 'center',
                  lineHeight: '18px',
                  marginLeft: '4px',
                  color: '#fff',
                  fontSize: '12px'
                }} data-event='showlinkmap'>{t("案")}</div>}

              {
                data.linkmapid.length > 0 &&
                <div style={{
                  width: '24px',
                  height: '18px',
                  background: '#549CFF',
                  borderRadius: '4px',
                  textAlign: 'center',
                  lineHeight: '18px',
                  marginLeft: '4px',
                  color: '#fff',
                  fontSize: '12px'
                }} data-event='showlinkmap'>{t("跨")}</div>}

            </div>
            <div style={{
              position: 'absolute',
              width: '185px',
              height: 'auto',
              top: showtag ? '200%' : '100%',
              textAlign: 'center',
              whiteSpace: 'pre-wrap',
              color: ' #fff',
              cursor: 'pointer'
            }} className="name_view">
              {/* 渲染展开收起按钮 */}
              {showunfold && arrEdges.length > 0 && unfold()}
              <span>{data.label}</span>
              {data.type == 5 && <span style={{marginLeft:'20px'}}>{data.score || '-'}分</span> }
              {resourcelength > 0 && data.type == 2 ? <span style={{ display: 'block' }}>{t("（资源")}{resourcelength}）</span> : null}
            </div>
            {
              data.type == 5 && <span style={{
                position: 'absolute',
                width:'130px',
                left: '-30px',
                top: '135%',
                fontSize: '12px',
                color: '#fff'
              }}>{data.studytime[0]?.replace('-','.')}{data.studytime.length>0 ? '~' :""}{data.studytime[1]?.replace('-','.')}</span>
            }
          </div>);

      });
    } catch (error) {

      // console.log('注册组件报错！',error);
    }
  };

  const initmap = () => {
    const containerdom: any = ReactDOM.findDOMNode(container.current);
    const width = containerdom.scrollWidth;
    const height = containerdom.scrollHeight || 800;
    if (graph.current) {
      try {
        graph.current.dispose();
        Graph.unregisterReactComponent('react-compont-v3');
      } catch (error) {
        console.log(error);
      }
    }
    // x6 注册react 组件
    registerstyle();
    try {
      // 实例初始化
      graph.current = new Graph({
        container: containerdom,
        autoResize: true,
        // width,
        // height,
        async: true,
        //   frozen: true,
        panning: {
          enabled: true
        },
        // 滚轮放大缩小
        mousewheel: {
          enabled: true,
          modifiers: [] //配置快捷键 触发
          // minScale: 0.1,                 
        },
        selecting: {
          enabled: true,
          rubberband: true, // 启用框选
          movable: true, //拖动选框框选的节点一起移动
          showNodeSelectionBox: false, //是否显示节点的选中样式
          showEdgeSelectionBox: false, //是否显示边的选中样式
          modifiers:['ctrl']// 快捷键
        },
        minimap: {
          enabled: true,
          container: minimap.current,
          width:190,
          height:126,
          padding:10
        }
      });

      // 节点点击事件
      graph.current.on('node:click', ({ e, x, y, node, view }: any) => {
        // 监听事件来源 unfold是展开收起  showlinkmap是展示地图关联关系
        if (e.target.dataset.event == 'unfold') {
          let childlength = graph.current.getSuccessors(node, { distance: 1 });
          x6collapsed(node, childlength, e.target.dataset.flage == 'true');
          if(e.target.dataset.flage == 'false'){
            shownode(node);
            setselectnode(true);
          }
        } else if (e.target.dataset.event == 'showlinkmap') {
          if (clicktag) {
            clicktag(node);
          }
        } else {
          if (nodeClick) {
            nodeClick(node);
          }
          shownode(node);
          setselectnode(true);
        }
        let succ = graph.current.getSuccessors(node);
        graph.current.select(succ);
      });
      graph.current.on('edge:click', ({ e, x, y, edge, view }: any) => {
        showlinetonode(edge);
        setselectnode(true);
      });
      //点击画布空白区域 
      graph.current.on('blank:click', ({ e, x, y, node, view }: any) => {
        resetstyle();
        setselectnode(false);
      });
      // 如果开启了缩放 隐藏节点功能才监听
      if (scale) {
        graph.current.on('scale', ({ sx, sy, ox, oy }: any) => {
          let preselectnode = false;
          setselectnode((pre) => {
            preselectnode = pre;
            return pre;
          });
          console.log(sx, preselectnode);

          if (!preselectnode) {
            if (sx >= 1) {
              // 显示所有的知识节点
              graph.current.getNodes().forEach((item: any) => {
                item.attr('foreignObject/opacity', 1);
              });
            } else if (sx >= 0.65 && sx < 0.9) {
              // 隐藏所有的知识节点
              graph.current.getNodes().forEach((item: any) => {
                const data = item.getData();
                if (data.type == 1) {
                  // 设置节点透明
                  item.attr('foreignObject/opacity', 1);
                } else if (data.type == 2) {
                  // 设置节点透明
                  item.attr('foreignObject/opacity', 0.2);
                }
              });
            } else if (sx < 0.65) {
              // 隐藏所有的分类节点
              graph.current.getNodes().forEach((item: any) => {
                const data = item.getData();
                if (data.type == 1 || data.type == 2) {
                  item.attr('foreignObject/opacity', 0.2);
                }
              });
            } else {

            }
          }
        });
      }
      // 执行布局
      layout(mapdata);
    } catch (error) {
      console.log(error);
    }
  };

  // 布局方法
  const layout = (data: any, factor: number = 2,type:number=1) => {
    if(type == 1){      
      const gridLayout = new Force2Layout({
        type: 'gForce',
        preventOverlap: true, // 防止节点重叠     
        factor: factor, //  斥力的一个系数  这个比较重要
        // maxIteration: 10000,        // 布局迭代次数
        workerEnabled: true,
        // linkDistance: 100,
        // maxSpeed:100,
        // clustering: true,
        // nodeClusterBy: 'type',
        // clusterNodeStrength: 100,         
        // tick: () => {
        //     console.log('ticking');
        //     graph.current.fromJSON(model);
        // },
        onLayoutEnd: () => {
          console.log('force layout done', data);
          graph.current.fromJSON(data);
          graph.current.centerContent();
          if (initover) {
            initover(graph.current);
          }
          setmapstatus(2);
        }
      });
      gridLayout.layout(data);    
    }else{
      const layout = new DagreLayout({
        type: 'dagre', 
        rankdir: 'LR',
        align: 'DR',
        controlPoints:true,
        nodesep:25, // 节点水平间距
        ranksep:70, // 节点垂直间距
        // radial: true, // 辐射状布局    
        onLayoutEnd: () => {
          console.log('force layout done', data);
          graph.current.fromJSON(data);
          // 根节点居中
          if (initover) {
            initover(graph.current);            
          }
          setmapstatus(2);
        }
      });
      layout.layout(data);
    }
  };


  // 点击连线　展示2个节点的关系
  const showlinetonode = (edge: any) => {
    graph.current.getNodes().forEach((item: any) => {
      // 设置节点透明
      item.attr('foreignObject/opacity', 0.2);
    });
    graph.current.getEdges().forEach((item: any) => {
      if((window as any).skin == '1'){
        // 设置节点透明
        item.attr('line/stroke', '#333333');
      }else{
        // 设置节点透明
        item.attr('line/opacity', 0.2);
      }
      edge.attr('line/strokeWidth', 1);
    });
    // 获取相邻节点
    let neinode = graph.current.getNeighbors(edge);
    neinode.forEach((item: any) => {
      item.attr('foreignObject/opacity', 1);
    });
    edge.attr('line/stroke', '#B3BAC4');
    edge.attr('line/opacity', 1);
    
  };

  // 筛选边
  const showedge = (type: number) => {
    resetstyle();
    graph.current.getEdges().forEach((item: any) => {
      const data = item.getData();
      // 选择全部
      if (type == 0) {
        item.attr('line/stroke', '#B3BAC4');
        item.attr('line/strokeWidth', 1);
        item.attr('line/opacity', 1);
      } else {
        //1包含 2等价 3后续
        if (type == data.type) {
          if((window as any).skin == '1'){
            item.attr('line/stroke', '#fff');
            item.attr('line/opacity', 1);
          }else{
            item.attr('line/stroke', '#B3BAC4');
            item.attr('line/opacity', 1);
          }
          // 加粗
          item.attr('line/strokeWidth', 2);          
        } else {
          if((window as any).skin == '1'){
            item.attr('line/stroke', '#333333');
            item.attr('line/opacity', 1);
          }else{
            item.attr('line/stroke', '#B3BAC4');
            item.attr('line/opacity', 0.4);
          }
          item.attr('line/strokeWidth', 1);
        }
      }
    });
    setshowmodel(type);
    setselectnode(true);
  };

  // 重置所有节点和连线的样式
  const resetstyle = () => {
    graph.current.getNodes().forEach((item: any) => {
      // 设置节点透明
      item.attr('foreignObject/opacity', 1);
    });
    graph.current.getEdges().forEach((item: any) => {
      // 设置节点透明
      item.attr('line/stroke', '#B3BAC4');
      item.attr('line/opacity', 1);
      item.attr('line/strokeWidth', 1);
    });
  };

  // 选中节点处理事件
  const shownode = (node: any) => {
    // 设置当前边的筛选模式
    setshowmodel(0);
    // 获取相邻节点
    let neinode = graph.current.getNeighbors(node);
    graph.current.getNodes().forEach((item: any) => {
      // 设置节点透明
      item.attr('foreignObject/opacity', 0.2);
    });
    if (neinode.length) {
      neinode.forEach((item: any) => {
        item.attr('foreignObject/opacity', 1);
      });
      node.attr('foreignObject/opacity', 1);
    }
    // 获取相邻边
    let neiedge = graph.current.getConnectedEdges(node);
    graph.current.getEdges().forEach((item: any) => {
      if((window as any).skin == '1'){
        // 设置节点透明
        item.attr('line/stroke', '#333333');
      }else{
        // 设置节点透明
        item.attr('line/opacity', 0.2);
      }
      item.attr('line/strokeWidth', 1);
    });
    if (neiedge.length) {
      neiedge.forEach((item: any) => {
        item.attr('line/stroke', '#B3BAC4');
        item.attr('line/opacity', 1);
      });
      node.attr('line/stroke', '#B3BAC4');
      node.attr('line/opacity', 1);
    }
  };

  // 放大缩小画布   code 有三个参数  init 是初始化   enlarge 是放大   narrow是缩小
  const zoomdom = (code: string) => {
    if (code == 'enlarge') {
      const zoom = parseFloat(graph.current.zoom().toFixed(2));
      graph.current.zoomTo(zoom + 0.2);
    } else if (code == 'narrow') {
      const zoom = parseFloat(graph.current.zoom().toFixed(2));
      graph.current.zoomTo(zoom - 0.2);
    }
  };

  // 展开所有
  const expandall = (flage: boolean) => {
    // 获取所有节点
    const nodes = graph.current.getNodes();
    const successors = graph.current.getSuccessors(nodes[0], { distance: 1 }).map((item: any) => item.id);
    nodes.forEach((node: any) => {
      const data = node.getData();
      const isroot = graph.current.isRootNode(node);
      // 如果不是根节点和二级菜单
      if (!isroot) {
        // 设置展开状态
        node.updateData({
          ...data,
          isCollapsed: flage
        });
      }
      if (isroot || successors.includes(node.id)) {
        node.setVisible(true);
      } else {
        node.setVisible(flage);
      }
      // if (!data.isroot && !successors.includes(node.id)) {
      //   node.setVisible(flage);
      // }
    });    
  };

  return (
    <div className={skin == '1' ? "mapv3_x6_view": "mapv4_x6_view_dark" }>
      <div className="map_canvas" ref={container}></div>
      {showmenu &&
        <div className="options_dropdown">
          <div 
            className="dropdown_trigger"
            onClick={() => setDropdownVisible(!dropdownVisible)}
          >
            {dropdownVisible ? t("收起筛选") : t("关系筛选")}
          </div>
          {dropdownVisible && (
            <div className="dropdown_content">
              <div className={showmodel == 0 ? 'item active' : 'item'} onClick={() => {showedge(0); setDropdownVisible(false)}}>
                <span>{t("全部")}</span>
              </div>
              <div className={showmodel == 1 ? 'item active' : 'item'} onClick={() => {showedge(1); setDropdownVisible(false)}}>
                <span>{t("包含")}</span>
                <IconFont className="icon_dropdown" type="icona-lujing7beifen5" alt="包含" />
              </div>
              <div className={showmodel == 3 ? 'item active' : 'item'} onClick={() => {showedge(3); setDropdownVisible(false)}}>
                <span>{t("后续")}</span>
                <IconFont className="icon_dropdown" type="icona-lujing7beifen4" alt="后续" />
              </div>              
              <div className={showmodel == 6 ? 'item active' : 'item'} onClick={() => {showedge(6); setDropdownVisible(false)}}>
                <span>{t("依赖")}</span>
                <IconFont className="icon_dropdown" type="icona-lujing7beifen4" alt="依赖" />
              </div>
              <div className={showmodel == 7 ? 'item active' : 'item'} onClick={() => {showedge(7); setDropdownVisible(false)}}>
                <span>{t("递进")}</span>
                <IconFont className="icon_dropdown" type="icona-lujing7beifen4" alt="递进" />
              </div>
              <div className={showmodel == 5 ? 'item active' : 'item'} onClick={() => {showedge(5); setDropdownVisible(false)}}>
                <span>{t("后修")}</span>
                <IconFont className="icon_dropdown" type="icona-lujing7beifen4" alt="后修" />
              </div>
              <div className={showmodel == 8 ? 'item active' : 'item'} onClick={() => {showedge(8); setDropdownVisible(false)}}>
                <span>{t("辩证")}</span>
                <IconFont className="icon_dropdown" type="iconxuxian" alt="辩证" />
              </div>              
              <div className={showmodel == 2 ? 'item active' : 'item'} onClick={() => {showedge(2); setDropdownVisible(false)}}>
                <span>{t("等价")}</span>
                <IconFont className="icon_dropdown" type="iconxuxian" alt="等价" />
              </div>
              <div className={showmodel == 4 ? 'item active' : 'item'} onClick={() => {showedge(4); setDropdownVisible(false)}}>
                <span>{t("关联")}</span>
                <IconFont className="icon_dropdown" type="iconxuxian" alt="关联" />
              </div>
              
            </div>
          )}
        </div>}

      {
        showzoombtn &&
        <div className="mapfunction">
          <Tooltip title={t("放大")} placement="right">
            <Button className="options_item" style={{ backgroundColor: '#2D3847', border: '0' }} icon={<ZoomInOutlined style={{ color: '#B3B3B3' }} />} onClick={() => zoomdom('enlarge')} />
          </Tooltip>
          <Tooltip title={t("缩小")} placement="right">
            <Button className="options_item" style={{ backgroundColor: '#2D3847', border: '0', marginTop: '20px' }} icon={<ZoomOutOutlined style={{ color: '#B3B3B3' }} />} title={t("缩小")} onClick={() => zoomdom('narrow')} />
          </Tooltip>
          <Tooltip title={t("展开")} placement="right">
            <Button className="options_item" style={{ backgroundColor: '#2D3847', border: '0', marginTop: '20px' }} icon={<IconFont type="icona-bianzu7" style={{ color: '#B3B3B3' }} />} title={t("展开所有节点")} onClick={() => expandall(true)} />
          </Tooltip>
          <Tooltip title={t("收起")} placement="right">
            <Button className="options_item" style={{ backgroundColor: '#2D3847', border: '0', marginTop: '20px' }} icon={<IconFont type="icona-bianzu7beifen1" style={{ color: '#B3B3B3' }} />} title={t("收起所有节点")} onClick={() => expandall(false)} />
          </Tooltip>
          <Tooltip title={t("重新布局")} placement="right">
            <Button className="options_item" style={{ backgroundColor: '#2D3847', border: '0', marginTop: '20px' }} icon={<SyncOutlined style={{ color: '#B3B3B3' }} />} title={t("重新布局")} onClick={() => layout(mapdata,2,layouttype)} />
          </Tooltip>

        </div>}
        {
          showmindmap && <div className='minmap_box' ref={minimap}></div>
        }

    </div>);

};

export default MapX6;