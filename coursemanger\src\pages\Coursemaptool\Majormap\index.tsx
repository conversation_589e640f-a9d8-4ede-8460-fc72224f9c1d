import React, { FC, useState, useEffect, useRef } from 'react';
import './index.less';
import { useSelector, useDispatch, useLocation, history } from 'umi';
import {
  Form,
  Input,
  Button,
  Select,
  Checkbox,
  Tooltip,
  message,
  Table,
  Space,
  Modal,
  List,
  Tag,
  Pagination,
  Divider,
  Popover,
} from 'antd';
import moment from 'moment';
import { IconFont } from '@/components/iconFont';
import {
  quermarjormap,
  addmap,
  deletemap,
  copymap,
  updatamapstatus,
} from '@/api/coursemap';
import { PlusCircleFilled, ReloadOutlined } from '@ant-design/icons';
import Search from '../components/Search';
import Addmap from './components/addmap';
import useLocale from '@/hooks/useLocale';
import { useScreenWidth } from '@/hooks/useScreenWidth';
const nomapcover = require('@/assets/imgs/coursemap/nomarjormap.png');
const { confirm } = Modal;

const Majormap: FC<{}> = () => {
  const { t } = useLocale();
  // form表单
  const [form] = Form.useForm();
  // 设置 indeterminate 状态，只负责样式控制
  const [indeterminate, setIndeterminate] = useState<boolean>(false);
  // 指定当前是否选中
  const [checkAll, setCheckAll] = useState<boolean>(false);
  // 表格数据
  const [tableData, setTableData] = useState<any[]>([]);
  // 弹窗的开关
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  // 地图名称
  const [mapName, setMapName] = useState<string>('');
  // table分页
  const [pagination, setPagination] = useState<any>({
    current: 1,
    pageSize: 18,
    total: 0,
    showQuickJumper: true,
    showSizeChanger: true,
    showTotal: (total: number) => t('共{name}条', String(total)),
    onChange: (page: number, pageSize: number) => {
      initmap(page, pageSize);
    },
  });
  const { mobileFlag, leftRightVisible } = useSelector<{ config: any }, any>(
    ({ config }) => config,
  );

  // 当前选中的行
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  // 检索的添加
  const [searchform, setSearchfrom] = useState<any>({
    search_name: null,
    search_status: null,
  });
  // 是否排序
  const [sorter, setSorter] = useState<any>('0');

  // 加载完毕
  const [loadingover, setLoadingover] = useState<boolean>(false);
  // 将要修改的状态
  const [mapstatus, setMapstatus] = useState<number>(0);
  // 修改地图状态弹窗的开关
  const [isModalVisible2, setIsModalVisible2] = useState<boolean>(false);
  // 当前选择的行
  const [selectedid, setSelectedId] = useState<any>(null);
  // 是否是批量
  const [isbatch, setIsbatch] = useState<boolean>(false);
  // 视图切换
  const [modeSwitch, setModeSwitch] = useState<boolean>(
    JSON.parse(localStorage.getItem('learn_view_mode') || '{}').maplist != '0',
  );

  const [operatMenuVisible, setOpreatMenuVisible] = useState<boolean>(false);
  // 动态判断是否是大于1920的尺寸
  const isLargeScreen = useScreenWidth();

  // 初始化数据
  useEffect(() => {
    initmap(1, pagination.pageSize);
  }, []);
  useEffect(() => {
    const temp = localStorage.getItem('learn_view_mode') || '{}';
    const value = {
      ...JSON.parse(temp),
      maplist: modeSwitch ? '1' : '0',
    };
    localStorage.setItem('learn_view_mode', JSON.stringify(value));
  }, [modeSwitch]);
  // 监听查询条件变化数据
  useEffect(() => {
    if (loadingover) {
      initmap(1, pagination.pageSize);
    }
  }, [searchform, sorter]);

  // 获取的我地图列表
  const initmap = (page: number, size: number) => {
    let temp = searchform;
    setSearchfrom((pre: any) => {
      temp = pre;
      return pre;
    });   
    quermarjormap({
      name: temp?.marjor_name || null,
      college: temp?.marjorId ? temp.marjorId.split(',')[0] : null,
      major: temp?.marjorId ? temp.marjorId.split(',')[1] : null,
      isShow: null,
      page: page,
      size: size,
      sort: sorter == '0' ? true : false,
    }).then((res: any) => {
      setTableData(res.data.results);
      let obj = {
        ...pagination,
        current: res.data.page,
        pageSize: res.data.size,
        total: res.data.total,
      };
      setPagination(obj);
      setLoadingover(true);
    });
  };

  // 添加地图
  const addminemap = (e: any) => {
    addmap({
      college: e.collegeId,
      major: e.majorId,
      mapName: e.majorName,
      type: 7,
    }).then((res: any) => {
      if (res.status === 200) {
        message.success(t('新建成功！'));
        setIsModalVisible(false);
        setMapName('');
        initmap(1, pagination.pageSize);
        window.open(
          `${window.location.pathname}#/mapv4/editmap?id=${res.data.id}&key=3`,
        );
      } else {
        message.error(t('操作失败！'));
      }
    });
  };

  // 删除地图
  const deletemamap = (arr: String[]) => {
    confirm({
      title: t('确认删除?'),
      onOk() {
        deletemap(arr).then((res: any) => {
          if (res.status === 200) {
            message.success(t('删除成功！'));
            setSelectedRowKeys([]);
            initmap(1, pagination.pageSize);
          } else {
            message.error(t('操作失败！'));
          }
        });
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };

  // 复制地图
  const copymymap = (arr: String[]) => {
    confirm({
      title: t('确认复制?'),
      onOk() {
        copymap(arr).then((res: any) => {
          if (res.status === 200) {
            message.success(t('复制成功！'));
            initmap(1, pagination.pageSize);
          } else {
            message.error(t('操作失败！'));
          }
        });
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };

  // 修改地图状态 0 下架 1发布 2共享
  const updatamap = (arr: String[], status: number) => {
    let newarr = arr;
    if (isbatch) {
      newarr = selectedRowKeys;
    }
    updatamapstatus(newarr, status).then((res: any) => {
      if (res.status === 200) {
        setIsModalVisible2(false);
        if (status == 1) {
          message.success(t('发布成功！'));
        } else if (status == 0) {
          message.success(t('下架成功！'));
        } else {
          message.success(t('共享成功！'));
        }
        setSelectedRowKeys([]);
        setSelectedId(null);
        initmap(pagination.current, pagination.pageSize);
      } else {
        message.error(t('操作失败！'));
      }
    });
  };

  // 排序改变事件
  const handleChange = (value: string) => {
    setSorter(value);
  };

  // 表格key
  const columns: any = [
    {
      title: t('名称'),
      dataIndex: 'mapName',
      key: 'mapName',
      align: 'center',
      // render: (text: any, record: any) => {
      //   return (
      //     <span
      //       style={{ cursor: 'pointer' }}
      //       onClick={() => {
      //         window.open(
      //           `${window.location.pathname}#/mapv4/editmap?id=${record.id}&key=3`,
      //         );
      //       }}
      //     >
      //       {text}
      //     </span>
      //   );
      // },
    },
    {
      title: t('状态'),
      dataIndex: 'isShow',
      key: 'isShow',
      align: 'center',
      render: (text: any, record: any) => {
        if (text == 2) {
          return <Tag color="#f50">{t('已共享')}</Tag>;
        } else {
          return <Tag color="#549cff">{t('未共享')}</Tag>;
        }
      },
    },
    {
      title: t('适用专业'),
      dataIndex: 'majorNames',
      key: 'majorNames',
      align: 'center',
      render: (text: any, record: any) => {
        return (
          <Tooltip title={text?.map((item: any) => item.value).toString()}>
            {text?.length && text?.length > 1 ? (
              <span>
                {text[0].value}{' '}
                <span style={{ color: '#00000073' }}>
                  ({t(`等{name}个专业`, text.length)})
                </span>
              </span>
            ) : null}
          </Tooltip>
        );
      },
    },
    {
      title: t('适用课程'),
      dataIndex: 'courseName',
      key: 'courseName',
      align: 'center',
    },
    {
      title: t('课程学科'),
      dataIndex: 'subjectName',
      key: 'subjectName',
      align: 'center',
    },
    {
      title: t('创建人'),
      dataIndex: 'teacherName',
      key: 'teacherName',
      align: 'center',
    },
    {
      title: t('创建时间'),
      dataIndex: 'createDate',
      key: 'createDate',
      align: 'center',
      render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss') || '-',
    },
    {
      title: t('操作'),
      dataIndex: 'address',
      key: 'address',
      align: 'center',
      render: (text: any, record: any) => {
        return (
          <>
            <Tooltip title={t('预览')}>
              <Button
                type="text"
                icon={<IconFont type="iconyulan" />}
                onClick={() => {
                  window.open(
                    `${window.location.pathname}#/mapv3?majorCode=${record.major}`,
                  );
                }}
              />
            </Tooltip>
            {/* <Tooltip title="编辑">
             <Button
               type="text"
               icon={<IconFont type="iconbianji-heise" />}
               onClick={() => {
                 window.open(
                   `${window.location.pathname}#/mapv4/editmap?id=${record.id}&key=3`,
                 );
               }}
             />
            </Tooltip>            
            <Tooltip title="删除">
             <Button
               type="text"
               icon={<IconFont type="iconshanchu-heise-copy" />}
               onClick={() => deletemamap([record.id])}
             />
            </Tooltip> */}
          </>
        );
      },
    },
  ];

  // 表格的多选事件
  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
      setSelectedRowKeys(selectedRowKeys);
    },
    selectedRowKeys: selectedRowKeys,
  };

  return (
    <div className="marjormap">
      <div className="search_box">
        <Search
          form={form}
          page="marjor"
          mobileFlag={mobileFlag}
          onSearch={(values: any) => {
            setSearchfrom(values);
          }}
        />

        <div className="right_box">
          <Select
            style={{ width: 120 }}
            defaultValue="0"
            onChange={handleChange}
          >
            <Select.Option value="0">
              {t('创建时间')}

              <IconFont type="iconchangjiantou-xia" />
            </Select.Option>
            <Select.Option value="1">
              {t('创建时间')}

              <IconFont type="iconchangjiantou-shang" />
            </Select.Option>
            {/* <Select.Option value="name,0">
                             课程名称
                             <IconFont type="iconchangjiantou-shang" />
                         </Select.Option>
                         <Select.Option value="name,1">
                             课程名称
                             <IconFont type="iconchangjiantou-xia" />
                         </Select.Option> */}
          </Select>
          {mobileFlag ? null : (
            <div className="mode_switch_wrapper">
              <div onClick={() => setModeSwitch(true)} className="mode_switch">
                <Tooltip title={t('图例模式')}>
                  <IconFont
                    type="iconhebingxingzhuangfuzhi2"
                    className={modeSwitch ? 'active' : ''}
                  />
                </Tooltip>
              </div>
              <div onClick={() => setModeSwitch(false)} className="mode_switch">
                <Tooltip title={t('列表模式')}>
                  <IconFont
                    type="iconliebiao"
                    className={modeSwitch ? '' : 'active'}
                  />
                </Tooltip>
              </div>
            </div>
          )}
        </div>
      </div>
      {modeSwitch ? (
        <List
          grid={{ gutter: 16, column:  isLargeScreen ? 8 : 6, sm: 2, xs: 2 }}
          dataSource={tableData}
          // pagination={pagination}
          renderItem={item => (
            <List.Item key={item.id}>
              <div
                className="card_item"
                onClick={() => {
                  if (mobileFlag) {
                    message.info(t('暂不支持手机端，请前往电脑端操作'));
                    return;
                  }
                  window.open(
                    `${window.location.pathname}#/mapv3?majorCode=${item.major}&perviewtype=3&grade=${item.grade}`,
                  );
                }}
              >
                <div className="icon_box">
                  <img
                    src={
                      item.mapCover
                        ? item.mapCover
                        : nomapcover
                    }
                    onError={(e:any)=>{
                      e.target.src = nomapcover;
                    }}
                  ></img>
                  {item.isShow == 2 && (
                    <div className="status_box">{t('已共享')}</div>
                  )}
                  {item.type == 5 && (
                    <div className="process_box">{t('转换中')}</div>
                  )}
                  <div
                    className="option_view"
                    onClick={e => e.stopPropagation()}
                  >
                    <div
                      className="item_caidan"
                      onClick={() => {
                        if (mobileFlag) {
                          message.info(t('暂不支持手机端，请前往电脑端操作'));
                          return;
                        }
                        window.open(
                          `${window.location.pathname}#/mapv3?majorCode=${item.major}&perviewtype=3&grade=${item.grade}`,
                        );
                      }}
                    >
                      <div className="caidan_icon">
                        <IconFont type="iconyulan" />
                      </div>
                      <div className="caidan_name">
                        <span>{t('预览')}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="name_box">
                  <div className="name">
                    <span className="name_span" title={item.mapName}>
                      {item.mapName}
                    </span>
                  </div>
                  <div className="user">{item.teacherName}</div>
                </div>
              </div>
            </List.Item>
          )}
        />
      ) : (
        <Table
          // rowSelection={{
          //   type: 'checkbox',
          //   ...rowSelection,
          // }}
          dataSource={tableData}
          scroll={{ y: 'calc(100vh - 350px)' }}
          columns={columns}
          rowKey="id"
          pagination={false}
        />
      )}
      <Pagination
        style={{ textAlign: 'center', marginBottom: 24 }}
        {...{
          size: 'small',
          showQuickJumper: true,
          showTotal: total => t('共{name}条', String(total)),
          current: pagination.current,
          total: pagination.total,
          pageSize: pagination.pageSize,
          showSizeChanger: true,
          pageSizeOptions: ['18', '24', '36', '48', '60'],
          onChange: pagination.onChange,
        }}
      />
      <Addmap
        isModalVisible={isModalVisible}
        title={t('选择专业')}
        onOk={e => addminemap(e)}
        onCancel={() => {
          setIsModalVisible(false);
          setMapName('');
        }}
      ></Addmap>
      <Modal
        title={t('提示')}
        visible={isModalVisible2}
        onCancel={() => {
          setIsModalVisible2(false);
        }}
        footer={
          <div className="footerclass">
            <Button type="primary" onClick={() => setIsModalVisible2(false)}>
              {t('取消')}
            </Button>
            {/* <Button type="primary" onClick={() => updatamap([selectedid], mapstatus)}>确认</Button> */}
            {mapstatus === 1 ? (
              <Button type="primary" onClick={() => updatamap([selectedid], 2)}>
                {t('共享')}
              </Button>
            ) : (
              <Button
                type="primary"
                onClick={() => updatamap([selectedid], mapstatus)}
              >
                {t('确认')}
              </Button>
            )}
          </div>
        }
      >
        {(() => {
          if (mapstatus == 1) {
            return t('确认共享?');
          } else if (mapstatus == 0) {
            return t('确认下架?');
          } else {
            return t('确认共享?');
          }
        })()}
      </Modal>
    </div>
  );
};

export default Majormap;
