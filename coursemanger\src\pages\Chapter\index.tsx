import chapterApis from '@/api/chapter';
import React, { FC, useEffect, useRef, useState } from 'react';
import { history, useDispatch, useLocation, useSelector } from 'umi';
import './index.less';
// 引用课程地图的接口
import baseInfo from '@/api/baseInfo';
import { CheckService } from '@/api/check';
import { addCourseSource } from '@/api/course';
import { addmap, addmaptocourse, savemap } from '@/api/coursemap';
import { deleteHomework, dragHomework } from '@/api/homework';
import { releaseCourse } from '@/api/mooclist';
import { ReactComponent as icon_text } from '@/assets/imgs/icon/icon_text.svg';
import CaseModal from '@/components/CaseModal';
import CasePreviewModal from '@/components/CasePreviewModal';
import ChapterCopyModal from '@/components/ChapterCopyModal';
import MoveChapter from '@/components/MoveChapter';
import ResourcePreviewModal from '@/components/ResourcePreviewModal';
import VideoModal from '@/components/VideoModal/VideoModal';
import { IconFont } from '@/components/iconFont';
import SelectTemplateModal from '@/components/selectTemplateModal';
import useLocale from '@/hooks/useLocale';
import usePermission from '@/hooks/usePermission';
import { IGlobalModelState } from '@/models/global';
import { formatChaptertoMap } from '@/pages/Coursemaptool/Editmap/util';
import AddHomework from '@/pages/HomeworkManagement/AddHomework';
import CopyHomeworkModal from '@/pages/HomeworkManagement/components/CopyHomeworkModal';
import { CUSTOMER_NPU, ModuleCfg2 } from '@/permission/moduleCfg';
import { getSensitiveWord } from '@/utils';
import Icon, {
  ExclamationCircleOutlined,
  FileWordFilled,
  PlusCircleFilled,
} from '@ant-design/icons';
import {
  Button,
  Collapse,
  Form,
  Input,
  Modal,
  Select,
  Space,
  Spin,
  Switch,
  Tree,
  message,
} from 'antd';
import AddHyperlinkModal from './components/AddHyperlinkModal';
import ChapterItem from './components/ChapterItem';
import ResourceItem, { IResourceItemProps } from './components/ResourceItem';
import SectionItem from './components/SectionItem';
import SyllabusModal, { UploadRefType } from './components/SyllabusModal';
import WordImportModal from './components/WordImportModal';
import AddBookModal from '../TeachingAssistant/components/AddBookModal';

const IconText = (props: any) => <Icon component={icon_text} {...props} />;

const getTreeItem = (tree: any, key: string) => {
  let result: any = null;
  tree.forEach((item: any) => {
    if (item.id === key) {
      result = item;
    } else if (item.children?.length > 0 && !result) {
      result = getTreeItem(item.children, key);
    }
  });
  return result;
};

const { Option } = Select;
const { Panel } = Collapse;

const Chapter: FC = () => {
  const { t } = useLocale();
  const location: any = useLocation();
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [sectionForm] = Form.useForm();
  const [chapterForm] = Form.useForm();
  const actionRef = useRef<UploadRefType>();

  const [modalSectionVisible, setModalSectionVisible] = useState<boolean>(
    false,
  );

  const [loading, setLoading] = useState(false);

  const [sectionConfirmLoading, setSectionConfirmLoading] = useState<boolean>(
    false,
  );

  const [modalChapterVisible, setModalChapterVisible] = useState<boolean>(
    false,
  );

  const [chapterConfirmLoading, setChapterConfirmLoading] = useState<boolean>(
    false,
  );

  const [modalContentVisible, setModalContentVisible] = useState<boolean>(
    false,
  );

  const [modalDeleteVisible, setModalDeleteVisible] = useState<boolean>(false); // 删除modal

  const [hyperlinkModalVisible, setHyperlinkModalVisible] = useState<boolean>(
    false,
  );
  const [isAddHyperlink, setIsAddHyperlink] = useState<boolean>(true);
  const [textbookOpen, setTextbookOpen] = useState<boolean>(false);
  const [hyperlinkData, setHyperlinkData] = useState<any>({});

  const [contentConfirmLoading, setContentConfirmLoading] = useState<boolean>(
    false,
  );

  const [chapter, setChapter] = useState<any[]>([]);

  // tree 数据--start
  const [editId, setEditId] = useState<string>(''); //记录正在编辑的名称
  const [deleteInfo, setDeleteInfo] = useState<any>({
    id: undefined,
    tip: undefined,
  });
  const [chapterTreeData, setChapterTreeDate] = useState<any[]>([]);
  const [selectKeys, setSelectKeys] = useState<any>([]); // 当前操作节点
  const toPublishItems = useRef<any[]>([]); // 待发布节点
  const publishedItems = useRef<any[]>([]); // 已发布节点
  const draftItems = useRef<any[]>([]); // 草稿节点
  const chapterItems = useRef<any[]>([]); // 章节点
  const sectionItems = useRef<any[]>([]); // 节节点
  const resourceItems = useRef<any[]>([]); // 资源节点
  const [expandItemKeys, setExpandItemKeys] = useState<string[]>([]); // 展开节点Key
  const dragNodeRef = useRef<any>(); // 记录正在拖动的节点
  const [formData, setFormData] = useState<any>({});
  // tree 数据--end

  const [resource, setResource] = useState<any>(null);

  const [modalMoveChapterVisible, setModalMoveChapterVisible] = useState(false); //移动
  const [moveChapterOrSection, setMoveChapterOrSection] = useState<string>(
    'chapter',
  );
  //移动
  const [moveId, setMoveId] = useState<any>(); //移动
  const [moveParentId, setMoveParentId] = useState<string>(''); //移动

  const [publishStatus, setPublishStatus] = useState<number>(2); //发布状态
  const [typeStatus, setTypeStatus] = useState<string>(''); //类型

  const courseResource: any[] = useSelector<Models.Store, any[]>(
    state => state.moocCourse.courseResource,
  );

  const [showChapterPre, setShowChapterPre] = useState<boolean>(false);
  const courseUpdata: any[] = useSelector<any, any>(
    state => state.updata.courseUpdata,
  );

  const courseDetail = useSelector<Models.Store, any>(
    state => state.moocCourse.courseDetail,
  );

  const [tplResModalVisible, setTplResModalVisible] = useState(false);
  // 资源预览相关state
  const [entityModalVisible, setEntityModalVisible] = useState(false);
  const [entityPreview, setEntityPreview] = useState<any>(null);
  const [copyHomeworkVisible, setCopyHomeworkVisible] = useState<boolean>(
    false,
  );

  const [templateInfo, setTemplateInfo] = useState<any>({
    state: -1,
  });
  const { parameterConfig, buttonPermission, permission } = useSelector<
    { global: any },
    { buttonPermission: string[]; parameterConfig: any; permission: any }
  >(state => state.global);

  const { parameterConfigObj } = useSelector<
    { global: IGlobalModelState },
    IGlobalModelState
  >(state => state.global);

  const [addHomeworkVisible, setAddHomeworkVisible] = useState<boolean>(false);
  const [homeworkItem, setHomeworkItem] = useState<any>({});
  const [homeworkCanEdit, setHomeworkCanEdit] = useState<boolean>(false);
  const [isAddResource, setIsAddResource] = useState<boolean>(false);
  const [wordImportModal, setWordImportModal] = useState<boolean>(false);
  const [copyCourseVisible, setCopyCourseVisible] = useState<boolean>(false);
  const [generationmode, setGenerationmode] = useState<string>('auto'); // 生成模式
  const [generatevisible, setGeneratevisible] = useState<boolean>(false); // 生成模式弹窗
  const [templateModalVisible, setTemplateModalVisible] = useState<boolean>(
    false,
  );
  const [showAddTopic, setShowAddTopic] = useState<boolean>(false);
  const [topicType, setTopicType] = useState<number>(0);
  const sourcePreview = useRef<any>();

  const [caseVisible, setCaseVisible] = useState<boolean>(false);
  const [casePreviewData, setCasePreviewData] = useState<any>({});
  const [materialData, setMaterialData] = useState<any>({});
  const [casePreviewOpen, setCasePreviewOpen] = useState<boolean>(false);

  const { getPermission } = usePermission();
  useEffect(() => {
    window.addEventListener('message', handleMessage);
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);
  const handleMessage = (e: any) => {
    if (e.data && typeof e.data === 'string') {
      const { action, data } = JSON.parse(e.data);
      if (action === 'addTopicBack') {
        setShowAddTopic(false);
      }
      if (data && (action === 'addTopicBack' || action === 'addTopic')) {
        setTimeout(() => {
          sourcePreview.current?.handleConfirmSelectTp?.([data]);
        }, 200);
      }
    }
  };

  useEffect(() => {
    if (courseDetail.copyFrom || courseDetail.entityData?.templateChange) {
      chapterApis
        .quoteTemplate(
          courseDetail.copyFrom ?? courseDetail.entityData?.templateChange,
          location.query.id,
        )
        .then(res => {
          if (res && res.message === 'OK') {
            setTemplateInfo(res.data);
          }
        });
    }
  }, [courseDetail.copyFrom, courseDetail.entityData?.templateChange]);
  useEffect(() => {
    if (courseDetail) {
      setShowChapterPre(
        Boolean(courseDetail.entityData?.chapter_display ?? true),
      );
    }
  }, [courseDetail]);
  const modallayout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 21 },
  };

  // console.log(courseDetail, parameterConfig);
  useEffect(() => {
    getChapter();
  }, [publishStatus, typeStatus, courseUpdata]);
  useEffect(() => {
    if (courseResource.length > 0) {
      const resourseId = courseResource[0].contentId;
      setFormData({ resourseId });
      chapterApis.resourceDetail(resourseId).then(res => {
        if (res.success) {
          if (Object.keys(res.data).length === 0) {
            setResource({
              resourseId,
              entityName: courseResource[0].name,
              type: courseResource[0].type,
            });
            setFormData({ resourseId, name: courseResource[0].name });
          } else {
            setResource(res.data);
            setFormData({ resourseId, name: res.data.entityName });
          }
        } else {
          setResource({
            resourseId,
            entityName: courseResource[0].name,
            type: courseResource[0].type,
          });
          setFormData({ resourseId, name: courseResource[0].name });
        }
      });
      dispatch({
        type: 'moocCourse/updateState',
        payload: {
          courseResource: [],
        },
      });
    }
  }, [courseResource]);
  // -----------------资源相关 start--------------
  /**
   * 资源预览
   *
   * @param {IResourceItemProps['info']} info
   */
  const handleResourcePreview = (info: IResourceItemProps['info']) => {
    const parent = getTreeItem(chapterTreeData, info.parentId);
    if (info.resourceAttributes === 'homework') {
      setAddHomeworkVisible(true);
      setHomeworkItem({ ...info, status: parent.status });
      setHomeworkCanEdit(false);
    } else if (info.resourceAttributes === 'hyperlink') {
      window.open(info.describe);
    } else if (info.resourceAttributes === 'case') {
      setCasePreviewData({
        id: info.describe,
        name: info.name,
      });
      setCasePreviewOpen(true);
    } else if (info.resourceAttributes === 'material') {
      window.open(`https://182.150.63.207:11023/web/#/Previewtextbook?contentId_=${info.describe}`)

    } else {
      setEntityPreview({
        id: info.describe,
        name: info.name,
        type: info.resourseType,
        sourceId: parent.guid,
      });
      setEntityModalVisible(true);
    }
  };
  /**
   * 资源编辑
   *
   * @param {IResourceItemProps['info']} info
   */
  const handleResourceEdit = (info: any, e: any) => {
    e.stopPropagation();
    if (info.resourceAttributes === 'homework') {
      const parent = getTreeItem(chapterTreeData, info.parentId);
      setHomeworkCanEdit(true);
      setHomeworkItem({ ...info, status: parent.status });
      setAddHomeworkVisible(true);
    } else if (
      info.resourceAttributes === 'hyperlink' &&
      info.change == 'all'
    ) {
      setHyperlinkModalVisible(true);
      setIsAddHyperlink(false);
      setHyperlinkData(info);
    } else {
      const {
        guid,
        id,
        parentId,
        name,
        describe,
        resourceAttributes,
        resourseType,
        duration,
      } = info;
      const param: Chapter.IupdateParam = {
        contentId: location.query.id,
        metadata: [
          {
            guid_: guid,
            parent_id: parentId,
            name,
            resource_attributes: resourceAttributes,
            identification: id,
            describe,
            resourse_type: resourseType,
            duration,
          },
        ],
      };
      onContentModalOk(param, true);
      // const { guid, id, parentId, name, describe } = info;
      // setIsAddResource(false);
      // setFormData({
      //   ...info,
      //   guid: guid,
      //   id,
      //   parent_id: parentId,
      //   name,
      //   resourseId: describe,
      // })
      // chapterApis.resourceDetail(describe).then(res => {
      //   if (res.success) {
      //     setResource(res.data);
      //   } else {
      //     setResource({});
      //   }
      // });
      // setModalContentVisible(true);
    }
  };
  /**
   * 添加资源
   *
   * @param {any} info
   */
  const addCourse = (info: any, type: 'reference' | 'courseware' | 'training_case') => {
    setFormData({
      ...info,
      parent_id: info.id,
      resourceAttributes: type,
    });
    setModalContentVisible(true);
  };

  /**
   * 添加/编辑 资源确认事件
   *
   */
  const onContentModalOk = async (param: any, isEdit: boolean) => {
    setContentConfirmLoading(true);
    const data = Array.isArray(param) ? param.map((item: any) => ({ ...item, courseSemester: location.query.sm ?? 1 })) : param
    if (isEdit) {
      chapterApis.updateAny(data).then(res => {
        if (res && res.message === 'OK') {
          message.success(`${isEdit ? t('修改') : t('添加')}${t('成功')}!`);
          getChapter();
          if (!isEdit) {
            dispatch({
              type: 'updata/changelayout',
              payload: {},
            });
          }
        } else {
          message.error(`${isEdit ? t('修改') : t('添加')}${'失败'}!`);
        }
        onContentModalClose();
        setContentConfirmLoading(false);
        setFormData(null);
      });
    } else {
      addCourseSource(data, location.query.id).then(res => {
        if (res.status !== 200) {
          message.error(`${t('添加失败')}`);
        } else {
          if (!expandItemKeys.includes(param[0].parentId))
            setExpandItemKeys([...expandItemKeys, param[0].parentId]);
          getChapter();
          setContentConfirmLoading(false);
          setFormData(null);
          setModalContentVisible(false);
          if (param[0].resourceType === 'hyperlink') {
            message.success(t('超链接添加成功!'));
          } else {
            message.success(t('资源添加成功！'));
          }
        }
      });
    }
  };
  const onContentModalClose = () => {
    setModalContentVisible(false);
    setFormData(null);
    setResource(null);
  };
  // ---------------资源相关 end--------------

  // ---------------章节相关 start-------------
  /**
   * @description: 修改名称编辑
   * @param {any} info
   * @return {*}
   */
  const handleEdit = (info: any) => {
    if (editId !== '') {
      message.info(t('请先保存当前编辑'));
      return;
    }
    setEditId(info.id);
  };
  /**
   * 章节 添加 操作
   *
   * @param {*} info
   * @param {string} type 'courseware' | 'reference' | 'section'
   * @param {*} e
   * @return {*}
   */
  const handleItemAdd = (
    info: any,
    type: string,
    e?: any,
    fromModal: boolean = false,
  ) => {
    e?.stopPropagation();
    switch (type) {
      case 'reference':
        addCourse(info, 'reference');
        return;
      case 'courseware':
        setIsAddResource(true);
        addCourse(info, 'courseware');
        return;
      case 'section':
        addSection(info.id);
        return;
      case 'training_case':
        addCourse(info, 'training_case');
        return;
      case 'homework':
        if (
          (courseDetail.copyFrom || courseDetail.entityData?.templateChange) &&
          templateInfo.state !== -1 &&
          !fromModal
        ) {
          setHomeworkItem(info);
          setCopyHomeworkVisible(true);
        } else {
          setCopyHomeworkVisible(false);
          setHomeworkItem(info);
          setHomeworkCanEdit(true);
          setAddHomeworkVisible(true);
        }
        return;
      case 'hyperlink':
        setIsAddHyperlink(true);
        setHyperlinkModalVisible(true);
        setHyperlinkData(info);
        return;
      case 'case':
        setCaseVisible(true);
        setHomeworkItem(info);
        return;
      case "material":
        setMaterialData(info);
        setTextbookOpen(true);
        return;
    }
  };

  /**
   * 添加节
   *
   * @param {string} parentId
   */
  const addSection = (parentId: string) => {
    sectionForm.setFieldsValue({
      parent_id: parentId,
    });
    setModalSectionVisible(true);
  };
  /**
   * 根据当前状态，修改item状态
   *
   * @param {string[]} ids
   * @param {string} status
   */
  const changStatus = (ids: string[], status: number) => {
    const contentId = location.query.id;
    if (status === -1 || status === 0) {
      // 草稿--设为待发布
      chapterApis.updataChapterOne(contentId, ids).then(res => {
        if (res && res.message === 'OK') {
          message.success(t('修改成功！'));
          getChapter();
        } else {
          message.error(t('修改失败！'));
        }
      });
    } else if (status === 1) {
      //已发布--下架
      chapterApis.updataChapterTwo(contentId, '0', ids).then(res => {
        if (res && res.message === 'OK') {
          message.success(t('修改成功！'));
          getChapter();
          dispatch({
            type: 'updata/changelayout',
            payload: {},
          });
        } else {
          message.error(t('修改失败！'));
        }
      });
    }
  };
  /**
   * 章节修改名称
   *
   * @param {*} info
   */
  const handleChangeName = (info: any, inputValue: string) => {
    const contentId = location.query.id;
    const { id, status, parentId, guid } = info;
    if (id) {
      const param: Chapter.IupdateParam = {
        contentId,
        metadata: [
          {
            guid_: guid,
            name: inputValue,
            identification: id,
            publish_status: status,
            parent_id: parentId,
          },
        ],
      };
      chapterApis.updateAny(param).then(res => {
        if (res && res.message === 'OK') {
          message.success(t('修改成功！'));
          getChapter();
        } else {
          message.error(t('修改失败！'));
        }
      });
    }
  };

  // ---------------章节相关 end-------------

  // ---------------统一数据处理 start---------------
  /**
   * 批量更新章节
   *
   * @param {any[]} treeInfo
   */
  const updateLearnInfoRq = (
    treeInfo: any[],
    parentId: string,
    sourceId: string,
  ) => {
    const courseId = location.query.id;
    const data = {
      courseId: courseId,
      list: treeInfo,
      parentId,
      sourceId,
    };
    chapterApis.updataLearnTree(data).then(res => {
      if (res && res.status === 200) {
        getChapter();
        dragHomework(data);
        console.log('更新成功！');
      } else {
        console.log('更新失败！');
      }
    });
  };
  const handleDelete = (info: any, e: any) => {
    let tip = '';
    let type = '';
    if (info.level === 1) {
      // 章
      tip = t('该目录下所有内容及其学生学习数据将一并删除，是否确定删除？');
      type = 'chapter';
    } else if (info.resourseType) {
      // 资源
      tip = t('该内容的学生学习数据将一并删除，是否确定删除？');
      type = info.resourseType;
    } else {
      // 节
      tip = t('该目录下所有内容及其学生学习数据将一并删除，是否确定删除？');
      type = 'section';
    }
    setDeleteInfo({
      id: info.guid,
      tip: tip,
      type,
      describe: info.describe,
      idH: info.id,
    });
    setModalDeleteVisible(true);
  };
  const handleDeleteHomework = () => {
    if (
      deleteInfo.type !== 'chapter' &&
      deleteInfo.type !== 'section' &&
      deleteInfo.type !== 'homework'
    ) {
      return;
    }
    const id =
      deleteInfo.type === 'homework' ? deleteInfo.describe : deleteInfo.idH;
    const param = {
      courseId: location.query.id,
      [`${deleteInfo.type}Id`]: id,
    };
    deleteHomework(param).then(res => {
      if (res.status !== 200) {
        message.error(res.message);
      }
    });
  };
  // 章/节/内容 统一删除确认事件
  const deleteConfirm = (id: any) => {
    const contentId = location.query.id;
    let param: Chapter.IdeleteParam = {
      contentId,
      guid_: id,
    };
    chapterApis.deleteAny(param).then(res => {
      if (res && res.message === 'OK') {
        message.success(t('删除成功！'));
        setModalDeleteVisible(false);
        handleDeleteHomework();
        getChapter();
        dispatch({
          type: 'updata/changelayout',
          payload: {},
        });
      } else {
        message.error(res.message);
      }
    });
  };
  /**
   * 获取章节资源树
   *
   */
  const getChapter = () => {
    const id = location.query.id;
    let data = `courseId=${id}`;
    if (publishStatus) {
      data = data + `&status=${publishStatus}`;
    }
    setLoading(true);
    chapterApis.getChapter(data).then((res: any) => {
      setLoading(false);
      if (res && res.status === 200 && res.data) {
        setChapter(res.data);
        toPublishItems.current = [];
        draftItems.current = [];
        publishedItems.current = [];
        chapterItems.current = [];
        sectionItems.current = [];
        resourceItems.current = [];
        const treeData = formatChapter(res.data, 0, []);
        setChapterTreeDate(treeData);
        if (treeData.length > 0 && selectKeys.length == 0) {
          setSelectKeys([treeData[0].id]);
        }
        // 设置展开节点
        if (expandItemKeys.length === 0) {
          setExpandItemKeys([
            ...toPublishItems.current.map(item => item.id),
            ...publishedItems.current.map(item => item.id),
            ...draftItems.current.map(item => item.id),
          ]);
        }
      }
    });
  };
  /**
   *根据type（章、节、资源）取item类型
   *
   * @param {*} item
   */
  const getLearnItem = (item: any) => {
    switch (item.level) {
      case 1: //章
        return (
          <ChapterItem
            showChapterPre={showChapterPre}
            editId={editId}
            onEditSuccess={info => setEditId('')}
            onEdit={info => handleEdit(info)}
            selectKeys={expandItemKeys}
            info={item}
            onChangeName={handleChangeName}
            onChapterDelete={handleDelete}
            onAdd={handleItemAdd}
            onOffItem={(ids, status) => {
              changStatus(ids, status);
            }}
            onDraftItem={(ids, status) => {
              changStatus(ids, status);
            }}
            onToPublishItem={(ids, status) => {
              changStatus(ids, status);
            }}
          />
        );

      case 2:
        if (item.resourseType) {
          return (
            <ResourceItem
              treeData={chapterTreeData}
              onEditSuccess={() => setEditId('')}
              onEdit={info => handleEdit(info)}
              editId={editId}
              info={item}
              onResourcePreview={handleResourcePreview}
              onResourceEdit={handleResourceEdit}
              onResourceDelete={handleDelete}
              onOffItem={(ids, status) => changStatus(ids, status)}
              onDraftItem={(ids, status) => changStatus(ids, status)}
              onToPublishItem={(ids, status) => changStatus(ids, status)}
              onFinish={onFinish}
              refresh={getChapter}
            />
          );
        } else {
          return (
            <SectionItem
              showChapterPre={showChapterPre}
              editId={editId}
              onEditSuccess={info => setEditId('')}
              onEdit={info => handleEdit(info)}
              selectKeys={expandItemKeys}
              info={item}
              onChangeName={handleChangeName}
              onSectionDelete={handleDelete}
              onAdd={handleItemAdd}
              onOffItem={(ids, status) => {
                changStatus(ids, status);
              }}
              onDraftItem={(ids, status) => {
                changStatus(ids, status);
              }}
              onToPublishItem={(ids, status) => {
                changStatus(ids, status);
              }}
            />
          );
        }
      case 3: //资源
        if (item.resourseType) {
          return (
            <ResourceItem
              treeData={chapterTreeData}
              editId={editId}
              info={item}
              onEditSuccess={() => setEditId('')}
              onEdit={info => handleEdit(info)}
              onResourcePreview={handleResourcePreview}
              onResourceEdit={handleResourceEdit}
              onResourceDelete={handleDelete}
              onOffItem={(ids, status) => changStatus(ids, status)}
              onDraftItem={(ids, status) => changStatus(ids, status)}
              onToPublishItem={(ids, status) => changStatus(ids, status)}
              onFinish={onFinish}
              refresh={getChapter}
            />
          );
        } else {
          return (
            <SectionItem
              showChapterPre={showChapterPre}
              editId={editId}
              onEditSuccess={info => setEditId('')}
              onEdit={info => handleEdit(info)}
              selectKeys={expandItemKeys}
              info={item}
              onChangeName={handleChangeName}
              onSectionDelete={handleDelete}
              onAdd={handleItemAdd}
              onOffItem={(ids, status) => {
                changStatus(ids, status);
              }}
              onDraftItem={(ids, status) => {
                changStatus(ids, status);
              }}
              onToPublishItem={(ids, status) => {
                changStatus(ids, status);
              }}
            />
          );
        }
      default:
        return (
          <ResourceItem
            treeData={chapterTreeData}
            editId={editId}
            info={item}
            onEditSuccess={() => setEditId('')}
            onEdit={info => handleEdit(info)}
            onResourcePreview={handleResourcePreview}
            onResourceEdit={handleResourceEdit}
            onResourceDelete={handleDelete}
            onOffItem={(ids, status) => changStatus(ids, status)}
            onDraftItem={(ids, status) => changStatus(ids, status)}
            onToPublishItem={(ids, status) => changStatus(ids, status)}
            onFinish={onFinish}
            refresh={getChapter}
          />
        );
    }
  };
  /**
   * 获取item的类型
   *
   * @param {*} item
   * @return {*}
   */
  const getItemType = (item: any, level: number) => {
    // debugger
    if (item.resourseType === 'homework') {
      return 'homework';
    } else if (item.resourseType && item.resourseType !== 'chapter') {
      return 'resource';
    } else if (level === 1) {
      return 'chapter';
    } else {
      return 'section';
    }
  };

  /**
   * 递归：原始chapter --> treeData
   *
   * @param {any[]} data
   * @return {*}
   */
  const formatChapter = (
    data: any[],
    level: number,
    orders: number[],
    pName?: string,
  ) => {
    let thisLevel = level + 1; // 记录当前层级
    let treeData: any[] = []; // 处理后的treeData
    let order = 0; // 记录资源数量，资源不增加小节编号
    let drafts: any[] = []; // 统计草稿章节
    let toPublish: any[] = []; // 统计待发布章节
    let published: any[] = []; // 统计已发布章节
    let chapters: any[] = []; // 统计章
    let sections: any[] = []; // 统计节
    let resources: any[] = []; // 统计资源

    data.forEach((item, index) => {
      let type = getItemType(item, thisLevel); // 判断type给item添加类名
      let curItem = {
        ...item,
        key: item.id,
        level: thisLevel,
        labelTitle: `${[...orders, index - order + 1].join('.')} ${item.name}`,
        className: 'type-' + type,
        title: item.name,
        orders: [...orders, index - order + 1],
        pName,
      };
      // 资源类型统计
      if (
        type === 'resource' ||
        type === 'homework' ||
        type === 'hyperlink' ||
        type === 'case' ||
        type === "material"
      ) {
        ++order;
        resources.push(curItem);
      } else if (type === 'chapter') {
        chapters.push(curItem);
      } else {
        sections.push(curItem);
      }
      //章节状态统计
      if (type !== 'resource') {
        if (item.status == '-1') {
          // 草稿
          drafts.push(curItem);
        } else if (item.status == '1') {
          // 已发布
          published.push(curItem);
        } else {
          // 待发布
          toPublish.push(curItem);
        }
      }
      if ((item.children && item.children.length <= 0) || !item.children) {
        // 无children，返回
        treeData.push(curItem);
      } else {
        // 有children，递归
        treeData.push({
          ...curItem,
          children: formatChapter(
            item.children,
            thisLevel,
            [...orders, index - order + 1],
            item.name,
          ),
        });
      }
    });
    toPublishItems.current = [...toPublishItems.current, ...toPublish];
    draftItems.current = [...draftItems.current, ...drafts];
    publishedItems.current = [...publishedItems.current, ...published];
    chapterItems.current = [...chapterItems.current, ...chapters];
    sectionItems.current = [...sectionItems.current, ...sections];
    resourceItems.current = [...resourceItems.current, ...resources];
    return treeData;
  };

  /**
   * 递归：拖动前treeData --> treeData【修改章节编号、parentId】
   *
   * @param {any[]} data
   * @return {*}
   */
  const formatDragChapter = (data: any[], orders: number[], parentId: '') => {
    let treeData: any[] = []; // 处理后的treeData
    let order = 0; // 记录资源数量，资源不增加小节编号

    data.forEach((item, index) => {
      let type = getItemType(item); // 判断type给item添加类名
      let curItem = {
        ...item,
        orders: [...orders, index - order + 1],
        parentId: parentId,
      };

      if (type === 'resource') {
        ++order;
      }

      if ((item.children && item.children.length <= 0) || !item.children) {
        // 无children，返回
        treeData.push(curItem);
      } else {
        // 有children，递归
        treeData.push({
          ...curItem,
          children: formatDragChapter(
            item.children,
            [...orders, index - order + 1],
            item.id,
          ),
        });
      }
    });
    return treeData;
  };
  // ---------------统一数据处理 end---------------

  // =============================章操作===================================
  // 添加章
  const addChapter = () => {
    setModalChapterVisible(true);
  };
  //编辑章
  const editorChapter = (e: React.MouseEvent<HTMLDivElement>, chapter: any) => {
    e.stopPropagation();
    const { id, name, describe, status } = chapter;
    chapterForm.setFieldsValue({
      id,
      name,
      describe,
      status,
    });
    setModalChapterVisible(true);
  };
  // 添加/编辑 章的Modal确认事件
  const onChapterModalOK = (e: any) => {
    chapterForm
      .validateFields()
      .then(values => {
        setChapterConfirmLoading(true);
        getSensitiveWord(
          values.name,
          '章',
          () => {
            const contentId = location.query.id;
            const { guid, id, name, describe, status } = values;
            if (id) {
              const param: Chapter.IupdateParam = {
                contentId,
                metadata: [
                  {
                    guid_: guid,
                    name,
                    describe,
                    identification: id,
                    publish_status: status,
                  },
                ],
              };
              chapterApis.updateAny(param).then(res => {
                if (res && res.message === 'OK') {
                  message.success(t('修改成功！'));
                  getChapter();
                  setExpandItemKeys([...expandItemKeys, res.data]);
                } else {
                  message.error(t('修改失败！'));
                }
                setModalChapterVisible(false);
                setChapterConfirmLoading(false);
                chapterForm.setFieldsValue({
                  id: undefined,
                  status: undefined,
                  name: '',
                  describe: '',
                });
              });
            } else {
              const param: Chapter.IaddChapterParamsItem = {
                contentId,
                name,
                describe,
                publishStatus: '-1',
              };
              chapterApis.addChapter(param).then(res => {
                if (res && res.message === 'OK') {
                  message.success(t('添加成功！'));
                  getChapter();
                  setExpandItemKeys([...expandItemKeys, res.data]);
                } else {
                  message.error(t('添加失败！'));
                }
                setModalChapterVisible(false);
                setChapterConfirmLoading(false);
                chapterForm.setFieldsValue({
                  id: undefined,
                  name: '',
                  describe: '',
                });
              });
            }
          },
          () => setChapterConfirmLoading(false),
        );
      })
      .catch(error => {
        console.log(error);
      });
  };

  // =============================节操作===================================

  // 编辑节
  const editorSection = (e: React.MouseEvent<HTMLDivElement>, section: any) => {
    e.stopPropagation();
    const { id, parentId, name, describe, status } = section;
    sectionForm.setFieldsValue({
      id,
      parent_id: parentId,
      name,
      describe,
      status,
    });
    setModalSectionVisible(true);
  };
  // 添加/编辑 节的Modal确认事件
  const onSectionModalOK = () => {
    sectionForm
      .validateFields()
      .then(values => {
        setSectionConfirmLoading(true);
        getSensitiveWord(
          values.name,
          '小节',
          () => {
            const contentId = location.query.id;
            const { guid, id, parent_id, name, describe, status } = values;
            if (id) {
              const param: Chapter.IupdateParam = {
                contentId,
                metadata: [
                  {
                    guid_: guid,
                    parent_id,
                    name,
                    describe,
                    identification: id,
                    publish_status: status,
                  },
                ],
              };
              chapterApis.updateAny(param).then(res => {
                if (res && res.message === 'OK') {
                  message.success(t('修改成功！'));
                  getChapter();
                } else {
                  message.error(t('修改失败！'));
                }
                setModalSectionVisible(false);
                setSectionConfirmLoading(false);
                sectionForm.setFieldsValue({
                  id: undefined,
                  parent_id: undefined,
                  status: undefined,
                  name: '',
                  describe: '',
                });
              });
            } else {
              const param: Chapter.IaddChapterParamsItem = {
                contentId,
                name,
                describe,
                parentId: parent_id,
                publishStatus: '0',
              };
              chapterApis.addChapter(param).then(res => {
                if (res && res.message === 'OK') {
                  message.success(t('添加成功！'));
                  getChapter();
                  setExpandItemKeys([...expandItemKeys, res.data]);
                } else {
                  message.error(t('添加失败！'));
                }
                setModalSectionVisible(false);
                setSectionConfirmLoading(false);
                sectionForm.setFieldsValue({
                  id: undefined,
                  parent_id: undefined,
                  name: '',
                  describe: '',
                });
              });
            }
          },
          () => setSectionConfirmLoading(false),
        );
      })
      .catch(error => {
        console.log(error);
      });
  };

  // 删除课程文件
  const deleteResourse = () => {
    setFormData({ resourseId: '' });
    setResource(null);
  };

  const resourceModalConfirm = (resource: any[]) => {
    dispatch({
      type: 'moocCourse/updateState',
      payload: {
        courseResource: resource,
      },
    });
  };
  /**
   * resource资源对象中取资源url
   *
   * @param {*} resource
   * @return {*}
   */
  const getPreviewPath = (resource: any) => {
    if (resource && resource.fileGroups) {
      const { fileGroups } = resource;
      const previewFile = fileGroups.filter(
        (file: any) => file.typeCode === 'previewfile',
      );

      const videogroupFile = fileGroups.filter(
        (file: any) => file.typeCode === 'sourcefile',
      );

      return previewFile.length > 0
        ? previewFile[0].fileItems[0].filePath
        : videogroupFile[0].fileItems[0].filePath;
    }
    return '';
  };

  /**
   * 更新章节状态
   *
   */
  const updataCourseState = () => {
    Modal.confirm({
      title: t('是否对章节进行更新?'),
      icon: <ExclamationCircleOutlined />,
      // content: 'Some descriptions',
      onOk() {
        const { contentId, entityData } = courseDetail;
        releaseCourse([contentId], entityData.courseType).then(res => {
          if (res && res.message === 'OK') {
            message.success(t('课程更新成功'));
            getChapter();
            dispatch({
              type: 'updata/changelayout',
              payload: {},
            });
          }
        });
      },
    });
  };

  // --------------拖动 start--------------
  /**
   * 是否允许放置
   *
   * @param {*} {
   *     dropNode,
   *     dropPosition, -1 | 0 | 1;
   * }
   */
  const handleAllowDrop = (info: any) => {
    // console.log('AllowDrop-drag-', dragNodeRef.current);
    // console.log('AllowDrop-drop-', info);
    const { dropNode, dropPosition } = info;
    const dragNode = dragNodeRef.current;
    // 章-只能放在第一层
    if (dragNode.className === 'type-chapter') {
      if (dropNode.level === 1 && dropPosition !== 0) {
        return true;
      }
    }

    // 节-只能放在第二层
    if (dragNode.className === 'type-section') {
      if (
        (dropNode.level === 1 && dropPosition === 0) ||
        (dropNode.level === 2 && dropPosition !== 0)
      ) {
        return true;
      }
    }

    // 资源-可以放在第二层和第三层（不能第一层 | 不能在资源下）
    if (
      dragNode.className === 'type-resource' ||
      dragNode.className === 'type-homework' ||
      dragNode.className === 'type-hyperlink' ||
      dragNode.className === 'type-case' ||
      dragNode.className === "type-material"
    ) {
      if (
        (dropNode.level === 1 && dropPosition !== 0) ||
        ((dropNode.className === 'type-resource' ||
          dropNode.className === 'type-homework' ||
          dropNode.className === 'type-hyperlink' ||
          dragNode.className === "type-material" ||
          dragNode.className === 'type-case') &&
          dropPosition === 0)
      ) {
        return false;
      } else {
        return true;
      }
    }

    return false;
  };
  /**
   * 开始拖动
   *
   * @param {*} info
   */
  const onDragStart = (info: any) => {
    // console.log('dragstart--', info);
    let dragNode = info.node;
    // if (dragNode.status == '1') {
    //   // 发布不调用
    //   return;
    // }

    dragNodeRef.current = dragNode;
    // 开始拖动章时收起所有章
    let chapterKeys = chapterItems.current.map(item => item.id);
    if (dragNode.className === 'type-chapter') {
      setExpandItemKeys(
        expandItemKeys.filter(item => !chapterKeys.includes(item)),
      );
    }
    // 开始拖动节时收起所有节
    let sectionKeys = sectionItems.current.map(item => item.id);
    if (dragNode.className === 'type-section') {
      setExpandItemKeys(
        expandItemKeys.filter(item => !sectionKeys.includes(item)),
      );
    }
  };
  /**
   *结束拖动
   *
   * @param {*} info
   * info几个参数的含义：https://github.com/ant-design/ant-design/issues/14244
   * - dropToGap：代表连接到节点之间的缝隙中，相对 node 参数是前后邻居关系，而不是子节点关系。
   * - dropPosition：相对于目标节点的 index。如果拖到了目标节点的上面是 index-1，下面则是 index+1。
   */
  const onDrop = (info: any) => {
    // if (info.dragNode.status == '1') {
    //   // 发布不调用
    //   return;
    // }
    // 拖动章/节时收起 子节点
    const dropKey = info.node.key;
    const dragKey = info.dragNode.key;
    const dropPosition = info.dropPosition;
    if (dragKey === dropKey) {
      return;
    }
    const loop = (data: any, key: string, callback: any, parentItem = null) => {
      console.log('loop', data, key);
      for (let i = 0; i < data.length; i++) {
        if (data[i].key === key) {
          return callback(data[i], i, data, parentItem);
        }
        if (data[i].children) {
          loop(data[i].children, key, callback, data[i]);
        }
      }
    };
    const data = [...chapterTreeData];
    let dragObj: any;
    let parentItem: any; // 记录父亲节点

    // 01 删除 dragObj
    loop(data, dragKey, (item: any, index: any, arr: any[]) => {
      arr.splice(index, 1);
      dragObj = item;
    });
    // 02 插入 dragObj
    if (!info.dropToGap) {
      loop(data, dropKey, (item: { children: any[] }) => {
        item.children = item.children || [];
        item.children.unshift(dragObj);
      });
      parentItem = info.node;
    } else {
      let ar: any[] = [];
      let i = 0;
      loop(data, dropKey, (item: any, index: any, arr: any, parent: any) => {
        ar = arr;
        i = index;
        parentItem = parent;
      });
      if (dropPosition === -1) {
        ar.splice(i, 0, dragObj);
      } else {
        ar.splice(i + 1, 0, dragObj);
      }
    }
    console.log('parentItem', parentItem);
    console.log('dragItem', info.dragNode);
    setChapterTreeDate(formatChapter(data, 0, []));
    console.info(1111111111, data, setChapterTreeDate(formatChapter(data, 0, [])))
    updateLearnInfoRq(data, parentItem?.id || '', dragKey);
    dragNodeRef.current = undefined;
  };
  // --------------拖动 end--------------

  // 生成课程地图
  const generateCourseMap = (value: any) => {
    addmap({
      mapName: value.mapname,
    }).then((res: any) => {
      if (res.status === 200) {
        // 绑定到当前课程
        addmaptocourse([
          {
            isShow: 1,
            courseId: location.query.id,
            mapId: res.data.id,
          },
        ]).then(async (res2: any) => {
          if (res2.message == 'OK') {
            let nodejson = await formatChaptertoMap(
              res.data.id,
              courseDetail,
              generationmode,
              chapterTreeData,
              location.query.id,
            );
            console.log('nodejson', nodejson);
            savemap(nodejson).then((res3: any) => {
              if (res3.status == 200) {
                setGeneratevisible(false);
                message.success(t('生成课程地图成功！'));
                history.push({
                  pathname: `/editcourse/moocmap`,
                  query: {
                    ...location.query,
                    mapid: res.data.id,
                  },
                });
              } else {
                message.error(t('课程地图保存失败！'));
              }
            });
          } else {
            message.error(t('绑定地图到课程失败！'));
          }
        });
      } else {
        message.error(t('新建空白地图操作失败！'));
      }
    });
  };

  const handleTemplateOk = (
    templateId?: string,
    isPublish?: boolean,
    data?: any,
  ) => {
    chapterApis
      .changeTemplate({ courseId: location.query.id, templateId })
      .then((res: any) => {
        if (res.status === 200) {
          setTemplateInfo({
            state: 1,
            ...(data ?? {}),
            isPublic: data.release_type === 'public',
          });
          message.success(t('设置成功！'));
          dispatch({
            type: 'moocCourse/updateState',
            payload: {
              courseDetail: { ...courseDetail, copyFrom: templateId },
            },
          });
          setTemplateModalVisible(false);
        }
      });
  };
  async function onFinish(info: any) {
    actionRef.current?.setConfig({
      open: true,
      title: '关联教学大纲内容',
      async onFinish(values, close) {
        try {
          // if (values.length === 0) {
          //     throw new Error('请选择关联内容')
          // }
          const { guid, name, describe, resourseType } = info;
          const {
            data: { data, message: msg },
          } = await CheckService.syllabusChapterContent({
            courseId: location.query.id,
            resource_id: describe,
            resource_name: name,
            courseSemester: location.query.sm,
            resource_type: resourseType,
            couresSyllabusContentUpdates: values ?? [],
            map_entity_id: guid,
          });
          if (!data) {
            throw new Error(msg);
          } else {
            message.success('关联成功');
            close();
            getChapter();
            // onRefresh()
          }
        } catch (error) {
          message.error(error.message);
        }
      },
      alreadyKeys: info?.syllabusContentIds ?? [],
    });
  }
  return (
    <div className="chapter_box">
      {addHomeworkVisible ? (
        <AddHomework
          chapterAllTreeData={chapterTreeData}
          chapter={chapter}
          handleBack={(data: any) => {
            setAddHomeworkVisible(false);
            getChapter();
            if (!expandItemKeys.includes(data.id))
              setExpandItemKeys([...expandItemKeys, data.id]);
          }}
          chapterItem={homeworkItem}
          copyFrom={
            courseDetail.copyFrom ?? courseDetail.entityData?.templateChange
          }
        // canEdit={homeworkCanEdit}
        />
      ) : showAddTopic ? (
        <iframe
          src={`/exam/#/topic/manage?opt_type=new&type=${topicType}&from=out`}
          height="100%"
          width="100%"
        />
      ) : (
        <div
          className={`chapter ${courseDetail.copyFrom || courseDetail.entityData?.templateChange
            ? 'has-tpl'
            : ''
            }`}
        >
          <div className="select-box">
            <div className="left-button">
              <Space>
                <Button
                  type="primary"
                  icon={<PlusCircleFilled />}
                  onClick={addChapter}
                >
                  {t('添加章')}
                </Button>
                {/*
                 * 按钮显示：课程发布-ok
                 *     按钮置灰：无待发布章节-ok
                 *     按钮正常：other-ok
                 * 按钮不显示：课程未发布-ok
                 */}
                <Button
                  type="primary"
                  onClick={() => setWordImportModal(true)}
                  ghost
                  icon={<FileWordFilled />}
                >
                  {t('Word导入章节')}
                </Button>
                <Button
                  type="primary"
                  icon={<IconFont type="iconcopy" />}
                  ghost
                  onClick={() => setCopyCourseVisible(true)}
                >
                  {t('复制到')}
                </Button>
                {(() => {
                  if (
                    location.query.type == 'mooc' &&
                    parameterConfigObj.mooc_kcgl?.includes(
                      'knowledge_map_display',
                    )
                  ) {
                    return (
                      <Button
                        type="primary"
                        icon={
                          <IconFont
                            type="icona-ditulei_ditu1"
                            style={{ fontSize: '15px' }}
                          />
                        }
                        ghost
                        onClick={() => setGeneratevisible(true)}
                      >
                        {t('生成课程地图')}
                      </Button>
                    );
                  } else if (
                    getPermission(['training', 'spoc'], 'knowledge_map_display')
                  ) {
                    return (
                      <Button
                        type="primary"
                        icon={
                          <IconFont
                            type="icona-ditulei_ditu1"
                            style={{ fontSize: '15px' }}
                          />
                        }
                        ghost
                        onClick={() => setGeneratevisible(true)}
                      >
                        {t('生成课程地图')}
                      </Button>
                    );
                  } else {
                    return null;
                  }
                })()}
                {/* {(courseDetail && courseDetail.entityData?.publishStatus === 1 || courseDetail.entityData?.publishStatus === 2) && (
                 <Button
                   type="primary"
                   ghost
                   icon={<IconFont type="icongengxinzhangjie" />}
                   disabled={toPublishItems.current.length <= 0}
                   onClick={updataCourseState}
                 >
                   发布章节
                 </Button>
                )} */}
              </Space>
            </div>
            <div className="right-button">
              {templateInfo.state === -1 ? (
                <div>
                  <IconFont type="iconmenu_coursetemplate" />
                  {parameterConfig.target_customer === CUSTOMER_NPU
                    ? t('暂未使用公共资源')
                    : t('暂未使用课程资源包')}
                  <a onClick={() => setTemplateModalVisible(true)}>
                    {t('立即使用')}
                  </a>
                </div>
              ) : (
                <div className="has-template-wrp">
                  <IconFont type="iconmenu_coursetemplate" />
                  {t('已使用')}

                  <span
                    onClick={() =>
                      window.open(
                        `/learn/workbench/#/tempatedetail/courseInfo?id=${courseDetail.copyFrom ??
                        courseDetail.entityData?.templateChange}&type=${templateInfo.isPublic ? 'see' : 'edit'
                        }&myOrShare=${!templateInfo.isPublic}`,
                      )
                    }
                  >
                    《{templateInfo.name}》
                  </span>
                  <a onClick={() => setTemplateModalVisible(true)}>
                    {t('更换')}
                  </a>
                </div>
              )}

              <div className="switch-wrp">
                <span>{t('显示章节序号：')}</span>
                <Switch
                  checked={showChapterPre}
                  onChange={(value: any) => {
                    chapterApis
                      .updateChapterDisplay({
                        chapterDisplay: Number(value),
                        contentId: courseDetail.contentId,
                      })
                      .then((res: any) => {
                        if (res.status === 200) {
                          baseInfo
                            .getCourseDetails(
                              courseDetail.contentId,
                              location.query.sm ?? 1,
                            )
                            .then(res => {
                              if (res && res.message === 'OK') {
                                dispatch({
                                  type: 'moocCourse/updateState',
                                  payload: {
                                    courseDetail: res.data,
                                  },
                                });
                              }
                            });
                        }
                      });
                  }}
                />
              </div>
            </div>
          </div>
          <div id="tree-container">
            <Spin spinning={loading}>
              {chapterTreeData.length > 0 && (
                <Tree
                  allowDrop={handleAllowDrop}
                  expandedKeys={expandItemKeys} // 控制展开节点，拖动章/节时收起
                  className="draggable-tree"
                  draggable={(node: any) => editId === ''} // 已发布课程不能拖动,正在修改名字不能拖动
                  blockNode
                  icon={null}
                  onExpand={expandKeys => {
                    setExpandItemKeys(expandKeys as string[]);
                  }}
                  onDragStart={onDragStart}
                  onDrop={onDrop}
                  treeData={chapterTreeData}
                  titleRender={node => {
                    return getLearnItem(node);
                  }}
                  onSelect={(selectedKeys, info: any) => {
                    if (selectKeys.includes(info.node?.id)) {
                      setSelectKeys(
                        selectKeys.filter(
                          (item: any) => item !== info.node?.id,
                        ),
                      );
                    } else {
                      setSelectKeys([...selectKeys, info.node.id]);
                    }
                    if (expandItemKeys.includes(info.node?.id)) {
                      setExpandItemKeys(
                        expandItemKeys.filter(
                          (item: any) => item !== info.node?.id,
                        ),
                      );
                    } else {
                      setExpandItemKeys([...expandItemKeys, info.node.id]);
                    }
                  }}
                />
              )}
            </Spin>
          </div>
          {/* 添加章modal */}
          <Modal
            title={t('编辑章')}
            centered
            visible={modalChapterVisible}
            onOk={onChapterModalOK}
            confirmLoading={chapterConfirmLoading}
            onCancel={() => {
              setModalChapterVisible(false);
              chapterForm.setFieldsValue({
                id: undefined,
                name: '',
                describe: '',
              });
            }}
          >
            <Form {...modallayout} form={chapterForm} name="chapterForm">
              <Form.Item name="id" style={{ display: 'none' }}></Form.Item>
              <Form.Item name="status" style={{ display: 'none' }}></Form.Item>
              <Form.Item
                label={t('名称：')}
                name="name"
                rules={[{ required: true, message: t('请填写名称！') }]}
              >
                <Input autoComplete="off" showCount maxLength={99} />
              </Form.Item>
            </Form>
          </Modal>
          {/* 添加节modal */}
          <Modal
            title={t('编辑节')}
            centered
            visible={modalSectionVisible}
            onOk={onSectionModalOK}
            confirmLoading={sectionConfirmLoading}
            onCancel={() => {
              setModalSectionVisible(false);
              sectionForm.setFieldsValue({
                id: undefined,
                parent_id: undefined,
                name: '',
                describe: '',
              });
            }}
          >
            <Form {...modallayout} form={sectionForm} name="sectionForm">
              <Form.Item name="id" style={{ display: 'none' }}></Form.Item>
              <Form.Item name="status" style={{ display: 'none' }}></Form.Item>
              <Form.Item
                name="parent_id"
                style={{ display: 'none' }}
              ></Form.Item>
              <Form.Item
                label={t('名称：')}
                name="name"
                rules={[{ required: true, message: t('请填写名称！') }]}
              >
                <Input autoComplete="off" showCount maxLength={99} />
              </Form.Item>
            </Form>
          </Modal>
          {/* 章、节、资源删除modal */}
          <Modal
            title={t('删除')}
            centered
            visible={modalDeleteVisible}
            onOk={() => deleteConfirm(deleteInfo.id)}
            onCancel={() => {
              setModalDeleteVisible(false);
              setDeleteInfo({
                id: undefined,
                tip: undefined,
              });
            }}
          // confirmLoading={modalDeleteLoading}
          >
            {deleteInfo.tip}
          </Modal>

          {/* 转换成课程地图 */}
          <Modal
            title={t('生成课程地图')}
            open={generatevisible}
            onOk={() => {
              form.submit();
            }}
            onCancel={() => setGeneratevisible(false)}
          >
            <Form
              form={form}
              {...{ labelCol: { span: 4 }, wrapperCol: { span: 20 } }}
              onFinish={generateCourseMap}
            >
              <Form.Item
                label={t('地图名称：')}
                name="mapname"
                rules={[{ required: true, message: t('请填写地图名称！') }]}
              >
                <Input
                  style={{ width: 200 }}
                  placeholder={t('请输入地图名称')}
                ></Input>
              </Form.Item>
              <Form.Item label={t('转换模式：')} name="generationmode">
                <Select
                  defaultValue="auto"
                  value={generationmode}
                  style={{ width: 120 }}
                  onChange={e => setGenerationmode(e)}
                  options={[
                    {
                      value: 'auto',
                      label: t('智能转换'),
                    },
                    {
                      value: 'classification',
                      label: t('分类节点'),
                    },
                    {
                      value: 'knowledge',
                      label: t('知识点'),
                    },
                  ]}
                />
              </Form.Item>
            </Form>
            <p style={{ fontSize: '14px', color: 'rgba(0, 0, 0, 0.65)' }}>
              {t(
                '智能转换：根据课程章节下有无教学内容来判断自动生成的节点类型；',
              )}
            </p>
            <p style={{ fontSize: '14px', color: 'rgba(0, 0, 0, 0.65)' }}>
              {t('分类节点：课程章节全部转换为分类节点；')}
            </p>
            <p style={{ fontSize: '14px', color: 'rgba(0, 0, 0, 0.65)' }}>
              {t('知识点：课程章节全部转换为知识点。')}
            </p>
          </Modal>

          {/* 添加内容Modal */}
          {/* <ResourceUploadModal
           visible={modalContentVisible}
           isAdd={isAddResource}
           resource={resource}
           formData={formData}
           courseCopyId={courseDetail.copyFrom}
           showTemplateModal={courseDetail.copyFrom && permission.includes(ModuleCfg2.template) && templateInfo.state !== -1}
           onOkHandle={onContentModalOk}
           onCancelHandle={onContentModalClose}
           resourceModalConfirm={resourceModalConfirm}
           deleteResourceHandle={deleteResourse}
           confirmLoading={contentConfirmLoading} /> */}
          <VideoModal
            visible={modalContentVisible}
            contentConfirmLoading={contentConfirmLoading}
            onlyVideo={false}
            formData={formData}
            courseCopyId={
              courseDetail.copyFrom ?? courseDetail.entityData?.templateChange
            }
            showTemplateModal={
              (courseDetail.copyFrom ||
                courseDetail.entityData?.templateChange) &&
              permission.includes(ModuleCfg2.template) &&
              templateInfo.state !== -1
            }
            onClose={onContentModalClose}
            onSave={(data: any) => onContentModalOk(data, false)}
          />

          <ChapterCopyModal
            chapter={chapter}
            showChapterPre={showChapterPre}
            sourceCourseId={location.query.id}
            visible={copyCourseVisible}
            onClose={() => setCopyCourseVisible(false)}
          />

          <MoveChapter
            modalVisible={modalMoveChapterVisible}
            modalClose={() => setModalMoveChapterVisible(false)}
            moveChapterOrSection={moveChapterOrSection}
            moveData={moveId}
            moveParentId={moveParentId}
            refresh={getChapter}
          />

          <ResourcePreviewModal
            ref={sourcePreview}
            showKnowledge
            className='video-test-modal'
            modalVisible={entityModalVisible}
            modalClose={(isRefresh?: boolean) => {
              if (isRefresh) getChapter();
              setEntityModalVisible(false);
            }}
            resource={entityPreview}
            onAddTopic={(type: number) => {
              setTopicType(type);
              setShowAddTopic(true);
            }}
          />

          <WordImportModal
            visible={wordImportModal}
            onClose={() => {
              setWordImportModal(false);
              getChapter();
            }}
          />
          <AddHyperlinkModal
            data={hyperlinkData}
            isAdd={isAddHyperlink}
            visible={hyperlinkModalVisible}
            onClose={() => {
              setHyperlinkModalVisible(false);
            }}
            onOK={onContentModalOk}
          />
          {(courseDetail.copyFrom ||
            courseDetail.entityData?.templateChange) && (
              <CopyHomeworkModal
                copyFrom={
                  templateInfo.state === -1
                    ? ''
                    : courseDetail.copyFrom ??
                    courseDetail.entityData?.templateChange
                }
                visible={copyHomeworkVisible}
                onClose={() => {
                  setCopyHomeworkVisible(false);
                  getChapter();
                }}
                parentData={homeworkItem}
                handleAdd={(data: any) =>
                  handleItemAdd(data, 'homework', null, true)
                }
              />
            )}
        </div>
      )}
      <SelectTemplateModal
        onCancel={() => setTemplateModalVisible(false)}
        visible={templateModalVisible}
        showSkip={false}
        onOk={handleTemplateOk}
      />

      <SyllabusModal actionRef={actionRef} />
      <CaseModal
        chapterItem={homeworkItem}
        open={caseVisible}
        onOk={() => getChapter()}
        onClose={() => setCaseVisible(false)}
      />
      <CasePreviewModal
        open={casePreviewOpen}
        onClose={() => setCasePreviewOpen(false)}
        data={casePreviewData}
      />
      <AddBookModal visible={textbookOpen} onClose={(isRefresh?: boolean) => {
        setTextbookOpen(false);
        if (isRefresh) {
          getChapter();
        }
      }} parentId={materialData.guid} />
    </div>
  );
};

export default Chapter;
