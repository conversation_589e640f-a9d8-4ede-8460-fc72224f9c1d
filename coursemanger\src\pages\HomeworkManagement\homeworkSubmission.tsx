import React, { FC, useEffect, useRef, useState } from 'react';
import { Input, Table, Button, Popover, Tabs, message, Modal, Upload, Dropdown, Menu } from 'antd';
// import { columnsConfig } from "./utils/columns";
import { LeftOutlined, DeleteOutlined, LoadingOutlined } from "@ant-design/icons";
import {
  getSubmissions, downloadHomeworkFilesCheck, downloadSubmissions, searchTeamPersonList, getTeamSubmissions,
  importScores, exportScoresTpl, downloadHomeworkFiles, getDownLoadFiles, urgeHomework, getDownLoadbatchFiles,getDownLoadbatchFilesbatch
} from "@/api/homework";
import HomeworkDetail from "./HomeworkDetail";
import CommonHeader from "./components/CommonHeader";
import type { TablePaginationConfig } from 'antd/es/table';
import "./homeworkSubmission.less";
import { useDispatch, useLocation, useSelector } from "umi";
import { handleDownload } from './utils/tools';
import StudentDoHomework from './StudentDoHomework';
import useLocale from '@/hooks/useLocale';
import moment from 'moment';

const { Search } = Input;
interface IHomeworkSubmission {
  handleBack: () => void;
  homeworkItem: any;
}

const HomeworkSubmission: FC<IHomeworkSubmission> = ({ homeworkItem, handleBack }) => {
  const { t } = useLocale();
  const location: any = useLocation();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<any[]>([]); // 个人提交列表
  const [dataTeamSource, setDataTeamSource] = useState<any[]>([]); // 小组提交列表
  const [detail, setDetail] = useState<any>({});
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [query, setQuery] = useState<any>({
    page: 1,
    pageSize: 10,
    txt: "",
    orderType: "desc"
  });

  const [teamPersonDom, setTeamPersonDom] = useState<any>({
    title: null,
    content: null,
  }); // 小组成员名单

  const [total, setTotal] = useState<number>(0);
  const [homeworkDetail, setHomeworkDetail] = useState<any>({});
  const [uploadGradesVisible, setUploadGradesVisible] = useState<boolean>(false);
  const [gradeFile, setGradeFile] = useState<any>(null);
  const [showSubPage, setShowSubPage] = useState<boolean>(false);
  const { courseDetail } = useSelector<Models.Store, any>(state => state.moocCourse,);

  useEffect(() => {
    getSubmissionList();
    dispatch({
      type: 'homework/handlePublicChange',
      payload: {
        homeworkData: homeworkItem
      }
    });
  }, [homeworkItem, query]);

  const handleViewDetail = (record: any) => {
    setDetail(record);
    setShowDetail(true);
  };

  const getSubmissionList = () => {

    setLoading(true);
    // 个人
    getSubmissions({ ...query, homeworkId: homeworkItem.id, parentId: homeworkItem.parentId, courseId: location.query.id }).then((res: any) => {
      if (res.status === 200) {
        setDataSource([...res.data.data]);
        setTotal(res.data.totalCount);
      }
    }).finally(() => { setLoading(false); });

    // 小组作业提交列表

    homeworkItem.howIsDone == 2 && getTeamSubmissions({ ...query, homeworkId: homeworkItem.id, parentId: homeworkItem.parentId, courseId: location.query.id }).then((res: any) => {
      if (res.status === 200) {
        setDataTeamSource([...res.data.data]);
        // setTotal(res.data.totalCount);
      }
    });
  };
  const onSearch = (value: string) => {
    setQuery({
      ...query,
      txt: value
    });
  };
  //作业导出
  const handleExport = () => {
    downloadSubmissions({ courseId: location.query.id, homeworkId: homeworkDetail?.id }).then((res: any) => {
      if (res.status === 400) {
        message.error(res.message);
      } else {
        let link = document.createElement('a');
        link.style.display = 'none';
        link.href = `/exam-api/resource/homework/download?courseId=${location.query.id}&homeworkId=${homeworkDetail?.id}&courseSemester=${location.query.sm ?? 1}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link); //下载完成移除元素
      }
    });
  };

  const downloadFiles = (record?: any) => {
    const param = {
      courseId: location.query.id,
      homeworkId: homeworkDetail?.id,
      parentId: homeworkItem.parentId,
      stuCode: record?.stuCode ?? null
    };
    console.log(record, 'record');
    if (record == '作业') {
      const params = {
        courseId: location.query.id,
        homeworkId: homeworkDetail?.id,
        parentId: homeworkItem.parentId,
        courseSemester: location.query.sm ?? 1,
        type: activeTabKey === 'personal' ? 1 : 2
      };
      getDownLoadbatchFiles(params).then((res1: any) => {
        if (res1.status === 400) {
          message.error(res1.message);
        } else {
          // downloadHomeworkFiles(param).then((res: any) => {
            const hide = message.loading(t('正在下载中，请稍后'), 0, () => {
              message.success(t('下载完成'));
            });
            const timer = setInterval(() => {
              getDownLoadFiles({ scheduleId: res1.data }).then((res: any) => {
                if (res.status === 200) {
                  if (res.data.status == 2) {
                    hide();
                    clearInterval(timer);
                    let link = document.createElement('a');
                    // link.target = '_blank';
                    link.style.display = 'none';
                    link.href = res.data.tempDir;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                  } else if (res.data.status === 0) {
                    message.error(t("附件下载失败"));
                  }
                }
              });
            }, 500);
          // });
        }
      });
    } else if (record == '附件') {
      downloadHomeworkFilesCheck(param).then((res: any) => {
        if (res.status === 400) {
          message.error(res.message);
        } else {
          downloadHomeworkFiles(param).then((res: any) => {
            const hide = message.loading(t('正在下载中，请稍后'), 0, () => {
              message.success(t('下载完成'));
            });
            const timer = setInterval(() => {
              getDownLoadFiles({ scheduleId: res.data }).then((res: any) => {
                if (res.status === 200) {
                  if (res.data.status == 2) {
                    hide();
                    clearInterval(timer);
                    let link = document.createElement('a');
                    // link.target = '_blank';
                    link.style.display = 'none';
                    link.href = res.data.tempDir;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                  } else if (res.data.status === 0) {
                    message.error(t("附件下载失败"));
                  }
                }
              });
            }, 500);
          });
        }
      });
    } else {
      const params = {
        courseId: location.query.id,
        homeworkId: homeworkDetail?.id,
        parentId: homeworkItem.parentId,
        courseSemester: location.query.sm ?? 1,
        type: activeTabKey === 'personal' ? 1 : 2
      };
      // /download/batchWordAndAttach和/download/batch
      getDownLoadbatchFilesbatch(params).then((res: any) => {
        if (res.status === 400) {
          message.error(res.message);
        } else {
         
            const hide = message.loading(t('正在下载中，请稍后'), 0, () => {
              message.success(t('下载完成'));
            });
            const timer = setInterval(() => {
              getDownLoadFiles({ scheduleId: res.data }).then((res: any) => {
                if (res.status === 200) {
                  if (res.data.status == 2) {
                    hide();
                    clearInterval(timer);
                    let link = document.createElement('a');
                    // link.target = '_blank';
                    link.style.display = 'none';
                    link.href = res.data.tempDir;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                  } else if (res.data.status === 0) {
                    message.error(t("附件作业下载失败"));
                  }
                }
              });
            }, 500);
         
        }
      });
    }
    // downloadHomeworkFilesCheck(param).then((res: any) => {
    //   console.log(param,'param');
    //   debugger
    //   if (res.status === 400) {
    //     message.error(res.message);
    //   } else {
    //     downloadHomeworkFiles(param).then((res: any) => {
    //       const hide = message.loading(t('正在下载中，请稍后'), 0, () => {
    //         message.success(t('下载完成'));
    //       });
    //       const timer = setInterval(() => {
    //           getDownLoadFiles({ scheduleId: res.data }).then((res: any) => {
    //             if (res.status === 200) {
    //               if (res.data.status == 2) {
    //                 hide();
    //                 clearInterval(timer);
    //                 let link = document.createElement('a');
    //                 // link.target = '_blank';
    //                 link.style.display = 'none';
    //                 link.href = res.data.tempDir;
    //                 document.body.appendChild(link);
    //                 link.click();
    //                 document.body.removeChild(link);
    //               } else if (res.data.status === 0) {
    //                 message.error(t("附件下载失败"));
    //               }
    //             }
    //           });
    //       }, 500);
    //     });
    //   }
    // });

  };
  const handleHandOver = (record: any) => {
    setDetail(record);
    setShowSubPage(true);
    dispatch({
      type: 'homework/handlePublicChange',
      payload: {
        stuCode: record.stuCode
      }
    });
  };


  // 查看小组成员
  const showTeamPerson = (record: any) => {
    setTeamPersonDom({
      title: `${record.teamName}成员`,
      content: <div><LoadingOutlined /></div>
    });

    // 失败
    const errorPersonDom = {
      title: `${record.teamName}成员`,
      content: '查询失败，请稍后重试'
    }
    searchTeamPersonList({ homeId: record.homeId, teamId: record.teamId }).then((res: any) => {
      if (res?.status === 200) {
        const title = `${record.teamName}成员（${res.data.length}）`

        const content = <div className='team-person-name'>
          {res.data.map((item: any) => (
            <div>{`${item.stuName}(${item.stuCode})`}</div>
          ))
          }
        </div>

        setTeamPersonDom({
          title,
          content
        });
      } else {
        setTeamPersonDom(errorPersonDom);
      }
    }).catch(() => { setTeamPersonDom(errorPersonDom); });
  }

  const columnsConfig = (handleViewDetail: (record: any) => void, downloadFiles: (record: any) => void, onHandOver: (record: any) => void, type: 'person' | 'team') => {
    let baseConfig = [
      {
        title: t("姓名"),
        dataIndex: 'stuName',
        width: 120,
        key: 'stuName',
        ellipsis: true
      },
      {
        title: t("学号"),
        dataIndex: 'stuCode',
        width: 150,
        key: 'stuCode',
        ellipsis: true
      },
      {
        title: t("学院"),
        dataIndex: 'collegeName',
        width: 150,
        key: 'collegeName',
        ellipsis: true
      },
      {
        title: t("专业"),
        dataIndex: 'majorName',
        width: 150,
        key: 'majorName',
        ellipsis: true
      },
      {
        title: t("小组"),
        dataIndex: 'teamName',
        width: 150,
        key: 'teamName',
        ellipsis: true
      },
      {
        title: t("提交状态"),
        dataIndex: 'submitState',
        width: 100,
        key: 'submitState',
        ellipsis: true,
        render: (value: number) => value === 1 ? t("未提交") : value === 2 ? t("已提交") : '',
        sorter: true

      },
      {
        title: t("操作"),
        dataIndex: 'action',
        width: 220,
        key: "action",
        render: (text: string, record: any) => {
          const isHomework = homeworkItem.resourseType == 'homework'
          return <>
            {record.submitState === 2 ? <>
              <a style={{ marginRight: "10px" }} onClick={() => handleViewDetail(record)}>{t("查看详情")}</a>
              {Object.keys(record.hasAttachments ?? {}).length > 0 && isHomework && <a onClick={() => downloadFiles(record)}>{t("下载附件")}</a>}
            </> : ""}
            {isHomework && <a style={{ marginLeft: "10px" }} onClick={() => onHandOver(record)}>{t("老师代交")}</a>}
          </>
        }
      }]

    const teamConfig = [
      {
        title: t("小组名"),
        dataIndex: 'teamName',
        key: 'teamName',
        ellipsis: true,
        width: 300,
        render: (value: number, record: any) => (
          <div >
            <Popover placement="right" title={teamPersonDom.title || `${value}成员`} content={teamPersonDom.content || <div><LoadingOutlined /></div>} trigger="click" overlayClassName='team-person-popverstyle'>
              <span className='team_name' onClick={() => { showTeamPerson(record) }} title='点击查看小组人员'>{value}</span>
            </Popover>
          </div>
        )
      },
      {
        title: t("提交状态"),
        dataIndex: 'submitState',
        width: 100,
        key: 'submitState',
        ellipsis: true,
        render: (value: number) => value === 1 ? t("未提交") : value === 2 ? t("已提交") : '',
        sorter: true

      },
      {
        title: t("操作"),
        dataIndex: 'action',
        width: 220,
        key: "action",
        render: (text: string, record: any) => {
          const isHomework = homeworkItem.resourseType == 'homework'
          return <>
            {record.submitState === 2 ? <>
              <a style={{ marginRight: "10px" }} onClick={() => handleViewDetail(record)}>{t("查看详情")}</a>
              {Object.keys(record.hasAttachments ?? {}).length > 0 && isHomework && <a onClick={() => downloadFiles(record)}>{t("下载附件")}</a>}
            </> : ""}
            {isHomework && <a style={{ marginLeft: "10px" }} onClick={() => onHandOver(record)}>{t("老师代交")}</a>}
          </>
        }
      }
    ]
    const extraConfig: any[] = [
      {
        title: t("提交时间"),
        dataIndex: 'submitTime',
        width: 150,
        key: 'submitTime',
        render: (value: number) => value ? moment(value).format("YYYY-MM-DD HH:mm") : '--',
        ellipsis: true,
        sorter: true
      },
      {
        title: t("批改状态"),
        dataIndex: 'checkState',
        width: 100,
        key: 'checkState',
        render: (value: number) => value === 1 ? <span style={{ color: "#ff4d4f" }}>{t("未批改")}</span> : value === 2 ? t("已批改 ") : '',
        ellipsis: true,
        sorter: true
      },
      {
        title: t("批改时间"),
        dataIndex: 'checkTime',
        width: 150,
        key: 'checkTime',
        render: (value: number, record: any) => record.checkState === 2 ? moment(value).format("YYYY-MM-DD HH:mm") : '--',
        ellipsis: true,
        sorter: true
      },
      {
        title: t("批改人"),
        dataIndex: 'checker',
        width: 120,
        key: 'checker',
        ellipsis: true
      },
      {
        title: t("得分"),
        dataIndex: 'score',
        width: 60,
        key: 'score',
        ellipsis: true,
        render: (value: number) => value != null ? value : '--',
        sorter: true
      },
    ]

    // 个人作业 去掉小组列信息
    if (homeworkItem.howIsDone == 1) {
      baseConfig = baseConfig.filter((item) => item.key !== "teamName")
    }
    if (homeworkItem.resourseType == 'homework') {
      baseConfig.splice(-1, 0, ...extraConfig)
      teamConfig.splice(-1, 0, ...extraConfig)
    }
    return type === 'person' ? baseConfig : teamConfig
  };
  const personColumns = columnsConfig(handleViewDetail, downloadFiles, handleHandOver, 'person');

  const teamColumns = columnsConfig(handleViewDetail, downloadFiles, handleHandOver, 'team');
  const handleTableChange = (pagination: TablePaginationConfig, filters: Record<string, any>, sorter: any) => {
    setQuery({
      ...query,
      pageSize: pagination.pageSize,
      page: pagination.current,
      orderType: sorter.order === "ascend" ? "asc" : "desc",
      order: sorter.columnKey
    });
  };
  const [activeTabKey, setActiveTabKey] = useState<string>('personal');
  const handleSubmitGrades = () => {
    Modal.confirm({
      content: t("导入的成绩将覆盖原已有成绩，是否确认导入？"),
      onOk() {
        const formData = new FormData();
        formData.append("file", gradeFile);
        importScores(formData, { courseId: location.query.id, homeworkId: homeworkDetail?.id, courseSemester: location.query.sm ?? 1 }).then((res: any) => {
          if (res.status === 200) {
            message.success(t("上传成功"));
            getSubmissionList();
          } else {
            message.error(res.message || t("上传失败"));
          }
        });
        onClose();
      }
    });

  };
  const downLoadTpl = () => {
    exportScoresTpl({ courseId: location.query.id, homeworkId: homeworkDetail?.id }).then((res: any) => {
      if (res.status !== 400) {
        const file = {
          attachmentSource: `/exam-api/resource/homework/batch-score/template?courseId=${location.query.id}&homeworkId=${homeworkDetail?.id}&courseSemester=${location.query.sm ?? 1}`,
          attachmentName: `${homeworkItem.name}${t("-打分模板.xls")}`
        };
        handleDownload(file);
      } else {
        message.error(res.message);
      }
    });

  };
  const handleBeforeUpload = (file: any) => {
    setGradeFile(file);
    return false;
  };
  const onClose = () => {
    setUploadGradesVisible(false);
    setGradeFile(null);
  };
  const handleUrge = () => {
    let detailUrl
    if (location.query.type == 'microMajor') {
      detailUrl = `/learn/workbench/#/microprofessional/homework?id=${location.query.id}&type=1&sm=${location.query.sm}&isJoin=true&type=${location.query.type}`
    } else {
      detailUrl = `/learn/course/detail/${location.query.type}/courseWare/${location.query.id}?semester=${location.query.sm || 1}&homework=${homeworkItem.id}`
    }

    Modal.confirm({
      content: "将给未提交作业的学生下发通知提醒，是否操作？",
      onOk() {
        urgeHomework(homeworkItem.id, {
          detailUrl,
          courseName: courseDetail.name
        }).then((res: any) => {
          if (res.status === 200) {
            message.success("操作成功");
          } else {
            message.error("操作失败");
          }
        });
      }
    });
  };

  const commonTemplate = () => (
    <div className="search-container">
      <div className='left'>
        <Search placeholder={t("请输入学生学号")} onSearch={onSearch} />
      </div>
      <div className="right">
        <Button type='primary' ghost onClick={handleUrge} style={{ marginRight: "10px" }}>{t("一键催办")}</Button>
        {
          homeworkItem.resourseType == 'homework' ? <>
            {/* <Button type='primary' ghost onClick={downloadFiles} style={{ marginRight: "10px" }}>{t("下载附件")}</Button> */}
            <Dropdown.Button type='primary' style={{ marginRight: "10px" }} overlay={
              <Menu>
                <Menu.Item key="1" onClick={() => downloadFiles('附件')}>{t("下载附件")}  </Menu.Item>
                <Menu.Item key="2" onClick={() => downloadFiles('作业')}>{t("仅作业")}</Menu.Item>
                <Menu.Item key="3" onClick={() => downloadFiles('附件+作业')} >{t("附件+作业")}</Menu.Item>
              </Menu>
            }>
              {t("选择下载操作")}
            </Dropdown.Button>
            <Button type='primary' ghost onClick={() => setUploadGradesVisible(true)} style={{ marginRight: "10px" }}>{t("上传成绩")}</Button>
            <Button type='primary' ghost onClick={handleExport}>{t("导出Excel")}</Button>
          </> : null
        }
      </div>

    </div>
  )

  // 个人
  const personHomeWorkDom = () => (
    <div className='person-homework-style'>
      {commonTemplate()}
      <div className="table_box">
        <Table
          dataSource={dataSource}
          columns={personColumns}
          loading={loading}
          rowKey="id"
          pagination={{
            size: "small",
            total: total,
            showTotal: (total) => t("共{name}条", String(total)),
            showSizeChanger: true,
            showQuickJumper: true,
            pageSize: query.pageSize,
            current: query.page
          }}
          onChange={handleTableChange}
          scroll={{ y: "calc(100vh - 536px)" }} />
      </div>
    </div>
  )

  // 小组
  const teamHomeWorkDom = () => (
    <div className='person-homework-style'>
      {commonTemplate()}
      <div className="table_box">
        <Table
          dataSource={dataTeamSource}
          columns={teamColumns}
          rowKey="id"
          loading={loading}
          pagination={{
            size: "small",
            total: total,
            showTotal: (total) => t("共{name}条", String(total)),
            showSizeChanger: true,
            showQuickJumper: true,
            pageSize: query.pageSize,
            current: query.page
          }}
          onChange={handleTableChange}
          scroll={{ y: "calc(100vh - 536px)" }} />
      </div>
    </div>
  )

  const items = [
    {
      label: '按人查看',
      key: 'personal',
      children: personHomeWorkDom(),
    },
    {
      label: '按小组查看',
      key: 'team',
      children: teamHomeWorkDom(),
    }
  ]

  return <div>
    {showDetail ?
      <HomeworkDetail
        query={query}
        homeworkDetail={homeworkDetail}
        homeworkItem={homeworkItem}
        submissionData={detail}
        handleBack={() => {
          setShowDetail(false);
          getSubmissionList();
        }} /> :
      showSubPage ? <StudentDoHomework
        homeworkItem={homeworkItem}
        handleBack={(isRefresh?: boolean) => {
          if (isRefresh) {
            getSubmissionList();
          }
          setShowSubPage(false);
        }}
        stuCode={detail.stuCode} /> :

        <div className='homeworkSubmission-container'>
          <div className="header-container">
            <div className="back-btn" onClick={() => handleBack()}><LeftOutlined />{t("返回")}</div>
          </div>
          <div className="content-container">
            <CommonHeader homeworkItem={homeworkItem} showFrom showPreview={true} onGetDetail={(data: any) => setHomeworkDetail(data)} />
            {homeworkItem.howIsDone == 2 ?
              <Tabs
                items={items}
                onChange={(key) => {
                  setActiveTabKey(key);
                }}
              />
              : personHomeWorkDom()}
          </div>
        </div>}
    <Modal
      title={t("上传成绩")}
      footer={null}
      width={420}
      open={uploadGradesVisible}
      onCancel={onClose}
      wrapClassName="grade-upload-modal">

      {
        !gradeFile ? <div className='no-file-wrp'>
          <p>{t("步骤1：下载")}<a onClick={downLoadTpl}>{t("成绩导入模板")}</a>{t("，使用模板填写学生成绩")}</p>
          <p>{t("步骤2：")}<Upload accept='.xls, .xlsx' beforeUpload={handleBeforeUpload}><a>{t("点击上传")}</a></Upload>{t("填写好的表格")}</p>
        </div> : <div className='file-upload-wrp'>
          <p>{t("上传文件：")}{gradeFile.name}<DeleteOutlined onClick={() => setGradeFile(null)} /></p>
          <Button type='primary' onClick={handleSubmitGrades}>{t("开始导入")}</Button>
        </div>}

    </Modal>
  </div>;

};
export default HomeworkSubmission;
