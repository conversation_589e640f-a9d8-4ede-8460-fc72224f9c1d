/*
 * @Author: 李武林
 * @Date: 2022-07-26 11:35:10
 * @LastEditors: 李武林
 * @LastEditTime: 2022-08-01 18:34:21
 * @FilePath: \coursemanger\src\pages\templateDeatil\Newstatistics\index.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 李武林/索贝数码, All Rights Reserved.
 */
import chapterApis from '@/api/chapter';
import ClassOpenPieChart from "@/pages/templateDeatil/Newstatistics/components/ClassOpenPieChart";
import { queryColleges } from '@/api/course';
import { getSeting } from '@/api/learnSet';
import statisticsApi from '@/api/statistics';
import { IconFont } from '@/components/iconFont';
import useLocale from '@/hooks/useLocale';
import usePermission from '@/hooks/usePermission';
import { IGlobalModelState } from '@/models/global';
import { numberToChinese } from '@/utils';
import { ExclamationCircleOutlined, SearchOutlined, StarFilled } from '@ant-design/icons';
import { Button, Empty, Input, Popconfirm, Popover, Progress, Select, Table, Tabs, Tag, Tooltip } from 'antd';
import moment from 'moment';
import React, { FC, useEffect, useRef, useState } from 'react';
import { history, useLocation, useSelector } from 'umi';
import Achievementdegree from './components/Achievementdegree';
import BarChart from './components/BarChart';
import BarLineChart from './components/BarLineChart';
import BarTwoLineChart from './components/BarTwoLineChart';
import DetailContent from './components/DetailContent';
import Echart from './components/Echart';
import KnowledgeDetail from './components/KnowledgeDetail';
import PieChart from './components/PieChart';
import StackLineChart from './components/StackLineChart';
import StudentDetail from './components/StudentDetail';
import './index.less';
const { TabPane } = Tabs;
const { Search } = Input;
const { Option } = Select;

interface IStudentFilter {
  /** 学院筛选 */
  isSearchCollege?: boolean
  /** 学院列表 */
  collegeList?: string[]
  /** 角色范围筛选 */
  isSearchRole?: boolean
  /** 角色范围 */
  role?: string
}

const Newstatistics: FC = () => {
  const { t } = useLocale();
  const { parameterConfigObj, userInfo, parameterConfig } = useSelector<
    { global: IGlobalModelState },
    IGlobalModelState
  >(state => state.global);
  const { getPermission } = usePermission();

  const location: any = useLocation();
  // 当前的课程id
  const courseId = location.query.id;
  const showdetail = location.query.showdetail;
  const studentId = location.query.studentId || '';
  const [dataSource1, setDataSource1] = useState<any[]>([]);
  const [dataSource2, setDataSource2] = useState<any[]>([]);
  //作业提交情况
  const [
    origenHomeworkSubmitStatistics,
    setOrigenHomeworkSubmitStatistics,
  ] = useState<any[]>([]);
  const [homeworkSubmitStatistics, setHomeworkSubmitStatistics] = useState<
    any[]
  >([]);
  // 参与学习人数 课程资源数 发布作业数 学生提问数
  const [heardinfo, setHeardinfo] = useState<any>({
    askNumber: 0,
    askRate: 0,
    completeRate: 0,
    homeworkCommitRate: 0,
    homeworks: 0,
    participateNumber: 0,
    resourceLearningTimes: 0,
    resources: 0,
  });
  const typeArr = ['mooc','spoc']
  const [keyword, setKeyword] = useState<string>('');
  const [keyword2, setKeyword2] = useState<string>('');
  const [keyword3, setKeyword3] = useState<string>('');
  const [columns, setColumns] = useState<any>([]);
  // 排序方式
  const [sortBy, setSortBy] = useState<number>(0);
  // 排序类型
  const [sortTarget, setSortTarget] = useState<number>(0);
  // 排序方式
  const [sortBy2, setSortBy2] = useState<number>(0);
  // 排序类型
  const [sortTarget2, setSortTarget2] = useState<number>(0);

  // 表格loading
  const [loading, setLoading] = useState<boolean>(false);
  // 分页
  const [pagination, setPagination] = useState<any>({
    current: 1,
    position: ['bottomCenter'],
    pageSize: 10,
    total: 0,
    // size: "small",
    showTotal: (total: number) => t('共{name}条', String(total)),
    showQuickJumper: true,
    showSizeChanger: true,
  });

  // 查询的角色
  const [roles, setRoles] = useState<string>('');

  // 分页2
  const [pagination2, setPagination2] = useState<any>({
    current: 1,
    pageSize: 10,
    position: ['bottomCenter'],
    total: 0,
    // size: "small",
    showTotal: (total: number) => t('共{name}条', String(total)),
    showQuickJumper: true,
    showSizeChanger: true,
  });

  //#region 课程地图权限关联展示(不展示地图,相应的功能需要隐藏)
  const isShowMap = useRef(getPermission(['spoc', 'mooc', 'training'], 'knowledge_map_display'))
  //#endregion
  const isRenderd = useRef(false);
  const isFromCourseAchieve = useRef(false)
  // 从课程目标达成度跳转
  useEffect(() => {
    if (studentId && dataSource1?.length > 0 && !isRenderd.current) {
      const aimStudent = dataSource1.find(item => item?.code === studentId);
      if (aimStudent) {
        setShowDetail({ courseId, ...aimStudent });
        setShowPage(2);
        isFromCourseAchieve.current = true
      }
      isRenderd.current = true;
    }
  }, [studentId, dataSource1]);

  //#region
  //学院列表
  const [colleges, setColleges] = useState<any[]>([]);
  const [checkColleges, setCheckColleges] = useState<any[]>([])
  const getColleges = () => {
    queryColleges().then((res: any) => {
      if (res.status === 200) {
        setColleges(res.data.organization ?? []);
      }
    });
  };
  useEffect(() => {
    getColleges();
  }, []);
  //#endregion

  //作业提交情况
  const [homeworkStatisticsParams, setHomeworkStatisticsParams] = useState<any>(
    {
      chapterId: '',
      homeworkName: '',
      sortBy: 0,
      sortName: t('章节'),
    },
  );
  // 资源类型
  const [resourceType, setResourceType] = useState<string>('');
  // 资源所有类型
  const resourceoptions: any = [
    { label: t('音频'), value: 'audio' },
    { label: t('视频'), value: 'video' },
    { label: t('图片'), value: 'picture' },
    { label: t('文档'), value: 'document' },
    { label: t('文件夹'), value: 'folder' },
    { label: t('超链接'), value: 'hyperlink' },
    // { label: '作业', value: 'homework' },
    { label: t('其他'), value: 'other' },
  ];

  //选择的章节
  const [chapterName, setChapterName] = useState<string>('');
  // 所有章节
  const [chapters, setChapters] = useState<any[]>([]);

  //统计图标展示类型，true为按学院统计
  // const [stasticsType, setStasticsType] = useState<boolean>(false)

  //是否查看详情
  const [showDetail, setShowDetail] = useState<any>(false);

  const [showPage, setShowPage] = useState<number>(1);

  //学习进度数据
  const [progressData, setProgressData] = useState<any>({});

  const [showStudentDetail, setShowStudentDetail] = useState<any>(false);

  const [showHomework, setShowHomework] = useState<boolean>(false);

  //资源学习情况
  const [resourceStudyData, setResourceStudyData] = useState<any>([]);
  const [resourceStudyParams, setResourceStudyParams] = useState<any>({
    chapterName: '',
    courseId,
    sortBy: 0,
    sortTarget: 2,
  });

  //作业完成情况
  const [homeworkPieData, setHomeworkPieData] = useState<any>({});
  const [homeworkSubmitData, setHomeworkSubmitData] = useState<any>([]);
  const [homeworkParams, setHomeworkParams] = useState<any>({
    chapterId: '',
    sortBy: 0,
    sortName: t('章节'),
  });

  // 知识点统计数据
  const [knowledgePointData, setKnowledgePointData] = useState<any>(null);
  // 统计提问数 掌握率 完成率
  const [knowledgePointParams, setKnowledgePointParams] = useState<any>({
    questionTotal: 0,
    totalFinishRate: 0,
    totalMasterRate: 0,
  });

  // 当前选择的模式
  const currentMode = useRef<string>('finishRateList');
  const [knowledgeitem, setKnowledgeitem] = useState<any>(null);
  // 当前选择的tab
  const [selectkey, setSelectkey] = useState<string>('1');
  const [learnSetting, setLearnSetting] = useState<any>({});

  useEffect(() => {
    if (showdetail == 'true') {
      setShowPage(4);
      let knowledgeitem: any = sessionStorage.getItem('knowledgeitem') || {};
      setKnowledgeitem(JSON.parse(knowledgeitem));
    }
    getData();
    getStudentData();
    // 获取所有章节
    getChapter();
    // 获取资源使用情况
    getResourceData();
    //获取学习进度分布
    getProgressData();
    //获取资源学习情况
    getResourceStudyData();
    //获取作业提交情况
    getHomeworkStatisticsList();
    //获取作业统计
    getHomeworkStatistics();
    // 获取知识点统计
    knowledgePointOptions();
    getLearnSetting();
  }, []);

  useEffect(() => {
    if (Object.keys(parameterConfigObj).length > 0) {
      if (location.pathname.includes('tempatedetail')) {
        setShowHomework(
          parameterConfigObj.kczx?.includes(
            'course_library_school_assignment_display',
          ),
        );
      } else {
        setShowHomework(
          getPermission(
            ['spoc', 'mooc', 'training'],
            '_school_assignment_display',
            true,
          ),
        );
      }
    }
  }, [parameterConfigObj]);
  useEffect(() => {
    let newColumns = defaultColumns.filter(
      (column: any) =>
        showHomework ||
        (column.dataIndex !== 'submitted' && column.dataIndex !== 'scoreRate'),
    );
    if (
      Reflect.ownKeys(learnSetting).length > 0 &&
      learnSetting.necessaryLearn === 0
    ) {
      let i = 0;
      newColumns = newColumns.map((item: any, index: number) => {
        if (item.dataIndex === 'learningProgressRate') {
          i = index + 1;
          item.title = () => (
            <span>
              <Tooltip
                title={t(
                  '学习进度=（完成学习的必学内容数+选学内容数）/（必学内容数+选学要求完成的个数）当选学内容的完成数超过了要求个数时，按要求个数计算',
                )}
              >
                <ExclamationCircleOutlined
                  style={{ marginRight: '10px', color: 'var(--primary-color)' }}
                />
              </Tooltip>
              {t('学习进度')}
            </span>
          );
        }
        return item;
      });
      newColumns.splice(
        i,
        0,
        {
          title: t('必学完成情况'),
          dataIndex: 'necessaryRate',
          key: 'necessaryRate',
          align: 'center',
          sorter: true,
        },
        {
          title: t('选学完成情况'),
          dataIndex: 'notNecessaryRate',
          key: 'notNecessaryRate',
          align: 'center',
          sorter: true,
        },
      );
    }
    setColumns(newColumns);
  }, [showHomework, learnSetting]);

  useEffect(() => {
    getStudentData();
  }, [sortBy, sortTarget, pagination.current, pagination.pageSize]);

  useEffect(() => {
    getResourceData();
  }, [
    chapterName,
    resourceType,
    sortBy2,
    sortTarget2,
    pagination2.current,
    pagination2.pageSize,
  ]);

  // 获取所有章节
  const getChapter = async () => {
    chapterApis.getChapter(`courseId=${courseId}&status=2`).then(res => {
      if (res.status == 200) {
        setChapters(res.data);
      }
    });
  };

  // 获取 参与学习人数 课程资源数 发布作业数 学生提问数
  const getData = () => {
    statisticsApi.getacademic({
      courseId: courseId,
      courseType: location.query.type == 'spoc' ? 2 : 1,
      type: 0
    }).then((res) => {
      if (res.status == 200) {
        setHeardinfo(res.data);
      }
    });
  };

  // 获取学生学习情况
  const getStudentData = (toPage1?: boolean, searchParam?: IStudentFilter) => {
    setLoading(true);
    if (toPage1) {
      setPagination({
        ...pagination,
        current: 1,
      });
    }
    statisticsApi
      .getstudent({
        courseId: courseId,
        courseType: location.query.type == 'spoc' ? 2 : 1,
        role:  searchParam?.isSearchRole? searchParam?.role : roles,
        page: toPage1 ? 1 : pagination.current,
        size: pagination.pageSize,
        keyword: keyword,
        sortBy: sortBy, //排序方式  0：降序、1：升序（默认0）
        sortTarget: sortTarget, //排序字段 0：学习进度、1：视频学习时长、2：作业提交情况、3：作业得分率、4：提问次数（默认0）
        college: searchParam?.isSearchCollege ? searchParam?.collegeList?.join(',') : checkColleges.join(',')
      })
      .then(res => {
        if (res.status == 200) {
          setDataSource1(res.data.results);
          setPagination({
            ...pagination,
            current: res.data.page,
            size: res.data.size,
            total: res.data.total,
          });
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  //获取资源使用情况
  const getResourceData = (toPage1?: boolean) => {
    setLoading(true);
    if (toPage1) {
      setPagination2({
        ...pagination2,
        current: 1,
      });
    }
    statisticsApi
      .getresource({
        courseId: courseId,
        page: toPage1 ? 1 : pagination2.current,
        size: pagination2.pageSize,
        resourceName: keyword2,
        sortBy: sortBy2, //排序方式  0：降序、1：升序（默认0）
        sortTarget: sortTarget2, //排序字段 0：学习进度、1：视频学习时长、2：作业提交情况、3：作业得分率、4：提问次数（默认0）
        chapterName: chapterName,
        resourceType: resourceType,
      })
      .then(res => {
        if (res.status == 200) {
          setDataSource2(
            res.data.results.map((item: any, index: number) => {
              return {
                ...item,
                key: index,
              };
            }),
          );
          setPagination2({
            ...pagination2,
            current: res.data.page,
            size: res.data.size,
            total: res.data.total,
          });
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    getHomeworkStatisticsList();
  }, [homeworkStatisticsParams]);
  //获取作业提交情况
  const getHomeworkStatisticsList = () => {
    setLoading(true);
    const {
      chapterId,
      homeworkName,
      sortBy,
      sortName,
    } = homeworkStatisticsParams;
    statisticsApi
      .getHomeworkStatisticsData(
        { chapterId, homeworkName, sortBy, sortName },
        courseId,
      )
      .then(res => {
        if (res.status == 200) {
          setHomeworkSubmitStatistics(res.data);
          setOrigenHomeworkSubmitStatistics(res.data);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 表格切换排序条件
  const tablechange = (pagination2: any, filters: any, sorter: any) => {
    setPagination({
      ...pagination,
      current: pagination2.current,
      pageSize: pagination2.pageSize,
    });
    // 升序
    if (sorter.order == 'ascend') {
      setSortBy(1);
      if (sorter.columnKey == 'learningProgressRate') {
        setSortTarget(0);
      } else if (sorter.columnKey == 'learningTime') {
        setSortTarget(1);
      } else if (sorter.columnKey == 'submitted') {
        setSortTarget(2);
      } else if (sorter.columnKey == 'scoreRate') {
        setSortTarget(3);
      } else if (sorter.columnKey == 'questionsNumber') {
        setSortTarget(4);
      } else if (sorter.columnKey == 'roleName') {
        setSortTarget(5);
      } else if (sorter.columnKey == 'finishRate') {
        setSortTarget(6);
      } else if (sorter.columnKey == 'masterRate') {
        setSortTarget(7);
      } else if (sorter.columnKey == 'keyDifficultPoints') {
        setSortTarget(8);
      } else if (sorter.columnKey == 'necessaryRate') {
        setSortTarget(9);
      } else if (sorter.columnKey == 'notNecessaryRate') {
        setSortTarget(10);
      }
    } else if (sorter.order == 'descend') {
      setSortBy(0);
      if (sorter.columnKey == 'learningProgressRate') {
        setSortTarget(0);
      } else if (sorter.columnKey == 'learningTime') {
        setSortTarget(1);
      } else if (sorter.columnKey == 'submitted') {
        setSortTarget(2);
      } else if (sorter.columnKey == 'scoreRate') {
        setSortTarget(3);
      } else if (sorter.columnKey == 'questionsNumber') {
        setSortTarget(4);
      } else if (sorter.columnKey == 'roleName') {
        setSortTarget(5);
      } else if (sorter.columnKey == 'finishRate') {
        setSortTarget(6);
      } else if (sorter.columnKey == 'masterRate') {
        setSortTarget(7);
      } else if (sorter.columnKey == 'keyDifficultPoints') {
        setSortTarget(8);
      } else if (sorter.columnKey == 'necessaryRate') {
        setSortTarget(9);
      } else if (sorter.columnKey == 'notNecessaryRate') {
        setSortTarget(10);
      }
    } else if (sorter.order === undefined) {
      setSortBy(0);
      setSortTarget(0);
    }
  };

  // 表格切换排序条件
  const tablechange2 = (paginati: any, filters: any, sorter: any) => {
    setPagination2({
      ...pagination2,
      current: paginati.current,
      pageSize: paginati.pageSize,
    });
    // 升序
    if (sorter.order == 'ascend') {
      setSortBy2(1);
      // 0：学习次数、1：学习人数、2：学习完成率
      if (sorter.columnKey == 'learningNumber') {
        setSortTarget2(0);
      } else if (sorter.columnKey == 'studyNumber') {
        setSortTarget2(1);
      } else if (sorter.columnKey == 'completeRateConvert') {
        setSortTarget2(2);
      } else if (sorter.columnKey == 'download') {
        setSortTarget2(3);
      } else if (sorter.columnKey == 'chapterOrder') {
        setSortTarget2(4);
      }
    } else if (sorter.order == 'descend') {
      setSortBy2(0);
      if (sorter.columnKey == 'learningNumber') {
        setSortTarget2(0);
      } else if (sorter.columnKey == 'studyNumber') {
        setSortTarget2(1);
      } else if (sorter.columnKey == 'completeRateConvert') {
        setSortTarget2(2);
      }
    } else if (sorter.order === undefined) {
      setSortBy2(0);
      setSortTarget2(0);
    }
  };

  //#region 学生重难点查询
  const [studentDifficultiesSort, setStudentDifficultiesSort] = useState({
    /** 章节id */
    chapterId: '',
     /** 排序方式 0：降序、1：升序（默认0） */
     sortBy: 0,
     /** 排序字段：0：章节、1：知识点数量（默认0） */
     sortTarget: 0
  })
  const [difficultiesTotal, setDifficultiesTotal] = useState<any>({})
  const getDifficultiesTotal = () => {
    statisticsApi.queryCourseDifficultiesTotal({chapterId: studentDifficultiesSort.chapterId, courseId: courseId}).then((res: any) => {
      if (res?.status == 200) {
        setDifficultiesTotal(res?.data)
      }
    })
  }
  useEffect(() => {
    if (!courseId) return
    getDifficultiesTotal()
  }, [courseId, studentDifficultiesSort.chapterId])
  //#endregion

  //#region 学生重难点统计图表
  const [studentDifficultiesChart, setStudentDifficultiesChart] = useState<any[]>([])
  const getStudentDifficulties = () => {
    statisticsApi.queryCourseHistogram({courseId: courseId, ...studentDifficultiesSort}).then((res: any) => {
      if (res?.status == 200) {
        setStudentDifficultiesChart(res?.data || [])
      }
    })
  }
  useEffect(() => {
    if (!courseId) return
    getStudentDifficulties()
  }, [courseId, studentDifficultiesSort])
  //#endregion


  const onChange = (key: string) => {
    // console.log(key);
    setSelectkey(key);
  };

  // 导出学习记录
  const exportLearning = () => {
    window.open(
      `/learn/v1/statistics/student/learning/export?courseId=${courseId}&courseType=${
        location.query.type == 'spoc' ? 2 : 1
      }&keyword=${keyword}&sortBy=${sortBy}&sortTarget=${sortTarget}&courseSemester=${
        location.query.sm
      }&college=${checkColleges?.join(',')}`,
    );
  };

  // 导出课程资源使用记录
  const exportresource = () => {
    // window.open(`/learn/v1/statistics/resource/learning/export?courseId=${courseId}&courseType=${location.query.type == 'spoc' ? 2 : 1}&keyword=${keyword2}&sortBy=${sortBy2}&sortTarget=${sortTarget2}&chapterName=${chapterName}&resourceType=${resourceType}`)
    const data = {
      courseId,
      courseType: location.query.type == 'spoc' ? 2 : 1,
      keyword: keyword2,
      sortBy: sortBy2,
      sortTarget: sortTarget2,
      chapterName,
      resourceType,
    };
    statisticsApi.exportResources(data).then((res: any) => {
      const url = window.URL.createObjectURL(res);
      const eleLink = document.createElement('a');
      eleLink.href = url;
      eleLink.download = t('资源学习情况.xlsx');
      eleLink.style.display = 'none';
      document.body.appendChild(eleLink);
      eleLink.click();
      document.body.removeChild(eleLink);
    });
  };

  const defaultColumns: any = [
    {
      title: t('姓名'),
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: t('学工号'),
      dataIndex: 'code',
      key: 'code',
      align: 'center',
    },
    {
      title: t('角色'),
      dataIndex: 'roleName',
      key: 'roleName',
      align: 'center',
      sorter: true,
      render: (text: any) => {
        return <Tag color="#f50">{text}</Tag>;
      },
    },
    {
      title: t('学院'),
      dataIndex: 'college',
      key: 'college',
      align: 'center',
    },
    {
      title: t('培养层次'),
      dataIndex: 'education',
      key: 'education',
      align: 'center',
      render: (text: any) => {
        if (text == '1') {
          return '研究生';
        }
        if (text == '0') {
          return '本科生';
        }
        return '';
      },
    },
    {
      title: t('专业'),
      dataIndex: 'major',
      key: 'major',
      align: 'center',
    },
    {
      title: () => (
        <span>
          <Tooltip title={t('学习进度=学员已学习的课程内容/总课程内容')}>
            <ExclamationCircleOutlined
              style={{ marginRight: '10px', color: 'var(--primary-color)' }}
            />
          </Tooltip>
          {t('学习进度')}
        </span>
      ),
      dataIndex: 'learningProgressRate',
      key: 'learningProgressRate',
      align: 'center',
      sorter: true,
      render: (text: any, record: any) => {
        return <span>{text < 100 ? text : 100}%</span>;
      },
    },
    {
      title: t('视频学习时长'),
      dataIndex: 'learningTime',
      key: 'learningTime',
      align: 'center',
      sorter: true,
      render: (text: any, record: any) => {
        let h = Math.floor(text / 1000 / 60 / 60) || 0;
        let min = Math.floor((text % 3600000) / 60000) || 0;
        let seconds = Math.floor((text / 1000) % 60) || 0;
        return (
          <span>
            {(h == 0 ? '' : h + t('时')) +
              (min == 0 && h == 0 ? '' : min + t('分')) +
              (seconds == 0 && min != 0 ? '' : seconds + t('秒'))}
          </span>
        );
      },
    },
    {
      title: t('作业提交情况'),
      dataIndex: 'submitted',
      key: 'submitted',
      align: 'center',
      sorter: true,
      render: (text: any, record: any) => {
        return (
          <span>
            {t('提交')}
            {record.submitted}/{record.submittedTotal}
            {t('次')}
          </span>
        );
      },
    },
    {
      title: () => (
        <span>
          <Tooltip
            title={t('作业得分率=已批改作业得分之和/已批改作业总分之和')}
          >
            <ExclamationCircleOutlined
              style={{ marginRight: '10px', color: 'var(--primary-color)' }}
            />
          </Tooltip>
          {t('作业得分率')}
        </span>
      ),
      dataIndex: 'scoreRate',
      key: 'scoreRate',
      align: 'center',
      sorter: true,
      render: (text: any, record: any) => {
        return <span>{text}%</span>;
      },
    },
    {
      title: t('提问次数'),
      dataIndex: 'questionsNumber',
      key: 'questionsNumber',
      align: 'center',
      sorter: true,
      render: (text: any, record: any) => {
        return (
          <span>
            {text}
            {t('次')}
          </span>
        );
      },
    },
    {
      title: t('知识点完成率'),
      dataIndex: 'finishRate',
      key: 'finishRate',
      align: 'center',
      sorter: true,
      render: (text: any, record: any) => {
        return <span>{Number(text)}%</span>;
      },
    },
    {
      title: t('知识点掌握率'),
      dataIndex: 'masterRate',
      key: 'masterRate',
      align: 'center',
      sorter: true,
      render: (text: any, record: any) => {
        return <span>{Number(text)}%</span>;
      },
    },
    {
      title: t("标记重难点"),
      dataIndex: 'keyDifficultPoints',
      key: 'keyDifficultPoints',
      align: 'center',
      sorter: true,
      render: (text: any, record: any) => {
        return <span>{Number(text)}{t("处")}</span>;
      }
    },
    {
      title: t("操作"),
      key: 'action',
      align: 'center',
      render: (_: any, record: any) => (
        <Button
          type="link"
          onClick={() => {
            setShowDetail({ courseId, ...record });
            setShowPage(2);
          }}
        >
          {t('查看详情')}
        </Button>
      ),
    },
  ];

  const columns2: any = [
    {
      title: t('资源名称'),
      dataIndex: 'resourceName',
      key: 'resourceName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: t('学习要求'),
      dataIndex: 'necessaryStudy',
      key: 'necessaryStudy',
      align: 'center',
      ellipsis: true,
      render: (text: any) => (text ? t('必学') : t('选学')),
    },
    {
      title: t('学习次数'),
      dataIndex: 'learningNumber',
      key: 'learningNumber',
      align: 'center',
      sorter: true,
    },
    {
      title: t('学习人数'),
      dataIndex: 'studyNumber',
      key: 'studyNumber',
      align: 'center',
      sorter: true,
    },
    {
      title: t('下载次数'),
      dataIndex: 'download',
      key: 'download',
      align: 'center',
      sorter: true,
    },
    {
      title: () => (
        <span>
          <Tooltip title={t('学习完成率=已完成学习的学生/学生总数')}>
            <ExclamationCircleOutlined
              style={{ marginRight: '10px', color: 'var(--primary-color)' }}
            />
          </Tooltip>
          {t('学习完成率')}
        </span>
      ),
      dataIndex: 'completeRateConvert',
      key: 'completeRateConvert',
      align: 'center',
      sorter: true,
    },
    {
      title: t("学生标记重难点"),
      dataIndex: 'keyDifficultPoints',
      key: 'keyDifficultPoints',
      align: 'center',
      sorter: true,
      render: (text: any, record: any) => {
        if (record?.resourceType === 'video') {
          return <span>{text || 0}{t("次")}</span>
        }
        return '——'
      }
    },
    {
      title: t("所属章"),
      dataIndex: 'chapterOrder',
      key: 'chapterOrder',
      align: 'center',
      sorter: true,
      render: (text: any, record: any) => (
        <span>
          {t('第')}
          {numberToChinese(text)}
          {t('章')}
        </span>
      ),
    },
    {
      title: t('章名称'),
      dataIndex: 'chapterName',
      key: 'chapterName',
      align: 'center',
    },
    {
      title: t('所属节'),
      dataIndex: 'sectionName',
      key: 'sectionName',
      align: 'center',
      render: (text: any, record: any) => <span>{text || '-'}</span>,
    },
    {
      title: t('资源类型'),
      dataIndex: 'resourceType',
      key: 'resourceType',
      align: 'center',
      render: (text: any, record: any) => {
        // { label: '音频', value: 'audio' },
        // { label: '视频', value: 'video' },
        // { label: '图片', value: 'picture' },
        // { label: '文档', value: 'document' },
        // { label: '文件夹', value: 'folder' },
        // { label: '其他', value: 'other' },
        if (text == 'audio') {
          return <span>{t('音频')}</span>;
        } else if (text == 'video') {
          return <span>{t('视频')}</span>;
        } else if (text == 'picture') {
          return <span>{t('图片')}</span>;
        } else if (text == 'document') {
          return <span>{t('文档')}</span>;
        } else if (text == 'folder') {
          return <span>{t('文件夹')}</span>;
        } else if (text == 'homework') {
          return <span>{t('作业')}</span>;
        } else if (text == 'hyperlink') {
          return <span>{t('超链接')}</span>;
        } else if (text == 'other') {
          return <span>{t('其他')}</span>;
        } else if (text == 'case') {
          return <span>{t('案例')}</span>;
        } else {
          return <span>{text}</span>;
        }
      },
    },
    {
      title: t('操作'),
      key: 'action',
      align: 'center',
      render: (_: any, record: any) => (
        <Button
          type="link"
          onClick={() => {
            setShowDetail({ courseId, ...record });
            setShowPage(3);
          }}
        >
          {t('查看详情')}
        </Button>
      ),
    },
  ];

  const homewordColumns: any = [
    {
      title: t('作业'),
      dataIndex: 'title',
      key: 'title',
      align: 'center',
      width: 300,
      ellipsis: { showTitle: true },
    },
    {
      title: t('所属章'),
      dataIndex: 'chapterName',
      key: 'chapterName',
      align: 'center',
    },
    {
      title: t('所属节'),
      dataIndex: 'sectionName',
      key: 'sectionName',
      align: 'center',
    },
    {
      title: t('总分'),
      dataIndex: 'totalScore',
      key: 'totalScore',
      align: 'center',
    },
    {
      title: t('截止提交时间'),
      dataIndex: 'closeTime',
      key: 'closeTime',
      align: 'center',
      render: (text: any, record: any) => (
        <span>{moment(text).format('YYYY-MM-DD') || '-'}</span>
      ),
    },
    {
      title: t('提交情况'),
      dataIndex: 'stuSubmitNum',
      key: 'stuSubmitNum',
      align: 'center',
      render: (text: any, record: any) => (
        <span>
          {text || 0}/{record.stuNum || 0}
          {t('已提交')}
        </span>
      ),
    },
    {
      title: () => (
        <span>
          <Tooltip
            title={t(
              '平均得分率=已批改学员作业得分之和/已批改学员作业的总和之和',
            )}
          >
            <ExclamationCircleOutlined
              style={{ marginRight: '10px', color: 'var(--primary-color)' }}
            />
          </Tooltip>
          {t('平均得分率')}
        </span>
      ),
      dataIndex: 'homeworkSoringRate',
      key: 'homeworkSoringRate',
      align: 'center',
      sorter: true,
    },
    {
      title: t('操作'),
      key: 'action',
      align: 'center',
      render: (_: any, record: any) => (
        <Button
          type="link"
          onClick={() => {
            const queryData: any = history.location.query;
            let href = '/editcourse/';
            if (queryData.type == 'mooc') {
              href += 'moochomework';
            } else {
              href += 'homework';
            }
            history.push(
              `${href}?id=${queryData.id}&type=${queryData.type}&homework_id=${
                record.id
              }&sm=${queryData.sm ?? 1}`,
            );
          }}
        >
          {t('查看详情')}
        </Button>
      ),
    },
  ];

  const getlearningTimeTotal = (text: number) => {
    let h = Math.floor(text / 1000 / 60 / 60) || 0;
    let min = Math.floor((text % 3600000) / 60000) || 0;
    let seconds = Math.floor((text / 1000) % 60) || 0;
    return (
      (h == 0 ? '' : h + t('时')) +
      (min == 0 && h == 0 ? '' : min + t('分')) +
      (seconds == 0 && min != 0 ? '' : seconds + t('秒'))
    );
  };

  //获取学习进度分布数据
  const getProgressData = () => {
    let data: any = {
      progressDistribute: [],
      complateData: [],
      collegeDistribute: [],
      personDistribute: [],
    };
    statisticsApi.getProgressDistribute({ courseId }).then(res => {
      // console.log('progressData', res)
      if (res.status == 200) {
        let obj = res.data;
        const { completeTotal, undoneTotal } = obj;
        delete obj.completeTotal;
        delete obj.undoneTotal;
        data = {
          ...data,
          progressDistribute: Object.keys(obj).map((key: any) => ({
            name: key + '%',
            value: obj[key],
          })),
          complateData: [
            { name: t('已完成学习'), value: completeTotal || 0 },
            { name: t('未完成学习'), value: undoneTotal || 0 },
          ],
        };
        setProgressData(data);
      }
    });
    statisticsApi.getCollegeDistribute({ courseId }).then(res => {
      // console.log('collegeDistribute', res)
      if (res.status == 200) {
        const collegeDistribute = res.data?.map((el: any) => ({
          name: el.college,
          value: el.distribution,
        }));
        data = {
          ...data,
          collegeDistribute,
        };
        setProgressData(data);
      }
    });
    statisticsApi.getPersonDistribute({ courseId }).then(res => {
      // console.log('getPersonDistribute', res)
      if (res.status == 200) {
        const personDistribute = res.data?.map((el: any) => ({
          name: el.student,
          value: el.learningProgressRate,
        }));
        data = {
          ...data,
          personDistribute,
        };
        setProgressData(data);
      }
    });
  };

  //学习进度分布按个人统计图表
  const getPersonDistributeOptions = () => {
    return {
      tooltip: {
        formatter: function(param: any) {
          let str = `<div style="display:flex;align-items:center;">${param.marker}<span style="flex:1;display:flex;justify-content: space-between;">${param.name}<span style="margin-left:20px;">${param.data}%</span></span></div>`;
          return str;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: progressData.personDistribute.map((el: any) => el.name),
        axisLine: {
          lineStyle: {
            color: '#eee',
          },
        },
        axisLabel: {
          color: '#333',
        },
      },
      yAxis: {
        type: 'value',
        splitLine: {
          lineStyle: {
            color: '#eee',
          },
        },
        axisLabel: {
          color: '#333',
          formatter: '{value}%',
        },
        minInterval: 1,
      },
      series: [
        {
          type: 'bar',
          data: progressData.personDistribute.map((el: any) => el.value),
          itemStyle: {
            color: '#549cff',
            barBorderRadius: [4, 4, 0, 0],
          },
          barWidth: 24,
        },
      ],

      dataZoom:
        progressData.personDistribute?.length > 12
          ? [
              // 有滚动条 平移
              {
                type: 'slider',
                realtime: true,
                start: 0,
                end:
                  progressData.personDistribute?.length > 12
                    ? (
                        (12 / progressData.personDistribute.length) *
                        100
                      ).toFixed(0)
                    : 100, // 初始展示20%
                height: 4,
                fillerColor: 'rgba(17, 100, 210, 0.42)', // 滚动条颜色
                borderColor: 'rgba(17, 100, 210, 0.12)',
                handleSize: 0, // 两边手柄尺寸
                showDetail: false, // 拖拽时是否展示滚动条两侧的文字
                top: '96%',

                // zoomLock:true, // 是否只平移不缩放
                // moveOnMouseMove:true, //鼠标移动能触发数据窗口平移
                // zoomOnMouseWheel :true, //鼠标移动能触发数据窗口缩放
              },
              {
                type: 'inside', // 支持内部鼠标滚动平移
                start: 0,
                end: 10,
                zoomOnMouseWheel: false, // 关闭滚轮缩放
                moveOnMouseWheel: true, // 开启滚轮平移
                moveOnMouseMove: true, // 鼠标移动能触发数据窗口平移
              },
            ]
          : [],
    };
  };

  // 知识点学习按知识点统计图表
  const getKnowledgePointOptions = () => {
    let PointData: any = [];
    if (currentMode.current == 'finishRateList') {
      knowledgePointData.finishRateList.forEach((el: any) => {
        PointData.push({ name: el.nodeName, value: el.finishRate });
      });
    } else if (currentMode.current == 'masterRateList') {
      knowledgePointData.masterRateList.forEach((el: any) => {
        PointData.push({ name: el.nodeName, value: el.masterRate });
      });
    } else if (currentMode.current == 'questionList') {
      knowledgePointData.questionList.forEach((el: any) => {
        PointData.push({ name: el.nodeName, value: el.questionCount });
      });
    }
    return {
      tooltip: {
        formatter: function(param: any) {
          let str = `<div style="display:flex;align-items:center;">${param.marker}<span style="flex:1;display:flex;justify-content: space-between;">${param.name}<span style="margin-left:20px;">${param.data}%</span></span></div>`;
          return str;
        },
      },
      grid: {
        left: '1%',
        right: '1%',
        bottom: '1%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: PointData.map((el: any) => el.name),
        axisLine: {
          lineStyle: {
            color: '#eee',
          },
        },
        axisLabel: {
          color: '#333',
        },
      },
      yAxis: {
        type: 'value',
        splitLine: {
          lineStyle: {
            color: '#eee',
          },
        },
        axisLabel: {
          color: '#333',
          formatter: '{value}%',
        },
        minInterval: 1,
      },
      series: [
        {
          type: 'bar',
          data: PointData.map((el: any) => el.value),
          itemStyle: {
            color: '#549cff',
            barBorderRadius: [4, 4, 0, 0],
          },
          barWidth: 24,
        },
      ],

      dataZoom:
        PointData?.length > 12
          ? [
              // 有滚动条 平移
              {
                type: 'slider',
                realtime: true,
                start: 0,
                end:
                  PointData?.length > 12
                    ? ((12 / PointData.length) * 100).toFixed(0)
                    : 100, // 初始展示20%
                height: 4,
                fillerColor: 'rgba(17, 100, 210, 0.42)', // 滚动条颜色
                borderColor: 'rgba(17, 100, 210, 0.12)',
                handleSize: 0, // 两边手柄尺寸
                showDetail: false, // 拖拽时是否展示滚动条两侧的文字
                // top: '96%',

                // zoomLock:true, // 是否只平移不缩放
                // moveOnMouseMove:true, //鼠标移动能触发数据窗口平移
                // zoomOnMouseWheel :true, //鼠标移动能触发数据窗口缩放
              },
              {
                type: 'inside', // 支持内部鼠标滚动平移
                start: 0,
                end: 10,
                zoomOnMouseWheel: false, // 关闭滚轮缩放
                moveOnMouseWheel: true, // 开启滚轮平移
                moveOnMouseMove: true, // 鼠标移动能触发数据窗口平移
              },
            ]
          : [],
    };
  };

  useEffect(() => {
    getResourceStudyData();
  }, [resourceStudyParams]);

  //获取资源学习情况图表数据
  const getResourceStudyData = () => {
    statisticsApi.getResourceStudyData({ ...resourceStudyParams }).then(res => {
      // console.log('getResourceStudyData', res);
      if (res.status == 200) {
        let obj = res.data.map((el: any) => ({
          name: el.resourceName,
          value1: el.learningNumber,
          value2: el.completeRate,
        }));
        setResourceStudyData(obj);
      }
    });
  };
  // 知识点学习情况图表
  const knowledgePointOptions = () => {
    statisticsApi
      .getKnowledgePie({
        courseId: courseId,
      })
      .then((res: any) => {
        if (res.data.status == 200) {
          setKnowledgePointData(res.data.data);
          setKnowledgePointParams({
            questionTotal: res.data.data.questionTotal,
            totalFinishRate: res.data.data.totalFinishRate,
            totalMasterRate: res.data.data.totalMasterRate,
          });
        }
      });
  };

  useEffect(() => {
    getHomeworkStatistics();
  }, [homeworkParams]);

  //获取作业完成情况数据
  const getHomeworkStatistics = () => {
    //饼图
    statisticsApi.getHomeworkScore(location.query.id).then(res => {
      console.log('getHomeworkStatistics', res);
      if (res.status == 200) {
        setHomeworkPieData({
          courseSubmissionRate: parseFloat(res.data.courseSubmissionRate) || 0,
          courseSoringRate: parseFloat(res.data.courseSoringRate) || 0,
        });
      }
    });
    //章节统计图
    let { chapterId, sortBy, sortName } = homeworkParams;
    statisticsApi
      .getHomeworkSubmitData({ chapterId, sortBy, sortName }, courseId)
      .then(res => {
        console.log('getHomeworkSubmitData', res);
        if (res.status == 200) {
          setHomeworkSubmitData(res.data || []);
        }
      });
  };

  //提交率
  const getHomeworkPieChart1 = () => {
    let rate = homeworkPieData.courseSubmissionRate.toFixed(1);
    let dataSource = [
      { name: t('提交率'), value: rate },
      { name: t('进行中'), value: 100 - rate },
    ];
    let colors = ['#C692FF', '#EBEDF4'];
    return {
      tooltip: {},
      title: [
        {
          text: rate + '%',
          textStyle: {
            fontSize: 14,
            color: 'black',
          },
          left: 'center',
          top: 'middle'
        },
        {
          text: t('提交率'),
          textStyle: {
            fontSize: 12,
            color: 'black',
          },
          left: 'center',
          top:'74%'
        },
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true,
      },
      series: [
        {
          type: 'pie',
          // center: ['50%', '50%'],
          radius: ['30%', '50%'],
          label: {
            show: false,
          },
          data: dataSource.map((el: any, i: number) => ({
            ...el,
            itemStyle: { color: colors[i] },
            tooltip: { show: i == 0 ? true : false },
            emphasis: { disabled: i == 0 ? false : true },
          })),
        },
      ],
    };
  };

  //得分率
  const getHomeworkPieChart2 = () => {
    let rate = homeworkPieData.courseSoringRate.toFixed(1);
    let dataSource = [
      { name: t('得分率'), value: rate },
      { name: '', value: 100 - rate },
    ];
    let colors = ['#F8CC28', '#EBEDF4'];
    return {
      tooltip: {},
      title: [
        {
          text: rate + '%',
          textStyle: {
            fontSize: 14,
            color: 'black',
          },
          left: 'center',
          top:'middle'
        },
        {
          text: t('得分率'),
          textStyle: {
            fontSize: 12,
            color: 'black',
          },
          left: 'center',
          top:'74%'
        },
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true,
      },
      series: [
        {
          type: 'pie',
          center: ['50%', '50%'],
          radius: ['30%', '50%'],
          label: {
            show: false,
          },
          data: dataSource.map((el: any, i: number) => ({
            ...el,
            itemStyle: { color: colors[i] },
            tooltip: { show: i == 0 ? true : false },
            emphasis: { disabled: i == 0 ? false : true },
          })),
        },
      ],
    };
  };

  const homeworkTableChange = (pagination: any, filters: any, sorter: any) => {
    if (sorter.order == 'ascend') {
      if (sorter.columnKey == 'homeworkSoringRate') {
        setHomeworkSubmitStatistics(
          homeworkSubmitStatistics.sort((prev: any, next: any) => {
            return (
              parseFloat(prev.homeworkSoringRate) -
              parseFloat(next.homeworkSoringRate)
            );
          }),
        );
      }
    } else if (sorter.order == 'descend') {
      if (sorter.columnKey == 'homeworkSoringRate') {
        setHomeworkSubmitStatistics(
          homeworkSubmitStatistics.sort((prev: any, next: any) => {
            return (
              parseFloat(next.homeworkSoringRate) -
              parseFloat(prev.homeworkSoringRate)
            );
          }),
        );
      }
    } else {
      setHomeworkSubmitStatistics(origenHomeworkSubmitStatistics);
    }
  };

  const getLearnSetting = () => {
    getSeting(location.query.id).then((res: any) => {
      if (res.status === 200) {
        setLearnSetting(res.data);
      }
    });
  };
  return (
    <div style={{ height: '100%' }}>
      {showPage == 4 ? (
        <KnowledgeDetail
          knowledgeitem={knowledgeitem}
          onback={() => setShowPage(1)}
        ></KnowledgeDetail>
      ) : showPage === 3 ? (
        <div className="view_box">
          <DetailContent
            detail={showDetail}
            onBack={() => {
              setShowDetail(null);
              setShowPage(1);
            }}
          />
        </div>
      ) : showPage === 2 ? (
        <StudentDetail
          chapters={chapters}
          code={showDetail?.code || location.query?.studentId}
          onBack={() => {
            setShowDetail(null);
            setShowPage(1);
            if (isFromCourseAchieve.current) {
              history.goBack()
            }
            isFromCourseAchieve.current = false
          }}
        />
      ) : (
        <div
          style={{ display: !Boolean(showDetail) ? 'block' : 'none' }}
          className="view_box"
        >
          <div className="top_box box_panel">
            <div className="header">
              <div className="title">
                <span>{t('课程情况')}</span>
              </div>
            </div>
            <div className="item_boxs">
              <div className="view_box">
                <div className="left_box">
                  <IconFont
                    type="iconbianzubeifen1"
                    style={{ color: '#549CFF', fontSize: '50px' }}
                  ></IconFont>
                </div>
                <div className="right_box">
                  <span>{t('参与学习人数')}</span>
                  <span>
                    {heardinfo.participateNumber}
                    <p style={{ display: 'inline-block', fontSize: '16px' }}>
                      {t('人')}
                    </p>
                  </span>
                  {/*  */}
                  <span>
                    {t('章节课程学习完成率')}
                    {Number(heardinfo.completeRate.toFixed(2))}%
                  </span>
                  <span>
                    {t('章节视频学习总时长')}
                    {getlearningTimeTotal(heardinfo.learningTimeTotal)}
                  </span>
                </div>
              </div>
              <div className="view_box">
                <div className="left_box">
                  <IconFont
                    type="iconziyuanku1"
                    style={{ color: '#549CFF', fontSize: '50px' }}
                  ></IconFont>
                </div>
                <div className="right_box">
                  <span>{t('课程章节资源数')}</span>
                  <span>
                    {heardinfo.resources}
                    <p style={{ display: 'inline-block', fontSize: '16px' }}>
                      {t('个')}
                    </p>
                  </span>
                  <span>
                    {t('章节资源学习次数')}
                    {heardinfo.resourceLearningTimes}
                  </span>
                </div>
              </div>
              {showHomework && (
                <div className="view_box">
                  <div className="left_box">
                    <IconFont
                      type="iconjiaocaijiaofu"
                      style={{ color: '#549CFF', fontSize: '50px' }}
                    ></IconFont>
                  </div>
                  <div className="right_box">
                    <span>{t('发布作业数')}</span>
                    <span>
                      {heardinfo.homeworks}
                      <p style={{ display: 'inline-block', fontSize: '16px' }}>
                        {t('个')}
                      </p>
                    </span>
                    <span>
                      {t('作业提交率')}
                      {heardinfo.homeworkCommitRate}%
                    </span>
                  </div>
                </div>
              )}
              <div className="view_box">
                <div className="left_box">
                  <IconFont
                    type="iconwenhao"
                    style={{ color: '#549CFF', fontSize: '50px' }}
                  ></IconFont>
                </div>
                <div className="right_box">
                  <span>{t('学员提问数')}</span>
                  <a
                    href={`#/editcourse/${
                      location.query.type === 'mooc'
                        ? 'mooccourseqa'
                        : 'courseqa'
                    }?id=${location.query.id}&type=${
                      location.query.type
                    }&sm=${location.query.sm ?? 1}`}
                  >
                    {heardinfo.askNumber}
                    <p style={{ display: 'inline-block', fontSize: '16px' }}>
                      {t('次')}
                    </p>
                  </a>
                  <span>
                    {heardinfo.askStudentNumber || 0}/
                    {heardinfo.participateNumber || 0}
                    {t('学员进行了提问')}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="chart_boxs box_panel">
            <div className="header">
              <div className="title">
                <span>{t('章节学习进度分布')}</span>
              </div>
            </div>
            <div style={{ padding: '0 15px' }}>
              <Tabs
                className="study_static_tabs"
                defaultActiveKey="total"
                items={[
                  {
                    key: 'total',
                    label: t('总览'),
                    children: (
                      <div className="row">
                        <PieChart
                          height="260px"
                          dataSource={progressData.complateData}
                        />
                        <BarChart
                          height="260px"
                          dataSource={progressData.progressDistribute}
                        />
                      </div>
                    ),
                  },
                  {
                    key: 'college',
                    label: t('按学院统计'),
                    children: (
                      <div
                        className="row"
                        style={{ transform: 'translateY(-22px)' }}
                      >
                        {progressData.collegeDistribute &&
                        progressData.collegeDistribute.length ? (
                          <StackLineChart
                            height="282px"
                            dataSource={progressData.collegeDistribute}
                          />
                        ) : (
                          <Empty style={{ marginTop: '40px' }} />
                        )}
                      </div>
                    ),
                  },
                  {
                    key: 'person',
                    label: t('按个人统计'),
                    children: (
                      <div
                        className="row"
                        style={{ transform: 'translateY(-22px)' }}
                      >
                        {progressData.personDistribute &&
                        progressData.personDistribute.length ? (
                          <Echart options={getPersonDistributeOptions} />
                        ) : (
                          <Empty style={{ marginTop: '40px' }} />
                        )}
                      </div>
                    ),
                  },
                ]}
              />
            </div>
          </div>

          <div className="chart_boxs box_panel">
            <div className="header">
              <div className="title">
                <span>{t('章节资源学习情况')}</span>
                <div className="right">
                  <Select
                    placeholder={t('全部章')}
                    onChange={e => {
                      setResourceStudyParams({
                        ...resourceStudyParams,
                        chapterName: e,
                      });
                    }}
                    allowClear
                  >
                    {chapters.map((item: any, index: number) => {
                      return (
                        <Option value={item.name} key={index}>
                          {item.name}
                        </Option>
                      );
                    })}
                  </Select>
                  <Select
                    placeholder={t('学习要求')}
                    onChange={e =>
                      setResourceStudyParams({
                        ...resourceStudyParams,
                        necessary: e,
                      })
                    }
                    allowClear
                  >
                    <Option value={0} key="0">
                      {t('选学内容')}
                    </Option>
                    <Option value={1} key="1">
                      {t('必学内容')}
                    </Option>
                  </Select>
                  <Select
                    defaultValue={resourceStudyParams.sortTarget}
                    onChange={(e, op) => {
                      setResourceStudyParams({
                        ...resourceStudyParams,
                        sortTarget: e,
                      });
                    }}
                  >
                    {[
                      { name: t('学习次数'), value: 0 },
                      { name: t('学习人数'), value: 1 },
                      { name: t('学习完成率'), value: 2 },
                      { name: t('下载次数'), value: 3 },
                      { name: t('章节顺序'), value: 4 },
                    ].map((item: any, index: number) => {
                      return (
                        <Option value={item.value} key={index}>
                          {item.name} {<IconFont type="iconjiangxu1" />}
                        </Option>
                      );
                    })}
                  </Select>
                </div>
              </div>
            </div>
            <div className="resource_study_chart_box">
              <div className="tips">
                <div>{t('学生学习次数')}</div>
                <Popover
                  placement="bottomRight"
                  content={t('学习完成率=已完成学习的学生/学生总数')}
                  trigger="hover"
                >
                  <div>
                    {t('学习完成率')}
                    <ExclamationCircleOutlined
                      style={{
                        color: 'var(--primary-color)',
                        marginLeft: '4px',
                      }}
                    />
                  </div>
                </Popover>
              </div>
              {resourceStudyData.length ? (
                <BarLineChart dataSource={resourceStudyData} />
              ) : (
                <Empty />
              )}
            </div>
          </div>
          <div className="box_panel">
            <div className="chart_boxs chart_boxs_homework">
              <div className="header">
                <div className="title">
                  <span>{t('章节作业完成情况')}</span>
                  <div className="right">
                    <Select
                      placeholder={t('全部章')}
                      onChange={e => {
                        setHomeworkParams({
                          ...resourceStudyParams,
                          chapterId: e,
                        });
                      }}
                      allowClear
                    >
                      {chapters.map((item: any, index: number) => {
                        return (
                          <Option value={item.id} key={index}>
                            {item.name}
                          </Option>
                        );
                      })}
                    </Select>
                    <Select
                      defaultValue={homeworkParams.sortName}
                      onChange={(e, op) => {
                        setHomeworkParams({ ...homeworkParams, sortName: e });
                      }}
                    >
                      {[
                        { name: t('章节顺序'), value: t('章节') },
                        { name: t('提交人数'), value: t('提交人数') },
                        { name: t('作业得分率'), value: t('作业得分率') },
                      ].map((item: any, index: number) => {
                        return (
                          <Option value={item.value} key={index}>
                            {item.name} {<IconFont type="iconjiangxu1" />}
                          </Option>
                        );
                      })}
                    </Select>
                  </div>
                </div>
              </div>
              <div className="homeword_complate_chart_box">
                <div className="left">
                  {Object.keys(homeworkPieData).length ? (
                    <>
                      <Echart options={getHomeworkPieChart1} />
                      <Echart options={getHomeworkPieChart2} />
                    </>
                  ) : (
                    <Empty style={{ marginTop: '100px' }} />
                  )}
                </div>
                <div className="right">
                  {homeworkSubmitData.length ? (
                    <>
                      <div className="tips">
                        <div>{t('作业提交人数')}</div>
                        <Popover
                          placement="bottomRight"
                          content={t(
                            '作业得分率=已批改学员得分/已批改作业总分',
                          )}
                          trigger="hover"
                        >
                          <div>
                            {t('作业得分率')}
                            <ExclamationCircleOutlined
                              style={{
                                color: 'var(--primary-color)',
                                marginLeft: '4px',
                              }}
                            />
                          </div>
                        </Popover>
                      </div>
                      <BarLineChart
                        colors={['#C692FF', '#F9D13E']}
                        names={[t('作业得分率'), t('作业提交人数')]}
                        dataSource={homeworkSubmitData.map((el: any) => ({
                          name: el.title,
                          value1: el.stuSubmitNum,
                          value2: parseFloat(el.homeworkSoringRate),
                        }))}
                      />
                    </>
                  ) : (
                    <Empty style={{ marginTop: '100px' }} />
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* 暂时隐藏-学习统计 */}
          <div className="box_panel">
            <div className="chart_boxs chart_boxs_homework">
              <div className="header">
                <div className="title">
                  <span>{t('章节重难点标记情况')}</span>
                  <div className="right">
                    <Select
                      placeholder={t('全部章')}
                      onChange={e => {
                        setStudentDifficultiesSort(pre => ({
                          ...pre,
                          chapterId: e
                        }))
                      }}
                      allowClear
                    >
                      {chapters.map((item: any, index: number) => {
                        return (
                          <Option value={item.id} key={index}>
                            {item.name}
                          </Option>
                        );
                      })}
                    </Select>
                    <Select
                      defaultValue={studentDifficultiesSort.sortTarget}
                      onChange={(e, op) => {
                        setStudentDifficultiesSort(pre => ({
                          ...pre,
                          sortTarget: e
                        }))
                      }}
                    >
                      {[
                        { name: t('章节顺序'), value: 0 },
                        { name: t('相关知识点'), value: 1 },
                      ].map((item: any, index: number) => {
                        return (
                          <Option value={item.value} key={index}>
                            {item.name} {<IconFont type="iconjiangxu1" />}
                          </Option>
                        );
                      })}
                    </Select>
                  </div>
                </div>
              </div>
              <div className="difficulties_chart_box">
                <div className="left difficulties_left">
                  <div className='point_difficulty' style={{background: 'rgba(255,179,65,0.06)'}}>
                    <div className='point_difficulty_title' >
                      <div>标记重点</div>
                      <div style={{color: '#FFB341'}} className='point_difficulty_count'>{difficultiesTotal?.keyPoints || 0}<span className='point_unit'>次</span></div>
                    </div>
                    <div className='diff_icon'><StarFilled style={{fontSize: '44px', color: '#FFDA76'}} /></div>
                  </div>
                  <div className='point_difficulty' style={{background: 'rgba(255,117,65,0.06)'}}>
                    <div className='point_difficulty_title' >
                      <div>标记难点</div>
                      <div style={{color: '#FF7541'}} className='point_difficulty_count'>{difficultiesTotal?.difficultPoints || 0}<span className='point_unit'>次</span></div>
                    </div>
                    <div className='diff_icon'><StarFilled style={{fontSize: '44px', color: '#FFA481'}} /></div>
                  </div>
                </div>
                <div className="right">
                  {studentDifficultiesChart.length ? (
                    <>
                      <div className="tips">
                        <div>{t('标记次数')}</div>
                        <div>
                            {t('相关知识点')}
                          </div>
                      </div>
                      <BarTwoLineChart
                        dataSource={studentDifficultiesChart.map((el: any) => ({
                          name: el?.resourceName,
                          value0: el?.knowledgePoints || 0,
                          value1: el?.keyPoints || 0,
                          value2: el?.difficultPoints || 0
                        }))}
                      />
                    </>
                  ) : (
                    <Empty style={{ marginTop: '100px' }} />
                  )}
                </div>
              </div>
            </div>
          </div>

          { isShowMap.current ? (
            <div className="box_panel">
            <div className="chart_boxs chart_boxs_homework">
              <div className="header">
                <div className="title">
                  <span>{t('知识点完成情况')}</span>
                  <div className="right">
                    <Select
                      defaultValue='finishRateList'
                      onChange={(e, op) => {
                        currentMode.current = e;
                        setKnowledgePointData({
                          ...knowledgePointData,
                          temp: new Date().getTime(),
                        });
                      }}
                    >
                      {
                        [
                          { name: t("按完成率"), value: 'finishRateList' },
                          { name: t("按掌握率"), value: 'masterRateList' },
                          { name: t("按提问数"), value: 'questionList' }
                        ].map((item: any, index: number) => {
                            return <Option value={item.value} key={index}>{item.name} {<IconFont type="iconjiangxu1" />}</Option>;
                          })
                      }
                      </Select>
                    </div>
                  </div>
                  </div>

              <div className="homeword_complate_chart_box">
                <div className="left2">
                    <>
                        <div className="item">
                          <div className="value_box">
                            <span>
                              {Number(knowledgePointParams.questionTotal)}
                            </span>
                          </div>
                          <div className='title'>
                            <span>{t("总提问数")}</span>
                          </div>
                        </div>
                        <div className='item'>
                          <Progress type="circle" strokeWidth={16} format={(percent) => `${percent}%`} percent={Number(knowledgePointParams.totalFinishRate)} />
                          <div className='title'>
                            <span>{t("总完成率")}</span>
                          </div>
                        </div>
                        <div className='item'>
                          <Progress type="circle" strokeWidth={16} format={(percent) => `${percent}%`} percent={Number(knowledgePointParams.totalMasterRate)} />
                          <div className='title'>
                            <span>{t("总掌握率")}</span>
                          </div>
                        </div>
                    </>
                </div>
                <div className="right2">
                  {/*班级课和公开课用散点图*/}
                  {knowledgePointData ? !typeArr.includes(location.query.type) ?  (
                    <Echart options={getKnowledgePointOptions} />
                  ) : <ClassOpenPieChart dataSource={knowledgePointData} leftActiveTab={currentMode.current}  /> : (
                    <Empty style={{ marginTop: '40px' }} />
                  )}
                </div>
              </div>
              </div>
            </div>
          ) : null}

            <div className="box_panel">
              <div className='tabs_box'>
                <Tabs defaultActiveKey="1" activeKey={selectkey} onChange={onChange} destroyInactiveTabPane>
                  <TabPane tab={t("学员学习情况")} key="1">
                    <div className='search_box'>
                      {/* <Search className='search_input' placeholder="请输入学生姓名/学工号" onSearch={(e) => setKeyword(e)} enterButton /> */}

                    <Input.Group compact>
                      <Select defaultValue="" onChange={e => {
                        setRoles(e)
                        getStudentData(true, {isSearchRole: true, role: e})
                      }} getPopupContainer={(triggerNode): any =>triggerNode.parentNode}>
                        <Option value="">{t('全部')}</Option>
                        <Option value="0">{t('学生')}</Option>
                        <Option value="1">{t('老师')}</Option>
                      </Select>
                      <Select
                      placeholder={t('请选择学院')}
                      allowClear
                      style={{ width: 300 }}
                      mode="multiple"
                      // onChange={setCheckColleges}
                      onChange={(value) => {
                        setCheckColleges(value)
                        getStudentData(true, {isSearchCollege: true, collegeList: value})
                      }}
                    >
                      {colleges.map((item: any) => (
                        <Select.Option key={item.code} value={item.name}>
                          {item.name}
                        </Select.Option>
                      ))}
                    </Select>
                      <div className="search" style={{borderRightWidth: '0'}}>
                        <Input
                          autoComplete="off"
                          placeholder={t('请输入学员姓名/学工号')}
                          onChange={(e: any) => setKeyword(e.target.value)}
                          onPressEnter={() => getStudentData(true)}
                          style={{borderRadius: 0}}
                        />
                        <SearchOutlined
                          onClick={() => getStudentData(true)}
                          title={t('搜索')}
                        />
                      </div>
                    </Input.Group>

                    <Popconfirm
                      title={t('确认导出学员学习情况?')}
                      onConfirm={exportLearning}
                      okText={t('导出')}
                      cancelText={t('取消')}
                    >
                      <Button
                        type="primary"
                        icon={<IconFont type="iconexport"></IconFont>}
                      >
                        {t('导出Excel')}
                      </Button>
                    </Popconfirm>
                  </div>
                  <Table
                    size="small"
                    dataSource={dataSource1}
                    loading={loading}
                    rowKey={(record: any) => record.code}
                    columns={columns}
                    pagination={{ ...pagination, size: 'small' }}
                    showSorterTooltip={false}
                    onChange={tablechange}
                  />
                </TabPane>
                <TabPane tab={t('章节资源学习情况')} key="2">
                  <div className="search_box">
                    <div>
                      {/* <Search className='search_input' placeholder="请输入资源名称" onSearch={(e) => setKeyword2(e)} enterButton /> */}
                      <div className="search">
                        <Input
                          autoComplete="off"
                          placeholder={t('请输入资源名称')}
                          onChange={(e: any) => setKeyword2(e.target.value)}
                          onPressEnter={() => getResourceData(true)}
                        />
                        <SearchOutlined
                          onClick={() => getResourceData(true)}
                          title={t('搜索')}
                        />
                      </div>
                      <Select
                        placeholder={t('全部章')}
                        style={{ width: 120, marginLeft: '30px' }}
                        onChange={e => {
                          setChapterName(e);
                          setPagination2({ ...pagination2, current: 1 });
                        }}
                        allowClear
                      >
                        {chapters.map((item: any, index: number) => {
                          return (
                            <Option value={item.name} key={index}>
                              {item.name}
                            </Option>
                          );
                        })}
                      </Select>
                      <Select
                        placeholder={t('全部类型')}
                        style={{ width: 120, marginLeft: '30px' }}
                        onChange={e => {
                          setResourceType(e);
                          setPagination2({ ...pagination2, current: 1 });
                        }}
                        allowClear
                      >
                        {resourceoptions.map((item: any, index: number) => {
                          return (
                            <Option key={index} value={item.value}>
                              {item.label}
                            </Option>
                          );
                        })}
                      </Select>
                    </div>
                    <Popconfirm
                      title={t('确认导出资源学习情况?')}
                      onConfirm={exportresource}
                      okText={t('导出')}
                      cancelText={t('取消')}
                    >
                      <Button
                        type="primary"
                        icon={<IconFont type="iconexport"></IconFont>}
                      >
                        {t('导出Excel')}
                      </Button>
                    </Popconfirm>
                  </div>
                  <Table
                    size="small"
                    dataSource={dataSource2}
                    loading={loading}
                    columns={columns2}
                    rowKey={(record: any) => record.key}
                    pagination={{ ...pagination2, size: 'small' }}
                    showSorterTooltip={false}
                    onChange={tablechange2}
                  />
                </TabPane>
                <TabPane tab={t('作业提交情况')} key="3">
                  <div className="search_box">
                    <div>
                      <div className="search">
                        <Input
                          autoComplete="off"
                          placeholder={t('请输入作业名称')}
                          onChange={(e: any) => setKeyword3(e.target.value)}
                          onPressEnter={() =>
                            setHomeworkStatisticsParams({
                              ...homeworkStatisticsParams,
                              homeworkName: keyword3,
                            })
                          }
                        />
                        <SearchOutlined
                          onClick={() =>
                            setHomeworkStatisticsParams({
                              ...homeworkStatisticsParams,
                              homeworkName: keyword3,
                            })
                          }
                          title={t('搜索')}
                        />
                      </div>
                      <Select
                        placeholder={t('全部章')}
                        style={{ width: 120, marginLeft: '30px' }}
                        onChange={e => {
                          setHomeworkStatisticsParams({
                            ...homeworkStatisticsParams,
                            chapterId: e,
                          });
                        }}
                        allowClear
                      >
                        {chapters.map((item: any, index: number) => {
                          return (
                            <Option value={item.id} key={index}>
                              {item.name}
                            </Option>
                          );
                        })}
                      </Select>
                    </div>
                  </div>
                  <Table
                    size="small"
                    dataSource={homeworkSubmitStatistics}
                    loading={loading}
                    columns={homewordColumns}
                    rowKey={(record: any) => record.key}
                    showSorterTooltip={false}
                    onChange={homeworkTableChange}
                  />
                </TabPane>
                {
                  isShowMap.current ? (
                    <TabPane tab={t('知识点学习情况')} key="4">
                  <Achievementdegree
                    sesdetail={(e: any) => {
                      setShowPage(4);
                      setKnowledgeitem(e);
                    }}
                  ></Achievementdegree>
                </TabPane>
                  ) : null
                }
              </Tabs>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Newstatistics;
