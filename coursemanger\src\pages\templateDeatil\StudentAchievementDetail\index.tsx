import { courseDetail } from '@/api/addCourse';
import statisticsApi from '@/api/statistics';
import { IconFont } from '@/components/iconFont';
import { LeftCircleFilled } from '@ant-design/icons';
import { Button, Empty, Select, Space, Tooltip } from 'antd';
import React, { FC, useEffect, useMemo, useRef, useState } from 'react';
import { history, useLocation } from 'umi';
import DetailTable from './components/DetailTable';
import GraduationRequirement from './components/GraduationReqirement';
import PieChartTwo from './components/PieChartTwo';
import RadarChart from './components/RadarChart';
import TablePart from './components/TablePart';
import TargetChart from './components/TargetChart';
import './index.less';

const StudentAchievementDetail: FC<any> = () => {
  const handleBack = () => {
    history.goBack();
  };
  let location: any = useLocation();
  const {
    id,
    sm,
    studentId,
    // courseCode,
    // name,
    // joinType = '',
  } = location.query;
  const [courseTip, setCcourseTip] = useState<any>([]);
  const [tableData, setTableData] = useState<any[]>([]);
  const [detailStates, setDetailStates] = useState<any[]>([]);

  // 列表和图的切换
  const [modeSwitch, setModeSwitch] = useState(true);
  //选中的目标
  const [selectTarget, setSelectTarget] = useState('');

  // 初始化目标列表的展示状态
  const handleInitStatus = (list: any[]) => {
    const statusList = Array(list.length).fill(false);
    statusList[0] = true;
    setDetailStates(statusList);
  };

  //#region 获取课程详细信息
  const [courseInfo, setCourseInfo] = useState<any>();
  const handleGetCourseCode = (course_id: string) => {
    statisticsApi
      .getCourseCode({
        contentId: course_id,
        courseSemester: 1,
      })
      .then((res: any) => {
        if (res.status === 200) {
          setCourseInfo(res.data?.data);
        }
      });
  };
  useEffect(() => {
    if (!id) return;
    handleGetCourseCode(id);
  }, [id]);
  //#endregion

  //#region 查询课程类型
  const [isMapCourse, setIsMapCourse] = useState(false);
  const [courseType, setCourseType] = useState<any>();
  useEffect(() => {
    if (!id) return;
    courseDetail(id).then((res: any) => {
      if (res?.status == 200) {
        setCourseType(res?.data?.entityData?.courseType);
        setIsMapCourse(res?.data?.entityData?.courseType == 4);
      }
    });
  }, [id]);
  //#endregion

  //根据课程获取目标
  const getGoalByCourseId = (courseId: string, courseCode?: string) => {
    statisticsApi
      .getCourseDetailByGoalId(courseId, { courseCode: courseCode })
      .then((res: any) => {
        if (res?.status === 200) {
          const tableList =
            res?.data?.data?.map((item: any, index: any) => {
              return {
                title: item.name,
                subTitle: item.desc,
                id: item.id,
              };
            }) || [];
          setTableData(tableList);
          setSelectTarget(tableList[0]?.id);
          getAchievementDetailByTargetId(tableList[0]?.id, () =>
            handleInitStatus(tableList),
          );
        }
      });
  };

  useEffect(() => {
    if (!id || !courseInfo?.entityData?.couresSyllabusCode) return;
    getGoalByCourseId(id, courseInfo?.entityData?.couresSyllabusCode || '');
  }, [id, courseInfo?.entityData?.couresSyllabusCode]);

  //目标列表
  const [achievementList, setAchievementList] = useState<any[]>([]);
  //获取目标（雷达图数据， 列表数据）
  const getTargetData = (params: any) => {
    statisticsApi.getGoalAchievementData(params).then((res: any) => {
      if (res?.status === 200) {
        setAchievementList(res.data?.data || []);
        const tips: any[] =
          res?.data?.data?.map((item: any) => ({
            name: item?.targetName,
            max: 100,
          })) || [];
        setCcourseTip(tips);
      }
    });
  };

  //获取数据
  useEffect(() => {
    if (!id || !studentId) return;

    const queryParam: any = {};
    queryParam.courseId = id;
    queryParam.userCode = studentId;
    queryParam.param = {
      type: 1,
      courseId: id,
      courseCode: courseInfo?.entityData?.couresSyllabusCode || '',
    };
    getTargetData(queryParam);
  }, [id, courseInfo?.entityData?.couresSyllabusCode, studentId]);

  //雷达图配置
  const getRadar = () => {
    return {
      title: {
        text: '课程各指标点达成度',
        left: '0px',
        top: '0px',
      },
      tooltip: {
        trigger: 'item',
        formatter: function () {
          let tooltipContent = '<div><div class="tooltipTitle">数据</div><div class="custom-tooltip">';
          let tooltipLeft = '<div class="tooltip_left">'
          let tooltipRight = '<div>'
          achievementList.forEach((item: any) => {
            tooltipLeft += `
              <div class="row_left">
                  <div>
                    <span class="dot">•</span>
                    <span class="name">${item.targetName}</span>
                  </div>
                  <span class="value">${item.targetAchievementDegree ? item.targetAchievementDegree.toFixed(1) : 0}</span>
              </div>
            `;
          });
          achievementList.forEach((item: any) => {
            tooltipRight += `<div class="rank"> 排名: ${item.achievementRank || 0}</div>`;
          });
          tooltipRight += '</div>';
          tooltipLeft += '</div>';
          tooltipContent += tooltipLeft + tooltipRight + '</div>' + '</div>';
          return tooltipContent;
        }
      },
      radar: {
        // shape: 'circle',
        triggerEvent: true,
        nameGap: 10,
        center: ['50%', '55%'],
        radius: ['0%', '68%'],
        axisName: {
          color: '#76A2D2',
          backgroundColor: '#E8F3FE',
          lineHeight: 27,
          borderRadius: [5, 5, 5, 5],
          padding: [0, 5, 0, 5],
        },
        splitArea: {
          areaStyle: {
            color: ['#fff', '#fff', '#fff', '#fff'],
            shadowColor: 'rgba(0, 100, 0, 0.3)',
          },
        },
        indicator: courseTip,
      },
      series: [
        {
          type: 'radar',
          symbolSize: 0,
          data: [
            {
              // value: [88.2, 78.6, 88.4, 0, 0, 0],
              value: achievementList?.map(
                item =>
                  (Number(item?.targetAchievementDegree) > 100
                    ? 100
                    : item?.targetAchievementDegree) || 0,
              ),
              name: '数据',
              itemStyle: {
                normal: {
                  color: 'rgba(5, 128, 242, 0.8)',
                },
              },
              areaStyle: {
                normal: {
                  color: '#B8D6FF',
                },
              },
            },
          ],
        },
      ],
    };
  };

  // 根据目标列表的点击事件更新目标的展示状态
  const handleChangeGoals = (index: number) => {
    let newStates = [...detailStates];
    newStates.map((item, i) => {
      if (i == index) {
        if (newStates[i] == true) {
          newStates[i] = false;
        } else {
          newStates[i] = true;
        }
      } else {
        newStates[i] = false;
      }
    });
    setDetailStates(newStates);
  };
  // 存储多个目标的数据，在多次切换时减少接口请求
  const goalsAchievementMap = useRef<Map<any, any>>(new Map());
  // 要展示的目标 的数据
  const [showData, setShowData] = useState<any[]>([]);
  // 目标列表的点击事件
  const toggleDetail = (index: number, targetId: string) => {
    if (goalsAchievementMap.current?.has(targetId)) {
      setShowData(goalsAchievementMap.current.get(targetId));
      handleChangeGoals(index);
    } else {
      getAchievementDetailByTargetId(targetId, () => handleChangeGoals(index));
    }
  };

  //根据雷达图的点击更新右边的柱状图
  const handleChangeIndexByChart = (index: number) => {
    setDetailStates(pre => {
      return pre.map((item, i) => {
        if (i == index) {
          if (pre[i] == true) {
            return false;
          } else {
            return true;
          }
        }
        return false;
      });
    });
  };
  //雷达图的点击事件
  const radarChartClick = (params: any) => {
    tableData.forEach((item: any, index) => {
      if (item.title == params) {
        if (goalsAchievementMap.current?.has(item.id)) {
          setShowData(goalsAchievementMap.current.get(item.id));
          handleChangeIndexByChart(index);
        } else {
          getAchievementDetailByTargetId(item.id, () =>
            handleChangeIndexByChart(index),
          );
        }
      }
    });
  };

  //数据加载状态
  const [targetDetailLoading, setTargetDetailLoading] = useState(false);
  // 根据目标id获取具体信息
  const getAchievementDetailByTargetId = (targetId: string, callBack: any) => {
    if (
      !id ||
      !targetId ||
      !studentId ||
      !courseInfo?.entityData?.couresSyllabusCode
    )
      return;
    setTargetDetailLoading(true);
    callBack();
    statisticsApi
      .getGoalAchievemetDetailById(studentId, id, {
        courseCode: courseInfo?.entityData?.couresSyllabusCode || '',
        targetId,
        type: 1,
      })
      .then((res: any) => {
        if (res?.status === 200) {
          goalsAchievementMap.current?.set(targetId, res?.data?.data || []);
          setShowData(res?.data?.data || []);
        }
      })
      .finally(() => {
        setTargetDetailLoading(false);
      });
  };

  const getBar = () => {
    return {
      color: ['#3398DB'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '4%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: showData.map(item => item?.entity),
          axisTick: {
            show: false,
            alignWithLabel: true,
          },
          axisLabel: {
            interval: 0,
            formatter: function(value: any) {
              let newName = '';
              const valueLenth = value.length;
              const provideNumber = 3; //每行能显示的字的个数；
              // const row = Math.ceil(valueLenth / provideNumber)//换行的话，需要显示几行，向上取整
              const row = 2; //最多显示两行；
              if (valueLenth > provideNumber) {
                for (let p = 0; p < row; p++) {
                  let tempstr = '';
                  const start = p * provideNumber;
                  const end = start + provideNumber;
                  if (p === row - 1) {
                    //最后一次不换行
                    // tempstr = value.substring(start,valueLenth);
                    tempstr =
                      value.substring(start, 6) +
                      `${valueLenth > 6 ? '..' : ''}`;
                  } else {
                    tempstr = value.substring(start, end) + '\n';
                  }
                  newName += tempstr;
                }
              } else {
                newName = value;
              }
              return newName;
            },
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
        },
      ],
      series: [
        {
          name: '掌握率',
          type: 'bar',
          barWidth: '15px',
          itemStyle: {
            borderRadius: [15, 15, 0, 0],
            color: 'rgba(104,164,251,1)',
          },
          data: showData.map(item => item?.masterRate),
        },
        {
          name: '完成率',
          type: 'bar',
          barWidth: '15px',
          itemStyle: {
            borderRadius: [15, 15, 0, 0],
            color: 'rgba(133,193,233,1)', // 第二根柱状图的颜色
          },
          // 第二根柱状图的数据
          data: showData.map(item => item?.finishRate),
        },
      ],
    };
  };
  // 雷达图  防止页面重绘
  const overviewRadar = useMemo(() => {
    return courseTip.length > 0 ? (
      <RadarChart options={getRadar()} onClick={radarChartClick} />
    ) : (
      <Empty style={{ marginTop: 100 }} />
    );
  }, [courseTip, tableData]);

  const [activeTab, setActiveTab] = useState('knowledge'); // 默认选中知识点学习情况

  const handleClick = (tab: any) => {
    setActiveTab(tab);
  };

  const [heardinfo, setHeardinfo] = useState<any>({
    userName: '',
    userCode: '',
    college: '',
    totalFinishRate: '0.0',
    totalMasterRate: '0.0',
    questionsNumber: 0,
  });
  const getData = () => {
    statisticsApi
      .getLearningPersonal({
        courseId: id,
        code: studentId,
        courseSemester: sm || 1,
      })
      .then((res: any) => {
        if (res?.status == 200) {
          setHeardinfo(res?.data?.data || {});
        }
      });
  };

  useEffect(() => {
    if (!studentId || !id) return;
    getData();
  }, [studentId, id]);

  function transformData(originalData: any[]) {
    let transformedData: { name: any; value: any; data: any[] }[] = [];

    originalData.forEach(chapter => {
      let chapterObj: { name: string; value: string; data: any[] } = {
        name: chapter.chapterNodeName,
        value: chapter.knowledgeNodeNumber,
        data: [],
      };

      chapter.knowledgeLearningInfos.forEach(
        (knowledge: {
          nodeName: any;
          masterRate: string;
          finishNotExamine: any;
          masterNotExamine: any;
        }) => {
          let knowledgeObj = {
            name: knowledge.nodeName,
            value: parseFloat(knowledge.masterRate) || 0,
            finishNotExamine: knowledge.finishNotExamine,
            masterNotExamine: knowledge.masterNotExamine,
          };
          chapterObj.data.push(knowledgeObj);
        },
      );

      transformedData.push(chapterObj);
    });

    return transformedData;
  }
  function transformData2(originalData: any[]) {
    let transformedData: { name: any; value: any; data: any[] }[] = [];
    originalData.forEach(chapter => {
      let chapterObj: { name: string; value: string; data: any[] } = {
        name: chapter.chapterNodeName,
        value: chapter.knowledgeNodeNumber,
        data: [],
      };
      chapter.knowledgeLearningInfos.forEach(
        (knowledge: {
          finishNotExamine: any;
          masterNotExamine: any;
          nodeName: any;
          finishRate: string;
        }) => {
          let knowledgeObj = {
            name: knowledge.nodeName,
            value: parseFloat(knowledge.finishRate) || 0,
            finishNotExamine: knowledge.finishNotExamine,
            masterNotExamine: knowledge.masterNotExamine,
          };
          chapterObj.data.push(knowledgeObj);
        },
      );

      transformedData.push(chapterObj);
    });

    return transformedData;
  }
  const [knowledgePointData, setKnowledgePointData] = useState<any>(null);
  const [pieDate, setPieDate] = useState<any>([]);
  const knowledgePointOptions = () => {
    statisticsApi
      .getKnowledgeCompletion({
        courseId: id,
        courseSemester: sm || 1,
        userCode: studentId,
        courseCode: courseInfo?.entityData?.couresSyllabusCode,
      })
      .then((res: any) => {
        if (res?.status == 200) {
          //完成率假数据

          let data: any = [];
          data = res?.data?.data?.chapterKnowledgeInfos || [];
          setPieDate(data);
          setKnowledgePointData(getShowData(data));
        }
      });
  };

  //演示数据生成
  const getShowData = (originalData: any) => {
    let transformedData: { name: any; value: any; data: any[] }[] = [];
    originalData?.forEach((chapter: any) => {
      let chapterObj = {
        name: chapter.chapterNodeName,
        value: chapter.knowledgeNodeNumber,
        data: [],
      };
      const arr = chapter.knowledgeLearningInfos.map(
        (item: any, index: number) => {
          return {
            name: item.nodeName,
            // value: getRandomNumber(31, 70),
            value: parseFloat(item?.targetAchievementDegree) || 0,
            finishNotExamine: item?.finishNotExamine || 0,
            masterNotExamine: item?.masterNotExamine,
          };
        },
      );
      chapterObj.data = arr;

      transformedData.push(chapterObj);
    });

    return transformedData;
  };

  const [leftActiveTab, setLeftActiveTab] = useState(1);
  const leftClick = (tab: any) => {
    setLeftActiveTab(tab);
    if (tab == 1) {
      setKnowledgePointData(getShowData(pieDate));
    } else if (tab == 3) {
      setKnowledgePointData(transformData(pieDate));
    } else {
      setKnowledgePointData(transformData2(pieDate));
    }
  };
  useEffect(() => {
    if (!courseInfo?.entityData?.couresSyllabusCode || !id || !studentId)
      return;
    knowledgePointOptions();
  }, [id, courseInfo?.entityData?.couresSyllabusCode, studentId]);

  // 课程目标完成度-饼状图
  const completionOfCourseObjectives = useMemo(() => {
    return !!knowledgePointData ? (
      <PieChartTwo
        dataSource={knowledgePointData}
        leftActiveTab={leftActiveTab}
      />
    ) : (
      <Empty style={{ marginTop: '100px' }} />
    );
  }, [knowledgePointData]);
  //#region 目标达成情况
  const [targetDetail, setTargetDetail] = useState<any[]>([]);
  const getTargetAchievementDetail = (
    course_id: string,
    course_code: string,
    target_id: string,
    user_code: string,
  ) => {
    statisticsApi
      .queryTargetDetailById({
        courseId: course_id,
        courseCode: course_code,
        targetId: target_id,
        userCode: user_code,
      })
      .then((res: any) => {
        if (res?.status == 200) {
          setTargetDetail(res?.data?.data || []);
        }
      });
  };
  useEffect(() => {
    if (
      !id ||
      !courseInfo?.entityData?.couresSyllabusCode ||
      !selectTarget ||
      !studentId
    )
      return;
    getTargetAchievementDetail(
      id,
      courseInfo?.entityData?.couresSyllabusCode,
      selectTarget,
      studentId,
    );
  }, [selectTarget, id, courseInfo?.entityData?.couresSyllabusCode, studentId]);
  //#endregion
  return (
    <div className="student-achievement-container">
      <div className="back-wrp">
        <Button
          type="text"
          icon={<LeftCircleFilled style={{ color: '#CBCBCB' }} />}
          onClick={handleBack}
        >
          返回上一级
        </Button>
      </div>
      <div className="detail_content">
        {/* {joinType === 'myStudy' ? null : <h2 className="top_content" onClick={handleToCourse}>{name}</h2>} */}
        <div className="main_content">
          <div className="header_content">
            <div className="block active_bg">
              <div className="student_info">
                <div className="avatar">
                  <img
                    src={require('@/assets/imgs/EditCourse/avatar.png')}
                  ></img>
                </div>
                <div className="detail">
                  <span className="name">{heardinfo.userName} </span>
                  <span className="code"> ({heardinfo.userCode})</span>
                  <div className="college">学院: {heardinfo.college}</div>
                </div>
              </div>
            </div>
            <div className="block">
              <div className="title">
                <img
                  src={require('@/assets/imgs/EditCourse/student_info_1.png')}
                ></img>
                总完成率
              </div>
              <div className="dept">
                <span>{heardinfo.totalFinishRate} </span> %
              </div>
            </div>
            <div className="block">
              <div className="title">
                <img
                  src={require('@/assets/imgs/EditCourse/student_info_2.png')}
                ></img>
                总掌握率
              </div>
              <div className="dept">
                <span>{heardinfo.totalMasterRate} </span> %
              </div>
            </div>
            <div className="block">
              <div className="title">
                <img
                  src={require('@/assets/imgs/EditCourse/student_info_5.png')}
                ></img>
                提问次数
              </div>
              <div className="dept">
                <span>{heardinfo.questionsNumber} </span> 次
              </div>
            </div>
          </div>
          <GraduationRequirement userCode={studentId} courseInfo={courseInfo} />
          <div className="radar">
            <div className="top_left">{overviewRadar}</div>
            <div className="top_right">
              {// type === 'map' &&
              tableData.map((item: any, index: number) => {
                return (
                  <div className="top_right_container" key={index}>
                    <div className="header">
                      <span className="text">
                        <span
                          // className={
                          //   detailStates[index] ? 'text1 active' : 'text1'
                          // }
                          className='text1'
                        >
                          {item.title}
                        </span>
                        <span className="text2" title={item.subTitle}>{item.subTitle}</span>
                      </span>
                      <div
                        className="end"
                        // onClick={() => toggleDetail(index, item.id)}
                      >
                        {/* {detailStates[index] ? '收起' : '展开'}
                        {detailStates[index] ? (
                          <img
                            src={require('@/assets/imgs/EditCourse/up.png')}
                          ></img>
                        ) : (
                          <img
                            src={require('@/assets/imgs/EditCourse/down.png')}
                          ></img>
                        )} */}
                      </div>
                    </div>
                    {/* <div
                      className={
                        detailStates[index] ? 'detail' : 'detail hidden'
                      }
                    >
                      <Echart
                        options={getBar()}
                        showLoading={targetDetailLoading && detailStates[index]}
                      />
                    </div> */}
                  </div>
                );
              })}
            </div>
          </div>
          <div className="center">
            <Select
              style={{ width: 120 }}
              value={selectTarget}
              options={tableData?.map((item: any) => ({
                label: item?.title,
                value: item?.id,
              }))}
              onChange={setSelectTarget}
            ></Select>
            达成情况
            <div className="mode_switch_wrapper">
              <div onClick={() => setModeSwitch(true)} className="mode_switch">
                <Tooltip title={'图例模式'}>
                  <IconFont
                    type="iconhebingxingzhuangfuzhi2"
                    className={modeSwitch ? 'active' : ''}
                  />
                </Tooltip>
              </div>
              <div onClick={() => setModeSwitch(false)} className="mode_switch">
                <Tooltip title={'列表模式'}>
                  <IconFont
                    type="iconliebiao"
                    className={modeSwitch ? '' : 'active'}
                  />
                </Tooltip>
              </div>
            </div>
            {targetDetail?.length > 0 ? (
              <>
                {modeSwitch ? (
                  <TargetChart data={targetDetail} />
                ) : (
                  <TablePart data={targetDetail} />
                )}
              </>
            ) : (
              <Empty style={{ marginTop: 40 }} />
            )}
          </div>
          {isMapCourse && (
            <div className="center">
              <Space size={'large'} className="antd_space">
                <span
                  className={`btn ${leftActiveTab === 1 ? 'active' : ''}`}
                  onClick={() => leftClick(1)}
                >
                  课程目标达成度
                </span>
                <span
                  className={`btn ${leftActiveTab === 2 ? 'active' : ''}`}
                  onClick={() => leftClick(2)}
                >
                  完成率
                </span>
                <span
                  className={`btn ${leftActiveTab === 3 ? 'active' : ''}`}
                  onClick={() => leftClick(3)}
                >
                  掌握率
                </span>
              </Space>
              <div className="Pie_content">{completionOfCourseObjectives}</div>
            </div>
          )}
          <div className="bottom_table">
            <Space size={'large'} className="antd_space">
              <span
                className={`btn ${activeTab === 'knowledge' ? 'active' : ''}`}
                onClick={() => handleClick('knowledge')}
              >
                知识点学习情况
              </span>
            </Space>
            <DetailTable
              type={activeTab}
              mapId=""
              courseId={id}
              sm={sm || 1}
              code={studentId}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentAchievementDetail;
