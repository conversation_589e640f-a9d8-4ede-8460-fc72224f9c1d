.rdrawer{
    width: 100%;
    // height: calc(100vh - 440px);
    height: 100%;
    overflow: auto;

    .entity-preview {
        #video-dom {
            position: relative;
            min-height: 308px !important;
            background-color: #000;
        }
    }

    .drawer_view{
        width: 100%;
        height: 100%;
        min-height: 100%;
        padding-right: 10px;
        padding-bottom: 100px;

        .content_item{
            width: 85%;
            overflow: hidden;
            display: flex;
            align-items: center;
            

            // 超出一行显示省略号
            .content_name{
                width: 85%;
                overflow:hidden;
                text-overflow:ellipsis;//文本溢出显示省略号
                white-space:nowrap;//文本不会换行
            }
        }
        .topic_container{
            .flex-sb{
                flex-wrap: wrap;
            }
            .info_msg{
                display: flex;
                flex-wrap: wrap;
                .ant-space-item-split{
                    display: none;
                }

                .ant-space{
                    width: 100%;
                }
            }
        }

        .rate_box{
            width: 100%;
            height: 85px;
            display: flex;
            align-items: center;

            .left_view{
                width: 50%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;

                .name_box{
                    width: 70%;
                    height: 100%;
                    display: flex;
                    align-items: center;

                    .redio_box{
                        width: 10px;
                        height: 10px;
                        background: #549CFF;
                        opacity: 0.5;
                        border-radius: 50%;
                        margin-right: 10px;
                    }

                    .rate_title{
                        font-size: 15px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #000;
                    }

                }

                .rate_value{
                    font-size: 20px;
                    font-family: Arial-BoldMT, Arial;
                    font-weight: 550;
                    color: #549CFF;
                    
                }
            }
        }
        .target_list {
            display: flex;
            flex-direction: column;
            gap: 5px;
            margin-bottom: 10px;
            .target_item {
                .ant-typography {
                    margin-bottom: 0;
                }
                display: flex;
                .text {
                    padding: 0 10px;
                    height: 22px;
                    text-align: center;
                    line-height: 22px;
                    border-radius: 13px;
                    margin-right: 10px;
                    font-size: 14px;
                    color: var(--primary-color);
                    background: var(--second-color);
                }

                .desc {
                    flex: 1;
                    width: 0;
                    font-weight: 400;
                    font-size: 14px;
                    color: #333;
                }
            }
        }
        .options_view{
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }


        .detail_view{
            width: 100%;
            height: calc(100vh - 400px);
            overflow: auto;
            border:1px solid #eee;
            margin-top:10px;

            .courseqa_container{
                padding:  0 15px 0 15px !important;
            }
        }

        .title{
            margin-top: 10px;
            margin-bottom: 10px;
            width: 100%;
            line-height:30px;
            display: flex;
            align-items: flex-start;
            justify-content: space-between;

            .span1{
                font-size: 16px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 600;
                color: #000000;
                cursor: pointer;
                max-width: 65%;
                user-select:text;
                // height: 100%;
                // overflow: hidden;
                // text-overflow:ellipsis;
                // white-space: nowrap;
            }
        }

        .detail{

            width: 100%;
            height: auto;

            p{
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #000000;
                line-height: 21px;
            }
        }

        .link_overflow{
            // 超出一行显示省略号
            width: 100%;
            overflow:hidden;
            text-overflow:ellipsis;//文本溢出显示省略号
            white-space:nowrap;//文本不会换行
        }

        .video_view{
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;

            .redio_view{
                width: 7px;
                height: 7px;
                border-radius: 50%;
                background-color: var(--primary-color);
                margin-top: 15px;
                margin-bottom: 15px;
            }

            .title_span{
                font-size: 16px;
                margin-left: 6px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #000000;
                line-height: 20px;
            }
        }


        .excel_view{
            width: 100%;
            height: 40px;
            border:1px solid #eeeeee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
            cursor: pointer;

            .left_view{
                width: 80%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: flex-start;

                span{
                    overflow:hidden;
                    text-overflow:ellipsis;//文本溢出显示省略号
                    white-space:nowrap;//文本不会换行
                }
            }


        }

        .ant-collapse > .ant-collapse-item{
            background-color: #fff;
        }

        .divider_dashed{
            margin-top: 20px;
            width: 100%;
            height: 0px;
            border-bottom: 1px dashed #E6E6E6;
        }

        .topic_box{
          position: relative;
          width: 100%;
          height: auto;

          &:hover{
            .remove_topic{
              display: block;
            }
          }

          .remove_topic{
            position: absolute;
            right: 10px;
            top: 20px;
            cursor: pointer;
            z-index: 2;
            display: none;
          }

        }

        .number_view{
            width: 100%;
            height: auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;

            .left_view{
                display: flex;
                align-items: center;
            }

            .center_view{
                display: flex;
                align-items: center;
                
            }
        }

        .options_box{
            width: 100%;
            display: flex;
            justify-content: flex-end;
            margin-bottom: 10px;
        }

        .other_view{
            width: 100%;
            height: auto;
            display: flex;
            align-items: center;
            flex-wrap: wrap;

            .redio_view{
                width: 7px;
                height: 7px;
                border-radius: 50%;
                background-color: var(--primary-color);
                margin-top: 20px;
                margin-bottom: 15px;
            }

            .other_title{
                font-size: 16px;
                font-family: Helvetica;
                color: #000000;
                line-height: 20px;
                margin-left: 6px;
                margin-top: 13.5px;
                margin-bottom: 8.5px;
                margin-right: 10px;
            }


            .other_item{
                padding-left: 10px;
                padding-right: 10px;
                border: 1px solid var(--primary-color);
                border-radius: 16px;
                margin-left: 10px;
                height: 28px;
                display: flex;
                align-items: center;
                margin-top: 9.5px;
                margin-bottom: 4.5px;
                cursor: pointer;

                .span_name{
                    font-size: 14px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: var(--primary-color);
                    line-height: 20px;
                    max-width: 160px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    -o-text-overflow:ellipsis;
                }

                .icon_delete{
                    color: var(--primary-color);
                    margin-left: 5px;
                }
            }
            .article{
                background: rgba(42,92,252,0.05);
                border-radius: 4px;
                font-weight: 400;
                font-size: 14px;
                color: #2A2A2A;
                padding: 13px;
                text-align: justify;
            }

            .file_item{
                height: 35px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #FDF4F3;
                border-radius: 4px;
                padding-left: 10px;
                padding-right: 10px;
                cursor: pointer;
                margin-right: 15px;

                .span1{
                    margin-left: 10px;
                    font-size: 14px;
                    font-family: Helvetica;
                    color: #DF4A43;
                    line-height: 17px;
                }
            }
        }
        .recommend_list{
                display: flex;
                align-items: center;
                padding: 12px 0;
                border-bottom: 1px dashed #EDEDED;
                .dot {
                    width: 8px;
                    height: 8px;
                    background: #C0C0C0;
                    border-radius: 50%;
                    margin-right: 6px;
                }
        }
    }
}

 .xiti_tools{
        width: 98%;
        height: calc(100vh - 240px); // 减去固定的头部/底部高度
    }

@media screen and (max-width: 768px) {
    .document_modal_wrap{
        width: 100%!important;
        top: 20px;
    }
}
