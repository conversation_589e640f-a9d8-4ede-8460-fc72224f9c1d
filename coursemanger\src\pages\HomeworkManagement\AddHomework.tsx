import React, { FC, useEffect, useState, useRef } from 'react';
import { useLocation, useSelector } from 'umi';
import { LeftOutlined, QuestionCircleOutlined, LoadingOutlined, ExclamationCircleOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { Button, Form, Input, DatePicker, Checkbox, Radio, Space, Cascader, Tooltip, message, Spin, Modal, TreeSelect, InputNumber, Select } from "antd";
import { disabledDateTime, disabledDate, dealScoreData } from "./utils/tools";
import type { SortableContainerProps, SortEnd } from 'react-sortable-hoc';
import { arrayMoveImmutable } from 'array-move';
import { addHomeworkColumns } from "./utils/columns";
import TopicSelectModal from "./components/TopicSelectModal";
import CustomgroupingModal from "./components/CustomgroupingModal";
import PointSetting from "./components/PointSetting";
import PreviewHomeworkModal from './components/PreviewHomeworkModal';
import ExamSelectModal from "./components/ExamSelectModal";
import PreviewTopicModal from './components/PreviewTopicModal';
import moment from "moment";
import { updateHomework, createHomework, getHomeworkDetail, addHomeworkTopic, updateTopic, sortTopic, updateScore, deleteTopic, batchDeleteTopic, updateTopicOnPub, checkIsSubmitTopic } from "@/api/homework";
import chapterApis from "@/api/chapter";
import courseTemplate from "@/api/courseTemplate";
import "./AddHomework.less";
import $ from "jquery";
import { IDataType } from "./utils/type";
import ImageModal from './components/ImageModal';
import TopicTypeSelectModal from "./components/TopicTypeSelectModal";
import TopicItem from './components/TopicItem';
import { SortableContainer, SortableElement, SortableHandle, arrayMove } from 'react-sortable-hoc';
import { IconFont } from '@/components/iconFont';
import { getSensitiveWord } from '@/utils';
import useLocale from '@/hooks/useLocale';
import { createMicroHomework, updateMicroHomework } from '@/api/micromajor';

const { confirm } = Modal;


interface IAddHomework {
  handleBack: (data?: any) => void;
  chapterItem: any;
  entityType: any;
  chapterAllTreeData: any[];
  chapter?: any;
  copyFrom?: string;
  // canEdit: boolean;
  from?: 'micro'; // 微专业作业处理
}
const DragHandle = SortableHandle(() => {
  const { t } = useLocale();
  return <div className='drag-btn'><IconFont type="iconsortnew" /><div className='drag-text'>{t("排序")}</div></div>;
});
const ListItem = SortableElement<any>((props: any) => {
  const { data, serial, canEdit, tempState, onChange } = props;
  return <div className='drag-topic-item'>
    {canEdit && <DragHandle />}
    <div className='drag-topic-item-content'>
      {canEdit && <>
        <Checkbox value={data.id}></Checkbox>
        {tempState && <div className='btn-wrp'>
          <IconFont type="icondelete" onClick={() => onChange("delete", data)} />
          <IconFont type="iconshuaxin" onClick={() => onChange("replace", data)} />
          <IconFont type="iconedit" onClick={() => onChange("edit", data)} />
        </div>}
      </>}
      <TopicItem index={serial} data={data} />
    </div>
  </div>;
});
const SortableList = SortableContainer<any>(({ list, canEdit, onChange, tempState }: any) => {
  return <div className="questions-wrp">
    {list.map((item: any, index: number) =>
      <ListItem data={item} index={index} serial={index} key={item.id} canEdit={canEdit} onChange={onChange} tempState={tempState} />)}
  </div>;
});
const checkboxOptions = [
  { label: '允许超时提交', value: 'timeOut', title: "勾选则未提交的学生在截止时间过后仍能提交作业" },
  { label: '截止日期后公开题目解析', value: 'openParse', title: "不勾选则学生无法查看题目解析" },
  { label: '截止日期前允许重复提交', value: 'resubmit', title: "不勾选则只允许学生提交一次" },
  { label: '截止日期前允许学生查看得分', value: 'checkScore', title: "勾选则在作业中只有客观题时，学生提交后可立即查看到自己的得分，但看不到详细正误情况" }
];

const radioOptions = [
  { label: '随机分组', value: 1 },
  { label: '自定义分组', value: 2 },
]


// const SortableItem = SortableElement((props: React.HTMLAttributes<HTMLTableRowElement>) => (
//   <tr {...props} />
// ));
// const SortableBody = SortableContainer((props: React.HTMLAttributes<HTMLTableSectionElement>) => (
//   <tbody {...props} />
// ));

const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

const AddHomework: FC<IAddHomework> = ({ chapter, chapterAllTreeData, entityType, handleBack, chapterItem, copyFrom, from }) => {
  const { t } = useLocale();
  const [form] = Form.useForm();
  const location: any = useLocation();
  const [dataSource, setDataSource] = useState([]);
  const [numberOfGroups, setnumberOfGroups] = useState<number>(0); // 分组数量
  const [numberOfPeopleGrouped, setnumberOfPeopleGrouped] = useState<number>(0); // 分组人数

  const [topicVisible, setTopicVisible] = useState<boolean>(false);

  const homeworkId = useRef<string>("");
  const [previewPaperVisible, setPreviewPaperVisible] = useState<boolean>(false);

  const [chapterTreeDataSource, setChapterTreeDataSource] = useState<any>([]);

  const [homeworkDoneType, setHomeworkDoneType] = useState<string>("1"); // 完成方式
  const [topicItem, setTopicItem] = useState<any>({});
  const [previewTopicVisible, setPreviewTopicVisible] = useState<boolean>(false);
  const [topicSelectType, setTopicSelectType] = useState<string>("");

  const [examVisible, setExamVisible] = useState<boolean>(false);

  const [selectConnectChapter, setSelectConnectChapter] = useState<any>(''); // 作业绑定所属章节
  const [openNoticeHover, setOpenNoticeHover] = useState<boolean>(false); // 鼠标是否悬停在章节菜单框上

  const [loading, setLoading] = useState<boolean>(false);
  const [totalScore, setTotalScore] = useState<number>(0);
  const [homeworkStatus, setHomeworkStatus] = useState<string>("pre");
  const stateRef = useRef<string>();
  const [canEdit, setCanEdit] = useState<boolean>(false);
  const [curImage, setCurImage] = useState<string>("");
  const [imageVisible, setImageVisible] = useState<boolean>(false);

  const [showAddTopic, setShowAddTopic] = useState<boolean>(false);
  const [topicTypeSelectVisible, setTopicTypeSelectVisible] = useState<boolean>(false);

  const [topicType, setTopicType] = useState<number>(0);
  const scoreRef = useRef<any>();
  const [checkedList, setCheckedList] = useState<any>([]);

  const [tempState, setTempState] = useState<boolean>(false); // 用于判定下发状态时，是否能够修改作业题目
  const tempEditData = useRef<any>(null);
  const [tempEditStatus, setTempEditStatus] = useState<string>(""); // 用于判断当前属于哪种编辑作业状态
  const [isSomeOneReply, setIsSomeOneReply] = useState<boolean>(false); // 用于判断是否有人提交作业
  const [groupOptions, setGroupOptions] = useState<any>(1); // 分组方式
  const [CustomSelectVisible, setCustomSelectVisible] = useState<boolean>(false);
  const [CustomVisible, setCustomVisible] = useState<boolean>(false); // 自定义分组弹窗
  const [PubState, setPubState] = useState('');
  const courseDetail = useSelector<Models.Store, any>(
    (state) => state.moocCourse.courseDetail);

  useEffect(() => {
    $(document).on("click", ".homework-list-container .special-dom img", (e: any) => {
      setCurImage(e.target.src);
      setImageVisible(true);
    });
    window.addEventListener("message", handleMessage);
    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, []);
  useEffect(() => {
    if (courseDetail?.entityData?.publishStatus != 1) {
      setCanEdit(true);
    } else if (homeworkStatus != "save" && homeworkStatus != "pub") {
      setCanEdit(true);
    } else {
      setCanEdit(false);
    }
  }, [courseDetail, homeworkStatus]);

  useEffect(() => {
    if (chapterAllTreeData && chapterAllTreeData.length > 0) {
      const list = filterTreeData(chapterAllTreeData);
      setChapterTreeDataSource(list);
    }
  }, [chapterAllTreeData])

  // 去掉树结构数据中不是章节的数据节点
  function filterTreeData(data: any[]) {
    // 如果是数组类型，逐一处理每个节点
    return data.filter(node => {
      // 如果节点是符合条件的，继续递归处理它的子节点
      if (node.resourseType === 'chapter' || !node.resourseType) {
        // 递归处理子节点（假设子节点存储在 children 数组里）
        if (node.children && node.children.length > 0) {
          node.children = filterTreeData(node.children);
        }
        return true; // 保留当前节点
      }
      return false; // 排除不符合条件的节点
    });
  }


  const handleMessage = (e: any) => {
    if (e.data && typeof e.data === "string") {
      const { action, data } = JSON.parse(e.data);
      if (action === "addTopicBack") {
        setShowAddTopic(false);
      }
      if (!data) return;
      if (["addTopicBack", "addTopic"].includes(action)) {
        setTimeout(() => {
          handleAddTopicConfirm([data], true);
        }, 200);
      }
    }
  };
  // const canEdit = true;
  const pageType = useRef<'resource' | 'template'>(location.pathname.includes("tempatedetail") ? 'template' : 'resource');
  const templateEdit = useRef<boolean>(pageType.current === "resource" || pageType.current === "template" && location.query.type === "edit");

  const handlePreviewTopic = (data: any) => {
    setTopicItem(data);
    setPreviewTopicVisible(true);
  };
  const handleTransTopic = (data: any) => {
    setTopicSelectType("radio");
    setTopicItem(data);
    setTopicVisible(true);
  };
  const handleDeleteTopic = (id?: string) => {
    if (checkedList.length === 0 && !id) {
      message.warning(t("请选择要删除的题目！"));
      return;
    }
    confirm({
      icon: <ExclamationCircleOutlined />,
      content: t("确定要删除这些题目？"),
      onOk() {
        batchDeleteTopic(pageType.current, id ? [id] : checkedList).then((res) => {
          if (res.status === 200) {
            message.success(t("删除成功"));
            handleGetHomeworkDetail(homeworkId.current);
          } else {
            message.error(t("删除失败"));
          }
        });
      }
    });

  };

  // 更改章节树结构
  // 将homeworkId作业移动到 parentId 章节下
  // const changeChapterStruct = (homeWorkId: string, parentId: string) => {
  //   chapter.forEach((item: any) => {
  //     if (item.id === homeWorkId) {

  //     }

  //   })
  // }

  /**
   * 获取item的类型
   *
   * @param {*} item
   * @return {*}
   */
  const getItemType = (item: any, level: number) => {
    // debugger
    if (item.resourseType === 'homework') {
      return 'homework';
    } else if (item.resourseType && item.resourseType !== 'chapter') {
      return 'resource';
    } else if (level === 1) {
      return 'chapter';
    } else {
      return 'section';
    }
  };

  /**
   * 递归：原始chapter --> treeData
   *
   * @param {any[]} data
   * @return {*}
   */
  const formatChapter = (
    data: any[],
    level: number,
    orders: number[],
    pName?: string,
  ) => {
    let thisLevel = level + 1; // 记录当前层级
    let treeData: any[] = []; // 处理后的treeData
    let order = 0; // 记录资源数量，资源不增加小节编号
    let drafts: any[] = []; // 统计草稿章节
    let toPublish: any[] = []; // 统计待发布章节
    let published: any[] = []; // 统计已发布章节
    let chapters: any[] = []; // 统计章
    let sections: any[] = []; // 统计节
    let resources: any[] = []; // 统计资源

    data.forEach((item, index) => {
      let type = getItemType(item, thisLevel); // 判断type给item添加类名
      let curItem = {
        ...item,
        key: item.id,
        level: thisLevel,
        labelTitle: `${[...orders, index - order + 1].join('.')} ${item.name}`,
        className: 'type-' + type,
        title: item.name,
        orders: [...orders, index - order + 1],
        pName,
      };
      // 资源类型统计
      if (
        type === 'resource' ||
        type === 'homework' ||
        type === 'hyperlink' ||
        type === 'case' ||
        type === "material"
      ) {
        ++order;
        resources.push(curItem);
      } else if (type === 'chapter') {
        chapters.push(curItem);
      } else {
        sections.push(curItem);
      }
      //章节状态统计
      if (type !== 'resource') {
        if (item.status == '-1') {
          // 草稿
          drafts.push(curItem);
        } else if (item.status == '1') {
          // 已发布
          published.push(curItem);
        } else {
          // 待发布
          toPublish.push(curItem);
        }
      }
      if ((item.children && item.children.length <= 0) || !item.children) {
        // 无children，返回
        treeData.push(curItem);
      } else {
        // 有children，递归
        treeData.push({
          ...curItem,
          children: formatChapter(
            item.children,
            thisLevel,
            [...orders, index - order + 1],
            item.name,
          ),
        });
      }
    });
    return treeData;
  };

  function moveElementToParent(chapters: any[], homeworkId: string, parentId: string) {
    let homeworkNode: any = null;
    let homeworkParentNode: any = null;
    let parentNode: any = null;

    // 递归查找目标元素(homeworkId)及其父节点
    function findNodeAndParent(node: any, parent: any = null) {
      if (node.id === homeworkId) {
        homeworkNode = node;
        homeworkParentNode = parent;
        return;
      }

      if (node.children) {
        for (let child of node.children) {
          findNodeAndParent(child, node);
          if (homeworkNode) return; // 如果找到了目标节点，停止递归
        }
      }
    }

    // 递归查找目标父节点(parentId)
    function findParentNode(node: any) {
      if (node.id === parentId) {
        parentNode = node;
        return true;
      }

      if (node.children) {
        for (let child of node.children) {
          if (findParentNode(child)) {
            return true; // 如果找到了目标父节点，停止递归
          }
        }
      }
      return false;
    }

    // 处理每一个chapter
    for (let chapter of chapters) {
      // 查找目标元素及其父节点
      findNodeAndParent(chapter);

      // 查找目标父节点
      if (findParentNode(chapter) && homeworkNode && homeworkParentNode) {
        // 从目标父节点的 children 中删除目标节点
        homeworkParentNode.children = homeworkParentNode.children.filter((child: any) => child.id !== homeworkId);

        // 确保目标父节点有 children 属性
        parentNode.children = parentNode.children || [];

        // 将目标节点添加到目标父节点的 children 中
        parentNode.children.push(homeworkNode);
      }
    }

    // 返回更新后的 chapters 数据
    return chapters;
  }

  // const columns = addHomeworkColumns(true, homeworkStatus === "pub" || homeworkStatus === "save", handlePreviewTopic, handleTransTopic, handleDeleteTopic);

  useEffect(() => {

    console.info({ chapterItem })
    if (!chapterItem?.className || chapterItem?.className === "new-homework" || chapterItem?.className === "type-section" || chapterItem?.className === "type-chapter") {
      const length = chapterItem?.order?.split(".")?.length;
      let chapterId = "";
      let sectionId = "";
      if (length === 1) {
        chapterId = chapterItem.id;
        sectionId = chapterItem.id;
      } else if (length === 2) {
        chapterId = chapterItem.parentId;
        sectionId = chapterItem.id;
      }
      form.setFieldsValue({ sectionId: sectionId });
      form.setFieldsValue({ howIsDone: '1' }); // 默认按个人完成
      if (location.pathname === '/editcourse/moochomework') {
        createMicroHomework({
          courseType: 3,
          resourceType: 'micro',
          chapterId, sectionId,
          courseId: location.query.id,
          courseStatus: pageType.current === "resource" ? courseDetail.entityData.publishStatus : null
        }).then((res: any) => {
          homeworkId.current = res.data.homeworkId;
          form.setFieldsValue({ multiScoreMode: "strict" });
        });
      }
      form.setFieldsValue({ teamType: 1 });
      if (from === 'micro') {
        createMicroHomework({
          resourceType: 'micro',
          chapterId, sectionId,
          courseId: location.query.id,
          courseStatus: pageType.current === "resource" ? courseDetail.entityData.publishStatus : null
        }).then((res: any) => {
          homeworkId.current = res.data.homeworkId;
          form.setFieldsValue({ multiScoreMode: "strict" });
        });
      } else {
        !chapterItem?.changeChapterItemValue && createHomework(pageType.current, {
          resourceType: pageType.current === "resource" ? location.query.type : null,
          chapterId, sectionId,
          courseId: location.query.id,
          courseStatus: pageType.current === "resource" ? courseDetail.entityData.publishStatus : null
        }).then((res: any) => {
          if (res.status === 200) {
            homeworkId.current = res.data.homeworkId;
            form.setFieldsValue({ multiScoreMode: "strict" });
          }
        });
      }
    } else if (chapterItem?.className === "type-homework") {
      homeworkId.current = chapterItem.describe;
      handleGetHomeworkDetail(chapterItem.describe, true);
    }
    console.log(chapterItem, 'chapterItem');
  }, []);

  const handleGetHomeworkDetail = (id: string, isFirst: boolean = false) => {
    setLoading(true);
    getHomeworkDetail(pageType.current, id, chapterItem.parentId, location.query.id).then((res: any) => {
      if (res.status === 200) {
        if (isFirst) {
          const checkItem = [];
          if (res.data.resubmit) checkItem.push("resubmit");
          if (res.data.openParse) checkItem.push("openParse");
          if (res.data.checkScore) checkItem.push("checkScore");
          if (res.data.timeOut) checkItem.push("timeOut");
          let formData: any = {
            title: res.data.title,
            multiScoreMode: res.data.multiScoreMode,
            teamNums: res.data.teamNums ? res.data.teamNums : 0,
            howIsDone: res.data.howIsDone + '',
            sectionId: res.data.sectionId || res.data.chapterId,
            teamType: groupOptions,
          };
          if (pageType.current === 'resource') {
            formData = { ...formData, checkItem };
            if (res.data.closeTime) {
              formData = { ...formData, closeTime: moment(res.data.closeTime) };
            }
          }
          form.setFieldsValue(formData);
          homeworkId.current = res.data.id;
        }
        setIsSomeOneReply(res?.data?.isSomeOneReply || false);
        setTotalScore(res.data.totalScore);
        setHomeworkStatus(res.data.state || "pre");
        setDataSource(res.data.questions.map((item: any) => ({
          ...(item.question ?? {}),
          topicId: item.question?.id,
          ...item
        })));
        // 仅当不是checkbox类型时设置分组选项
        if (topicSelectType !== 'checkbox') {
          form.setFieldsValue({ teamType: res?.data.teamType || 1 }); // 默认按个人完成
          setGroupOptions(res?.data.teamType || 1);
          setnumberOfGroups(res?.data.numberOfGroups ?? 0);
          setnumberOfPeopleGrouped(res?.data.numberOfPeopleGrouped ?? 0);
        }
        // console.log(res?.data?.state,'state');
        setPubState(res?.data?.state)

      } else {
        message.error(t("获取作业详情失败"));
        homeworkId.current = "";
      }
    }).finally(() => {
      setLoading(false);
    });
  };



  const onSortEnd = ({ oldIndex, newIndex }: SortEnd) => {
    if (oldIndex !== newIndex) {
      const newData = arrayMoveImmutable(dataSource.slice(), oldIndex, newIndex).filter(
        (el: IDataType) => !!el);

      console.log('Sorted items: ', newData);
      let sorts: any = {};
      newData.forEach((item: any, index: number) => {
        sorts[item.id] = index;
      });
      sortTopic(pageType.current, { homeworkId: homeworkId.current, sorts }).then((res) => {
        if (res.status === 200) {
          handleGetHomeworkDetail(homeworkId.current);
        }
      });

    }
  };

  // const DraggableBodyRow: React.FC<any> = ({ className, style, ...restProps }) => {
  //   // function findIndex base on Table rowKey props and should always be checkScore right array index
  //   const index = dataSource.findIndex((x: any) => x.id === restProps['data-row-key']);
  //   return <SortableItem index={index} {...restProps} />;
  // };

  // const DraggableContainer = (props: SortableContainerProps) => (
  //   <SortableBody
  //     useDragHandle
  //     disableAutoscroll
  //     helperClass="row-dragging"
  //     onSortEnd={onSortEnd}
  //     {...props}
  //   />
  // );

  // 获取信息
  const findSecectChapterInfo = (data: any[], sectionId: string) => {
    for (const node of data) {
      if (node.id === sectionId) {
        return { ...node, changeChapterItemValue: true, className: chapterItem?.className }; // 找到匹配的节点，直接返回 id
      }
      if (node.children && node.children.length > 0) {
        const result: any = findSecectChapterInfo(node.children, sectionId); // 递归查找子节点
        if (result) {
          return result; // 一旦在子节点中找到，立即返回 id
        }
      }
    }
    return null; // 如果没有找到，返回 null
  }

  // 获取章Id值
  const findSectionId = (data: any[], sectionId: string) => {
    for (const node of data) {
      if (node.id === sectionId) {
        return node.parentId; // 找到匹配的节点，直接返回 id
      }
      if (node.children && node.children.length > 0) {
        const result: any = findSectionId(node.children, sectionId); // 递归查找子节点
        if (result) {
          return result; // 一旦在子节点中找到，立即返回 id
        }
      }
    }
    return null; // 如果没有找到，返回 null
  }

  // 根据节点id，获取title
  const findSectionTitle = (data: any[], sectionId: string) => {
    for (const node of data) {
      if (node.id === sectionId) {
        return node.labelTitle; // 找到匹配的节点，直接返回 id
      }
      if (node.children && node.children.length > 0) {
        const result: any = findSectionTitle(node.children, sectionId); // 递归查找子节点
        if (result) {
          return result; // 一旦在子节点中找到，立即返回 id
        }
      }
    }
    return null; // 如果没有找到，返回 null
  }


  const onFinish = (values: any) => {
    setLoading(true);
    const scoreObj = dealScoreData(scoreRef.current?.scoreData ?? []);
    if (Object.keys(scoreObj).length === 0) {
      message.warning(t("未添加题目！"));
      setLoading(false);
      return;
    }
    const isScoreEmpty = Object.keys(scoreObj).some((key: string) => !Number(scoreObj[key]));
    if (isScoreEmpty) {
      message.warning(t("有题目没有设置分数！"));
      setLoading(false);
      return;
    }
    if (Object.keys(scoreObj).some(key => !/^\d+(\.\d{1,1})?$/.test(scoreObj[key]))) {
      message.error(t("输入的分数必须为数字，且最多为一位小数"));
      setLoading(false);
      return;
    }
    getSensitiveWord(values.title, t("作业标题"), () => {
      let isAdd = false;
      // const length = chapterItem.order.split(".").length;
      // if (length === 1) {
      // } else if (length === 2) {
      //   if (chapterItem.className === "type-section") {
      //   } else {
      //     isAdd = false;
      //   }
      // } else {
      //   isAdd = false;
      // }

      // 章节className: new-homework和type-homework的区别在于
      // new-homework是在作业处新建作业；新建完成后不需要调用更新章节信息接口
      // edit-homework是在编辑作业；
      // type-section 是在章节内容处新建作业；新建完成需要调用 addCourse 接口将章节信息更新
      // type-homework是在章节内容处编辑作业；编辑了章节信息需要调用 updateAny 接口更新章节信息
      if (chapterItem.className === "type-section" || chapterItem.className === "type-chapter" || chapterItem.connectChapter === 'noConnect') {
        isAdd = true;
      }

      let params = {
        chapterId: findSectionId(chapterTreeDataSource, values.sectionId) || values.sectionId,
        courseId: location.query.id,
        id: homeworkId.current,
        multiScoreMode: values.multiScoreMode,
        howIsDone: values.howIsDone,
        teamNums: values.teamNums ? values.teamNums : 0,
        list: chapter,
        sectionId: findSectionId(chapterTreeDataSource, values.sectionId) ? values.sectionId : '',
        chapterName: findSectionTitle(chapterTreeDataSource, findSectionId(chapterTreeDataSource, values.sectionId) || values.sectionId),
        sectionName: findSectionId(chapterTreeDataSource, values.sectionId) ? findSectionTitle(chapterTreeDataSource, values.sectionId) : '',
        title: values.title,
        teamType: groupOptions,
      };
      if (pageType.current === 'resource') {
        const noTemplateObj = {
          state: stateRef.current,
          fromCopy: copyFrom ?? "",
          homeWorkOrder: findSecectChapterInfo(chapterTreeDataSource, values.sectionId)?.order,
          closeTime: values.closeTime.valueOf(),
          openParse: values.checkItem?.includes("openParse"),
          checkScore: values.checkItem?.includes("checkScore"),
          timeOut: values.checkItem?.includes("timeOut"),
          resourceType: location.query.type == 'microMajor' ? 'micro' : location.query.type,
          resubmit: values.checkItem?.includes("resubmit")
          // stuNum: studentNum,
        };
        params = { ...params, ...noTemplateObj };
      }
      if (homeworkStatus !== "pub" && homeworkStatus !== "save") {
        handlePointConfirm(scoreRef.current?.scoreData ?? []);
      }
      if (from == 'micro') {
        updateMicroHomework(params).then(res => {
          if (res.status === 200) {
            message.success(t("更新成功"));
            handleBack(chapterItem);
          }
        }).catch(() => {
          setLoading(false);
        });
      } else {
        updateHomework(pageType.current, params).then((res: any) => {
          if (res.status === 200) {
            return true;
          } else {
            message.error(res.message);
            throw new Error();
          }
        }).then((success) => {
          const obj = pageType.current === 'resource' ? chapterApis : courseTemplate;
          if (isAdd && success) {
            if (values.sectionId) {
              obj.addCourse({
                contentId: location.query.id,
                name: values.title,
                parentId: values.sectionId,
                resourceType: "homework",
                resourseId: homeworkId.current,
                resourceAttributes: "homework",
                homeworkType: stateRef.current === "draft" ? 0 : 1
              }).then((res) => {
                if (res.status === 200) {
                  handleBack(chapterItem);
                  message.success(t("新建成功"));
                } else {
                  message.error(res.message);
                }
              }).finally(() => {
                setTimeout(() => {
                  setLoading(false);
                }, 1000);
              });
            } else {
              handleBack(chapterItem);
              setLoading(false);
            }

          } else if (success) {
            // 作业在章节挂载中的信息
            const homeworkChapterparams: Chapter.IupdateParam = {
              contentId: location.query.id,
              metadata: [
                {
                  guid_: chapterItem.guid,
                  name: values.title,
                  identification: chapterItem.id,
                  parent_id: chapterItem.parentId,
                  resourse_type: "homework",
                  describe: chapterItem.describe,
                  homeworkType: stateRef.current === "draft" ? 0 : 1
                }]

            };

            values.sectionId && chapterItem.className === 'type-homework' && obj.updateAny(homeworkChapterparams);


            // console.info(homeworkId.current, chapterItem.id)
            // 更新作业在章节中的挂载（drag）
            const params = {
              courseId: location.query.id,
              list: formatChapter(moveElementToParent(chapter, chapterItem.id, values.sectionId), 0, []),
              parentId: values.sectionId,
              sourceId: chapterItem.id,
            };
            // 变更编辑：作业章节节点挂载(仅章节内容/作业中作业变更章节 调用) 1、章节变更  2、作业编辑
            console.info('执行拖拽-----', chapterItem.parentId !== values.sectionId, chapterItem);

            if (chapterItem.parentId !== values.sectionId && chapterItem.className === 'type-homework') {
              obj.updataLearnTree(params).then((res) => {
                if (res.status === 200) {
                  handleBack(chapterItem);
                  message.success(t("更新成功"));
                } else {
                  message.error(res.message);
                }
              }).finally(() => {
                setTimeout(() => {
                  setLoading(false);
                }, 1000);
              });
            } else {
              handleBack(chapterItem);
              setLoading(false);
            }
          }
        }).catch(() => {
          setLoading(false);
        });
      }
      return true;
    }, () => { setLoading(false); });

  };
  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };
  const handleAddTopic = () => {
    setTopicSelectType("checkbox");
    setTopicVisible(true);
  };
  const handleAddTopicConfirm = (data: any, isAdd?: boolean) => {
    console.log(homeworkId, 'homeworkIdhomeworkId');

    if (tempEditData.current) {
      const params = {
        homeworkId: homeworkId.current,
        // ids: data.map((item: any) => item.id),
        oldAndNewIdVOS: [{
          newId: data?.[0]?.id,
          oldId: tempEditData.current.question?.id
        }],
        modifyTheMode: true
      }
      updateTopicOnPub(params).then((res: any) => {
        if (res.status === 200) {
          message.success(t("替换成功"));
          handleGetHomeworkDetail(homeworkId.current);
          setTopicVisible(false);
          tempEditData.current = null;
        } else {
          message.error(t("替换失败"));
        }
      });
    } else if (topicSelectType === 'checkbox' || isAdd) {
      const params = {
        homeworkId: homeworkId.current,
        ids: data.map((item: any) => item.id),
        sourceBy: "question",
        modifyTheMode: tempState
      };
      addHomeworkTopic(pageType.current, params).then((res: any) => {
        if (res.status === 200) {
          message.success(t("添加成功"));
          handleGetHomeworkDetail(homeworkId.current);
          setTopicVisible(false);
        } else {
          message.error(t("添加失败"));
        }
      });
    } else {
      updateTopic(pageType.current, topicItem.id, { sourceId: data[0].id, modifyTheMode: tempState }).then((res) => {
        if (res.status === 200) {
          message.success(t("更新成功"));
          handleGetHomeworkDetail(homeworkId.current);
          setTopicVisible(false);
        } else {
          message.error(t("更新失败"));
        }
      });
    }
  };

  // const handlePointSetting = () => {
  //   setPointVisible(true);
  // };
  const handlePointConfirm = (data: any) => {
    const scoreObj = dealScoreData(data);
    const isScoreEmpty = Object.keys(scoreObj).some((key: string) => !Number(scoreObj[key]));
    if (isScoreEmpty) {
      message.warning(t("有题目没有设置分数！"));
      return;
    }
    if (Object.keys(scoreObj).some(key => !/^\d+(\.\d{1,1})?$/.test(scoreObj[key]))) {
      message.error(t("输入的分数必须为数字，且最多为一位小数"));
      return;
    }
    const params = {
      homeworkId: homeworkId.current,
      scores: dealScoreData(data)
    };
    updateScore(pageType.current, params).then((res) => {
      if (res.status === 200) {
        handleGetHomeworkDetail(homeworkId.current);
        message.success(t("更新分数成功"));
      } else {
        message.error(t("更新分数失败"));
      }
    });


  };
  const handleExamSelectConfirm = (data: any) => {
    console.info(data);
    const params = {
      homeworkId: homeworkId.current,
      ids: data.map((item: any) => item.id),
      sourceBy: "paper"
    };
    addHomeworkTopic(pageType.current, params).then((res: any) => {
      if (res.status === 200) {
        message.success(t("添加成功"));
        handleGetHomeworkDetail(homeworkId.current);
        setExamVisible(false);
      } else {
        message.error(t("添加失败"));
      }
    });
  };

  const handleAddTomgrouping = (data: any) => {
    setnumberOfGroups(data?.length - 1)
    setnumberOfPeopleGrouped(data[0].teamNums)
  }

  const handleSave = (state: 0 | 1) => {
    if (state === 0) {
      stateRef.current = "draft";
    } else if (state === 1) {
      if (from === 'micro') {
        stateRef.current = "pub"; // 微专业作业的发布
      } else {
        // stateRef.current = chapterItem.status == 1 ? "pub" : "save";
        stateRef.current = "pub";
      }
    }
    // if (chapterItem.status == 1 && state === 1 && dataSource.some((item: any) => !item.score)) {
    //   message.warning("有题目没有设置分值！");
    //   return;
    // }
    form.submit();
  };
  const onTopicChange = (status: string, cur: any) => {
    if (status === "edit") {
      tempEditData.current = cur;
      setShowAddTopic(true);
    } else if (status === "delete") {
      handleDeleteTopic(cur.id);
    } else if (status === "replace") {
      setTopicSelectType("radio");
      setTopicVisible(true);
      tempEditData.current = cur;
    }
    setTempEditStatus(status);
  }

  const confirmTopic = () => {
    checkIsSubmitTopic(homeworkId.current).then((res: any) => {
      if (res.status === 200) {
        setTempState(true);
        res.data && message.warning("题目修改后，已作答学生需要重新答题！");
      }
    })
  }

  const checkTopic = () => {
    if (isSomeOneReply) { // 有学生提交作业时 要进行提示
      Modal.info({
        title: t("提示"),
        content: t("当前已有学生提交作业，修改题目后，已提交的作业将会清空，是否确定？"),
        okCancel: true,
        okText: t("确定"),
        cancelText: t("取消"),
        onOk: confirmTopic,
      })
    } else {
      confirmTopic();
    }
  }

  return <div style={{ height: "100%" }}>
    {showAddTopic ? <iframe src={`/exam/#/topic/manage?opt_type=${!tempEditStatus || tempEditStatus === "replace" ? "new" : `copy&detail=${tempEditData.current?.question?.id}`}${!tempEditStatus || tempEditStatus === "replace" ? `&type=${topicType}` : ""}&from=out`} height="100%" width="100%" /> :
      <Spin indicator={antIcon} spinning={loading}>
        <div className='add-homework-container'>
          <div className="header-container">
            <div className="back-btn" onClick={() => handleBack(chapterItem)}><LeftOutlined />{t("返回")}</div>
            {
              pageType.current === "resource" && homeworkStatus != "pub" && homeworkStatus !== "save" ?
                <div>
                  <Button ghost type='primary' disabled={loading} onClick={() => handleSave(0)}>{t("暂存")}</Button>
                  <Button type='primary' disabled={loading} onClick={() => handleSave(1)}>{t("下发")}</Button>
                </div> : templateEdit.current ? <div>
                  {!tempState && <Button type="primary" ghost onClick={checkTopic}>{t("修改题目")}</Button>}
                  <Button type="primary" disabled={loading} onClick={() => handleSave(1)}>{t("保存")}</Button>
                </div> : null}

          </div>
          <div className="content-container">
            <div className='title'>{t("作业信息")}</div>
            <Form
              form={form}
              onFinish={onFinish}
              onFinishFailed={onFinishFailed}
              autoComplete="off">

              <Form.Item
                label={t("作业名称")}
                name="title"
                rules={[{ required: true, message: t("请输入作业名称") }]}>

                <Input disabled={!templateEdit.current} placeholder={t("请输入作业名称")} showCount maxLength={99} style={{ width: '480px' }} />
              </Form.Item>
              {pageType.current === 'resource' ? <div style={{ display: 'flex' }}>
                <Form.Item
                  label={t("截止提交时间")}
                  name="closeTime"
                  rules={[{ required: true, message: t("请选择截止提交时间") }]}>

                  <DatePicker
                    format="YYYY-MM-DD HH:mm"
                    // disabledDate={disabledDate}
                    // disabledTime={disabledDateTime}

                    showTime={{ defaultValue: moment('00:00:00', 'HH:mm:ss') }} />

                </Form.Item>

                <Form.Item
                  name="checkItem"
                  className='checkbox-item'
                  rules={[{ required: false }]}>

                  <Checkbox.Group>
                    {checkboxOptions.map((item) =>
                      <>
                        <Tooltip title={t(item.title)}>
                          <Checkbox value={item.value}>{t(item.label)}</Checkbox>

                          {/* <span><QuestionCircleOutlined /></span> */}
                        </Tooltip>
                      </>)}
                  </Checkbox.Group>
                </Form.Item>
              </div> : ""}

              <Form.Item
                label={t("多选题得分设置")}
                name="multiScoreMode"
                rules={[{ required: true, message: t("请选择多选题得分设置") }]}>

                <Radio.Group disabled={!canEdit || !templateEdit.current}>
                  <Radio value={"strict"}>{t("全部正确得分")}</Radio>
                  <Radio value={"relax"}>{t("部分正确得分")}</Radio>
                </Radio.Group>
              </Form.Item>
              {entityType !== 'template' && (
                <Form.Item
                  label={t("作业完成方式")} required>

                  <Form.Item
                    noStyle
                    name="howIsDone"
                    rules={[{ required: true, message: t("请选择作业完成方式") }]}>

                    <Radio.Group disabled={!canEdit || !templateEdit.current} onChange={(e: any) => { setHomeworkDoneType(e.target.value) }}>
                      <Radio value={"1"}>{t("按个人完成")}</Radio>
                      <Radio value={"2"}>{t("按小组完成")}</Radio>
                    </Radio.Group>
                  </Form.Item>
                  {form.getFieldValue('howIsDone') === '2' && <div style={{ display: 'inline-block', marginLeft: 20 }}>
                    <Form.Item
                      noStyle
                      name="teamType"
                      rules={[{ required: true, message: t("请选择作业完成方式") }]}>

                      <Select
                        value={groupOptions}
                        style={{ width: 120 }}
                        options={radioOptions}
                        onChange={(value) => { setGroupOptions(value) }}
                      />
                    </Form.Item>
                    {
                      groupOptions == 1 && <span style={{ paddingLeft: '20px' }} >
                        随机分组，每小组
                        <Form.Item
                          noStyle
                          name="teamNums"
                          colon={false}
                          rules={[{ required: true, message: t("请输入小组人数") }]}>
                          <InputNumber min={1} max={100} style={{ width: 60, marginLeft: 8 }} step={1} />
                        </Form.Item>
                        人
                      </span>
                    }
                    {
                      groupOptions == 2 && <span style={{ paddingLeft: '20px' }} >
                        当前已将学生分为{numberOfGroups} 组,还有{numberOfPeopleGrouped} 个学生未分组。
                        <Button type="primary" style={{ marginLeft: 10 }} onClick={() => { setCustomVisible(true) }}>
                          管理分组
                        </Button>
                      </span>
                    }
                  </div>}

                </Form.Item>
              )}


              <Form.Item
                label={t("作业所属章节")}
              >
                <Tooltip open={openNoticeHover && !form.getFieldValue('sectionId')} title='无所属章节的作业无法在章节内容中显示'>
                  <span onMouseOver={() => setOpenNoticeHover(true)} onMouseLeave={() => setOpenNoticeHover(false)}>
                    <Form.Item
                      label=''
                      name="sectionId"
                      noStyle
                    >
                      <TreeSelect
                        style={{ width: 190 }}
                        dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                        treeData={chapterTreeDataSource}
                        allowClear
                        onChange={(value) => setSelectConnectChapter(value)}
                        fieldNames={{ label: 'labelTitle', value: 'id', children: 'children' }}
                        placeholder="请选择作业所属章节"
                        treeDefaultExpandAll
                      />
                    </Form.Item>
                  </span>
                </Tooltip>


              </Form.Item>
            </Form>
            <div className="bottom-wrp">
              <div className="homework-list-container">
                <div className="btn-container">
                  <div className="left">
                    <div className='title'>{t("作业题目")}</div>
                  </div>
                  <div className="right">
                    {/* 总分：{totalScore} */}

                    {/* <Button type="primary" onClick={() => setPreviewPaperVisible(true)}>预览</Button> */}
                    {((homeworkStatus === "pub" || homeworkStatus === "save") && !tempState) ? <Tooltip title={t("已下发的作业不能再增加或修改题目")}>
                      <Space>
                        <Button disabled type='primary' ghost>{t("添加题目")}</Button>
                        <Button disabled type='primary' ghost>{t("添加试卷")}</Button>
                        <Button disabled danger>{t("删除题目")}</Button>
                        {/* <Button disabled>设置分值</Button> */}
                      </Space>
                    </Tooltip> : templateEdit.current ? <Space>
                      <Button onClick={handleAddTopic} type='primary' ghost>{t("添加题目")}</Button>
                      <Button onClick={() => setExamVisible(true)} type='primary' ghost>{t("添加试卷")}</Button>
                      {/* <Button onClick={handlePointSetting}>设置分值</Button> */}
                      <Button onClick={() => handleDeleteTopic()} danger>{t("删除题目")}</Button>
                    </Space> : null}
                  </div>
                </div>
                <div className='tips'>{t("拖拽排序、新增题目、删除题目后，作业分数会重置，请调整完左侧题目再设置分数")}</div>
                <div className='tips'>{t("长按题目前“排序”按钮可拖拽调整题目")}</div>
                {
                  dataSource.length > 0 ? <Checkbox.Group value={checkedList} onChange={(value: any) => setCheckedList(value)}>
                    <SortableList
                      useDragHandle
                      canEdit={!((homeworkStatus === "pub" || homeworkStatus === "save") && !tempState) && templateEdit.current}
                      tempState={tempState}
                      helperClass='custom-row-dragging'
                      list={dataSource}
                      onChange={onTopicChange}
                      onSortEnd={onSortEnd} />

                  </Checkbox.Group> : <div className="empty-btn">
                    {((homeworkStatus === "pub" || homeworkStatus === "save") && !tempState) ? <div className='empty-tips'>{t("已下发的作业不能再增加或修改题目")}</div> : templateEdit.current ? <>
                      <div className='empty-tips'>{t("你还没有添加作业题目哦~")}</div>
                      <div className='btn-box'>
                        <div className="btn" onClick={handleAddTopic}>
                          <PlusCircleOutlined />
                          <div>{t("添加题目")}</div>
                        </div>
                        <div className="btn" onClick={() => setExamVisible(true)}>
                          <PlusCircleOutlined />
                          <div>{t("添加试卷")}</div>
                        </div>
                      </div>
                    </> : null}
                  </div>}


              </div>
              {dataSource.length > 0 && <PointSetting ref={scoreRef} tempData={dataSource} btnShow={(homeworkStatus === "pub" || homeworkStatus === "save") || tempState} onConfirm={handlePointConfirm} />}
            </div>
          </div>
          <CustomgroupingModal
            visible={CustomVisible}
            PubState={PubState}
            homeworkId={homeworkId.current}
            courseId={location.query.id}
            resourceType={pageType.current === "resource" ? location.query.type : null}
            onAdd={() => setCustomSelectVisible(true)}
            onConfirm={handleAddTomgrouping}
            onclose={() => setCustomVisible(false)}
          />
          <TopicSelectModal selectKeys={topicSelectType === "radio" ? [topicItem] : dataSource} visible={topicVisible} type={topicSelectType} onAdd={() => setTopicTypeSelectVisible(true)} onConfirm={handleAddTopicConfirm} onclose={() => setTopicVisible(false)} />
          <PreviewHomeworkModal detail={dataSource} visible={previewPaperVisible} onClose={() => setPreviewPaperVisible(false)} />
          <PreviewTopicModal detail={topicItem} visible={previewTopicVisible} onClose={() => setPreviewTopicVisible(false)} />
          <ExamSelectModal visible={examVisible} onConfirm={handleExamSelectConfirm} onclose={() => setExamVisible(false)} />
          <ImageModal image={curImage} visible={imageVisible} onClose={() => setImageVisible(false)} />
          <TopicTypeSelectModal
            visible={topicTypeSelectVisible}
            onClose={() => setTopicTypeSelectVisible(false)}
            onConfirm={(type: number) => {
              setTopicType(type);
              setShowAddTopic(true);
              setTopicVisible(false);
            }} />
        </div>
      </Spin>}
  </div>;
};

export default AddHomework;
