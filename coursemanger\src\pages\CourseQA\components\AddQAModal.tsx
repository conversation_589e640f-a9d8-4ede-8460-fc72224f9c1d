import { Form, Input, Modal, message } from 'antd';
import React, { FC, useState } from 'react';
import { useLocation, useSelector } from 'umi';
import QAService from "@/api/qa";
import useLocale from '@/hooks/useLocale';

const { useForm } = Form;

interface IProps {
  visible: boolean;
  onClose: (isRefresh?: boolean) => void;
}

const AddQAModal: FC<IProps> = ({ visible, onClose }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const { t } = useLocale();
  const location: any = useLocation();
  const { userInfo } = useSelector<any, any>((state) => state.global);
  const [form] = useForm();
  const handleCancel = () => {
    form.resetFields();
    onClose();
  };
  const extendType: any = {
    "mooc": "08",
    "spoc": "07",
    "training": "09",
    "map": "12",
    "microMajor": "15"
  }
  const handleOK = () => {
    form.validateFields().then((values: any) => {
      setLoading(true);
      const data = {
        ...values,
        anonymity: false,
        course_semester: location.query.sm || null,
        link_id: location.query.id,
        extend_type: extendType[location.query.type],
        user_id: userInfo.userCode,
        user_name: userInfo.nickName
      };
      QAService.addTopic(data).then((res: any) => {
        if (res.error_msg === 'Success') {
          message.success(t("发表成功！"));
          form.resetFields();
          onClose(true);
        } else {
          message.error(t("发表失败！"));
        }
      }).finally(() => {
        setLoading(false);
      });
    });
  };
  return <Modal title="发起提问" width={450} open={visible} onCancel={handleCancel} onOk={handleOK} confirmLoading={loading} >
    <Form form={form}  layout="vertical">
      <Form.Item label="标题" required name="title">
        <Input placeholder='请输入标题' maxLength={30} showCount />
      </Form.Item>
      <Form.Item label="内容" required name="content">
        <Input.TextArea 
          placeholder={`如果您是老师：可以向学生问需要思考、启发之处\n如果您是学生：可以向老师问自己不懂的之处`} 
          rows={5} 
          maxLength={300} 
          showCount 
        />
      </Form.Item>
    </Form>
  </Modal>;
};

export default AddQAModal;