import React, { FC, useState } from 'react';
import { Button, Checkbox, Dropdown, Menu, Popover, Image } from 'antd';
import { EllipsisOutlined } from '@ant-design/icons';

import './CourseBolck.less';
import { useSelector } from 'umi';
import perCfg from '@/permission/config';
import { IconFont } from '../iconFont';
import moment from 'moment';
import useLocale from '@/hooks/useLocale';

interface ICourseBlock {
  item: any;
  courseClick: () => void;
  onUnPublish?: () => void;
  onPublish?: () => void;
  onPreview?: () => void;
  onDelete?: () => void;
}

const CourseBlockForOther: FC<ICourseBlock> = ({
  item,
  courseClick,
  onUnPublish,
  onPublish,
  onPreview,
  onDelete
}) => {
  const { t } = useLocale()
  const { mobileFlag } = useSelector<{ config: any; }, any>(
    ({ config }) => config);

  const btn =
    item.publishStatus == 0 ? <div className="icon-container" onClick={(e: any) => {
      e.stopPropagation();
      onPublish?.();
    }}>
      <IconFont type="iconfabu" />
      <span>{t("发布")}</span>
    </div> :
      <div className="icon-container" onClick={(e: any) => {
        e.stopPropagation();
        onUnPublish?.();
      }}>
        <IconFont type="iconquxiaofabu" />
        <span>{t("下架")}</span>
      </div>;

  return (
    <div
      className="course_block_item"
      key={item.contentId_}>

      <div className="img_box" onClick={courseClick}>
        <Image src={item.cover} fallback='/learn/static/imgs/other-course-bg.jpg' preview={false} />
        <div className="action_icon">
          <div className="bottom_btn">
            {/* {!mobileFlag && btn} */}
            <div onClick={onPreview}>
              <IconFont type="iconviews" />
              <span>{t("预览")}</span>
            </div>

            {item.publishStatus == 0 ? <div className="icon-container" onClick={(e: any) => {
              e.stopPropagation();
              onPublish?.();
            }}>
              <IconFont type="iconfabu" />
              <span>{t("发布")}</span>
            </div> :
              <div className="icon-container" onClick={(e: any) => {
                e.stopPropagation();
                onUnPublish?.();
              }}>
                <IconFont type="iconquxiaofabu" />
                <span>{t("下架")}</span>
              </div>}

            {item.publishStatus == 0 &&
              <div onClick={(e:any) => {
                e.stopPropagation();
                onDelete?.()
              }}>
                <IconFont type="icondelete" />
                <span>{t("删除")}</span>
              </div>}
          </div>
        </div>
        {item.publishStatus == 0 ?
          <div className="label_item">{t("未发布")}</div> :

          <div className="label_item label_item2">{t("已发布")}</div>}

      </div>
      <div className={"detail_box_other"}>
        <div className="title_box">
          <Checkbox
            value={item.contentId_}
            onClick={(e) => e.stopPropagation()} />

          <span className="title_dock" title={item.name_}>
            {item.name_}
          </span>
        </div>
        <div className="teacher">
          <span
            title={
              Array.isArray(item.teacher_names) ?
                item.teacher_names.join(',') :
                item.teacher_names}>


            {Array.isArray(item.teacher_names) ?
              item.teacher_names.join(',') :
              item.teacher_names}
          </span>
          {mobileFlag && btn}
        </div>
      </div>
    </div>);

};
export default CourseBlockForOther;