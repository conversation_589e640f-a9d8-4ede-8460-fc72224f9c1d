.Persopathmap_view{
    width: 100%;
    height: 100%;
    
    .render_view{
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;

        .jindu_box{
            position: absolute;
            width: 614px;
            height: 77px;
            background: rgba(255, 255, 255, 1);
            border-radius: 10px;
            top:10px;
            left: 30px;
            z-index: 1;
            display: flex;
            justify-content: space-evenly;
            
        }

        .right_box{
            position: absolute;
            right: 20px;
            top: 15px;
            width: 650px;
            height: 58px;
            background: #FFFFFF;
            border-radius: 10px;

            display: flex;
            align-items: center;
            justify-content: space-evenly;
            z-index: 1;
        }

        .Teachingmodule_drawer{
            .ant-drawer-header{
                padding: 0 !important;
                
            }
        
            .ant-drawer-content{
                background-color: transparent !important;
            }

            .ant-drawer-body{
                background-color: #fff;
            }

            .ant-drawer-content-wrapper{
                border-top-left-radius: 20px;
            }
        }

        
        .node_detail_view{

                .ant-drawer-header{
                    padding: 0 !important;
                    
                }
            
                .ant-drawer-content{
                    background-color: transparent !important;
                }

                .ant-drawer-body{
                    background-color: #fff;
                }

                .ant-drawer-content-wrapper{
                    border-top-left-radius: 20px;
                }
        }


    }
}