/*
 * @Author: lijin
 * @Description: 评论组件
 * @Date: 2022-02-22 10:50:23
 * @LastEditTime: 2022-04-20 14:59:17
 * @LastEditors: 李武林
 * @FilePath: \coursemanger\src\pages\CourseQA\components\customComment.tsx
 */
import React from 'react';
import { FC } from 'react';
import { Comment, Avatar, Space, Divider, Popconfirm, Tag, Spin, Tooltip, Button } from 'antd';
import './customComment.less';
import {
  MessageOutlined,
  FieldTimeOutlined,
  DeleteOutlined,
  UserOutlined
} from
  '@ant-design/icons';
import moment from 'moment';
import ReplyInput from './replyInput';
import useLocale from '@/hooks/useLocale';
interface CustomCommentProps {
  avatar?: string;
  type?: 'reply' | 'comment';
  comment: QA.Comment | QA.Reply;
  ifEdit?: boolean;
  ifTeacher?: boolean;
  ifCommenting?: boolean;
  canDelete?: boolean;
  onDelete?: (comment: QA.Comment | QA.Reply) => void;
  onReply?: (
    replyMsg: string,
    currentItem: QA.Comment | QA.Reply) =>
    Promise<boolean>;
  onComment?: (comment: QA.Comment | QA.Reply) => void;
}
const CustomComment: FC<CustomCommentProps> = ({
  type = 'reply',
  avatar = undefined,
  comment,
  ifTeacher = false,
  ifEdit = true,
  ifCommenting = false,
  children,
  canDelete,
  onDelete,
  onComment,
  onReply
}) => {
  const { t } = useLocale();

  return (
    <div className={'customcomment_container'}>
      <Comment
        actions={[
          <Space size={4}>
            <FieldTimeOutlined />
            {moment.unix(comment.created).format('YYYY-MM-DD HH:mm:ss')}
          </Space>]}

        author={
          <Space>
            {type === 'reply' ?
              <>
                <span className={'bold'}>{comment.user_name}</span>
                {ifTeacher && <Tag color="blue">{t("老师")}</Tag>}
              </> :

              <>
                <span className={'bold'}>{comment.user_name}</span>
                {ifTeacher && <Tag color="blue">{t("老师")}</Tag>}
                <span style={{ color: 'grey' }}>{t("回复")}</span>
                <span className={'bold'}>
                  {(comment as QA.Comment).target_user_name}
                </span>
                {(comment as QA.Comment).target_user_roles?.map((item) => item?.code ?? item)?.includes('r_teacher') && <Tag color="blue">{t("老师")}</Tag>}
              </>}

            {comment.unread_flag &&
              <div className={'new_reply_tag'}>{t("新回复")}</div>}

          </Space>}

        avatar={
          // <Spin spinning={avatar === undefined || avatar === ''}>
          // {
          avatar ? <Avatar src={avatar} alt={comment.user_name} /> : <Avatar icon={<UserOutlined />} />
          // }

          // </Spin>
        }
        content={
          <div className={'comment_main'}>
            <p>{comment.content}</p>
            {ifEdit &&
              <div className={'comment_opt'}>
                <Space size={0}>
                  {onReply && <div>
                    <Tooltip title={`${!comment.can_reply && comment.permissions == "1" ? t("仅教师团队可继续回复") : !comment.can_reply && comment.permissions == "2" ? t("仅提问人和教师团队可继续回复") : ""}`}>
                      <Space size={2} className={'comment_btn'}>
                        <Button type="text" icon={<MessageOutlined />} disabled={!comment.can_reply} onClick={() => onComment && onComment(comment)}>{t("回复")}

                        </Button>
                      </Space>
                      {comment.reply_count ? <span>（{comment.reply_count}）</span> : ''}
                    </Tooltip>
                  </div>}
                  {canDelete ?
                    <Popconfirm
                      title={t("是否删除该回复？")}
                      okText={t("确认")}
                      cancelText={t("取消")}
                      onConfirm={() => onDelete && onDelete(comment)}>

                      <Space size={2} className={'comment_btn'}>
                        <DeleteOutlined />{t("删除")}

                      </Space>
                    </Popconfirm> : ''}

                </Space>
              </div>}

          </div>}>


        {ifCommenting && onReply &&
          <ReplyInput canReply={comment.can_reply} permissions={comment.permissions} onReply={(replyMsg) => onReply(replyMsg, comment)} />}

        {children}
      </Comment>
    </div>);

};
export default CustomComment;