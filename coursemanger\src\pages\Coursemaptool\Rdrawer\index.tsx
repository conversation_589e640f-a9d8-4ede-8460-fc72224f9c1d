import chapterApis from '@/api/chapter';
import Entity from '@/components/entity/entity';
import { IconFont } from '@/components/iconFont';
import { IGlobal, IGlobalModelState } from '@/models/global';
import CourseQAdetail from '@/pages/CourseQA/detail';
import CourseQA from '@/pages/CourseQA/index';
import Icon, {
  CheckCircleOutlined,
  CloseCircleOutlined,
  CloseOutlined,
  EditOutlined,
} from '@ant-design/icons';
import {
  Button,
  Dropdown,
  Empty,
  Input,
  InputNumber,
  List,
  Modal,
  Select,
  Spin,
  Tabs,
  Tag,
  Tooltip,
  message,
  Image,
} from 'antd';
import React, {
  FC,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useDispatch, useLocation, useSelector } from 'umi';
import ResourceModal from '../../../components/ResourceModal';
import ResourcePreviewModal from '../../../components/ResourcePreviewModal';
import Askquestions from '../askquestions';
import './index.less';
const { TabPane } = Tabs;
const { confirm } = Modal;
// 对比辨析组件
// import TabComparativeanalysis from '../TabComparativeanalysis';
// 引用选择作业的弹窗
import TopicSelectModal from '@/pages/HomeworkManagement/components/TopicSelectModal';
//从题库自动添加弹窗
import AutomaticProblem from '@/pages/Coursemaptool/components/AutomaticProblem';
// 作业组件
import HomeworkSubItem from '@/pages/HomeworkManagement/components/HomeworkSubItem';

// 引用svg
import { ReactComponent as chatgpticon } from '@/assets/imgs/icon/chatgpt.svg';

import { addlog, getTreebylevel } from '@/api/addCourse';
import { getSmartExplain } from '@/api/chatgpt';
import {
  checkNumber,
  deleteResourceStudyRecord,
  examinationAdd,
  finishKnowledge,
  getStudyResourceList,
  getknowledgelist,
  knowledrecommend,
  questionsanswers,
  recommendanswers,
  updateNumber
} from '@/api/coursemap';
import statisticsApi from '@/api/statistics';
import RenderHtml from '@/components/renderHtml';
import useLocale from '@/hooks/useLocale';
import { useBus } from 'react-bus';
import { createguid, defaultNodeData, getContentLength } from '../Editmap/util';
import ProgressBox from '../Mapv4/components/ProgressBox';
// 绑定大纲的弹窗
import baseInfo from '@/api/baseInfo';
import { CheckService } from '@/api/check';
import { cloneDeep } from 'lodash';
import debounce from 'lodash/debounce';
import SynchronizedTeaching from '../SynchronizedTeaching';
import Case from '../components/Case';
import TextMore from './Textmore';
import MicroMajorGoals from './components/MicroMajorGoals';
import RelatedOutlineModal from './components/RelatedOutlineModal';
import TinymceEditor from '../components/TinymceEditor';
import { handleDownload } from '@/pages/HomeworkManagement/utils/tools';
import TagSpan from '../components/TagSpan';
import { article, target } from './demo';
import UploadFile from '../components/Uploadfile';
import Comparativeanalysis from '../Comparativeanalysis';
import ResourcePreview from './EntityPreview';
import AddLink from './components/AddLink';

// 添加类型定义
interface AddResourceDropdownProps {
  loading: boolean;
  onAddFromLibrary: () => void;
  type?: 'resource' | 'reference';
}

interface ResourceListProps {
  dataSource: any[];
  onItemClick?: (item: any) => void;
  onDelete?: (e: React.MouseEvent, item: any, index: number) => void;
  showFinishStatus: boolean;
  finishResource: string[];
  isedit: boolean;
  type?: 'resource' | 'reference';
}

const Rdrawer: FC<any> = ({
  setVisible,
  x6node,
  isedit,
  updatanode = Function,
  graph,
  centerednode,
  visible,
  mapid,
  perviewtype,
  updatanodecompare,
  setTotalTime,
  setCurrentTime,
  currentTime,
  courseid,
  previewEntity,
  setPreviewEntity,
  bindCourseCode,
  setBindCourseCode,
  coursename,
  isv3 = false,
  handleResource,
  /** 绑定的教学大纲 */
  courseCode,
  /** 选中的节点 */
  // checkNode = {},
  /** 是否是教师端 */
  isMain = false,
  /** 是否是编辑地图页 */
  isEditMap = false,
  /** 是否是微专业 */
  isMicroMajor = false,
  /** 节点列表 */
  nodeList = [],
  /** 是否不记录当前节点学习信息 */
  notrecord = false,
  /** 对比辨析列表 */
  knowledgeDiscriminateList = [],
  /** 对比辨析列表发生变化 */
  discriminateListChange = Function
}) => {
  const { t } = useLocale();
  const bus = useBus();
  const location: any = useLocation();
  const parameterConfig = useSelector<any, any>(
    ({ global }) => global.parameterConfig,
  );
  const dispatch = useDispatch();

  const { parameterConfigObj, buttonPermission } = useSelector<
    { global: IGlobalModelState },
    IGlobalModelState
  >(state => state.global);
  const mapinfo: any = useSelector<any, any>(
    (state: { coursemap: { mapinfo: any } }) => state.coursemap.mapinfo,
  );
  useEffect(() => {
    console.log(previewEntity, 'previewEntity');
  }, [])
  const { userInfo } = useSelector<any, IGlobal>(state => state.global);
  //微专业的planId
  const microCourseInfo: any = useSelector<any, any>(state => state.moocCourse?.courseDetail)

  const [selectNode, setSelectNode] = useState<any>({}); //选中的节点
  const [nodeinfo, setNodeinfo] = useState<any>(null); //选中的节点
  // const [courseknowledge, setCourseknowledge] = useState<any>([]);
  // const [knowledgeindex, setKnowledgeindex] = useState<number>(0);
  //课程目标相关的弹窗
  const [showModalGoal, setShowModalGoal] = useState('');
  const [questionsitems, setQuestionsitems] = useState<any>([
    {
      key: '1',
      label: (
        <a
          target="_blank"
          rel="noopener noreferrer"
          onClick={e => setAddtopicvisible(true)}
        >
          {t('从题库')}
        </a>
      ),
    },
  ]);
  const [perviewvisit, setPerviewvisit] = useState<boolean>(false);
  const [perviewsrc, setPerviewsrc] = useState<string>(''); //预览路径
  // 引用的知识点
  const [referencedknowledge, setReferencedknowledge] = useState<any>([]);

  const [entityLoading, setEntityLoading] = useState<boolean>(false);

  // 选择资源弹窗的数据
  const [modalTreeData, setModalTreeData] = useState<ResourceModal.treeItem[]>(
    [],
  );

  //   选择资源弹窗的开关
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  // 资源预览相关state
  const [entityModalVisible, setEntityModalVisible] = useState(false);
  const [entityPreview, setEntityPreview] = useState<any>(null);

  const [modalLoading, setModalLoading] = useState<boolean>(false); //选择资源弹窗控制
  // 父级节点
  const [parentnode, setParentnode] = useState<any>([]);
  const [preorder, setPreorder] = useState<any>([]); //前序知识点
  const [nextorder, setNextorder] = useState<any>([]); //后序知识点  
  // // 包含的知识点
  const [comprisingknowledge, setComprisingKnowledge] = useState<any>([]);
  // 等价的知识点
  const [equivalentknowledge, setEquivalentKnowledge] = useState<any>([]);
  // 关联的知识点
  const [associatedknowledge, setAssociatedKnowledge] = useState<any>([]);
  const [seecontentid, setSeecontentid] = useState<string>(''); //查看内容id
  const [questionvisible, setQuestionvisible] = useState<number>(1); //1 是展示列表  2是添加评论  3是展示详情
  const [todetail, setTodetail] = useState<any>(null); //跳转到评论详情的 参数
  // 参考资料
  const [reference, setReference] = useState<any>([]);
  // 作业
  const [homework, setHomework] = useState<any>([]);
  // 添加题目的开关
  const [addtopicvisible, setAddtopicvisible] = useState<boolean>(false);
  // 添加题目的开关
  const [addtopicvisibleAi, setAddtopicvisibleAi] = useState<boolean>(false);
  // 当前是添加绑定资源还是添加参考资料
  const [addtype, setAddtype] = useState<string>('bindresource');
  //当前用户选择的所有答案
  const [useranswer, setUseranswer] = useState<any>({});
  // 是否查看答案
  const [isseeanswer, setIsseeanswer] = useState<boolean>(false);
  const [recommendId, setRecommendId] = useState<string>(''); //解析
  //当前推荐的所有答案
  const [recommendanswer, setRecommendanswer] = useState<any>([]);
  // 是否查看推荐答案
  const [isseerecommendanswer, setIsseerecommendanswer] = useState<boolean>(
    false,
  );

  // 上传相关的状态变量
  const [uploadModalVisible, setUploadModalVisible] = useState<boolean>(false);
  const [uploadType, setUploadType] = useState<'resource' | 'reference'>('resource');

  // 当前查看视频的窗口是在哪里
  const [currentvideotype, setCurrentvideotype] = useState<number>(1); //顶部

  const [explanation, setExplanation] = useState<string>(''); //解析

  // 智能解析loading
  const [intelligentloading, setIntelligentloading] = useState<boolean>(false);

  // 当前新绑定的资源
  const newBindResource = useRef<any>([]);

  const finishresourceRef = useRef<any>({});

  // 当前已经学习完毕的资源
  const [finishresource, setFinishresource] = useState<any>([]);

  // 掌握率  完成率
  const [rate, setRate] = useState<any>({
    finishRate: 0,
    masterRate: 0,
    nodeAttainment: 0,
  });

  // 知识点编号编辑状态
  const [editlabel, setEditlabel] = useState<boolean>(false);
  // 知识点编号
  const [serialNumber, setSerialNumber] = useState<string>('');

  // 防止数据粘连
  const [homeworkloading, setHomeworkloading] = useState<boolean>(false);
  const [recommendloading, setRecommendloading] = useState<boolean>(false);

  const showSearch = location.query.showSearch !== '0';
  const couresSyllabusCode: string = useMemo(() => {
    return bindCourseCode || courseCode;
  }, [bindCourseCode, courseCode]);

  // 获取新建的ID
  const [getNodeinfoId, setNodeinfoId] = useState<number>(0);
  // 当前tab激活的key
  const [activeKey, setActiveKey] = useState<string>('1');

  const [previewImageVisible, setPreviewImageVisible] = useState(false);
  const [previewImageSrc, setPreviewImageSrc] = useState('');

  //刷新地图数据
  const handleRefreshMapInfo = () => {
    // 更新dva的store
    baseInfo
      .getCourseDetails(location?.query?.id, location?.query?.sm ?? 1)
      .then(res => {
        if (res && res.message === 'OK') {
          dispatch({
            type: 'moocCourse/updateState',
            payload: {
              courseDetail: res.data,
            },
          });
          if (res?.data?.entityData?.couresSyllabusCode) {
            setBindCourseCode(res?.data?.entityData?.couresSyllabusCode);
          }
        }
      });
  };
  // 目标列表
  const [goalList, setGoalList] = useState<any[]>([]);

  // 知识点详解的富文本
  const [explanationContent, setExplanationContent] = useState<string>('');

  // 根据地图id查询课程目标
  const getGoalList = useCallback(() => {
    if (!couresSyllabusCode || !mapid || !x6node?.id) {
      return;
    }
    setModalLoading(true)
    CheckService.queryGoalByMapNode({
      courseCode: couresSyllabusCode,
      courseId: '',
      mapId: mapid,
      nodeId: x6node.id,
      courseSemester: '1',
    }).then(res => {
      if (res?.status == 200) {
        const { data } = res;
        setGoalList(data?.data || []);
      }
    }).finally(() => {
      setModalLoading(false)
    })
  }, [couresSyllabusCode, mapid, x6node?.id]);
  useEffect(() => {
    setSeecontentid(previewEntity?.tempid);
  }, [previewEntity, visible])
  useEffect(() => {
    getGoalList();
  }, [getGoalList]);
  //绑定成功后的回调
  const handleBindOk = () => {
    getGoalList();
    setShowModalGoal('');
  };

  useEffect(() => {
    if(visible == 1){
      x6node && getresourcelearning();
    }
  }, [x6node,visible]);
  //添加试题下拉选项
  useEffect(() => {
    if (parameterConfig.auto_question_generation_display == 'true') {
      questionsitems.push({
        key: '2',
        label: (
          <a
            target="_blank"
            rel="noopener noreferrer"
            onClick={e => setAddtopicvisibleAi(true)}
          >
            {t('自动出题')}
          </a>
        ),
      });
      setQuestionsitems(questionsitems);
    }
  }, [parameterConfig]);

  const init = async () => {
    console.log('mapinfo.id',mapinfo.id,mapid);
    if (x6node) {
      const data = x6node.getData();
      // 从查看案例跳转过来的自动打开案例列表
      if(location.query?.case_id || location.query?.caseId){
        const caseflage  = data.caselist.findIndex((item:any)=>((item.caseId == location.query?.case_id) || (item.caseId == location.query?.caseId)));
        setActiveKey(caseflage > -1 ? '6' : '1');
      }
      if (!showSearch) {
        let parent = graph.getPredecessors(x6node);
        window.parent.postMessage(
          JSON.stringify({
            id: parent[parent.length - 3]?.id,
            label: data.label,
            action: 'selectnode',
          }),
          '*',
        );
      }
      if (
        data.bindresource &&
        data.bindresource.length &&
        typeof data.bindresource[0] == 'string'
      ) {
        await chapterApis
          .resourceDetailNew(data.bindresource)
          .then((res: any) => {
            if (res.status != 200) return (data.bindresource = []);
            data.bindresource = data.bindresource.map(
              (contentId: any, index: number) => ({
                name: res.data[index]?.name,
                contentId,
                contentId_: contentId,
                type: res.data[index]?.type,
                keyframe_: res.data[index]?.cover,
              }),
            );
            console.log('data.bindresource', data.bindresource);
          });
      }
      setExplanationContent(data.explanation);
      setSelectNode((pre: any) => {
        return {
          id: x6node.id,
          ...defaultNodeData,
          ...data,
          temp: new Date().getTime(),
        };
      });
      setNodeinfoId(0);
      // 获取所有绑定的资源 并且 如果第一个是视频  默认播放
      if (data.bindresource.length) {
        // previewEntity.name ? previewEntity:
        let item = data.bindresource[0];
        let type = item.type.split('_');
        if (item.type == 'biz_sobey_point' || item.recousetype == 'point') {
          onEntityPreview(item.parentcontentId, {
            name: item.name,
            type: 'video',
            knowledge: {
              inpoint: item.inpoint,
              outpoint: item.outpoint,
            },
          }, item);
          setSeecontentid(item.tempid);
        } else if (type[type.length - 1] == 'video') {
          onEntityPreview(item.contentId, {
            name: item.name,
            type: type[type.length - 1],
          }, item);
          setSeecontentid(item.tempid);
        } else if (type[type.length - 1] == 'picture') {
          onEntityPreview(item.contentId, {
            name: item.name,
            type: type[type.length - 1],
          }, item);
          setSeecontentid(item.tempid);
        }else{
          // 清空previewEntity
          setPreviewEntity?.({});
        }
      }else{
        // 清空previewEntity
        setPreviewEntity?.({});
      }
      let referencedarr = [];
      // 获取所有的引用知识点
      if (data.quoteKnowledge?.length) {
        let nodearr = graph
          .getNodes()
          .filter((item: any) => item.store.data.data.type == '2');
        if (nodearr.length) {
          let arr = nodearr.filter((item: any) =>
            data.quoteKnowledge.includes(item.id),
          );

          setReferencedknowledge(arr);
          referencedarr = arr;
        }
      } else {
        setReferencedknowledge([]);
      }

      if (referencedarr.length) {
        let str = data.explanation;
        referencedarr.forEach((element: any) => {
          // 替换匹配到的字符串
          str = str.replace(
            element.store.data.data.label,
            `<span style="color:#549CFF;cursor: pointer;" data-id="${element.id}" >${element.store.data.data.label}</span>`,
          );
        });
        setExplanation(str);
      } else {
        setExplanation(data.explanation);
      }

      // // 获取所有的参考资料
      // if(data.referenceMaterial?.length){
      //   setReference(data.referenceMaterial);
      // }

      // // 获取所有的
      // if (data.homework?.length) {
      //   setUseranswer(
      //     data.homework.map((item: any) => item.questions_answers || []),
      //   );
      // }

      // type 1包含 2等价 3后续 4关联

      // 包含的知识点
      let comprisingknowledgearr: any = [];
      // 等价的知识点
      let equivalentknowledgearr: any = [];
      // 关联的知识点
      let associatedknowledgearr: any = [];
      // 前序的知识点
      let preorderarr: any = [];
      // 后续的知识点
      let nextorderarr: any = [];

      // 获取所有的出点
      let pre = graph.getOutgoingEdges(x6node);
      // 入点
      let next = graph.getIncomingEdges(x6node);
      // 1连接到2   1就是2的前序  2就是1的后序
      if (pre && pre.length) {
        pre.forEach((element: any) => {          
          if (element.store.data.data.type == 1) {
            let cell = graph.getCellById(element.store.data.target.cell);
            if(cell.data.type != 1){
              comprisingknowledgearr.push(cell);
            }
          }
          if (element.store.data.data.type == 2) {
            let cell = graph.getCellById(element.store.data.target.cell);
            equivalentknowledgearr.push(cell);
          }
          if (element.store.data.data.type == 3) {
            let cell = graph.getCellById(element.store.data.target.cell);
            nextorderarr.push(cell);
          }
          if (element.store.data.data.type == 4) {
            let cell = graph.getCellById(element.store.data.target.cell);
            associatedknowledgearr.push(cell);
          }
        });
      }

      if (next && next.length) {
         next.forEach((element: any) => {         

          if (element.store.data.data.type == 2) {
            let cell = graph.getCellById(element.store.data.source.cell);
            equivalentknowledgearr.push(cell);        
          }

          if (element.store.data.data.type == 3) {
            let cell = graph.getCellById(element.store.data.source.cell);
            preorderarr.push(cell);
          }

          if (element.store.data.data.type == 4) {
            let cell = graph.getCellById(element.store.data.source.cell);
            associatedknowledgearr.push(cell);           
          }
        });
      }
      
      setPreorder(preorderarr);
      setNextorder(nextorderarr);
      setComprisingKnowledge(comprisingknowledgearr);
      setEquivalentKnowledge(equivalentknowledgearr);
      setAssociatedKnowledge(associatedknowledgearr);

      // setPreviewEntity(null);
      showSearch && getlist();
      getRate();
      setUseranswer({});
      setUseranswer({});
      setIsseeanswer(false);
    }
  };

  /**
   * 查询资源预览网址，显示资源详情modal
   *
   * @param {string} id
   * @param {*} { name, type }
   */
  const onEntityPreview = (id: string, { name, type, knowledge }: any, info?: any) => {
    setEntityLoading(true);
    chapterApis
      .resourceDetailNew([id])
      .then(async(res) => {
        bus.emit('selectId', id)
        if (res.status == 200) {
          if (res.data.length) {
            if (type == 'document') {
              setPerviewvisit(true);
              setPerviewsrc(res.data[0].filePath);
            } else {
              const data = {
                src: res.data[0].filePath,
                cover: res.data[0].cover,
                finishStatus: Array.isArray(finishresourceRef.current)
                  ? finishresourceRef.current.includes(info?.tempid)
                  : true,
                contentId: id,
                name,
                type,
                tempid: info?.tempid,
                knowledge: {
                  ...(knowledge
                    ? knowledge
                    : {
                        inpoint: 0,
                      }),
                    contentId: id,
                },
              };
              setPreviewEntity(data);
              // if(id !== previewEntity?.contentId){
              // }
            }
          }
        } else {
          if(info.iscanvas){
            message.info('资源迁移中，请稍后再试！');
          }else{
            message.error(res.message);
          }
        }
      })
      .finally(() => setEntityLoading(false));
  };
  // 取消知识点关联资源
  const removevideobyid = (e: any, item: any, index: number) => {
    e.stopPropagation();
    confirm({
      title: `解除绑定资源`,
      content: `该内容的学生学习数据将一并清除，是否确认删除？`,
      onOk() {
        // 根据下标删除数组中的元素
        let data = selectNode.bindresource.filter(
          (item: any, index2: number) => index2 != index,
        );
        if(data.length == 0){
          // 清空previewEntity
          setPreviewEntity?.({});
        }
        setSelectNode({
          ...selectNode,
          bindresource: data,
          temp: new Date().getTime(),
        });
        updatanode(x6node.id, {
          ...selectNode,
          bindresource: data,
        });
        deleteResourceStudyRecord(mapid, x6node.id, [
          item.contentId,
        ]).then((res: any) => {});
      },
      onCancel() {},
    });
  };

  // 取消知识点关联参考资料
  const removeReference = (e: any, item: any, index: number) => {
    e.stopPropagation();
    confirm({
      title: `解除绑定资源`,
      content: `该内容的学生学习数据将一并清除，是否确认删除？`,
      onOk() {
        // 根据下标删除数组中的元素
        let data = selectNode.referenceMaterials.filter(
          (item: any, index2: number) => index2 != index,
        );
        if(data.length == 0){
          // 清空previewEntity
          setPreviewEntity?.({});
        }
        setSelectNode({
          ...selectNode,
          referenceMaterials: data,
          temp: new Date().getTime(),
        });
        updatanode(x6node.id, {
          referenceMaterials: data,
        });
        deleteResourceStudyRecord(mapid, x6node.id, [
          item.contentId,
        ]).then((res: any) => {});
      },
      onCancel() {},
    });
  };

  // 取消知识点关联题目
  const removeQuestion = (e: any, item: any, index: number) => {
    e.stopPropagation();
    confirm({
      title: t(`确认删除题目吗？`),
      onOk() {
        let deletearr: any = [];
        // 根据下标删除数组中的元素
        let data = selectNode.homework.filter((item: any, index2: number) => {
          if (index2 != index) {
            return true;
          } else {
            deletearr.push(item);
            return false;
          }
        });
        // 重置用户答题记录
        if(data.length == 0){
          setUseranswer({});
          setUseranswer({});
        }else{
          let newuseranswer = useranswer;
          newuseranswer[item.id] = []
          setUseranswer(newuseranswer);
        }
        setSelectNode({
          ...selectNode,
          homework: data,
          temp: new Date().getTime()
        });
        updatanode(x6node.id, {
          homework: data
        });
        // deletequestion(deletearr);
      },
      onCancel() {},
    });
  };

  //显示Modal
  const showModal = (e: string) => {
    setModalLoading(true);
    setAddtype(e);
    const getTreeData = () => {
      getTreebylevel().then(res => {
        if (res && res.success) {
          let treeData = forTree(res.data, []);
          setModalTreeData(treeData);
          setTimeout(() => {
            setModalLoading(false);
            setModalVisible(true);
          }, 100);
        } else {
          console.error(res);
        }
      });
    };
    // 遍历目录树
    const forTree = (tree: any, parentsKeys: string[]) => {
      return tree.map((item: any) => {
        return {
          key: item.id,
          parentsKeys,
          title: item.name,
          path: item.path,
          children: item.children
            ? forTree(item.children, [...parentsKeys, item.code])
            : [],
        };
      });
    };
    getTreeData();
  };

  // 动态渲染文件类型
  const getTag = (item: any) => {
    if (item.type == 'biz_sobey_document_point') {
      return <Tag color="magenta">{t('文档知识点')}</Tag>;
    } else if (item.type == 'biz_sobey_point' || item.recousetype == 'point') {
      return <Tag color="green">{t('视频知识点')}</Tag>;
    } else if (item.type == 'biz_sobey_video') {
      return <Tag color="green">{t('视频')}</Tag>;
    } else if (item.type == 'biz_sobey_audio') {
      return <Tag color="blue">{t('音频')}</Tag>;
    } else if (item.type == 'biz_sobey_picture') {
      return <Tag color="orange">{t('图片')}</Tag>;
    } else if (item.type == 'biz_sobey_document') {
      return <Tag color="purple">{t('文档')}</Tag>;
    } else if (item.type == 'biz_sobey_case') {
      return <Tag color="magenta">{t('案例')}</Tag>;
    } else if (item.type == 'canvas_resource') {
      return <Tag color="magenta">{item.mime_class || 'canvas'}</Tag>;
    }else {
      return <Tag color="lime">{t('其他')}</Tag>;
    }
  };

  // 知识点关联资源
  const addrecouse = (selectrecouse: any) => {
    let obj: any = null;
    setSelectNode((pre: any) => {
      obj = pre;
      return pre;
    });
    let newdata = selectrecouse.map((item: any) => {
      return {
        name: item.name,
        contentId: item.contentId,
        contentId_: item.contentId_,
        type: item.type_,
        keyframe_: item.keyframe_,
        tempid: createguid(),
        iscanvas:item.iscanvas || false,
        duration:item.duration || 0
      };
    });
    if (obj.bindresource && obj.bindresource.length) {
      let newarr = newdata.filter((item: any) => {
        return obj.bindresource.every(
          (item2: any) => item.contentId_ != item2.contentId_,
        );
      });
      if (newarr.length) {
        obj.bindresource = [...obj.bindresource, ...newarr];
        createlogdata(obj, newarr, 1);
      } else {
        message.warning(t('已存在该资源'));
        setModalVisible(false);
        return;
      }
    } else {
      obj.bindresource = newdata || [];
      createlogdata(obj, newdata, 1);
    }
    setSelectNode({
      ...selectNode,
      ...obj,
      temp: new Date().getTime(),
    });
    updatanode(x6node.id, {
      bindresource: obj.bindresource,
    });
    setModalVisible(false);
    addlogs();
  };

  // 知识点绑定资源知识点
  const addpoint = (selectrecouse: any) => {
    let obj = selectNode;
    let newdata = selectrecouse.map((item: any) => {
      return {
        recousetype:
          item.video_clip_path == 'document' ? 'document_point' : 'point',
        name: item.title,
        contentId: item.guid_,
        contentId_: item.guid_,
        type:
          item.video_clip_path == 'document'
            ? 'biz_sobey_document_point'
            : 'biz_sobey_point',
        keyframe_: item.keyframepath,
        tempid: createguid(),
        keyframeno: item.keyframeno,
        parentcontentId: item.contentId,
        fragment_description: item.fragment_description,
        inpoint: item.inpoint,
        outpoint: item.outpoint,
        parentname: item.name,
        createDate: item.createDate,
        suffix: item.suffix,
        duration:item.outpoint - item.inpoint || 0
      };
    });
    if (obj[addtype] && obj[addtype].length) {
      let newarr = newdata.filter((item: any) => {
        return obj[addtype].every(
          (item2: any) => item.contentId_ != item2.contentId_,
        );
      });
      if (newarr.length) {
        obj[addtype] = [...obj[addtype], ...newarr];
        createlogdata(obj, newarr, 2);
      } else {
        message.warning(t('已存在该知识点'));
        setModalVisible(false);
        return;
      }
    } else {
      obj[addtype] = newdata || [];
      createlogdata(obj, newdata, 2);
    }
    setSelectNode({
      ...selectNode,
      ...obj,
      temp: new Date().getTime(),
    });
    updatanode(x6node.id, {
      [addtype]: obj[addtype]
    });
    setModalVisible(false);
    addlogs();
  };

  // 添加文档知识点
  const adddocumentpoint = (selectrecouse: any) => {
    let obj = selectNode;
    let newdata = selectrecouse.map((item: any) => {
      return {
        recousetype: 'biz_sobey_document_point',
        name: item.knowledgePoints[0].title,
        contentId: item.knowledgePoints[0].guid_,
        contentId_: item.knowledgePoints[0].guid_,
        type: 'biz_sobey_document_point',
        keyframe_: item.knowledgePoints[0].keyframepath,
        tempid: createguid(),
        keyframeno: item.knowledgePoints[0].keyframeno,
        parentcontentId: item.contentId,
        fragment_description: item.knowledgePoints[0].fragment_description,
        inpoint: item.knowledgePoints[0].inpoint,
        outpoint: item.knowledgePoints[0].outpoint,
        parentname: item.name,
        createDate: item.createDate,
        suffix: item.suffix
      };
    });
    if (obj[addtype] && obj[addtype].length) {
      let newarr = newdata.filter((item: any) => {
        return obj[addtype].every(
          (item2: any) => item.contentId_ != item2.contentId_,
        );
      });
      if (newarr.length) {
        obj[addtype] = [...obj[addtype], ...newarr];
        createlogdata(obj, newarr, 2);
      } else {
        message.warning(t('已存在该知识点'));
        setModalVisible(false);
        return;
      }
    } else {
      obj[addtype] = newdata || [];
      createlogdata(obj, newdata, 2);
    }
    setSelectNode({
      ...selectNode,
      ...obj,
      temp: new Date().getTime(),
    });
    updatanode(x6node.id, {
      [addtype]: obj[addtype]
    });
    setModalVisible(false);
    addlogs();
  }
  

  const addlogs = () => {
    if (newBindResource.current.length) {
      addlog(mapinfo.mapName, newBindResource.current).then(res => {
        if (res.success) {
          newBindResource.current = [];
          console.log('添加资源日志记录成功！');
        } else {
          console.log('添加资源日志记录失败！');
        }
      });
    }
  };

  const createlogdata = (node: any, arr: any, type: number) => {
    arr.forEach((item: any) => {
      newBindResource.current.push({
        contentId: item.contentId, //资源id
        name: item.name, //资源名称
        courseKnowledgeName: node.label, //知识节点名称
        knowledgeNames: type == 2 ? [item.name] : [],
      });
    });
  };

  // 知识点添加参考资料
  const addreferenceMaterials = (selectrecouse: any) => {
    let obj = selectNode;
    let newdata = selectrecouse.map((item: any) => {
      return {
        name: item.name,
        contentId: item.contentId,
        contentId_: item.contentId_,
        type: item.type_,
        keyframe_: item.keyframe_,
        tempid: createguid(),
        duration:item.duration || 0
      };
    });
    if (obj.referenceMaterials && obj.referenceMaterials?.length) {
      let newarr = newdata.filter((item: any) => {
        return obj.referenceMaterials.every(
          (item2: any) => item.contentId_ != item2.contentId_,
        );
      });
      if (newarr.length) {
        obj.referenceMaterials = [...obj.referenceMaterials, ...newarr];
        createlogdata(obj, newarr, 1);
      } else {
        message.warning(t('已存在该资源'));
        setModalVisible(false);
        return;
      }
    } else {
      obj.referenceMaterials = newdata || [];
      createlogdata(obj, newdata, 1);
    }
    setSelectNode({
      ...selectNode,
      ...obj,
      temp: new Date().getTime(),
    });
    updatanode(x6node.id, {
      referenceMaterials: obj.referenceMaterials
    });
    setModalVisible(false);
    addlogs();
  };

  // 智能详解
  const getGptdetail = (nodeid:string) => {
    setIntelligentloading(true);
    let label = selectNode?.label;
    if (label && label != '') {
      getSmartExplain(
        label,
        (responseText: any) => {
          if (responseText && responseText != '') {
            (window as any).tinymce.editors[nodeid]?.setContent(responseText);
          }
        },
        (responseText: any) => {
          if (responseText) {
            setSelectNode({
              ...selectNode,
              explanation: responseText,
              temp: new Date().getTime(),
            });
            updatanode(x6node.id, {
              explanation: responseText,
            });
          }
          setIntelligentloading(false);
        },
      );
    } else {
      message.info(t('请输入当前知识节点名称'));
    }
  };

  // 一键引用
  const onOneKeyQuote = () => {
    let nodearr = graph
      .getNodes()
      .filter((item: any) => item.store.data.data.type == '2');
    if (explanationContent) {
      // 判断详解是否包含知识点的名称
      let iscontain = nodearr.filter(
        (item: any) =>
          explanationContent.indexOf(item.store.data.data.label) >= 0 &&
          selectNode.label != item.store.data.data.label,
      );

      if (iscontain.length) {
        message.success(t('一键引用成功'));
        setReferencedknowledge(iscontain);
        setSelectNode({
          ...selectNode,
          quoteKnowledge: iscontain.length
            ? iscontain.map((item: any) => item.id)
            : [],
          temp: new Date().getTime(),
        });
        updatanode(x6node.id, {
          quoteKnowledge: iscontain.length
            ? iscontain.map((item: any) => item.id)
            : [],
        });
      } else {
        message.warning(
          t('引用失败，图中无可用内容。类型为知识点的节点才可引用！'),
        );
        return;
      }
    } else {
      message.warning(t('请先填写知识点详解'));
      return;
    }
  };

  // 根据下标删除元素
  const deleteQuote = (e: any, index: number) => {
    e.stopPropagation();
    let arr: [] = referencedknowledge.filter(
      (item: any, index2: number) => index2 != index,
    );

    setReferencedknowledge(arr);
    updatanode(x6node.id, {
      quoteKnowledge: arr.length ? arr.map((item: any) => item.id) : [],
    });
  };

  // 获取当前节点的前序节点
  const getlevelable = (data: any) => {
    if(data){
      //获取所有前序节点
      const nodes = graph.getCellById(data.id);
      if(nodes){
        const Predenode = graph.getPredecessors(nodes);
        let str = nodes.store.data.data.label;
        if (Predenode.length >= 1) {
          str = Predenode[0].store.data.data.label + '/' + str;
        }
        if (Predenode.length >= 2) {
          str = Predenode[1].store.data.data.label + '/' + str;
        }
        return str;
      }else{
        return ''
      }
    }
  };
  const getknowledrecommend = () => {
    setRecommendloading(true);
    knowledrecommend({
      courseSemester: 1,
      entity: x6node.getData().label, //知识点名称
      sourceQuestion: 2,
      mapId: mapid,
      courseId: courseid,
      nodeId: x6node.id,
      stuCode: userInfo.userCode,
    }).then(res => {
      setRecommendloading(false);
      if (res.status == 200 && res.data) {
        let newuseranswer: any = [];
        setRecommendId(res.data.id);
        let newhomework = res.data.questions || [];
        newhomework.forEach((item: any, index: number) => {
          newuseranswer.push(item.student_answers || []);
        });
        setRecommendanswer(newuseranswer);
        let newdata = null;
        setSelectNode((pre: any) => {
          newdata = {
            ...pre,
            recommend: newhomework,
            temp: new Date().getTime(),
          };
          return newdata;
        });
        // 这里只更新当前节点的试题  不要把data全部更新了 会导致数据同步问题
        if (perviewtype == 0 || perviewtype == 1) {
          updatanode(x6node.id, {
            homework: newhomework,
          });
        }
      }
    });
  };
  //当前试题批改结果
  const [correctionMap, setCorrecttionMap] = useState<any>({});
  // 获取当前节点的所有试题
  const getlist = () => {
    // setHomeworkloading(true);
    getknowledgelist({
      mapId: Number(mapid),
      courseId: courseid,
      // "mapName": mapinfo.mapName,
      nodeId: x6node.data.id || x6node.id,
      stuCode: userInfo.userCode,
    }).then((res: any) => {
      if (res.status == 200 && res.data) {
        //保存批改结果
        if (res.data?.answersExtends) {
          setCorrecttionMap(res.data.answersExtends);
        }
        setNodeinfo(res.data);
        // let newuseranswer: any = {};
        // let newhomework = res.data.questions || [];
        // newhomework.forEach((item: any, index: number) => {
        //   // newuseranswer.push(item.student_answers || []);
        //   newuseranswer[item.id] = item.student_answers || [];
        //   // newuseranswer.push(item.student_answers || []);
        //   newuseranswer[item.id] = item.student_answers || [];
        // });
        // setUseranswer(newuseranswer);       
      }
      // setHomeworkloading(false);
    });
  };

  // 调用试题的新增接口
  const addquestion = (arr: any = []) => {
    // 为每个试题添加字数统计
    const questionsWithCount = arr.map((item: any) => ({
      ...item,
      questionsNumber: getContentLength(item.questions_content)
    }));

    updatanode(x6node.id, {
      homework: questionsWithCount
    });    
  };


  // 提交答案
  const submitanswer = () => {
    confirm({
      title: t(`查看答案`),
      content: t(`若此刻提交，未做的题目将被记为0分，是否查看？`),
      onOk() {
        setIsseeanswer(!isseeanswer);
        let newhomework = selectNode.homework.map(
          (item: any, index: number) => {
            return {
              ...item,
              student_answers: useranswer[item.id] || [],
            };
          },
        );
        questionsanswers(nodeinfo.id, courseid, newhomework).then(
          (res: any) => {
            if (res.status == 200 && res.data) {
              message.success(t('提交成功'));
              updatanode(x6node.id, {
                homework: newhomework,
              });
            } else {
              message.error(t('提交失败'));
            }
          },
        );
      },
      onCancel() {},
    });
  };

  // 提交推荐答案
  const submitrecommendanswer = () => {
    confirm({
      title: t(`查看答案`),
      content: t(`若此刻提交，未做的题目将被记为0分，是否查看？`),
      onOk() {
        setIsseerecommendanswer(!isseerecommendanswer);
        let newhomework = selectNode.recommend.map(
          (item: any, index: number) => {
            return {
              ...item,
              student_answers: recommendanswer[index] || [],
            };
          },
        );
        let pre = graph.getPredecessors(x6node, { distance: 1 });

        let parent = pre.find(item => item.store.data.data.type == 1);
        if (!parent) {
          parent = pre.find(item => item.store.data.data.type == 2);
        }
        let next = graph.getSuccessors(parent);

        // 用当前选择的节点进行判断
        recommendanswers(mapid, courseid, recommendId, {
          predecessorKnowledgePoints: pre.map(item => item.id),
          parentKnowledgePoints: [parent.id],
          siblingKnowledgePoints: next
            .filter(item => item.id !== x6node.id)
            .map(item => item.id),
          knowledgeTestQuestion: newhomework,
        }).then((res: any) => {
          if (res.status == 200 && res.data) {
            message.success(t('提交成功'));
            // getlist();
          } else {
            message.error(t('提交失败'));
          }
        });
      },
      onCancel() {},
    });
  };
  // 获取资源学习记录
  const getresourcelearning = () => {
    if (perviewtype != 0 && courseid && location.pathname != '/perviewemap') {
      getStudyResourceList({
        courseId: courseid,
        mapId: mapid,
        nodeId: x6node.id,
      }).then((res: any) => {
        if (res.status == 200) {
          setFinishresource(res.data);
          finishresourceRef.current = res.data;
          init();
        } else {
          message.error(t('获取资源学习记录失败'));
        }
      });
    } else {
      init();
    }
  };

  // 添加资源学习
  const addResourcelearning = debounce((resourceId: any = null) => {
    if(notrecord){
      message.info(t('当前资源学习未记录，请先完成先修模块的学习！'));
      return;
    }
    // 判断是否已经学习过了
    if (finishresource.includes(seecontentid) || (!resourceId && !seecontentid)) {
      return;
    }
    const flage: any[] = [
      ...finishresource,
      resourceId ? resourceId : seecontentid,
    ];

    let type = 0; //0 视频 1文档 2 图片 3音频 4 作业 5 问答  课程地图目前没有 作业 和 问答
    if (resourceId) {
      type = 1;
    } else {
      if (
        previewEntity.type == 'biz_sobey_video' ||
        previewEntity.type == 'video'
      ) {
        type = 0;
      } else if (
        previewEntity.type == 'biz_sobey_audio' ||
        previewEntity.type == 'audio'
      ) {
        type = 3;
      } else if (
        previewEntity.type == 'biz_sobey_picture' ||
        previewEntity.type == 'picture'
      ) {
        type = 2;
      } else if (
        previewEntity.type == 'biz_sobey_document' ||
        previewEntity.type == 'document'
      ) {
        type = 1;
        console.log('添加文档学习记录');
      } else if (
        previewEntity.type == 'biz_sobey_document' ||
        previewEntity.type == 'document'
      ) {
        type = 1;
        console.log('添加文档学习记录');
      }
    }

    let data = [
      {
        courseId: courseid,
        courseName: coursename || '',
        mapId: mapid,
        nodeId: x6node.id,
        resourceId: resourceId ? resourceId : seecontentid,
        resourceType: type,
        userCode: userInfo.userCode,
      },
    ];
    finishKnowledge(data).then((res: any) => {
      if (res.status == 200) {
        setFinishresource(flage);
        finishresourceRef.current = flage;

        // message.success('添加成功');
      } else {
        // message.error(t('添加学习记录失败！'));
        console.log('添加学习记录失败！', res.message);
      }
      // getresourcelearning();
      getRate();
    });
  }, 1000);

  // 查询当前知识点 完成率 掌握率
  const getRate = () => {
    if (perviewtype != 0 && courseid && location.pathname != '/perviewemap') {
      statisticsApi
        .getTeacherOrStudentRate({
          courseId: courseid,
          mapId: mapid,
          nodeId: x6node.id,
          // type: perviewtype == 1 ? 0 : 1, //教师端 1  学生端 0
          type: isMain ? 0 : 1,
        })
        .then((res: any) => {
          if (res.data.status == 200) {
            setRate({
              finishRate: res.data.data.finishRate,
              masterRate: res.data.data.masterRate,
              nodeAttainment: Number(res.data.data.nodeAttainment),
            });
          } else {
            message.error({
              content:t('获取完成率失败！'),
              duration:1.5
            });
          }
        });
    }
  };

  useEffect(() => {
    // 触发学习记录事件
    if (rate.finishRate > 0 || rate.masterRate > 0 || rate.nodeAttainment > 0) {
      bus.emit('nodelearning', {
        id: x6node.id,
        finishRate: Number(rate.finishRate),
        masterRate: Number(rate.masterRate),
        nodeAttainment: Number(rate.nodeAttainment),
      });
    }
  }, [rate]);

  // 修改知识点编号
  const verificationSerialNum = () => {
    checkNumber({ serialNum: serialNumber }).then((res: any) => {
      if (res.data) {
        updateNumber({
          mapId: mapid,
          Id: selectNode.id,
          serialNum: serialNumber,
        }).then((res2: any) => {
          if (res2.status == 200) {
            updatanode(x6node.id, { serialNumber: serialNumber });
            setSelectNode({
              ...selectNode,
              serialNumber: serialNumber,
              temp: new Date().getTime(),
            });
            message.success(t('修改成功！'));
            setEditlabel(false);
          } else {
            message.error(t('修改失败！'));
          }
        });
      } else {
        // 编号不符合规则
        message.error(t('编号不符合规则，请重新输入！'));
      }
    });
  };

  // 在组件内添加一个变量来控制是否显示课程目标
  const showGoal = useMemo(() => {
    return location.query?.router !== 'minemap';
  }, [location.query?.router]);

  // 抽取添加按钮下拉菜单组件
  const AddResourceDropdown: React.FC<AddResourceDropdownProps> = ({ loading, onAddFromLibrary, type = 'resource' }) => {
    const { t } = useLocale();
    return (
      <Dropdown
        menu={{
          items: [
            {
              key: '1',
              label: t('本地上传'),
              onClick: (e: any) => {
                setUploadType(type);
                setUploadModalVisible(true);
              }
            },
            {
              key: '2',
              label: t('资源库'),
              onClick: onAddFromLibrary
            }
          ]
        }}
        placement="bottomRight"
      >
        <Button loading={loading} size="small" type="primary">
          {t('添加')}
        </Button>
      </Dropdown>
    );
  };

  // 处理上传成功
  const handleUploadSuccess = (data: any) => {
    if (data && data.length > 0) {
      const newResources = data.map((item: any) => ({
        name: item.name,
        contentId: item.resourseId,
        contentId_: item.resourseId,
        type: `biz_sobey_${item.resourceType}`,
        keyframe_: item.cover || '',
        tempid: createguid(),
        duration:item.duration || 0
      }));
      if (uploadType === 'resource') {
        let obj = selectNode;
        if (obj.bindresource && obj.bindresource.length) {
          obj.bindresource = [...obj.bindresource, ...newResources];
        } else {
          obj.bindresource = newResources;
        }
        createlogdata(obj, newResources, 1);
        setSelectNode({
          ...selectNode,
          ...obj,
          temp: new Date().getTime(),
        });
        updatanode(x6node.id, {
          bindresource: obj.bindresource
        });
      } else {
        let obj = selectNode;
        if (obj.referenceMaterials && obj.referenceMaterials.length) {
          obj.referenceMaterials = [...obj.referenceMaterials, ...newResources];
        } else {
          obj.referenceMaterials = newResources;
        }
        createlogdata(obj, newResources, 1);
        setSelectNode({
          ...selectNode,
          ...obj,
          temp: new Date().getTime(),
        });
        updatanode(x6node.id, {
          referenceMaterials: obj.referenceMaterials
        });
      }
      addlogs();
    }
    setUploadModalVisible(false);
  };

  // 抽取资源列表组件
  const ResourceList: React.FC<ResourceListProps> = ({
    dataSource,
    onItemClick,
    onDelete,
    showFinishStatus,
    finishResource = [],
    isedit,
    type = 'resource'
  }) => {
    const { t } = useLocale();
    return (
      <List
        bordered
        className={type === 'resource' ? 'resource_list' : 'references_list'}
        locale={{ emptyText: t(type === 'resource' ? '暂无绑定资源' : '暂无绑定参考资料') }}
        dataSource={dataSource}
        renderItem={(item, index) => (
          <List.Item
            style={{ cursor: 'pointer' }}
            key={index}
            onClick={() => onItemClick?.(item)}
          >
            <div className="content_item">
              {getTag(item)}
              <span className="content_name">
                {item.name}
                {item.type == 'biz_sobey_document_point' && item.outpoint && `（${item.inpoint}-${item.outpoint}页）`}
              </span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
              {showFinishStatus && (
                <span style={finishResource.includes(item.tempid) ? { color: '#999', fontSize: '12px' } : { color: '#549CFF', fontSize: '12px' }}>
                  {finishResource.includes(item.tempid) ? t('已完成') : t('去完成')}
                </span>
              )}
              {isedit && (
                <IconFont
                  title={t('取消关联')}
                  onClick={(e) => onDelete?.(e, item, index)}
                  style={{ color: 'var(--primary-color)', fontSize: '14px', marginLeft: '20px' }}
                  type="iconhuishouzhan-huise"
                />
              )}
            </div>
          </List.Item>
        )}
      />
    );
  };

  // 添加handleResourceClick函数
  const handleResourceClick = (item: any) => {
    setCurrentTime?.(0);
    if (item.type == 'biz_sobey_point' || item.recousetype == 'point') {
      onEntityPreview(item.parentcontentId, {
        name: item.name,
        type: 'video',
        knowledge: {
          inpoint: item.inpoint,
          outpoint: item.outpoint,
        },
      }, item);
    } else if (item.type == 'biz_sobey_document_point' || item.recousetype == 'document_point') {
      window.open(`/rman/#/basic/rmanDetail/${item.parentcontentId}?showArrow=true&guid_=${item.contentId}&inpoint=${item.inpoint}&outpoint=${item.outpoint}`);
    } else if(item.type == 'canvas_resource'){
      window.open(item.url);
    } else {
      let type = item.type.split('_');
      onEntityPreview(item.contentId, {
        name: item.name,
        type: type[type.length - 1],
      }, item);
    }
    setSeecontentid(item.tempid);
    setCurrentvideotype(item.type == 'reference' ? 2 : 1);
    if (item.type == 'biz_sobey_document' && perviewtype == 2 && !finishresource.includes(item.tempid) && location.query.isJoin == 'true') {
      addResourcelearning(item.tempid);
    }
  };

  return (
    <>
      <Tabs
        defaultActiveKey="1"
        activeKey={activeKey}
        onChange={e => {
          setActiveKey(e);
          if (perviewtype != 0 && courseid && e == '1') {
            getRate();
          }
          if (e === '5') {
            getknowledrecommend();
          }
        }}
      >
        <TabPane tab={t('知识点详情')} key="1">
          <div className="rdrawer">
            <div className="drawer_view">
              {/* <div className="options_view">
                     <Button type="primary" icon={<IconFont type="iconjiaohuan" />} onClick={() => getbianxiAll(selectNode.id)}>对比辨析</Button>
                     <Button type="primary" icon={<IconFont type="icondaochu1" />} style={{ marginLeft: '20px' }} onClick={()=>{}}>关联节点</Button>
                 </div> */}
              {perviewtype != 0 &&
                courseid &&
                location.pathname != '/perviewemap' && (
                  <div className="rate_box">
                    {
                      !isMicroMajor && (
                        <div className="left_view">
                      <ProgressBox
                        label="目标达成度"
                        data={
                          Number(rate.nodeAttainment) > 100
                            ? '100'
                            : Number(rate.nodeAttainment)
                        }
                        color="#549CFF"
                      ></ProgressBox>
                    </div>
                      )
                    }
                    <div className="left_view">
                      <ProgressBox
                        label="完成率"
                        data={
                          Number(rate.finishRate) > 100
                            ? '100'
                            : Number(rate.finishRate)
                        }
                        color="#44D6E5"
                      ></ProgressBox>
                    </div>
                    <div className="left_view">
                      <ProgressBox
                        label="掌握率"
                        data={
                          Number(rate.masterRate) > 100
                            ? '100'
                            : Number(rate.masterRate)
                        }
                        color="#F3B764"
                      ></ProgressBox>
                    </div>
                  </div>
                )}

              {perviewtype != 0 && location.pathname != '/perviewemap' && (
                <div
                  className="divider_dashed"
                  style={{ marginTop: '5px' }}
                ></div>
              )}

              <div className="title">
                <span
                  className="span1"
                  onClick={() => {
                    centerednode(selectNode.id);
                  }}
                  title={selectNode ? selectNode.label : t('暂无名称')}
                >
                  {selectNode ? selectNode.label : t('暂无名称')}
                </span>
              </div>
              <TagSpan key={selectNode?.id} nodedata={selectNode} />
              {!showSearch && article[x6node?.id] && (
                <div className="other_view">
                  <div className="redio_view"></div>
                  <span className="other_title">教材原文</span>
                  <div className="article">{article[x6node?.id]}</div>
                </div>
              )}
              {showSearch && parameterConfig.serialNumber == 'true' && (
                <div className="number_view">
                  <div className="left_view">
                    <span>{t('编号：')}</span>
                    {editlabel ? (
                      <>
                        <Input
                          style={{ width: '60%' }}
                          value={serialNumber}
                          defaultValue={selectNode?.serialNumber || ''}
                          placeholder={t('请输入编号')}
                          onChange={(e: any) => setSerialNumber(e.target.value)}
                        />
                        <CheckCircleOutlined
                          style={{ fontSize: '22px', marginLeft: '10px' }}
                          onClick={verificationSerialNum}
                        />
                        <CloseCircleOutlined
                          style={{ fontSize: '22px', marginLeft: '10px' }}
                          onClick={() => setEditlabel(false)}
                        />
                      </>
                    ) : (
                      <>
                        <span style={{ color: '#000000' }}>
                          {selectNode?.serialNumber || ''}
                        </span>
                        {/* 修改按钮 */}
                        {isedit && (
                          <EditOutlined
                            style={{ fontSize: '18px', marginLeft: '10px' }}
                            onClick={() => {
                              setEditlabel(true);
                              setSerialNumber(selectNode?.serialNumber || '');
                            }}
                          />
                        )}
                      </>
                    )}
                  </div>
                  <span className='node_createuser'>{(selectNode?.createuser!='' && selectNode?.createuser) ? `创建人：${selectNode?.createuser}`:''}</span>
                </div>
              )}

                <div className='number_view'>
                    <div className='left_view'>
                      <span>属性：</span>
                      {
                          isedit ?
                          <Select
                            placeholder='请选择'
                            style={{ width: 100 }}
                            onChange={(e)=>{
                              setSelectNode({
                                ...selectNode,
                                attribute:e
                              })
                              updatanode(selectNode.id,{
                                attribute:e,
                                status:1
                              })
                            }}
                            value={selectNode?.attribute || null}
                            allowClear
                            options={[
                              {
                                value: '记忆',
                                label: '记忆',
                              },
                              {
                                value: '理解',
                                label: '理解',
                              },
                              {
                                value: '应用',
                                label: '应用',
                              },
                              {
                                value: '分析',
                                label: '分析',
                              },
                              {
                                value: '评价',
                                label: '评价',
                              },
                              {
                                value: '创新',
                                label: '创新',
                              },
                            ]}
                          />:
                          <span>{selectNode?.attribute || ''}</span>
                      }
                    </div>
                    <div className='left_view'>
                      <span>参考学时：</span>
                      {isedit ? <InputNumber style={{width:'80px'}} min={0} value={selectNode?.referenceStudyTime || null} defaultValue={0} onChange={(e)=>{
                        setSelectNode({
                          ...selectNode,
                          referenceStudyTime:e
                        })
                        updatanode(selectNode.id,{
                          referenceStudyTime:e,
                          status:1
                        })
                      }} />: <span>{selectNode?.referenceStudyTime}小时</span> }
                    </div>
                    <span className='node_status'>状态：{selectNode?.status == 0 ? '未编辑' : '已编辑'}</span>
                </div>
              {/* 智能详解  一键引用 */}
              {isedit ? (
                <div className='options_box'>
                  {// 判断是否有权限
                   parameterConfig.Intelligent_explanation == 'true' ? (
                    <Button
                      size="small"
                      type="primary"
                      style={{ marginRight: '10px' }}
                      icon={<Icon component={chatgpticon}></Icon>}
                      onClick={() => getGptdetail(selectNode.id)}
                      loading={intelligentloading}
                    >
                      {t('智能详解')}
                    </Button>
                  ) : null}

                  {parameterConfig.reference == 'true'&& <Button
                    size="small"
                    type="primary"
                    onClick={() => onOneKeyQuote()}
                  >
                    {t('一键引用')}
                  </Button>}
                </div>
              ) : null}
              <div className="detail">
                {(isedit && selectNode) ? (                 
                    // <WangEditor key={selectNode.id} value={selectNode.explanation} nodeid={selectNode.id} onHtmlChange={(e:string)=>{
                    //   setExplanationContent(e);
                    // }} />
                    <TinymceEditor id={selectNode.id} value={selectNode.explanation} nodeid={selectNode.id} onChange={(e:string)=>{
                      setExplanationContent(e);
                    }} />
                ) : (
                  // <p>{selectNode?.explanation}</p>
                  // 这里是我自己截取拼接的字段 来源是安全的 所以不用xss过滤
                  <div>
                    <RenderHtml onClick={(e:any)=>{
                      if(e.target.tagName == 'SPAN' && e.target.dataset.id){
                        centerednode(e.target.dataset.id)
                      }else if(e.target.tagName == 'IMG'){
                        setPreviewImageSrc(e.target.src);
                        setPreviewImageVisible(true);
                      }
                    }}
                      dangerouslySetInnerHTML={{ __html: explanation }}
                    ></RenderHtml>
                    <Image
                      style={{ display: 'none' }}
                      src={previewImageSrc}
                      preview={{
                        visible: previewImageVisible,
                        src: previewImageSrc,
                        onVisibleChange: (value) => {
                          setPreviewImageVisible(value);
                        },
                      }}
                    />
                  </div>
                )}
              </div>
              {showSearch && target[selectNode?.id] && (
                <div
                  className="divider_dashed"
                  style={{ marginTop: '5px' }}
                ></div>
              )}
              {
                // showSearch && target[selectNode?.id] &&
                parameterConfig.show_curriculum_objectives == 'true' &&<div id="course_goal">
                  {isMicroMajor ? <MicroMajorGoals sm={location.query?.sm} courseId={location.query?.id} planId={location.query?.planId || microCourseInfo?.planId} mapId={mapid} nodeId={x6node?.id} /> : (
                     <>
                     {/* 添加 showGoal 条件判断 */}
                     {showGoal && (
                       <>
                         <div className="title">
                           <span className="span1" style={{fontWeight:'initial'}}>课程目标</span>
                           <div style={{ flex: 1, textAlign: 'right' }}>
                             {isedit ? (
                               <Button
                                 loading={modalLoading}
                                 size="small"
                                 type="primary"
                                 onClick={() => {
                                   if (!!couresSyllabusCode) {
                                     setShowModalGoal('related');
                                   } else {
                                     setShowModalGoal('syllabus');
                                   }
                                 }}
                               >
                                 {t('添加')}
                               </Button>
                             ) : null}
                           </div>
                         </div>
                         <div className="target_list">
                           {goalList?.map((item, index) => (
                             <div className="target_item" key={index}>
                               <div className="text">{item?.targetName || '目标'}</div>
                               <div className="desc">
                                 <TextMore rows={1} text={item?.description}></TextMore>
                               </div>
                             </div>
                           ))}
                         </div>
                         <div
                           className="divider_dashed"
                           style={{ marginTop: '5px' }}
                         ></div>
                       </>
                     )}
                   </>
                  )}
                </div>
              }
              <div className="video_view">
                <div className="redio_view"></div>
                <span className="title_span">{t('绑定资源')}</span>
                <div style={{ flex: 1, textAlign: 'right' }}>
                  {isedit && (
                    <AddResourceDropdown
                      loading={modalLoading}
                      onAddFromLibrary={() => showModal('bindresource')}
                    />
                  )}
                </div>
              </div>
              {/* 预览资源 */}
              {currentvideotype == 1 &&<ResourcePreview
                loading={entityLoading}
                previewEntity={previewEntity}
                visible={visible}
                currentvideotype={currentvideotype}
                selectNode={selectNode}
                centerednode={centerednode}
                onFinish={() => {
                  if (perviewtype == 2 && location.query.isJoin == 'true') {
                    addResourcelearning();
                  }
                }}
                type={1}
              />}
              <ResourceList
                dataSource={selectNode?.bindresource || []}
                onItemClick={(item) => {
                  handleResourceClick(item);
                }}
                onDelete={removevideobyid}
                showFinishStatus={perviewtype == 2 && location.query.isJoin == 'true'}
                finishResource={finishresource}
                isedit={isedit}
              />
              <AddLink selectNode={selectNode} updatanode={updatanode} x6node={x6node} isedit={isedit} setSelectNode={setSelectNode} />

              <div className="video_view">
                <div className="redio_view"></div>
                <span className="title_span">{t('参考资料')}</span>
                <div style={{ flex: 1, textAlign: 'right' }}>
                  {isedit && (
                    <AddResourceDropdown
                      loading={modalLoading}
                      onAddFromLibrary={() => showModal('referenceMaterials')}
                      type="reference"
                    />
                  )}
                </div>
              </div>

              {/* 预览资源 */}
              {currentvideotype == 2 && <ResourcePreview
                loading={entityLoading}
                previewEntity={previewEntity}
                visible={visible}
                currentvideotype={currentvideotype}
                selectNode={selectNode}
                centerednode={centerednode}
                onFinish={() => {
                  if (perviewtype == 2 && !finishresource.includes(seecontentid) && location.query.isJoin == 'true') {
                    addResourcelearning();
                  }
                }}
                type={2}
              />}

              <ResourceList
                dataSource={selectNode?.referenceMaterials || []}
                onItemClick={(item) => {
                  handleResourceClick(item);
                }}
                onDelete={removeReference}
                showFinishStatus={perviewtype == 2 && location.query.isJoin == 'true'}
                finishResource={finishresource}
                isedit={isedit}
                type="reference"
              />
              {referencedknowledge.length ? (
                <>
                  <div className="other_view">
                    <div className="redio_view"></div>
                    <span className="other_title">{t('引用知识点')}</span>
                    {referencedknowledge?.map((item: any, index: number) => {
                      console.log('item.id',item);
                      return (
                        <div
                          className="other_item"
                          key={index}
                          onClick={() => {
                            centerednode(item?.data?.id);
                          }}
                        >
                          <Tooltip title={getlevelable(item)}>  
                            <span className="span_name">
                              {item.store.data.data.label}
                            </span>
                          </Tooltip>

                          {isedit ? (
                            <CloseOutlined
                              className="icon_delete"
                              onClick={e => deleteQuote(e, index)}
                            />
                          ) : null}
                        </div>
                      );
                    })}
                    {referencedknowledge.length && isedit ? (
                      <Button
                        onClick={() => {
                          setReferencedknowledge([]);
                          setSelectNode({
                            ...selectNode,
                            quoteKnowledge: [],
                            temp: new Date().getTime(),
                          });
                          updatanode(x6node.id, {
                            // ...selectNode,
                            quoteKnowledge: [],
                          });
                        }}
                        icon={
                          <IconFont
                            title={t('清空引用')}
                            style={{ fontSize: '14px' }}
                            type="iconhuishouzhan-huise"
                          />
                        }
                        style={{
                          marginLeft: '10px',
                          height: '28px',
                          marginTop: '5px',
                        }}
                        size="small"
                        type="primary"
                        shape="round"
                        danger
                      >
                        {t('清空引用')}
                      </Button>
                    ) : null}
                  </div>
                </>
              ) : null}

              {preorder?.length ? (
                <>
                  <div
                    className="divider_dashed"
                    style={{ marginTop: '5px' }}
                  ></div>
                  <div className="other_view">
                    <div className="redio_view"></div>
                    <span className="other_title">{t('前序知识点')}</span>
                    {preorder?.map((item: any, index: number) => {
                      return (
                        <div
                          className="other_item"
                          key={index}
                          onClick={() => {
                            centerednode(item.id);
                          }}
                        >
                          <span className="span_name">
                            {item.store.data.data.label}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </>
              ) : null}

              {nextorder?.length ? (
                <>
                  <div
                    className="divider_dashed"
                    style={{ marginTop: '5px' }}
                  ></div>
                  <div className="other_view">
                    <div className="redio_view"></div>
                    <span className="other_title">{t('后续知识点')}</span>
                    {nextorder?.map((item: any, index: number) => {
                      return (
                        <div
                          className="other_item"
                          key={index}
                          onClick={() => {
                            centerednode(item.id);
                          }}
                        >
                          <span className="span_name">
                            {item.store.data.data.label}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </>
              ) : null}
              {/* 包含 */}
              {comprisingknowledge?.length ? (
                <>
                  <div
                    className="divider_dashed"
                    style={{ marginTop: '5px' }}
                  ></div>
                  <div className="other_view">
                    <div className="redio_view"></div>
                    <span className="other_title">{t('包含知识点')}</span>
                    {comprisingknowledge?.map((item: any, index: number) => {
                      return (
                        <div
                          className="other_item"
                          key={index}
                          onClick={() => {
                            centerednode(item.id);
                          }}
                        >
                          <span className="span_name">
                            {item.store.data.data.label}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </>
              ) : null}
              {/* 等价 */}
              {equivalentknowledge?.length ? (
                <>
                  <div
                    className="divider_dashed"
                    style={{ marginTop: '5px' }}
                  ></div>
                  <div className="other_view">
                    <div className="redio_view"></div>
                    <span className="other_title">{t('等价知识点')}</span>
                    {equivalentknowledge?.map((item: any, index: number) => {
                      return (
                        <div
                          className="other_item"
                          key={index}
                          onClick={() => {
                            centerednode(item.id);
                          }}
                        >
                          <span className="span_name">
                            {item.store.data.data.label}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </>
              ) : null}
              {/* 关联 */}
              {associatedknowledge?.length ? (
                <>
                  <div
                    className="divider_dashed"
                    style={{ marginTop: '5px' }}
                  ></div>
                  <div className="other_view">
                    <div className="redio_view"></div>
                    <span className="other_title">{t('关联知识点')}</span>
                    {associatedknowledge?.map((item: any, index: number) => {
                      return (
                        <div
                          className="other_item"
                          key={index}
                          onClick={() => {
                            centerednode(item.id);
                          }}
                        >
                          <span className="span_name">
                            {item.store.data.data.label}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </>
              ) : null}
            </div>
            {showSearch && modalVisible && (
              <ResourceModal
                currentname={selectNode ? selectNode.label : ''}
                treeData={modalTreeData}
                visible={modalVisible}
                onConfirm={(e: any) => {
                  if (addtype == 'bindresource') {
                    addrecouse(e);
                  } else if (addtype == 'referenceMaterials') {
                    addreferenceMaterials(e);
                  }
                }}
                PointConfirm={(e: any) => {
                  addpoint(e);
                }}
                CanvasConfirm={(e: any) => {
                  addrecouse(e);
                }}
                DocumentConfirm={(e: any) => {
                  adddocumentpoint(e);
                }}
                showPoint={true}
                onCancel={() => setModalVisible(false)}
                onShowDetail={(id, detail) => {
                  setEntityPreview({
                    id: id,
                    name: detail.name,
                    type: detail.type,
                  });
                  setEntityModalVisible(true);
                }}
                // fileType={['biz_sobey_video','biz_sobey_audio','biz_sobey_picture','biz_sobey_document']}
                multi={true}
              />
            )}

            {/* 资源预览modal */}
            <ResourcePreviewModal
              modalVisible={entityModalVisible}
              modalClose={() => setEntityModalVisible(false)}
              resource={entityPreview}
            />

            <Modal
              width={'70%'}
              title={t('文档预览')}
              visible={perviewvisit}
              className="document_modal_wrap"
              onCancel={() => setPerviewvisit(false)}
              footer={null}
            >
              <div style={{ height: '65vh' }}>
                {perviewsrc != '' && perviewsrc ? (
                  <Entity
                    src={perviewsrc}
                    type="document"
                    onListener={(e: string) => {
                      // 只有在学生端才会有学习记录
                      if (
                        e == 'ended' &&
                        perviewtype == 2 &&
                        location.query.isJoin == 'true'
                      ) {
                        addResourcelearning();
                      }
                    }}
                  ></Entity>
                ) : null}

                {/* <Entity src={'http://172.16.151.202/bucket-z/u-cwcc268v239qk92m/document/2022/12/19/f9981dc0b2324739a94d43d1a8130f02/0/知了高校智慧教育平台解决方案 V2·1 1208.pptx'} type="document"></Entity> */}
              </div>
            </Modal>

            {/* 选择题目 excludeQuestionTypes-排除不需要的题目类型，0:单选,1:多选,2:填空,3:主观问答 4：判断，多个类型使用英文逗号分割 */}
            {addtopicvisible ? (
              <TopicSelectModal
                currentname={selectNode ? selectNode.label : ''}
                selectKeys={homework}
                visible={addtopicvisible}
                type={'checkbox'}
                disabled={[3]}
                onConfirm={e => {
                  let arr = selectNode.homework || [];
                  if (arr && arr.length) {
                    // 去重
                    let newarr = e.filter((item: any) => {
                      let flage = true;
                      arr.forEach((item2: any) => {
                        if (item.id == item2.id) {
                          flage = false;
                        }
                      });
                      return flage;
                    });
                    arr = [...arr, ...newarr];
                    if (newarr.length == 0) {
                      message.warning(t('试题重复添加！'));
                      return;
                    }
                  } else {
                    arr = e;
                  }

                  // 为每个试题添加字数统计
                  const questionsWithCount = arr.map((item: any) => ({
                    ...item,
                    questionsNumber: getContentLength(item.questions_content)
                  }));

                  setSelectNode({
                    ...selectNode,
                    homework: questionsWithCount,
                    temp: new Date().getTime(),
                  });
                  updatanode(x6node.id, {
                    homework: questionsWithCount,
                  });
                  setAddtopicvisible(false);
                  addquestion(questionsWithCount);
                }}
                iscoursemap={true}
                onclose={() => setAddtopicvisible(false)}
                onAdd={() => {
                  window.open(
                    `${window.location.origin}/exam/#/exam/topicManage`,
                  );
                }}
              />
            ) : null}
            <AutomaticProblem
              currentname={selectNode ? selectNode.label : ''}
              selectKeys={homework}
              visible={addtopicvisibleAi}
              disabled={[3]}
              onConfirm={(e, x) => {
                let arr = selectNode.homework || [];
                console.log(e);
                console.log(nodeinfo);

                let param: any = [];
                e.map((item: any) => {
                  let arr: any = {
                    knowledge_points: [
                      {
                        entity: nodeinfo.entity,
                        entity_id: nodeinfo.entity_id,
                        mapId: nodeinfo.mapId,
                        mapName: nodeinfo.mapName,
                        nodeId: nodeinfo.nodeId,
                        parentNode: nodeinfo.parentNode,
                        propertyValue: nodeinfo.propertyValue,
                      },
                    ],
                    hasAttachment: [],
                    questions_type: x,
                    questions_analysis: `<p>${item.analysis}</p>`,
                    questions_answers: [
                      x == 4 ? (item.answer[0] ? 'A' : 'B') : item.answer[0],
                    ],
                    questions_content: `<p>${item.content}</p>`,
                    labels: [],
                    fileList: [],
                  };
                  if (x == 1 || x == 0) {
                    param.push({
                      ...arr,
                      questions_options: item.options.map(
                        (i: any, index: number) => {
                          return {
                            seq: index + 1,
                            content: `<p>${i.slice(2)}</p>`,
                          };
                        },
                      ),
                    });
                  } else if (x == 2) {
                    param.push({
                      ...arr,
                      questions_options: item.answer.map(
                        (i: any, index: number) => {
                          return {
                            seq: index + 1,
                            content: `<p>${i.content}</p>`,
                            answerType: 1,
                            answerRange: null,
                            answerMax: null,
                            answerMin: null,
                          };
                        },
                      ),
                    });
                  } else if (x == 3) {
                    param.push({
                      ...arr,
                      questions_options: [],
                    });
                  } else {
                    param.push({
                      ...arr,
                      questions_options: [
                        {
                          seq: 1,
                          content: '正确',
                        },
                        {
                          seq: 2,
                          content: '错误',
                        },
                      ],
                    });
                  }
                });
                examinationAdd(param).then((res: any) => {
                  console.log(res.data);
                  console.log(arr);
                  if (arr && arr.length) {
                    // 去重
                    let newarr = res.data.filter((item: any) => {
                      let flage = true;
                      arr.forEach((item2: any) => {
                        if (item.id == item2.id) {
                          flage = false;
                        }
                      });
                      return flage;
                    });
                    arr = [...arr, ...newarr];
                    if (newarr.length == 0) {
                      message.warning(t('试题重复添加！'));
                      return;
                    }
                  } else {
                    arr = res.data;
                  }
                  setSelectNode({
                    ...selectNode,
                    homework: arr,
                    temp: new Date().getTime(),
                  });
                  updatanode(x6node.id, {
                    // ...selectNode,
                    homework: arr,
                  });
                  setAddtopicvisibleAi(false);
                  addquestion(arr);
                });
              }}
              onclose={() => setAddtopicvisibleAi(false)}
            />
          </div>
        </TabPane>
        {showSearch && (
            <TabPane tab={t('试题')} key="3">
              <div className="rdrawer">
                <div className="drawer_view">
                  {homeworkloading && (
                    <Spin
                      style={{
                        width: '100%',
                        height: '300px',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                      size="large"
                    />
                  )}
                  <div className="video_view" style={{ marginTop: '-10px' }}>
                    {/* <div className="redio_view"></div> */}
                    {/* <span className="title_span">试题</span> */}
                    <div style={{ flex: 1, textAlign: 'right' }}>
                      {!homeworkloading && selectNode?.homework?.length ? (
                        <Button
                          size="small"
                          type="primary"
                          onClick={e => {
                            if (!isseeanswer) {
                              // 学生端 并且 不是管理员角色
                              // if (
                              //   perviewtype == 2 &&
                              //   location.query.isJoin == 'true'
                              // ) {
                              //   submitanswer();
                              // } else {
                              //   setIsseeanswer(!isseeanswer);
                              // }
                              //修改原因： 填空题新增了范围填空，需要后端进行校验
                              submitanswer();
                            } else {
                              setIsseeanswer(!isseeanswer);
                            }
                          }}
                          style={{ marginRight: '10px' }}
                        >
                          {isseeanswer ? t('重新答题') : t('查看答案')}
                        </Button>
                      ) : null}
                      {isedit ? (
                        <Dropdown menu={{ items:questionsitems }} placement="bottomRight">
                          <Button size="small" type="primary">
                            {t('添加')}
                          </Button>
                        </Dropdown>
                      ) : null}
                    </div>
                  </div>
                  {!homeworkloading &&
                    selectNode?.homework?.map((item: any, index: number) => {
                      return (
                        <div className="topic_box" key={index}>
                          {isedit ? (
                            <IconFont
                              title={t('删除题目')}
                              className="remove_topic"
                              onClick={e => removeQuestion(e, item, index)}
                              style={{
                                color: 'var(--primary-color)',
                                fontSize: '16px',
                              }}
                              type="iconhuishouzhan-huise"
                            />
                          ) : null}

                          <div style={{ width: '95%' }}>
                            <HomeworkSubItem
                              score={0}
                              showscore={false}
                              key={index}
                              isEnd={isseeanswer}
                              files={null}
                              canEdit={true}
                              openParse={true}
                              index={index + 1}
                              data={item}
                              fromType=""
                              answer={useranswer[item.id]}
                              isCorrect={correctionMap?.[item.id]?.map(
                                (cell: any) => cell?.correct,
                              )}
                              onChange={e => {
                                setUseranswer((pre:any) => {
                                  let obj = {...pre}
                                  obj[item.id] = e;
                                  return obj;
                                });
                              }}
                              comment=""
                              onPreview={() => {}}
                              onDownload={handleDownload}
                              onImagePreview={() => {}}
                            />
                          </div>
                        </div>
                      );
                    })}
                  {selectNode?.homework?.length == 0 && (
                    <Empty
                      style={{ marginTop: 50 }}
                      description={t('暂无试题')}
                    ></Empty>
                  )}
                </div>
              </div>
            </TabPane>          
        )}
        {parameterConfig.forum_display == 'true' &&
        perviewtype != 0 &&
        courseid &&
        location.pathname != '/perviewemap' ? (
          <TabPane tab={t('问答')} key="4">
            <div className="rdrawer">
              {questionvisible == 1 ? (
                <div className="drawer_view">
                  <div className="options_view">
                    <Button
                      type="primary"
                      icon={<IconFont type="iconbiji" />}
                      onClick={() => setQuestionvisible(2)}
                    >
                      {t('发起提问')}
                    </Button>
                  </div>
                  <div className="detail_view">
                    <CourseQA
                      ismap={true}
                      extend_link_id={selectNode?.id}
                      extend_type={(() => {
                        if (location.query.type == 'map') {
                          return '11';
                        } else if (location.query.type == 'mooc') {
                          return '03';
                        } else if (location.query.type == 'spoc') {
                          return '01';
                        } else if (location.query.type == 'training') {
                          return '05';
                        } else if (location.query.type == 'microMajor') {
                          return '14';
                        }
                      })()}
                      showdetail={(e: any) => {
                        setTodetail(e);
                        setQuestionvisible(3);
                      }}
                    ></CourseQA>
                  </div>
                </div>
              ) : (
                ''
              )}

              {questionvisible == 2 ? (
                <div className="drawer_view">
                  <Askquestions
                    mapid={mapid}
                    courseid={courseid}
                    perviewtype={perviewtype}
                    selectNode={selectNode}
                    coursename={coursename}
                    extend_type={(() => {
                      if (location.query.type == 'map') {
                        return '11';
                      } else if (location.query.type == 'mooc') {
                        return '03';
                      } else if (location.query.type == 'spoc') {
                        return '01';
                      } else if (location.query.type == 'training') {
                        return '05';
                      } else if (location.query.type == 'microMajor') {
                        return '14';
                      }
                    })()}
                    onback={() => {
                      setQuestionvisible(1);
                    }}
                  ></Askquestions>
                </div>
              ) : (
                ''
              )}

              {questionvisible == 3 ? (
                <div className="drawer_view">
                  <CourseQAdetail
                    ismap={true}
                    topicid={todetail.topicid}
                    onback={() => {
                      setQuestionvisible(1);
                    }}
                  ></CourseQAdetail>
                </div>
              ) : (
                ''
              )}
            </div>
          </TabPane>
        ) : (
          ''
        )}
        {showSearch &&  <TabPane tab={t('对比辨析')} key="2">
            <Comparativeanalysis
              key="Comparativeanalysis"
              graph={graph}
              selectnode={x6node}
              mapid={mapid}
              perviewtype={perviewtype}
              updatanodecompare={updatanodecompare}
              visible={4}
              knowledgeDiscriminateList={knowledgeDiscriminateList}
              onChange={discriminateListChange}
            />
          </TabPane>
        }
        {parameterConfig.recommend_display == 'true' && !isedit && <TabPane tab={t('推荐')} key="5">
          <div className="rdrawer">
            <div className="drawer_view">
              {recommendloading && (
                <Spin
                  style={{
                    width: '100%',
                    height: '300px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  size="large"
                />
              )}
              <div className="video_view" style={{ marginTop: '-10px' }}>
                {/* <div className="redio_view"></div> */}
                {/* <span className="title_span">试题</span> */}
                <div style={{ flex: 1, textAlign: 'right' }}>
                  {!recommendloading && selectNode?.recommend?.length ? (
                    <Button
                      size="small"
                      type="primary"
                      onClick={e => {
                        if (!isseerecommendanswer) {
                          // 学生端 并且 不是管理员角色
                          if (
                            perviewtype == 2 &&
                            location.query.isJoin == 'true'
                          ) {
                            submitrecommendanswer();
                          } else {
                            setIsseerecommendanswer(!isseerecommendanswer);
                          }
                        } else {
                          setIsseerecommendanswer(!isseerecommendanswer);
                        }
                      }}
                      style={{ marginRight: '10px' }}
                    >
                      {isseerecommendanswer ? t('重新答题') : t('查看答案')}
                    </Button>
                  ) : null}
                  {/* {isedit ? (
                  <Dropdown menu={{ items }} placement="bottomRight">
                    <Button
                      size="small"
                      type="primary"
                    >
                      {t('添加')}
                    </Button>
                  </Dropdown>
                ) : null} */}
                </div>
              </div>
              {!recommendloading &&
                selectNode?.recommend?.map((item: any, index: number) => {
                  return (
                    <div className="topic_box" key={index}>
                      {/* {isedit ? (
                      <IconFont
                        title={t('删除题目')}
                        className="remove_topic"
                        onClick={e => removeQuestion(e, item, index)}
                        style={{
                          color: 'var(--primary-color)',
                          fontSize: '16px',
                        }}
                        type="iconhuishouzhan-huise"
                      />
                    ) : null} */}

                      <div style={{ width: '95%' }}>
                        <HomeworkSubItem
                          score={0}
                          showscore={false}
                          key={index}
                          isEnd={isseerecommendanswer}
                          files={null}
                          canEdit={true}
                          openParse={true}
                          index={index + 1}
                          data={item}
                          fromType=""
                          answer={recommendanswer[index]}
                          isCorrect={correctionMap?.[item.id]?.map(
                            (cell: any) => cell?.correct,
                          )}
                          onChange={e => {
                            let arr = cloneDeep(recommendanswer);
                            arr[index] = e;
                            setRecommendanswer(() => {
                              return [...arr];
                            });
                          }}
                          comment=""
                          onPreview={() => {}}
                          onImagePreview={() => {}}
                          onDownload={handleDownload}
                        />
                      </div>
                    </div>
                  );
                })}
              {selectNode?.recommend?.length == 0 && (
                <Empty
                  style={{ marginTop: 50 }}
                  description={t('暂无试题')}
                ></Empty>
              )}
            </div>
          </div>
        </TabPane>}
        {parameterConfig.case_display == 'true' && (
          <TabPane tab={t('案例')} key="6">
            <Case
              mapid={mapid}
              x6node={x6node}
              selectNode={selectNode}
              setSelectNode={setSelectNode}
              isedit={isedit}
              perviewtype={perviewtype}
              finishresource={finishresource}
              updatanode={updatanode}
              createlogdata={createlogdata}
              addlogs={addlogs}
              addResourcelearning={addResourcelearning}
            ></Case>
          </TabPane>
        )}
        {parameterConfig.show_maxkb_tool == 'true' && [288,306,307].includes(Number(mapid))  && (
          <TabPane tab={t('习题助手')} key="7">
            <iframe src={parameterConfig.show_maxkb_url} frameBorder={0} className="xiti_tools"></iframe>
          </TabPane>
        )}
      </Tabs>
      <SynchronizedTeaching
        visible={showModalGoal === 'syllabus'}
        onSuccess={() => {
          setShowModalGoal('');
          handleRefreshMapInfo();
        }}
        onClose={() => setShowModalGoal('')}
        mapId={mapinfo.id}
        checkedId={couresSyllabusCode || ''}
        isEditMap={isEditMap}
      />
      <RelatedOutlineModal
        courseId={location?.query?.id}
        onOk={handleBindOk}
        checkedList={goalList?.map(item => item?.syllabusContentId || '')}
        courseCode={couresSyllabusCode}
        visible={showModalGoal === 'related'}
        onClose={() => {
          setShowModalGoal('');
        }}
        x6nodeId={x6node?.id}
        nodeList={nodeList}
      />
      
      {/* 添加上传文件Modal */}
      <Modal
        title={t(uploadType === 'resource' ? '上传资源' : '上传参考资料')}
        open={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        footer={null}
        width={1000}
        destroyOnClose
      >
        <UploadFile
          onlyVideo={false}
          formData={{
            parent_id: x6node?.id,
            name: selectNode?.label
          }}
          mapid={mapid}
          mapname={mapinfo.mapName}
          courseid={courseid}
          coursename={coursename}
          onSave={handleUploadSuccess}
        />
      </Modal>
    </>
  );
};

export default Rdrawer;
