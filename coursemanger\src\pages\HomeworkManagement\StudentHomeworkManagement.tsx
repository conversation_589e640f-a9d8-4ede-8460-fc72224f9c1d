import React, { FC, useEffect, useState } from 'react';
import { useSelector, useLocation } from "umi";
import "./index.less";
import "./StudentHomeworkManagement.less";
import { Tree, message, Empty, Popover, Tooltip, Spin, Button } from "antd";
import ChapterItem from "./components/ChapterItem";
import StudentHomeworkItem from './components/StudentHomeworkItem';
import chapterApis from "@/api/chapter";
import { getHomeworkSubmitInfo, studentGetMicroMajorHomework, searchTeamPersonList } from "@/api/homework";
import { dealHomeworkTree, initExpandKeys, getCurNode } from "./utils/tools";
import StudentDoHomework from "./StudentDoHomework";
import useLocale from '@/hooks/useLocale';
import { getHomework } from '@/api/micromajor';
import { LoadingOutlined } from '@ant-design/icons';
import { reqModules } from '@/api/teacher';
import statisticsApi from '@/api/statistics';
import moment from 'moment';

const StudentHomeworkManagement: FC = () => {

  const { userInfo } = useSelector<any, any>((state) => state.global);

  const { t } = useLocale();
  const location: any = useLocation();
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [chapter, setChapter] = useState<any[]>([]);
  const [treeData, setTreeData] = useState<any[]>([]);
  const [selectNode, setSelectNode] = useState<any>({});
  const [showPage, setShowPage] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [teamPersonDom, setTeamPersonDom] = useState<any>({
    title: null,
    content: null,
  }); // 小组成员名单
  const type: any = location.query.type

  const insertBase = () => {
    const base = document.createElement("base");
    base.id = "base-target-blank";
    base.target = "_blank";
    document.head.appendChild(base);
  }
  const removeBase = () => {
    const base = document.querySelector("#base-target-blank");
    base?.remove();
  }
  useEffect(() => {
    getChapterTree();
    getChapter();
    insertBase();
    return () => {
      removeBase();
    }
  }, []);

  const titleRender = (node: any) => {
    switch (node.resourseType) {
      case "":
        return <ChapterItem data={node} />;
      case "homework":
        return <StudentHomeworkItem data={node} />;
    }
    return <div></div>;
  };

  const handleToFirst = (isRefresh?: boolean) => {
    if (isRefresh) {
      getChapterTree();
    }
    setShowPage(1);
  };

  /**
       * 获取章节资源树
       *
       */
  const getChapter = () => {
    const id = location.query.id;
    let data = `courseId=${id}`;
    data = data + `&status=2`;
    chapterApis.getChapter(data).then((res: any) => {
      if (res && res.status === 200 && res.data) {
        setChapter(res.data);
      }
    });
  };

  const assembleTreeNode = (nodeInfo: any[], homework: any) => {
    if (!homework) return nodeInfo
    return nodeInfo.map((item: any) => {
      let secondLevelChildren: any[] = []
      for (let obj in homework) {
        if (homework[obj].chapterId == item.nodeId) {
          secondLevelChildren.push({
            ...homework[obj],
            title: <StudentHomeworkItem data={homework[obj]} />,
            key: homework[obj].id,
            className: 'second-level',
            isLeaf: true,
            parentTitle: item.nodeName,
            name: homework[obj].title,
            describe: homework[obj].id
          })
        }
      }

      return {
        title: item.nodeName,
        key: item.nodeId,
        className: 'chapter-item',
        children: secondLevelChildren
      };
    });
  };

  const initOpenKeys: any[] = []
  const handleOpenKeys = (nodeList: any[]) => {
    if (!nodeList.length) return initOpenKeys
    nodeList.forEach(node => {
      initOpenKeys.push(node.key)
      if (node.children && node.children.length) {
        handleOpenKeys(node)
      }
    })
    return initOpenKeys
  }

  const onSelectTreeNode = (selectedKeys: any, info: any) => {
    if (info.node?.isLeaf) {
      setShowPage(2);
      setSelectNode({ ...info.node, describe: info.id });
    }
  }

  const microTitleRender = (node: any) => {
    return node.title;
  };

  const getMicroMajorHomework = async () => {
    setLoading(true)
    try {
      const res1 = await statisticsApi.getMapId({ courseId: location.query.id, isShow: 2, courseSemester: location.query.sm })
      const res2 = await reqModules({ mapId: res1.data.data[0].id })
      const res3 = await studentGetMicroMajorHomework(location.query.id, location.query.sm)
      const treeNode = assembleTreeNode(res2.data, res3.data)
      const openKeys = handleOpenKeys(treeNode)
      setExpandedKeys(openKeys)
      setTreeData(treeNode)
    } catch (error) {
      message.error('查询失败')
    } finally {
      setLoading(false)
    }
  }

  const getCommonCourseHomework = async () => {
    setLoading(true)
    getHomeworkSubmitInfo(location.query.id).then((res: any) => {
      if (res?.status === 200) {
        console.log(res);
        setTreeData(Object.values(res.data) || []);
      } else {
        message.error('查询失败')
      }
    }).finally(() => {
      setLoading(false);
    })
  }

  const getChapterTree = async () => {
    if (type.includes('microMajor')) {
      getMicroMajorHomework()
    } else {
      getCommonCourseHomework()
    }
  };

  // 获取信息
  const findSecectChapterInfo = (data: any[], sectionId: string) => {

    for (const node of data) {
      if (node.describe === sectionId) {
        return node; // 找到匹配的节点，直接返回 id
      }
      if (node.children && node.children.length > 0) {
        const result: any = findSecectChapterInfo(node.children, sectionId); // 递归查找子节点
        if (result) {
          return result; // 一旦在子节点中找到，立即返回 id
        }
      }
    }
    return null; // 如果没有找到，返回 null
  }

  // 提交作业
  const handleSubmitHomework = (info: any) => {
    setShowPage(2);
    const data = findSecectChapterInfo(chapter, info.id);
    setSelectNode({ ...data, describe: info.id, ...info });
  }

  // 查看小组成员
  const showTeamPerson = (record: any) => {
    setTeamPersonDom({
      title: `${record.teamName || '小组'}成员`,
      content: <div><LoadingOutlined /></div>
    });

    // 失败
    const errorPersonDom = {
      title: `${record.teamName || '小组'}成员`,
      content: '查询失败，请稍后重试'
    }
    searchTeamPersonList({ homeId: record.id, userCode: userInfo?.userCode }).then((res: any) => {
      if (res?.status === 200) {
        const title = `${record.teamName || '小组'}成员（${res.data.length}）`

        const content = <div className='team-person-name'>
          {res.data.map((item: any) => (
            <div>{`${item.stuName}(${item.stuCode})`}</div>
          ))
          }
        </div>

        setTeamPersonDom({
          title,
          content
        });
      } else {
        setTeamPersonDom(errorPersonDom);
      }
    }).catch(() => { setTeamPersonDom(errorPersonDom); });
  }

  const getScoreValue = (score: any, closeTime: any) => {
    if (typeof score === 'number') {
      return score;
    } else {
      // 超时
      if (moment().format("yyyy-MM-DD HH:mm:ss") > moment(closeTime).format("yyyy-MM-DD HH:mm:ss")) {
        return 0;
      } else {
        return '--'
      }
    }
  }


  const homeworkListDom = () => {
    return (
      <div className='homework-table-style'>
        {treeData.map((item: any) => (
          <div key={item.id} className='homework-item'>
            <div className='top'>
              <p className='homework-title' onClick={() => { handleSubmitHomework(item) }}>{item.title}</p>
              <div className='action-btn'>
                {item.submitState === 0 ? ( moment().format("yyyy-MM-DD HH:mm:ss") > moment(item.closeTime).format("yyyy-MM-DD HH:mm:ss") ?
                <Tooltip title='查看作业' placement='right'><span style={{ color: 'red', fontSize: 14 }} onClick={() => { handleSubmitHomework(item) }}>已截止</span></Tooltip> :
                <Button onClick={() => { handleSubmitHomework(item) }} type='primary'>待提交</Button>) :
                 <Tooltip title='查看作业' placement="right"><span onClick={() => { handleSubmitHomework(item) }} className='submit-tip'>√ 已提交</span> </Tooltip>}
              </div>
              <div className='top-right'>
                <p className="top-right-content">得分/总分：
                  {/*<span>*/}
                    <span style={{ color: item.submitState === 2 ? 'var(--primary-color)' : '', fontSize: 16 }}>{getScoreValue(item.score ?? '--', item.closeTime)}</span>
                    <span>/{item.totalScore ?? '--'}分</span>
                    {item.submitState === 2 && typeof item.score !== 'number' && <span style={{ fontSize: 12 }}>（待批改）</span>}
                  {/*</span>*/}
                </p>
              </div>
            </div>
            <div className="bottom">
                <div className="bottom-content" style={{ color: 'rgba(0,0,0,0.5)', fontSize: 13, display: 'flex'}}>
                  <span>完成方式：
                    <span style={{ color: 'rgba(0,0,0,0.7)' }}>
                      {item.howIsDone ? (item.howIsDone == 1 ? <span>个人</span> :
                        <Popover placement="rightTop" title={teamPersonDom.title || `${item.teamName || '小组'}成员`} content={teamPersonDom.content || <div><LoadingOutlined /></div>} trigger="click" overlayClassName='team-person-popverstyle'>
                          <span className='team_name' onClick={() => { showTeamPerson(item) }} title='点击查看小组人员'>小组</span>
                        </Popover>) : '-'}
                    </span>
                  </span>
                  <span>截止时间：<span style={{ color: 'rgba(0,0,0,0.7)' }}>{item.closeTime ? moment(item.closeTime).format("yyyy-MM-DD HH:mm:ss") : '-'}</span></span>
                  <span>所属章节：<span style={{ color: 'rgba(0,0,0,0.7)' }}>{item.sectionName || item.chapterName}</span></span>
                </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return <div className='homework-container'>
    {showPage === 1 ? <div className="tree-container">
      <Spin spinning={loading}>
        {treeData.length > 0 ? (
          type.includes('microMajor') ? <Tree
            blockNode
            defaultExpandAll
            expandedKeys={expandedKeys}
            icon={null}
            onExpand={expandKeys => setExpandedKeys((expandKeys as string[]))}
            treeData={treeData}
            titleRender={(node) => microTitleRender(node)}
            onSelect={onSelectTreeNode}
            className="custom-tree"
          /> :
            homeworkListDom()
          // <Tree
          //   blockNode
          //   defaultExpandAll
          //   expandedKeys={expandedKeys}
          //   icon={null}
          //   onExpand={(expandKeys) => { setExpandedKeys((expandKeys as string[])); }}
          //   treeData={treeData}
          //   titleRender={(node) => titleRender(node)}
          //   onSelect={(selectedKeys, info: any) => {
          //     if (info.node.resourseType === "homework") {
          //       setShowPage(2);
          //       setSelectNode(info.node);
          //     }

          //   }} />
        ) :
          <Empty description={`${loading ? t("加载中") : t("暂无数据")}`} />}
      </Spin>

    </div> : <StudentDoHomework homeworkItem={selectNode} handleBack={handleToFirst} />}

  </div>;
};

export default StudentHomeworkManagement;
