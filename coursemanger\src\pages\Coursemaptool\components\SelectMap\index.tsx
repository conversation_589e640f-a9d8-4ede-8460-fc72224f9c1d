import { CheckService } from '@/api/check';
import {
  addquestiontocourse,
  copyselectmap,
  knowledgequote,
  queryKnowledgeList,
  querymap,
  queryminemap,
} from '@/api/coursemap';
import useLocale from '@/hooks/useLocale';
import {
  Button,
  Checkbox,
  Form,
  Input,
  Modal,
  Select,
  Table,
  Tabs,
  Tag,
  Tooltip,
  message,
} from 'antd';
import moment from 'moment';
import React, { FC, useEffect, useState } from 'react';
import { Link, useLocation, useSelector } from 'umi';
import './index.less';
const { confirm } = Modal;

// base64图标
const ZHIPU_ICON =
  'data:image/png;base64,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';

const SelectMap: FC<any> = ({
  mapid,
  courseid,
  perviewtype,
  visible,
  onClose,
  onSuccess,
}) => {
  const parameterConfig = useSelector<any, any>(
    ({ global }) => global.parameterConfig,
  );
  const { t } = useLocale();
  const { Option } = Select;
  const [form] = Form.useForm();
  const [knowledgeresult, setKnowledgeresult] = useState([]);
  const [querypage, setQuerypage] = useState({
    page: 1,
    size: 10,
    total: 0,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [knowledgeId, setKnowledgeId] = useState([]);
  // 加载完毕
  const [loadingover, setLoadingover] = useState<boolean>(false);
  // 检索的添加
  const [searchform, setSearchfrom] = useState<any>({
    search_name: null,
    search_status: null,
  });
  // 知了与知谱的数据
  const [sourceType, setSourceType] = useState('ZHILIAO');
  const [loading, setLoading] = useState<boolean>(false);
  const [confirmisModalOpen, setConfirmisModalOpen] = useState<boolean>(false);
  const [bindtarget, setBindtarget] = useState<boolean>(true)
  const { query }: any = useLocation();
  const isCanvasMinemap = query.router === 'canvasminemap';
  const [columns, setColumns] = useState<any>([
    {
      title: t('名称'),
      dataIndex: 'mapName',
      key: 'mapName',
      align: 'center',
      render: (text: any, record: any) => {
        return (
          <span
            style={{ cursor: 'pointer' }}
            onClick={() => {
              window.open(
                `${window.location.pathname}#/mapv4/editmap?id=${record.id}&key=3`,
              );
            }}
          >
            {text}
          </span>
        );
      },
    },
    {
      title: t('状态'),
      dataIndex: 'isShow',
      key: 'isShow',
      align: 'center',
      render: (text: any, record: any) => {
        if (text == 2) {
          return <Tag color="#f50">{t('已发布')}</Tag>;
        } else {
          return <Tag color="#549cff">{t('未发布')}</Tag>;
        }
      },
    },
    {
      title: t('适用课程'),
      dataIndex: 'courseName',
      key: 'courseName',
      align: 'center',
    },
    {
      title: t('学科专业'),
      dataIndex: 'subjectName',
      key: 'subjectName',
      align: 'center',
    },
    {
      title: t('创建人'),
      dataIndex: 'teacherName',
      key: 'teacherName',
      align: 'center',
    },
    {
      title: t('创建时间'),
      dataIndex: 'createDate',
      key: 'createDate',
      align: 'center',
      render: (text: any) =>text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
  ]);
 
  //重置筛选
  const handleResetQuery = () => {
    setQuerypage({
      page: 1,
      size: 10,
      total: 0,
    });
    // setSearchfrom({
    //   search_name: null,
    //   search_status: null,
    // });
  };
  // 知谱 与 知了 数据切换
  const handleChangeSource = (key: string) => {
    if (key === sourceType) {
      return;
    }
    if(key == 'ZHILIAO'){
      setColumns([
        {
          title: t('名称'),
          dataIndex: 'mapName',
          key: 'mapName',
          align: 'center',
          render: (text: any, record: any) => {
            return (
              <span
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  window.open(
                    `${window.location.pathname}#/mapv4/editmap?id=${record.id}&key=3`,
                  );
                }}
              >
                {text}
              </span>
            );
          },
        },
        {
          title: t('状态'),
          dataIndex: 'isShow',
          key: 'isShow',
          align: 'center',
          render: (text: any, record: any) => {
            if (text == 2) {
              return <Tag color="#f50">{t('已发布')}</Tag>;
            } else {
              return <Tag color="#549cff">{t('未发布')}</Tag>;
            }
          },
        },
        {
          title: t('适用课程'),
          dataIndex: 'courseName',
          key: 'courseName',
          align: 'center',
        },
        {
          title: t('学科专业'),
          dataIndex: 'subjectName',
          key: 'subjectName',
          align: 'center',
        },
        {
          title: t('创建人'),
          dataIndex: 'teacherName',
          key: 'teacherName',
          align: 'center',
        },
        {
          title: t('创建时间'),
          dataIndex: 'createDate',
          key: 'createDate',
          align: 'center',
          render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss') || '-',
        },
      ])
    }else if(key == 'ZHIPU'){
      setColumns([
        {
          title: t('名称'),
          dataIndex: 'mapName',
          key: 'mapName',
          align: 'center',
          render: (text: any, record: any) => {
            return (
              <span
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  window.open(
                    `${window.location.pathname}#/mapv4/editmap?id=${record.id}&key=3`,
                  );
                }}
              >
                {text}
              </span>
            );
          },
        },        
        {
          title: t('适用课程'),
          dataIndex: 'courseName',
          key: 'courseName',
          align: 'center',
        },
        {
          title: t('学科专业'),
          dataIndex: 'subjectName',
          key: 'subjectName',
          align: 'center',
        },
        {
          title: t('创建人'),
          dataIndex: 'teacherName',
          key: 'teacherName',
          align: 'center',
        },
        {
          title: t('同步时间'),
          dataIndex: 'createDate',
          key: 'createDate',
          align: 'center',
          render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss') || '-',
        },
      ])
    }else if(key == 'SHARE'){
      setColumns([
        {
          title: t('名称'),
          dataIndex: 'mapName',
          key: 'mapName',
          align: 'center',
          render: (text: any, record: any) => {
            return (
              <span
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  window.open(
                    `${window.location.pathname}#/mapv4/editmap?id=${record.id}&key=3`,
                  );
                }}
              >
                {text}
              </span>
            );
          },
        },
        {
          title: t('适用课程'),
          dataIndex: 'courseName',
          key: 'courseName',
          align: 'center',
        },
        {
          title: t('学科专业'),
          dataIndex: 'subjectName',
          key: 'subjectName',
          align: 'center',
        },
        {
          title: t('创建人'),
          dataIndex: 'teacherName',
          key: 'teacherName',
          align: 'center',
        },
        {
          title: t('发布时间'),
          dataIndex: 'publishTime',
          key: 'publishTime',
          align: 'center',
          render: (text: any) =>text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-',
        },
      ])
    }

    setSourceType(key);
    handleResetQuery();
    getData(1, 10, key);
  };

  // 监听查询条件变化数据
  useEffect(() => {
    if (loadingover) {
      getData(1, querypage.size);
    }
  }, [searchform]);

  useEffect(() => {
    if (visible == 16) {
      getData(1, querypage.size);
    }
  }, [visible]);

  // 绑定知识图谱
  const bindRelation = () => {
      setLoading(true);
      // 将知谱空间的数据的地图放到前端绑定
      if (sourceType === 'ZHIPU') {
        onSuccess(knowledgeId[0]);
        onClose();
        setConfirmisModalOpen(false);
        return;
      }
      copyselectmap({
        sourMapId: knowledgeId[0],
        targetMapId: mapid,
        //是否同步绑定课程目标
        isBand: bindtarget,
        //课程code，根据地图列表来取
        courseCode: bindtarget ? (selectedRows[0]?.courseCode || '') : '',
        applicableMajorName: selectedRows[0]?.applicableMajorName,
        applicableMajorCode: selectedRows[0]?.applicableMajorCode,
        grade: selectedRows[0]?.trainingGrade,
      }).then((res: any) => {
        if (courseid) {
          const cloneParam: any = {
            isShow: 1,
            courseId: courseid,
            mapId: mapid,
            // mapOldId: knowledgeId[0],
          };
          if (sourceType === 'ZHIPU') {
            cloneParam.graphId = knowledgeId[0];
          } else {
            cloneParam.mapOldId = knowledgeId[0];
          }
          addquestiontocourse([cloneParam]).then(res2 => {
            if(selectedRows[0].courseCode && bindtarget){
              CheckService.addCourseSyllabus({
                courseId:courseid,
                coures_code:selectedRows[0].courseCode,
                coures_cversion:selectedRows[0].couresCversion,
                courseSemester:query.sm,
                applicableMajorName: selectedRows[0]?.applicableMajorName,
                applicableMajorCode: selectedRows[0]?.applicableMajorCode,
                grade: selectedRows[0]?.trainingGrade,
                // trainingPlanId: selectedRows[0]?.trainingPlanId
              }).then((res3:any)=>{
                if(res3.status == 200){
                  message.success(t('同步绑定课程目标成功！'))
                }
                setConfirmisModalOpen(false); 
                setLoading(false);
                onClose();
                onSuccess();
              })
            }else{
              setConfirmisModalOpen(false);
              setLoading(false);
              onClose();
              onSuccess();
            }
          });
        } else {
          knowledgequote({
            mapId: mapid,
            mapOldId: knowledgeId[0],
          }).then(res2 => {
            if(selectedRows[0].courseCode && bindtarget){
              CheckService.bindOutlineByCourseCode({
                id:mapid,
                courseCode:selectedRows[0].courseCode,
                couresCversion:selectedRows[0].couresCversion,
                applicableMajorName: selectedRows[0]?.applicableMajorName,
                applicableMajorCode: selectedRows[0]?.applicableMajorCode,
                grade: selectedRows[0]?.trainingGrade,
                // trainingPlanId: selectedRows[0]?.trainingPlanId
              }).then((res3:any)=>{
                if(res3.status == 200){
                  message.success(t('同步绑定课程目标成功！'))
                }
                setConfirmisModalOpen(false);
                setLoading(false);
                onClose();
                onSuccess();
              })
            }else{
              setConfirmisModalOpen(false);
              setLoading(false);
              onClose();
              onSuccess();
            }
          });
        }
      });
  };

  // 自定义
  const rowSelection: any = {
    onChange: (selectedRowKeys: any, selectedRows: []) => {
      setKnowledgeId(selectedRowKeys);
      setSelectedRows(selectedRows);
    },
    getCheckboxProps: (record: any) => ({
      disabled: record.id == mapid, // Column configuration not to be checked
      name: record.name,
    }),
  };


  // 自定义分页展示页面
  const showTotal = (total: any, range: any) => {
    return t('共{name}条', String(total));
  };

  //页码切换和
  const paginationChange = (current: any, size: any) => {
    getData(current, size);
  };
  // 数据格式化
  const formatData = (data: any) => {
    return data.map((item: any) => ({
      mapName: item.name,
      id: item.graphId,
      teacherName: item.author,
    }));
  };

  const getKnowledgeMapData = (current: number, size: number) => {
    queryKnowledgeList({
      name: searchform?.search_name,
      isShow:
        searchform?.search_status == null ? null : searchform?.search_status,
      page: current,
      size: size,
      sort: true,
    }).then((res: any) => {
      const formatlist = formatData(res?.extend_message || []);

      setKnowledgeresult(formatlist);
      // 数据返回的比较少，暂时没有分页
      setQuerypage({
        page: 1,
        size: 10,
        total: res.data?.extend_message?.length || 0,
      });
      setLoadingover(true);
    });
  };

  //  获取所有知识点
  const getknowledge = (current: number, size: number) => {
    queryminemap({
      name: searchform?.search_name,
      isShow: searchform?.search_status == null ? null : searchform?.search_status,
      page: current,
      size: size,
      sort: true,
    }).then((res: any) => {
      setKnowledgeresult(res.data.results.filter((item:any) => item.id != mapid));
      setQuerypage({
        page: res.data.page,
        size: res.data.size,
        total: res.data.total,
      });
      setLoadingover(true);
    });
  };

  // 获取共享地图
  const getShareMap = (current: number, size: number) => {
    querymap({
      name: searchform?.search_name,
      isShow: 2,
      page: current,
      size: size,
      field:0,
      sort:true
    }).then((res: any) => {
      setKnowledgeresult(res.data.results.filter((item:any) => item.id != mapid));
      setQuerypage({
        page: res.data.page,
        size: res.data.size,
        total: res.data.total,
      });
      setLoadingover(true);
    });
  }

  const getData = (current: number, size: number, type?: string) => {
    if(type === 'ZHILIAO'){
      return getknowledge(current, size);
    }else if(type == 'ZHIPU'){
      return getKnowledgeMapData(current, size)
    }else if(type == 'SHARE'){
      return getShareMap(current, size)
    }else{
      return getknowledge(current, size);
    }
  };

  return (
    <Modal
      title={t('选择课程地图')}
      width={1200}
      visible={visible == 16}
      onOk={()=>{
        if (knowledgeId.length > 0) {
          if(sourceType === 'ZHIPU'){
            confirm({
              title: '引用地图会覆盖当前地图和问题图谱内容，是否确定？',
              onOk() {
                bindRelation();
              }
            });
          }else{
            setConfirmisModalOpen(true);
          }
        }else{
          message.info(t('请选择图谱！'))
        }
      }}
      onCancel={onClose}
      confirmLoading={loading}
    >
      <div className="search_box">
        <Form
          style={{ width: '80%' }}
          form={form}
          layout="inline"
          onFinish={(values: any) => {
            setSearchfrom(values);
          }}
        >
          <Form.Item name="search_name" initialValue={null}>
            <Input
              style={{ width: 200 }}
              placeholder={t('输入地图名称')}
              allowClear
            />
          </Form.Item>
          {sourceType == 'ZHILIAO' && <Form.Item name="search_status" initialValue={null}>
            <Select
              style={{ width: 200 }}
              placeholder={t('请选择状态')}
              allowClear
            >
              <Option value="1">{t('未发布')}</Option>
              <Option value="2">{t('已发布')}</Option>
            </Select>
          </Form.Item>}
          <Form.Item>
            <Button type="primary" ghost htmlType="submit">
              {t('查询')}
            </Button>
          </Form.Item>
          <Form.Item>
            <Button
              htmlType="button"
              onClick={() => {
                form.resetFields();
                setSearchfrom({
                  search_name: null,
                  search_status: null,
                });
              }}
            >
              {t('清空')}
            </Button>
          </Form.Item>
        </Form>
      </div>
      <div className="source-tab">
        <div className="tabs-container">
          <Tabs
            defaultActiveKey="ZHILIAO"
            tabBarGutter={10}
            onChange={handleChangeSource}
          >
            <Tabs.TabPane
              key="ZHILIAO"
              tab={
                <div className="tab-title">
                  {t('我的地图')}
                </div>
              }
            />
            {!isCanvasMinemap && (
              <Tabs.TabPane
                key="SHARE"
                tab={
                  <div className="tab-title">
                    {t('已发布地图')}
                  </div>
                }
              />
            )}
            {!isCanvasMinemap && parameterConfig?.Spectralspace === 'true' && (
              <Tabs.TabPane
                key="ZHIPU"
                tab={
                  <div className="tab-title">
                    <img className="tab-icon" src={ZHIPU_ICON} />
                    {t('知谱空间')}
                  </div>
                }
              />
            )}
          </Tabs>
        </div>
        <Link target="_blank" to="/coursemap/minemap">
          {t('前往课程地图创建')}
        </Link>
      </div>
      <Table
        rowSelection={{
          type: 'radio',
          ...rowSelection,
        }}
        style={{ marginTop: '-16px' }}
        rowKey={(e: any) => e.id}
        dataSource={knowledgeresult}
        columns={columns}
        pagination={{
          pageSize: querypage.size,
          current: querypage.page,
          total: querypage.total,
          showQuickJumper: true,
          showTotal: showTotal,
          onChange: paginationChange,
        }}
        scroll={{ y: 400 }}
      />
      <Modal title={query.router=='canvasminemap' ? "系统提示" :"引用地图会覆盖当前地图内容，是否确定？"} open={confirmisModalOpen} onOk={bindRelation} onCancel={()=>setConfirmisModalOpen(false)} confirmLoading={loading}>
        {
          query.canvasid == undefined ? <Checkbox disabled={sourceType === 'ZHIPU'} value={bindtarget} defaultChecked={true} onChange={(e)=>setBindtarget(e.target.checked)}>同步绑定课程目标</Checkbox> :'此操作会覆盖当前地图内容，可在历史记录进行恢复'
        }        
      </Modal>
    </Modal>
  );
};

export default SelectMap;
