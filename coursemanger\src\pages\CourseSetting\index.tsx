import baseInfo from '@/api/baseInfo';
import { getteacherlist } from '@/api/teacher';
import { IconFont } from '@/components/iconFont';
import {
  CaretDownOutlined,
  CaretRightOutlined,
  CheckOutlined,
  CloseOutlined,
  EyeFilled,
  EyeInvisibleFilled,
  PlusOutlined,
} from '@ant-design/icons';
import {
  Button,
  Image,
  Input,
  Popconfirm,
  Radio,
  Select,
  Space,
  Tabs,
  Tooltip,
  message,
} from 'antd';
import { debounce } from 'lodash';
import React, { FC, useEffect, useMemo, useRef, useState } from 'react';
import {
  SortableContainer,
  SortableElement,
  SortableHandle,
  arrayMove,
} from 'react-sortable-hoc';
import {
  IGlobalModelState,
  Prompt,
  useDispatch,
  useLocation,
  useSelector,
} from 'umi';
import BaseInfo, { IBaseInfoRef } from '../BaseInfo';
import LearningSettings, { ILearningRef } from '../LearningSettings';
import MoocBaseInfo, { IMoocBaseInfoRef } from '../MoocBaseInfo';
import MoocTeachTeam from '../MoocTeachTeam';
import TeachingTeam from '../TeachingTeam';
import Editor from './components/Editor';
import TeachTeamList from './components/TeachTeamList';
import './index.less';
// import CourseOpSetting from "./components/CourseOpSetting";
import { addLog } from '@/api/addCourse';
import useLocale from '@/hooks/useLocale';
import usePermission from '@/hooks/usePermission';
import Syllabus from '@/pages/Syllabus';
import { CUSTOMER_NPU, CUSTOMER_SHTECH, typeEn } from '@/permission/moduleCfg';
import { getSensitiveWord } from '@/utils';
import CourseOpSetting from './components/CourseOpSetting';

const DragHandle = SortableHandle(() => (
  <IconFont type="iconsortnew" style={{ cursor: 'grab' }} />
));

const handleDealList = (value: any, key: string, list: any, cur: any) => {
  return list.map((item_: any) => {
    if (item_.value === cur.value) {
      return {
        ...item_,
        [key]: value,
      };
    } else {
      return item_;
    }
  });
};

const ListItem = SortableElement<any>((props: any) => {
  const dispatch = useDispatch();
  const { canPageEdit } = useSelector<Models.Store, any>(
    state => state.moocCourse,
  );
  const {
    data,
    content,
    afterToEdit,
    handleEdit,
    editStateChange,
    handleExpand,
    onDelete,
    handleVisible,
  } = props;
  const [isExpand, setIsExpand] = useState<boolean>(true);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [inputValue, setInputValue] = useState<string>('');
  const { t } = useLocale();
  useEffect(() => {
    setInputValue(data.name);
  }, [data.name]);
  useEffect(() => {
    if (data.toEdit) {
      setIsEdit(data.toEdit);
      afterToEdit(false);
    }
  }, [data.toEdit]);
  useEffect(() => {
    editStateChange(isEdit);
  }, [isEdit]);
  useEffect(() => {
    handleExpand(data, isExpand);
  }, [isExpand]);

  // useEffect(() => {
  //   const handleBeforeUnload = () => {
  //     localStorage.setItem('page_closed', 'true');
  //   };
  //   window.addEventListener('beforeunload', handleBeforeUnload);
  //
  //   return () => {
  //     debugger
  //     localStorage.setItem('page_closed', 'true');
  //     // window.removeEventListener('beforeunload', handleBeforeUnload);
  //   };
  // }, []);


  return (
    <div className="list-item-wrp" id={`list-${data.value}`}>
      <div className="list-item-title flex-y-c">
        <div className="expand-icon" onClick={() => setIsExpand(!isExpand)}>
          {isExpand ? <CaretDownOutlined /> : <CaretRightOutlined />}
        </div>
        {isEdit ? (
          <div className="input-wrp flex-y-c">
            <Input
              value={inputValue}
              onClick={(e: any) => {
                e.stopPropagation();
              }}
              onChange={(e: any) => setInputValue(e.target.value)}
              maxLength={20}
              showCount
            />
            <div className="btn-wrp">
              <CheckOutlined
                onClick={(e: any) => {
                  e.stopPropagation();
                  if (inputValue == '' || inputValue == null) {
                    message.error(t('请输入名称'));
                    return;
                  }
                  handleEdit(inputValue);
                  setIsEdit(false);
                  dispatch({
                    type: 'moocCourse/handlePageEditChange',
                    payload: { saveLoading: false },
                  });
                }}
              />
              <CloseOutlined
                onClick={(e: any) => {
                  e.stopPropagation();
                  dispatch({
                    type: 'moocCourse/handlePageEditChange',
                    payload: { saveLoading: false },
                  });
                  setIsEdit(false);
                  if (data.name === '') {
                    onDelete(data.value);
                    return;
                  }
                  setInputValue(data.name);
                }}
              />
            </div>
          </div>
        ) : (
          <div
            className="name-wrp flex-y-c"
            onClick={() => setIsExpand(!isExpand)}
          >
            <div className="title">
              {data.name}
              {!['courseInfo', 'chapter', 'teacherTeam'].includes(data.value) &&
              canPageEdit ? (
                data.visible ? (
                  <EyeFilled
                    onClick={(e: any) => {
                      e.stopPropagation();
                      handleVisible(false);
                    }}
                  />
                ) : (
                  <EyeInvisibleFilled
                    onClick={(e: any) => {
                      e.stopPropagation();
                      handleVisible(true);
                    }}
                  />
                )
              ) : null}
            </div>
            <div className="btn-wrp hover">
              {data.value != 'courseInfo' && canPageEdit && <DragHandle />}
              {!['courseInfo', 'courseLp'].includes(data.value) && canPageEdit && (
                <IconFont
                  type="iconedit"
                  onClick={(e: any) => {
                    e.stopPropagation();
                    setIsEdit(true);
                    dispatch({
                      type: 'moocCourse/handlePageEditChange',
                      payload: { saveLoading: true },
                    });
                  }}
                />
              )}
              {!['courseInfo', 'chapter', 'teacherTeam'].includes(
                  data.value,
                ) &&
                canPageEdit && (
                  <Popconfirm
                    title={t('确定要删除吗？')}
                    onConfirm={(e: any) => {
                      e.stopPropagation();
                      onDelete(data.value);
                    }}
                    onCancel={(e: any) => e.stopPropagation()}
                  >
                    <IconFont
                      type="icondelete"
                      onClick={(e: any) => e.stopPropagation()}
                    />
                  </Popconfirm>
                )}
            </div>
          </div>
        )}
      </div>
      {content()}
    </div>
  );
});

const SortableList = SortableContainer<any>(
  ({
     list,
     listChange,
     toSave,
     template,
     agent,
     onSave,
   }: {
    list: any;
    template: string;
    listChange: (value: any, key: any, cur?: any) => void;
    toSave: boolean;
    agent: string;
    onSave: () => void;
  }) => {
    const location: any = useLocation();
    const { t } = useLocale();
    const dispatch = useDispatch();
    const [disabled, setDisabled] = useState<boolean>(false);
    const [expandList, setExpandList] = useState<any>({});
    const { courseDetail, canPageEdit } = useSelector<Models.Store, any>(
      state => state.moocCourse,
    );

    const curList = useRef<any>([]);
    const curTemplate = useRef<any>('');
    const courseDetailRef = useRef<any>({});

    useEffect(() => {
      if (courseDetail) {
        courseDetailRef.current = courseDetail;
      }
    }, [courseDetail]);
    useEffect(() => {
      curTemplate.current = template;
    }, [template]);

    useEffect(() => {
      if (toSave) {
        dispatch({
          type: 'moocCourse/handlePageEditChange',
          payload: { saveLoading: true },
        });
        const func: any = ['mooc', 'map'].includes(location.query.type)
          ? moocBaseInfo.current
          : spocBaseInfo.current;
        func.preservation().then((data: any) => {
          console.info(data);
          if (data) {
            const { param, check } = data;
            save(param, check);
          } else {
            message.warning(t('有必填项未填，请填写！'));
            dispatch({
              type: 'moocCourse/handlePageEditChange',
              payload: { saveLoading: false },
            });
          }
        });
        onSave();
      }
    }, [toSave]);
    useEffect(() => {
      let temp = { ...expandList };
      list.forEach((item: any) => {
        temp[item.value] = temp[item.value] ?? true;
      });
      setExpandList(temp);
      curList.current = list;
    }, [list]);
    const { getPermission } = usePermission();

    const save = debounce((data: any, check: boolean) => {
      const param = JSON.parse(JSON.stringify(data));
      if (
        getPermission(
          ['training', 'spoc', 'mooc'],
          '_show_template_dsplay',
          true,
        )
      ) {
        param.updateData.template = curTemplate.current ?? 1;
      }
      param.updateData.agentId = agent ?? "";
      param.updateData.menu_list = curList.current.map((item: any) =>
        JSON.stringify(item),
      );
      if (courseDetailRef.current.entityData.target) {
        param.updateData.target = '';
      }
      if (courseDetailRef.current.entityData.describe) {
        param.updateData.describe = '';
      }
      const content = curList.current.map((item: any) => {
        if (item.value === 'teacherTeam') {
          return (
            JSON.stringify(item.content.map((user: any) => user?.name ?? '')) +
            item.name
          );
        } else {
          return (item?.content ?? '') + item.name;
        }
      });
      const words =
        param.updateData.name +
        (param.updateData.tag?.join('') ?? '') +
        JSON.stringify(content).replace(RegExp('<.+?>', 'g'), '');
      getSensitiveWord(
        words,
        t('保存的内容'),
        () => {
          baseInfo.changeBasicInfo(param, { Check: check }).then(res => {
            if (res && res.message === 'OK') {
              // isEdit = false;
              dispatch({
                type: 'updata/changelayout',
                payload: {},
              });
              dispatch({
                type: 'moocCourse/handlePageEditChange',
                payload: { canPageEdit: false, saveLoading: false },
              });
              message.success(t('编辑成功！'));
              addLog({
                courseIds: [location.query.id],
                courseType: typeEn[location.query.type as keyof typeof typeEn],
                operateType: 1,
              });
            } else {
              message.error(res.message);
              dispatch({
                type: 'moocCourse/handlePageEditChange',
                payload: { saveLoading: false },
              });
            }
          });
        },
        () => {
          dispatch({
            type: 'moocCourse/handlePageEditChange',
            payload: { saveLoading: false },
          });
        },
      );
    }, 500);

    const spocBaseInfo = useRef<IBaseInfoRef>(null);
    const moocBaseInfo = useRef<IMoocBaseInfoRef>(null);
    const itemRender = (item: any, canEdit: boolean) => {
      let dom: any = '';
      if (item.value == 'courseInfo') {
        dom = ['mooc', 'map'].includes(location.query.type) ? (
          <MoocBaseInfo ref={moocBaseInfo} />
        ) : (
          <BaseInfo ref={spocBaseInfo} />
        );
      } else if (item.value === 'chapter') {
        dom = t('展示课程章节目录，可在‘章节内容’中编辑');
      } else if (item.value === 'teacherTeam') {
        dom = (
          <TeachTeamList
            list={item.content}
            onChange={(list: any) => listChange(list, 'content', item)}
          />
        );
      } else {
        dom = (
          <Editor
            disabled={!canEdit}
            name={item.value || 'default'}
            value={item.content}
            height={300}
            wordlimit={2000}
            onChange={(e: any) => {
              let value = '';
              if (
                e.level?.content !== '<p><br></p>' &&
                e.level?.content !== '<p><br data-mce-bogus="1"></p>'
              ) {
                value = e.level?.content;
              }
              listChange(value, 'content', item);
            }}
          />
        );
      }
      return (
        <div style={{ display: expandList[item.value] ? 'block' : 'none' }}>
          {dom}
        </div>
      );
    };
    return (
      <div>
        <ul className="video-list">
          {list.map((item: any, index: number) => (
            <ListItem
              index={index}
              disabled={!canPageEdit || disabled || item.value == 'courseInfo'}
              editStateChange={(state: boolean) => setDisabled(state)}
              key={item.value}
              data={item}
              handleExpand={(data: any, expand: boolean) => {
                setExpandList({ ...expandList, [data.value]: expand });
              }}
              onDelete={(value: any) => {
                const temp = list.filter((item: any) => item.value === value);
                listChange(null, null, temp);
              }}
              content={() => itemRender(item, canPageEdit)}
              afterToEdit={(isEdit: boolean) => {
                listChange(isEdit, 'toEdit', item);
              }}
              handleEdit={(name: string) => {
                listChange(name, 'name', item);
              }}
              handleVisible={(visible: boolean) => {
                listChange(visible, 'visible', item);
              }}
            />
          ))}
        </ul>
      </div>
    );
  },
);

let DEFAULT_LIST = [
  {
    name: '课程基本信息',
    value: 'courseInfo',
    visible: true,
  },
  {
    name: '课程介绍',
    value: 'courseIntro',
    visible: true,
  },
  {
    name: '章节',
    value: 'chapter',
    visible: true,
  },
  {
    name: '教学团队',
    value: 'teacherTeam',
    visible: true,
  },
  {
    name: '教学目标',
    value: 'teachTarget',
    visible: true,
  },
  {
    name: '课程思政',
    value: 'courseLp',
    visible: true,
  },
];

const CourseSetting: FC = () => {
  const { t } = useLocale();
  const dispatch = useDispatch();
  const location: any = useLocation();
  const [tabKey, setTabKey] = useState<string>('1');
  const [courseInfoList, setCourseInfoList] = useState<any[]>([]);
  const { courseDetail, canPageEdit, saveLoading } = useSelector<
    Models.Store,
    any
  >(state => state.moocCourse);
  const { userInfo, parameterConfig } = useSelector<
    { global: IGlobalModelState },
    any
  >(state => state.global);
  const [template, setTemplate] = useState<string>('1');
  const [toSave, setToSave] = useState<boolean>(false);
  const [activeItem, setActiveItem] = useState<string>('courseInfo');
  const canEditPageRef = useRef<boolean>(false);
  const learnSettingRef = useRef<ILearningRef>(null);
  const [agentList, setAgentList] = useState<any[]>([]);
  const [agent, setAgent] = useState<string>('');
  const { microPermission } = useSelector<Models.Store, any>(state => state.moocCourse);
  const isMicroSuper = useMemo(() => {
    if (location.query.type !== "microMajor") return true;
    return microPermission?.isManager;
  }, [microPermission])
  const { getCourseParameter } = usePermission();
  useEffect(() => {
    canEditPageRef.current = canPageEdit;
  }, [canPageEdit]);

  useEffect(() => {
    if (sessionStorage.getItem('jumpSemester')) {
      setTimeout(() => {
        setTabKey('5');
      });
      sessionStorage.removeItem('jumpSemester');
    }
  }, [sessionStorage.getItem('jumpSemester')]);

  useEffect(() => {
    if (window.location.hash?.includes('learningsettings')) {
      setTabKey('4');
    } else if (
      window.location.hash?.includes('moocteachteam') ||
      window.location.hash?.includes('teachingteam')
    ) {
      setTabKey('2');
    } else {
      setTabKey('1');
    }
    if (location.query.first) {
      handleEditChange(true);
      history.pushState('', '', window.location.href.replace('&first=1', ''));
    }
    window.onbeforeunload = () => {
      const href = window.location.href;
      if (
        canEditPageRef.current &&
        (href.includes('baseinfo') ||
          href.includes('teachingteam') ||
          href.includes('learningsettings') ||
          href.includes('baseInfo') ||
          href.includes('moocteachteam'))
      ) {
        return t('还没有完成,确认退出吗?');
      }
    };
    getAgent();
    return () => {
      handleEditChange(false);
    };
  }, []);

  useEffect(() => {
    if (courseDetail?.name) {
      handleInit();
    }
  }, [courseDetail]);
  const getAgent = () => {
    baseInfo.reqAgent({ page: 1, page_size: 999999 }).then(res => {
      setAgentList(res?.extend_message?.records ?? []);
    });
  };
  const handleInit = async () => {
    let list: any = [];
    // 获取动态参数控制栏目
    const displayColumns = getCourseParameter('display_column', undefined, ',');
    if (displayColumns.length) {
      list = DEFAULT_LIST.filter((item: any) => !displayColumns.includes(item.name));
    }else{
      list = DEFAULT_LIST;
    }
    // 如果课程之前存在菜单列表，则使用菜单列表
    if (courseDetail?.entityData?.menu_list?.length) {
      list = courseDetail?.entityData?.menu_list.map((item: any) =>
        JSON.parse(item),
      );
    }

    if (courseDetail?.entityData?.template) {
      setTemplate(String(courseDetail?.entityData?.template));
    }
    if (courseDetail?.entityData?.agentId) {
      setAgent(courseDetail?.entityData?.agentId);
    }
    const teacherTeamIndex = list.findIndex(
      (item: any) => item.value === 'teacherTeam',
    );
    if (teacherTeamIndex !== -1 && list[teacherTeamIndex].content == null) {
      const res: any = await getTeachList();
      list = list.map((item: any, index: number) =>
        index === teacherTeamIndex ? { ...item, content: res } : item,
      );
    }

    if (
      courseDetail?.entityData?.target &&
      list.map((item: any) => item.value).includes('teachTarget')
    ) {
      list = handleDealList(
        `<p>${courseDetail?.entityData?.target}</p>`,
        'content',
        list,
        list.filter((item: any) => item.value === 'teachTarget')?.[0] ?? {},
      );
    }
    if (
      courseDetail?.entityData?.describe &&
      list.map((item: any) => item.value).includes('courseIntro')
    ) {
      list = handleDealList(
        `<p>${courseDetail?.entityData?.describe}</p>`,
        'content',
        list,
        list.filter((item: any) => item.value === 'courseIntro')?.[0] ?? {},
      );
    }
    setCourseInfoList(list);
  };

  const getTeachList = () => {
    return getteacherlist({ id: location.query.id }).then((res: any) => {
      if (res.status === 200) {
        return res.data;
      }
    });
  };

  useEffect(() => {
    if (location?.query?.tabKey) {
      tabChange(location.query.tabKey);
    }
  }, [location?.query?.tabKey]);

  const tabChange = (key: string) => {
    if (
      (tabKey === '1' && canPageEdit) ||
      (tabKey === '3' && learnSettingRef.current?.canPageEdit)
    ) {
      const isLeave = window.confirm('你所做的更改可能未保存，确认离开？');
      if (isLeave) {
        tabKey === '1'
          ? handleEditChange(false)
          : learnSettingRef.current?.setCanPageEdit(false);
        setTabKey(key);
      }
    } else {
      setTabKey(key);
    }
  };

  const onSortEnd = ({
                       oldIndex,
                       newIndex,
                     }: {
    oldIndex: number;
    newIndex: number;
  }) => {
    if (newIndex === 0) return;

    const arr = arrayMove(courseInfoList, oldIndex, newIndex);
    const dragIds = arr.map((item: any) => item.value);
    // console.info(courseInfoList, )
    setCourseInfoList(arr);
    dispatch({
      type: 'moocCourse/dragIdChange',
      payload: {
        dragIds,
      },
    });
  };

  const handleInfoSave = () => {
    setToSave(false);
  };

  const handleEditChange = (canPageEdit: boolean) => {
    dispatch({
      type: 'moocCourse/handlePageEditChange',
      payload: { canPageEdit },
    });
  };
  const handleCancel = () => {
    baseInfo.getCourseDetails(location.query.id).then(res => {
      if (res && res.message === 'OK') {
        dispatch({
          type: 'moocCourse/updateState',
          payload: {
            courseDetail: res.data,
            saveLoading: false,
          },
        });
      }
    });
    handleEditChange(false);
  };
  useEffect(() => {
    if (!saveLoading) {
      setCourseOpVis(false);
      setTimeout(() => setCourseOpVis(true));
    }
  }, [saveLoading]);
  const { getPermission } = usePermission();

  const [courseSettingVis, setCourseSettingVis] = useState<boolean>(true);
  const [courseOpVis, setCourseOpVis] = useState<boolean>(true);

  //课程达成度权限校验
  const verifyPermission = (type: string) => {
    // ['map', 'mooc 公开课', 'spoc 班级课' ,training 培训课, map 图谱课]
    if (type === 'spoc') {
      return parameterConfig?.spoc_course_achievement_display === 'true'
    }
    if (type === 'mooc') {
      return parameterConfig?.mooc_course_achievement_display === 'true'
    }
    if (type === 'training') {
      return parameterConfig?.training_course_achievement_display === 'true'
    }
    if (type === 'map') {
      return parameterConfig?.map_course_achievement_display === 'true'
    }
    return true
  }
  return (
    <div
      className={`course-setting-container ${parameterConfig.target_customer ===
      CUSTOMER_NPU && 'npu'}`}
    >
      <Tabs
        destroyInactiveTabPane
        activeKey={tabKey}
        defaultActiveKey="1"
        onChange={tabChange}
      >
        <Tabs.TabPane tab={t('课程信息设置')} key="1">
          {courseSettingVis && (
            <div className="course-info-container">
              <div className="course-info-box">
                {getPermission(
                  ['training', 'spoc', 'mooc', 'map'],
                  'ai_agent_display',
                ) && (
                  <div className="agent-wrp">
                    <div className="label">AI Agent</div>
                    <Tooltip
                      title={
                        !canPageEdit && t('点击 “ 编辑 ” 按钮进行内容编辑')
                      }
                    >
                      <Select
                        disabled={!canPageEdit}
                        value={agent}
                        onChange={(value: string) => setAgent(value)}
                        allowClear
                      >
                        {agentList.map((item: any) => (
                          <Select.Option
                            key={item.id}
                            value={item.id}
                          >
                            {item.name}
                          </Select.Option>
                        ))}
                      </Select>
                    </Tooltip>
                  </div>
                )}
                {getPermission(
                  ['training', 'mooc', 'map'],
                  '_show_template_dsplay',
                  true,
                ) && (
                  <div className="show-template-wrp box">
                    <div className="title-wrp">{t('展示模板')}</div>
                    <Radio.Group
                      disabled={!canPageEdit}
                      value={template}
                      onChange={(e: any) => setTemplate(e.target.value)}
                    >
                      {['1', '2', '3', '4'].map(item => (
                        <div className="template_wrp">
                          <Image
                            src={require(`@/assets/imgs/EditCourse/course_template_${item}.jpg`)}
                            preview={{
                              src: require(`@/assets/imgs/EditCourse/course_template_preview_${item}.jpg`),
                            }}
                            alt=""
                          />
                          <Tooltip
                            title={
                              !canPageEdit &&
                              t('点击 “ 编辑 ” 按钮进行内容编辑')
                            }
                          >
                            <Radio key={item} value={item}>
                              {t('模板')}
                              {item}
                            </Radio>
                          </Tooltip>
                        </div>
                      ))}
                    </Radio.Group>
                  </div>
                )}
                <div className="content-edit-wrp box">
                  <div className="title-wrp">{t('内容编辑')}</div>
                  <SortableList
                    useDragHandle
                    toSave={toSave}
                    template={template}
                    agent={agent}
                    helperClass="custom-row-dragging"
                    list={courseInfoList}
                    onSortEnd={onSortEnd}
                    listChange={(value: any, key: any, cur?: any) => {
                      if (key) {
                        setCourseInfoList((list: any) => {
                          return handleDealList(value, key, list, cur);
                        });
                      } else {
                        setCourseInfoList(
                          courseInfoList.filter(
                            (item: any) => item.value !== cur?.[0]?.value,
                          ),
                        );
                      }
                    }}
                    onSave={handleInfoSave}
                  />
                </div>
                {canPageEdit && location.query.type !== 'map' && (
                  <div className="add-btn">
                    <Button
                      style={{ width: '240px', marginLeft: '255px' }}
                      type="primary"
                      ghost
                      icon={<PlusOutlined />}
                      onClick={() => {
                        setCourseInfoList(
                          courseInfoList.concat([
                            {
                              name: '',
                              value: Date.now(),
                              content: '',
                              toEdit: true,
                              visible: true,
                            },
                          ]),
                        );
                        dispatch({
                          type: 'moocCourse/handlePageEditChange',
                          payload: { saveLoading: true },
                        });
                      }}
                    >
                      {t('栏目')}
                    </Button>
                  </div>
                )}
              </div>
              {isMicroSuper && (
                <div className="btn-wrp-save">
                  {/* <Button type="primary" ghost onClick={() => {
               window.open(`/learn/course/preview/${location.query.type}/${location.query.id}?preview=1&show=1&type=released`);
              }}>预览</Button> */}
                  {canPageEdit ? (
                    <Space>
                      <Button
                        type="primary"
                        disabled={saveLoading}
                        onClick={() => setToSave(true)}
                      >
                        {t('保存')}
                      </Button>
                      <Button onClick={handleCancel}>{t('取消')}</Button>
                    </Space>
                  ) : (
                    <Button
                      type="primary"
                      onClick={() => handleEditChange(true)}
                    >
                      {t('编辑')}
                    </Button>
                  )}
                </div>
              )}

              {courseInfoList.length > 1 && <div className="scrollView-wrp">
                {courseInfoList.map((item: any) => (
                  <div
                    className={`scrollView-item ${activeItem === item.value ? 'active' : ''
                    }`}
                    onClick={() => {
                      (document.getElementById(
                        `list-${item.value}`,
                      ) as any).scrollIntoView();
                      setActiveItem(item.value);
                    }}
                  >
                    {item.name}
                  </div>
                ))}
              </div>}
            </div>
          )}
        </Tabs.TabPane>

        <Tabs.TabPane tab={t('教学团队管理')} key="2">
          {['mooc', 'map'].includes(location.query.type) ? (
            <MoocTeachTeam />
          ) : (
            <TeachingTeam />
          )}
        </Tabs.TabPane>

        {location.query.type !== 'microMajor' &&
          verifyPermission(location.query.type) && (
            <Tabs.TabPane tab={t('课程教学大纲')} key="3">
              <Syllabus />
            </Tabs.TabPane>
          )}

        <Tabs.TabPane tab={t('学习设置')} key="4">
          {courseSettingVis && <LearningSettings ref={learnSettingRef} />}
        </Tabs.TabPane>

        {location.query.type === 'mooc' && (
          <Tabs.TabPane tab={t('课程运行设置')} key="5">
            {courseOpVis && (
              <CourseOpSetting
                onRefreshOther={() => {
                  setCourseSettingVis(false);
                  setTimeout(() => setCourseSettingVis(true));
                }}
              />
            )}
          </Tabs.TabPane>
        )}
      </Tabs>
      <Prompt
        when={canPageEdit}
        message={t('你所做的更改可能未保存，确认离开？')}
      />
    </div>
  );
};

export default CourseSetting;
