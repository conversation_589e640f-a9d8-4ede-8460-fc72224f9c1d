@import '~antd/dist/antd.less';
@width: 250px;

.course_block_item {
  position: relative;
  margin-right: calc((100% - @width * 6 - 10px) / 5);
  width: @width;
  // height: 285px;
  height: 239px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #ededed;
  overflow: hidden;
  margin-bottom: 20px;

  &.npu-card {
    .img_box {

      .label_item,
      .label_item2 {
        border-radius: 0 6px 0px 6px;
      }

      .label_item {
        background: #929292;
      }

      .label_item2 {
        background: #4DCB58;
      }

      .label_item4 {
        background: #ff4d4f;
      }

      .label_item3 {
        background: var(--primary-color);
      }
    }

    .title_box {
      .title {
        font-weight: bold;
      }
    }

    .progress-time-wrp {
      display: flex;
      align-items: center;
      font-size: 10px;
      height: 20px;

      .progress-wrp {
        line-height: 20px;
        padding: 0 6px;
        color: #fff;
        background: #ADADAD;
        border-radius: 4px 0 0 4px;
      }

      .date-range-wrp {
        line-height: 20px;
        background: rgba(143, 143, 143, 0.1);
        color: #B7B7B7;
        padding: 0 10px;
        border-radius: 0 4px 4px 0;
      }

      &.running {
        .progress-wrp {
          background: #F09429;
        }

        .date-range-wrp {
          background: rgba(255, 169, 103, 0.1);
          color: #F09429;
        }
      }

      &.end {
        .progress-wrp {
          background: #0546D2;
        }

        .date-range-wrp {
          background: rgba(5, 70, 210, 0.1);
          color: #0546D2;
        }
      }
    }
  }

  .img_box {
    width: @width;
    height: 142px;
    position: relative;
    overflow: hidden;

    &:hover {
      .ant-checkbox-wrapper {
        display: inline-flex;
      }

      .icon-wrp {
        display: none;
      }
    }

    .ant-checkbox-wrapper {
      display: none;
      position: absolute;
      left: 10px;
      top: 10px;

      &.ant-checkbox-wrapper-checked {
        display: inline-flex;
      }
    }

    .icon-wrp {
      height: 26px;
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 10px;
      width: 100%;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.7) 100%);
      bottom: 0;
      left: 0;
      color: #fff;

      .left {
        opacity: .7;
        min-width: 10px;
        display: flex;
        align-items: center;
      }

      .icon {
        margin-right: 10px;
        font-size: 12px;

        .anticon {
          margin-right: 3px;
        }
      }
    }

    img {
      cursor: pointer;
      width: 100%;
      height: 100%;
    }

    .label_item {
      color: white;
      position: absolute;
      right: 0;
      top: 0px;
      min-width: 58px;
      padding: 0 7px;
      height: 25px;
      background: #acacac;
      line-height: 25px;
      text-align: center;
      border-radius: 0px 6px 0px 14px;
    }

    .label_item2 {
      background: #31AC3C;
    }

    .label_item3 {
      background: var(--primary-color);
    }

    .label_item4 {
      background: #ff4d4f;
    }


    .certification {
      position: absolute;
      right: 0;
      bottom: 9px;
      width: 136px;
      height: 32px;
      display: flex;
      align-items: center;
      background: linear-gradient(134deg, #ffdd4f 0%, #ffa43c 100%);
      border-radius: 99px 0 0 99px;

      .anticon {
        font-size: 18px;
        color: #fff;
        width: 36px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        background: linear-gradient(149deg, #ffd74a 0%, #ff9718 100%);
        border-radius: 50%;
        border: 2px solid #fff;
      }

      div {
        flex: 1;
        width: 0;
        text-align: center;
        color: #fff;
        font-size: 14px;
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .detail_box {
    // padding: 13px;
    padding: 8px;
    // height: 150px;
    height: 97px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
  }

  // .spoc_box {
  //   height: 117px;
  // }
  .detail_box_other {
    // padding: 13px;
    padding: 16px 8px;
    // height: 150px;
    height: 97px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
  }

  .detail_box_for_micro {
    padding: 13px 8px;
    // padding: 8px;
    // height: 150px;
    height: 77px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
  }

  &:hover {
    transition: all 0.4s;
    transform: scale(1.05, 1.05);

    .action_icon {
      opacity: 0.8;
    }
  }

  .action_icon {
    position: absolute;
    // bottom: 10px;
    // right: 20px;
    // font-size: 20px;
    width: 100%;
    bottom: 0;
    opacity: 0;
    transition: all 0.5s;
    z-index: 1;

    // .ant-menu.ant-menu-root {
    //   display: flex;
    //   color: white;
    //   background: rgba(0, 0, 0, 0.73);
    //   justify-content: space-between;
    //   .ant-menu-item {
    //     display: flex;
    //     flex-direction: column;
    //     align-items: center;
    //     padding: 0;
    //     justify-content: flex-end;
    //     .ant-menu-title-content {
    //       margin: 0;
    //       height: auto;
    //       line-height: 20px;
    //     }
    //   }
    // }
    .bottom_btn {
      display: flex;
      color: white;
      align-items: center;
      height: 48px;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.7) 100%);
      justify-content: space-evenly;

      >div {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0;
        justify-content: flex-end;
        cursor: pointer;

        &:hover {
          color: var(--primary-color);
        }

        .btn-name {
          margin-top: 4px;
          font-size: 10px;
        }
      }
    }
  }

  .title_box {
    display: flex;
    align-items: center;

    .type-tag {
      margin-left: 5px;
      font-size: 12px;
      padding: 2px 16px;
      border-radius: 11px;
      color: var(--primary-color);
      background: var(--third-color);
      // &.micro {
      //   background: #EDF4FF;
      //   color: #77B1FF;
      // }
      // &.mooc {
      //   background: #ECFBF6;
      //   color: #6FE0BA;
      // }
      // &.spoc {
      //   background: #FEF6F2;
      //   color: #FFB297;
      // }
      // &.training {
      //   background: #f9f0ff;
      //   color: #531dab;
      // }
    }

    .title {
      font-size: 18px;
      color: #525252;
      font-weight: 500;
      flex: 1;
      margin-left: 5px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .title_dock {
      font-size: 14px;
      color: #525252;
      font-weight: 500;
      margin-left: 5px;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .teacher {
    font-size: 12px;
    color: #868686;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;

    &>span:first-child {
      flex: 1;
      width: 0;
    }

    span {
      display: block;
      // margin-right: 5px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    span:last-child {
      font-size: 12px;
    }
  }

  .statusCount {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    height: 22px;
    align-items: center;
    font-size: 12px;
    background: #F7F9FA;
    border-radius: 11px;
    padding-right: 10px;

    >div {
      span:last-child {
        margin-left: 4px;
      }
    }

    .status {
      border-radius: 11px;
      padding: 0 10px;
    }

    .date {
      color: #525252;
    }

    .nostarted {
      //未开始
      color: #FB8D3B;
      background-color: rgba(251, 141, 59, .1);
    }

    .starting {
      //进行中
      background: rgba(49, 172, 60, .1);
      color: #31AC3C;
    }

    .finished {
      //已结束
      color: #919191;
      background: rgba(145, 145, 145, .1);
    }
  }

  .spoc_bottom {
    font-size: 12px;

    .spoc_bottom_wrp {
      color: #aeaeae;
      cursor: pointer;
      display: inline-block;

      &>span {
        margin-right: 5px;
      }
    }

    .anticon {
      margin-right: 3px;
    }

    .active {
      color: var(--primary-color);
    }
  }

  .collage {
    font-size: 12px;
    color: #787878;

    span {
      width: 80%;
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .hits {
    display: inline-flex;
    align-items: center;

    .anticon {
      margin-right: 4px;
      font-size: 14px;
    }
  }
}

// .course_spoc_item {
//   height: 259px;
// }

.course_block_item:nth-child(6n) {
  margin-right: 0;
}

// 修改 24条/页 时没占满就分页
// @media screen and (max-width: 1780px) {
//   .course_block_item,
//   .course_block_item:nth-child(6n) {
//     margin-right: calc((100% - 230px * 5 - 10px) / 4);
//   }

//   .course_block_item:nth-child(5n) {
//     margin-right: 0;
//   }
// }

@media screen and (max-width: 1760px) {

  .course_block_item,
  .course_block_item:nth-child(5n),
  .course_block_item:nth-child(6n) {
    margin-right: calc((100% - @width * 4 - 10px) / 3);
  }

  .course_block_item:nth-child(4n) {
    margin-right: 0;
  }
}

@media screen and (max-width: 1300px) {

  .course_block_item,
  .course_block_item:nth-child(4n),
  .course_block_item:nth-child(5n),
  .course_block_item:nth-child(6n) {
    margin-right: calc((100% - @width * 3 - 10px) / 2);
  }

  .course_block_item:nth-child(3n) {
    margin-right: 0;
  }
}

@media screen and (max-width: 768px) {
  .course_block_item {
    width: 48%;
    height: 182px;

    &:hover {
      transform: scale(1) !important;
    }

    .img_box {
      width: 100% !important;
      height: 100px !important;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .detail_box {
      height: 84px !important;

      .spoc_bottom {
        display: flex;
        justify-content: space-between;
        flex-wrap: nowrap;
        align-items: center;
        height: 16px;

        .spoc_bottom_wrp {
          width: 80%;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }

      .statusCount {
        align-items: center;
      }

      .mobile_single_btns {
        color: var(--primary-color);
        font-size: 25px;
        font-size: 25px;
        line-height: 25px;
        height: 25px;
      }
    }

    .detail_box_for_micro {
      padding: 8px !important;
      height: 72px !important;
      box-sizing: border-box;

      .teacher {
        height: 16px;

        .mobile_single_btns {
          color: var(--primary-color);
          font-size: 25px !important;
          line-height: 25px;
          height: 25px;
        }
      }
    }

    .detail_box {
      height: 84px !important;

      .teacher {
        height: 16px;

        .mobile_single_btns {
          color: var(--primary-color);
          font-size: 25px !important;
          line-height: 25px;
          height: 25px;
        }
      }
    }

    .detail_box_other {
      height: 84px !important;

      .teacher {
        height: 16px;

        .icon-container {
          display: flex;
          flex-direction: row;
          align-items: center;
          color: var(--primary-color);
          margin-left: 5px;

          >span:last-child {
            display: none;
          }
        }
      }
    }
  }

  .course_block_item:nth-child(odd) {
    margin: 0 2% 15px 0 !important;
  }

  .course_block_item:nth-child(even) {
    margin: 0 0 15px 2% !important;
  }
}

.ant-popover-inner-content {
  .bottom_btn {
    >div {
      white-space: nowrap;

      &:not(:last-child) {
        margin-bottom: 5px;
      }

      >span {
        &.anticon {
          margin-right: 8px;
        }
      }
    }
  }
}

.course-card-other-btn-wrp {
  .ant-popover-inner-content {
    padding: 12px 0;
  }

  .other-btn-wrp {
    display: flex;
    align-items: center;
    padding: 0 12px;
    height: 28px;
    cursor: pointer;

    &:hover {
      background: var(--third-color);
      color: var(--primary-color);
    }

    .other-btn-name {
      margin-left: 4px;
    }
  }
}

.micromajor_item_style {
  height: 220px !important;



  .detail_box {
    height: calc(100% - 142px) !important;
    justify-content: flex-start;
    gap: 8px;
  }

  .img_box .label_item {
    left: 0;
    right: auto;
    border-radius: 6px 0px 6px 0px;
  }

  .img_box .ant-checkbox-wrapper {
    display: inline-flex;
  }

  .course_no_box {
    display: none;
  }
  .statusCount {
    display: none;
  }

  @media screen and (max-width: 1600px) {
    margin-right: 15px !important;
    width: calc(100% / 5 - 15px) !important;

    .img_box {
      width: 100% !important;
    }
  }

  @media screen and (max-width: 1400px) {
    width: @width !important;
  }
}
