import FilePreviewModal from '@/components/FilePreviewModal';
import {
  CheckOutlined,
  CloseOutlined,
  DownOutlined,
  UpOutlined,
} from '@ant-design/icons';
import { Checkbox, Input, Radio, Tag } from 'antd';
import React, { FC, useEffect, useState } from 'react';
import { optionType_ } from '../utils/columns';
import './HomeworkCheckItem.less';
import UploadItem from './UploadItem';
// @ts-ignore
import RenderHtml from '@/components/renderHtml';
import useLocale from '@/hooks/useLocale';
import $ from 'jquery';
import CommentUpload from './CommentUpload';

const { TextArea } = Input;
interface IHomeworkCheckItem {
  index: number;
  data: any;
  answer: any;
  score: string;
  comment: string;
  recordId: string;
  isCorrect?: boolean[];
  teacherFiles?: any[];
  isStu?: boolean
  submissionList?: any;
  onChange: (score: string, id: string) => void;
  onCommentChange: (score: string, id: string) => void;
  onPreview: (file: any) => void;
  onDownload: (file: any) => void;
  onImageClick: (target: any) => void;
  onTeacherFilesChange?: (files: any[]) => void;
}

const HomeworkCheckItem: FC<IHomeworkCheckItem> = ({
  index,
  data,
  answer,
  score,
  comment,
  isCorrect,
  recordId,
  teacherFiles,
  isStu,
  submissionList,
  onChange,
  onPreview,
  onDownload,
  onCommentChange,
  onImageClick,
  onTeacherFilesChange
}) => {
  const [scoreValue, setScoreValue] = useState<string>('');
  const [commentValue, setCommentValue] = useState<string>('');
  const [isExpand, setIsExpand] = useState<boolean>(false);
  const [previewVisible, setPreviewVisible] = useState<boolean>(false);
  const [currentFile, setCurrentFile] = useState<any>({});
  const { t } = useLocale();

  useEffect(() => {
    $(document).on(
      'click',
      `.question-item-${index} .subjective-topic-answer img`,
      (e: any) => {
        onImageClick(e.target);
      },
    );
    return () => {
      $(document).off(
        'click',
        `.question-item-${index} .subjective-topic-answer img`,
      );
    };
  }, []);

  useEffect(() => {
    setScoreValue(String(score ?? ''));
  }, [score]);

  useEffect(() => {
    setCommentValue(String(comment ?? ''));
  }, [comment]);

  const scoreChange = (e: any) => {
    setScoreValue(e.target.value ?? '');
    onChange(e.target.value, data.id);
  };
  const handleCommentChange = (e: any) => {
    setCommentValue(e.target.value ?? '');
    onCommentChange(e.target.value, data.id);
  };
  return (
    <div className={`homework-check-item question-item-${index}`}>
      <div className="serial-number">{index}</div>
      <div className="check-content-container">
        <div className="title-container">
          <span>{`（${t(optionType_[data.questions_type])}${t(
            '题',
          )}${data.score ?? 0}${t('分）')}`}</span>
          <RenderHtml
            dangerouslySetInnerHTML={{ __html: data.questions_content }}
            className="special-dom"
          ></RenderHtml>
        </div>
        {(data.cognitive_level || data.questions_difficulty) && <div className='cognitive_level'>
          {data.cognitive_level && <span>认知层次：{data.cognitive_level}</span>}
          {data.questions_difficulty && <span>难度：{data.questions_difficulty}</span>}
        </div>}
        {data?.fileList?.length > 0 && (
          <>
            <div>{t('附件：')}</div>
            <div style={{ marginBottom: '10px' }}>
              {data?.fileList?.map((item: any, index: number) => (
                <div>
                  {index + 1}.{' '}
                  <a key={item.contentId} onClick={() => onPreview(item)}>
                    {item.attachmentName}
                  </a>
                  <a
                    style={{ marginLeft: '10px' }}
                    onClick={() => onDownload(item)}
                  >
                    {t('下载')}
                  </a>
                </div>
              ))}
            </div>
          </>
        )}
        <div className="answers">
          {data.questions_type === 0 ? ( //单选
            <Radio.Group value={answer?.[0]} disabled>
              {data.questions_options?.map((item_0: any, index_0: number) => {
                return (
                  <div className="answer-item" key={index_0}>
                    <Radio value={String.fromCharCode(64 + Number(item_0.seq))}>
                      {String.fromCharCode(64 + Number(item_0.seq))}
                    </Radio>
                    <RenderHtml
                      className="radio-content"
                      dangerouslySetInnerHTML={{ __html: item_0.content }}
                    ></RenderHtml>
                    <div>
                      {answer?.[0] ==
                      String.fromCharCode(64 + Number(item_0.seq)) ? (
                        data.questions_answers[0] ===
                        String.fromCharCode(64 + Number(item_0.seq)) ? (
                          <Tag icon={<CheckOutlined />} color="success">
                            {t('正确')}
                          </Tag>
                        ) : (
                          <Tag icon={<CloseOutlined />} color="error">
                            {t('错误')}
                          </Tag>
                        )
                      ) : (
                        ''
                      )}
                    </div>
                  </div>
                );
              })}
            </Radio.Group>
          ) : data.questions_type === 1 ? ( //多选
            <Checkbox.Group disabled value={answer}>
              {data.questions_options?.map((item_1: any, index_1: number) => {
                return (
                  <div className="answer-item" key={index_1}>
                    <Checkbox
                      value={String.fromCharCode(64 + Number(item_1.seq))}
                    >
                      {String.fromCharCode(64 + Number(item_1.seq))}
                    </Checkbox>
                    <RenderHtml
                      dangerouslySetInnerHTML={{ __html: item_1.content }}
                    ></RenderHtml>
                    <div>
                      {data.questions_answers?.includes(
                        String.fromCharCode(64 + Number(item_1.seq)),
                      ) ? (
                        answer?.includes(
                          String.fromCharCode(64 + Number(item_1.seq)),
                        ) ? (
                          <Tag icon={<CheckOutlined />} color="success">
                            {t('正确')}
                          </Tag>
                        ) : (
                          <Tag icon={<CloseOutlined />} color="error">
                            {t('错误')}
                          </Tag>
                        )
                      ) : (
                        ''
                      )}
                    </div>
                  </div>
                );
              })}
            </Checkbox.Group>
          ) : data.questions_type === 2 ? ( // 填空题
            data.questions_options?.map((item_2: any, index_2: number) => {
              return (
                <div className="answer-item blanks" key={index_2}>
                  <span>{t('第{name}空', item_2.seq)}</span>
                  {/* <span>{`${answer?.[index_2] ?? ""}`}</span> */}
                  <RenderHtml
                    className="blank-editor-answer"
                    dangerouslySetInnerHTML={{
                      __html: answer?.[index_2] ?? '',
                    }}
                  ></RenderHtml>
                  {isCorrect?.[index_2] ??
                  item_2.content === answer?.[index_2] ? (
                    <Tag
                      style={{ maxHeight: '22px' }}
                      icon={<CheckOutlined />}
                      color="success"
                    >
                      {t('正确')}
                    </Tag>
                  ) : (
                    <Tag
                      style={{ maxHeight: '22px' }}
                      icon={<CloseOutlined />}
                      color="error"
                    >
                      {t('错误')}
                    </Tag>
                  )}
                </div>
              );
            })
          ) : data.questions_type === 3 ? ( // 主观题
            <div key={data.id + index}>
              <div className="answer-item topic-answer-item">
                <span>{t('解答：')}</span>
                {/* <Editor name={data.id} height={300} disabled={true} value={answer?.[0]} hasAttachment={data.hasAttachment} /> */}
                <div className="subjective-topic-answer-wrp">
                  <RenderHtml
                    className="subjective-topic-answer"
                    dangerouslySetInnerHTML={{ __html: answer?.[0] }}
                  ></RenderHtml>
                  {data.hasAttachment?.map((item: any, index: number) => (
                    <UploadItem
                      isTeacher
                      id={data.id}
                      index={index}
                      data={item}
                      key={item.key}
                      canEdit={false}
                      submissionList={submissionList}
                      onPreview={(file: any) => {
                        setCurrentFile(file);
                        setPreviewVisible(true);
                      }}
                    />
                  ))}
                </div>
              </div>
            </div>
          ) : (
            // 判断题
            <Radio.Group disabled value={answer?.[0]}>
              {data.questions_options?.map((item_4: any, index_4: number) => {
                return (
                  <div className="answer-item" key={index_4}>
                    <Radio value={String.fromCharCode(64 + Number(item_4.seq))}>
                      {String.fromCharCode(64 + Number(item_4.seq))}
                    </Radio>
                    <div className="radio-content">{item_4.content}</div>+
                    <div>
                      {answer?.[0] ==
                      String.fromCharCode(64 + Number(item_4.seq)) ? (
                        data.questions_answers[0] ===
                        String.fromCharCode(64 + Number(item_4.seq)) ? (
                          <Tag icon={<CheckOutlined />} color="success">
                            {t('正确')}
                          </Tag>
                        ) : (
                          <Tag icon={<CloseOutlined />} color="error">
                            {t('错误')}
                          </Tag>
                        )
                      ) : (
                        ''
                      )}
                    </div>
                  </div>
                );
              })}
            </Radio.Group>
          )}

          <div className="score-box">
            <div className="mark-container">
              <span>{t('得分：')}</span>
              {data.questions_type === 3 ? (
                <Input
                  disabled={data.questions_type !== 3 && data.score != null}
                  value={scoreValue}
                  size="small"
                  onChange={scoreChange}
                />
              ) : score == null ? (
                '--'
              ) : (
                score
              )}
              <span>{t('分')}</span>
            </div>
            <div className="comment-container">
              <span>{t('教师评语：')}</span>
              <TextArea
                value={commentValue}
                onChange={handleCommentChange}
                disabled={isStu}
              ></TextArea>
            </div>
            {data.questions_type === 3 && <CommentUpload
              recordId={recordId}
              showUpload
              id={data.id}
              fileList={teacherFiles}
              onPreview={(file: any) => {
                setCurrentFile(file);
                setPreviewVisible(true);
              }}
              onFileChange={(files: any) => onTeacherFilesChange?.(files)}
            />}
            <div className="parse-item">
              <span onClick={() => setIsExpand(!isExpand)}>
                {isExpand ? t('收起') : t('查看')}
                {t('解析')}
                {isExpand ? <UpOutlined /> : <DownOutlined />}
              </span>
              {isExpand && (
                <div>
                  {data.questions_type !== 3 && (
                    <div>
                      {t('答案：')}
                      {data.questions_answers?.map((item: any) => (
                        <RenderHtml
                          dangerouslySetInnerHTML={{
                            __html: item,
                          }}
                        ></RenderHtml>
                      ))}
                      {/* {data.questions_answers?.join('，')} */}
                    </div>
                  )}
                  {data.questions_analysis && (
                    <RenderHtml
                      dangerouslySetInnerHTML={{
                        __html: data.questions_analysis,
                      }}
                    ></RenderHtml>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <FilePreviewModal
        visible={previewVisible}
        onClose={() => setPreviewVisible(false)}
        file={{
          filePath: currentFile.fileUrl,
          extraData: currentFile.filename,
        }}
        fileType={currentFile.type}
      />
    </div>
  );
};

export default HomeworkCheckItem;
