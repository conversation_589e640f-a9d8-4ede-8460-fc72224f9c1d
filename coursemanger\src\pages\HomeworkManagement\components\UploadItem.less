.upload-item {
  line-height: 32px;
  display: flex;
  & >div {
    display: flex;
    align-items: center;
  }

  .file-size {
    color: #aaa;
  }
  .annotate-wrp {
    height: 26px;
    background-color: var(--third-color);
    color: var(--primary-color);
    font-size: 12px;
    padding: 0 6px;
    border-radius: 4px;
    line-height: 26px;
    margin-right: 5px;
  }

  .anticon-close {
    color: var(--primary-color);
    margin-left: 10px;
    cursor: pointer;
  }

  .anticon-paper-clip {
    margin-right: 3px;
  }

  .title {
    .required::before {
      display: inline-block;
      margin-right: 4px;
      color: #ff4d4f;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: "*";
    }
  }
  .uploading {
    margin-left: 10px;
    .anticon-spin {
      margin-right: 3px;
    }
  }
}
