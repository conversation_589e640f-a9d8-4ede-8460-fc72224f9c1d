.editmap_box{
    position:relative;
    height: 100%;
    width: 100%;
    background-color: #ffffff;
    // padding: 0 20px 0 20px;

    .jindu_box{
        position: absolute;
        width: 614px;
        height: 77px;
        background: #fff;
        border-radius: 10px;
        top:10px;
        left: 30px;
        z-index: 1;
        display: flex;
        justify-content: space-evenly;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

        .top_view{
            span{
                color: #000;
            }
        }
        
    }

    .custom_drawer{
        .ant-drawer-header{
            display: none;
        }
        .ant-drawer-body{
            padding: 0 !important;
            padding-left: 20px !important;
        }
    }
    .ant-drawer .arrow{
        transform: translateY(-50%);
        top: 50%;
        left: 0;
        position: absolute;
        cursor: pointer;
    }
    // 这里是连线流动效果的css样式 用的时候可以取消注释 
    @keyframes ant-line {
      to {
          stroke-dashoffset: -1000
      }
    }

    .lock_box{
        position:absolute;
        left: 0;
        top: 0;
        width:100%;
        height:100%;
        background-color:#ffffff;
        z-index: 999;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .search_view{
        width: 100%;
        height: auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 20px;
        display: none;

        .left{
            width: 60%;
            height: 32px;
            display: flex;
            align-items: center;
        }

        .right{
            width: 40%;
            height: auto;
            display: flex;
            justify-content: flex-end;

            button{
                margin-left: 15px;
            }

            .dachengdu_btn{
                opacity: 1;
            }
        }
        .ant-radio-button-wrapper{
            white-space: nowrap;
        }
    }

    .divider{
        width: 100%;
        height: 1px;
        background-color: #f0f0f0;
        margin-top: 16px;
    }

    .mapview{
        position: relative;
        width: 100%;
        // height: calc(100% - 85px);
        overflow: hidden;

        .mapdetail{
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;

            .map_box{
                flex: 1;
                width: 100% !important;
                height: 100% !important;
            }

            .right_search_box{
                position: absolute;
                width: 500px;
                height: auto;
                right: 20px;
                top: 20px;
                display: flex;
                justify-content: flex-end;
            }

            .mapfunction{
                position: absolute;
                width: 32px;
                // top: 30%;
                left: 20px;
                display: flex;
                flex-wrap: wrap;
            }

            .options_box{
                position: absolute;
                bottom: 0px;
                width: 1080px;
                height: 73px;
                border-radius: 50px 50px 0px 0px;
                overflow: hidden;
                border: 1px solid #C8C8C8;
                border-bottom: none;
                // 从上到下渐变
                background: #F7F9FA;
                padding-left: 20px;
                padding-right: 20px;
                display: flex;
                align-items: center;
                justify-content: space-evenly;

                .item_ops{
                    width: 75px;
                    height: 60px;
                    border-radius: 10px;
                    // border: 1px solid #C8C8C8;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-wrap: wrap;
                    align-content: center;
                    cursor: pointer;

                    &:hover{
                        border: 1px solid  #C8C8C8;
                    }

                    // 点击效果
                    &:active{
                        background-color: #F0F0F0;
                    }

                    .anticon{
                        color: #686868;
                    }

                    .op_name{
                        width: 100%;
                        text-align: center;
                        margin-top: 3px;
                    }
                    
                }

                .active{
                    background-color: var(--primary-color);
                    
                    .anticon,.op_name{
                        color: #fff;
                    }
                }

            }
        }

    }

    // 这个是解决右键下拉菜单不出现的问题
    .x6-widget-selection-box{
        pointer-events:none !important;
    }


}

.x6-dropdown{
    overflow-y: hidden !important;
}

.new_tab3_view{
    background-color: #fff !important;
    background-image: none !important;


    .ant-tabs-tab-btn{
        color: #6C6C6C;
    }
}

.new_tab3_dark_view{
    background-image: none !important;
    
    &>.ant-tabs-nav{
        background: linear-gradient(to bottom, #EAFAFF, #F3F8FE) !important;
    }

    .ant-tabs-tab-btn{
        color: #6C6C6C;
    }
}

.ant-popover .ant-popover-inner-content {
    padding: 12px 16px;
}