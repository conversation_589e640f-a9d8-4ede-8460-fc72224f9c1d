import React, { useState, useEffect, FC } from 'react';
import { useSelector, useLocation } from 'umi';
import perCfg from '@/permission/config';
import { IconFont } from '@/components/iconFont';
import './index.less';
import { ColumnsType } from 'antd/es/table';
import { Input, Button, Table, Modal, message, Avatar } from 'antd';
import courseTemplate from '@/api/courseTemplate';
import AddTeacherModal from '@/pages/templateDeatil/TeachTeam/addTeacherModal';
import { UserOutlined } from '@ant-design/icons';
import useLocale from '@/hooks/useLocale';
const TeachTeam: FC = () => {
  const { t } = useLocale();
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedTeacher, setSelectedTeacher] = useState<any[]>([]);
  const [dataSource, setDataSource] = useState<[]>([]);
  const [addTeacherVisible, setAddTeacherVisible] = useState<boolean>(false);
  const [teacherName, setTeacherName] = useState<string>('');
  const [deleteList, setDeleteList] = useState<any>([]);
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [editData, setEditData] = useState<any>(null);
  const [newOrEdit, setNewOrEdit] = useState<boolean>(true);
  const location: any = useLocation();
  const [canEdit, setCanEdit] = useState<boolean>(true);
  const [allList, setAllList] = useState<[]>([]);

  const fetchDataList = () => {
    let contentId = location.query.id;
    console.log(contentId);

    courseTemplate.geteditorteam({
      contentId: contentId,
      isPrivate: location.query.myOrShare,
      userCode: '' //教师工号
    }).then((res: any) => {
      if (res && res.message === 'OK') {
        //去重
        let hash = {};
        res.data = res.data.reduce(function (item: any, next: number) {
          hash[next.id] ? '' : hash[next.id] = true && item.push(next);
          return item;
        }, []);
        console.log(res.data);
        const sysLength = res.data.filter((item: any) => item.siteName == 1).length;
        if (sysLength > 1) {
          setCanEdit(true);
        } else {
          setCanEdit(false);
        }
        setDataSource(res.data);
        setAllList(res.data);
      }
    });
  };
  const templateCourseDetail = useSelector<Models.Store, any>(
    (state) => state.moocCourse.templateCourseDetail);


  // 初始化获取表格数据
  useEffect(() => {
    searchTeacherHandle();
  }, [teacherName]);

  useEffect(() => {
    fetchDataList();
  }, []);
  const searchTeacherHandle = () => {
    const list = allList.filter((item: any) => item.nickName?.includes(teacherName)) || [];
    setDataSource(list);
  };

  // 自定义搜索框的图标
  const suffix =
    <IconFont
      type="iconsousuo1"
      style={{ color: '#333333', fontSize: '16px' }}
      onClick={() => fetchDataList()} />;



  // 表格的标题
  const columns: ColumnsType<any> | undefined = [
    {
      title: t("头像"),
      dataIndex: 'avatarUrl',
      key: 'avatarUrl',
      align: 'center',
      render: (text: string) => {
        if (text) {
          return <Avatar size={60} src={text} icon={<UserOutlined />} />;
        } else {
          return <Avatar size={60} icon={<UserOutlined />} />;
        }
      }
    },
    {
      title: t("姓名"),
      dataIndex: 'nickName',
      key: 'nickName',
      align: 'center'
    },
    {
      title: t("学号/工号"),
      dataIndex: 'userCode',
      key: 'userCode',
      align: 'center'
    },
    {
      title: t("权限"),
      dataIndex: 'siteName',
      align: 'center',
      key: 'siteName',
      render: (text: string) => text === '1' ? t("管理员") : t("编辑者")
    },
    {
      title: t("操作"),
      dataIndex: 'x',
      align: 'center',
      key: 'x',
      render: (text: any, data: any) =>
        location.query.type == 'edit' && templateCourseDetail.entityData?.permission == 2 && (canEdit || data.siteName != 1) ? <div>
          <IconFont type='iconedit' onClick={() => editTeachar(data)} />
          <IconFont type='icondelete'
            style={{ marginLeft: '15px' }}
            onClick={() => {
              setDeleteList([data]);
              setIsModalVisible(true);
            }} />
        </div> : ''

    }];

  const editTeachar = (data: any) => {
    setEditData(data);
    setNewOrEdit(false);
    setAddTeacherVisible(true);
  };
  const deleteTeacher = () => {
    let contentId = location.query.id;
    const adminIds = deleteList.
      filter((t: any) => t.siteName === '1').
      map((t: any) => t.userCode);
    const editorIds = deleteList.
      filter((t: any) => t.siteName === '0').
      map((t: any) => t.userCode);
    const results: Promise<any>[] = [];
    if (adminIds.length > 0) {
      results.push(courseTemplate.deleteTemplateAdmin(contentId, adminIds));
    }
    if (editorIds.length > 0) {
      results.push(courseTemplate.deleteTemplateEditor(contentId, editorIds));
    }
    Promise.all(results).then((res) => {
      const succ1 = res[0].message === "OK";
      const succ2 = res[1] ? res[1].message === "OK" : true;
      if (succ1 && succ2) {
        message.success(t('删除成功'));
      } else {
        !succ1 && message.error(res[0].message);
        res[1] && !succ2 && message.error(res[1].message);
      }
      setIsModalVisible(false);
      fetchDataList();
    });
  };
  // 添加教师
  const addteacherBtn = () => {
    setNewOrEdit(true);
    setAddTeacherVisible(true);
  };
  const handleAddTeacherOk = (teachers: any[], newTeachers: boolean) => {

    let contentId = location.query.id;
    if (newTeachers) {
      const adminIds = teachers.
        filter((t) => t.role === 'admin').
        map((t) => t.userCode);
      const editorIds = teachers.
        filter((t) => t.role === 'editor').
        map((t) => t.userCode);
      const results: Promise<any>[] = [];
      if (adminIds.length > 0) {
        results.push(courseTemplate.addTemplateAdmin(contentId, adminIds));
      }
      if (editorIds.length > 0) {
        results.push(courseTemplate.addTemplateEditor(contentId, editorIds));
      }
      Promise.all(results).then((_) => {
        setAddTeacherVisible(false);
        message.success(t('添加成功'));
        fetchDataList();
      });
    } else {
      courseTemplate.templateteacherUpdate(contentId, teachers[0].userCode, teachers[0].oldrole, teachers[0].role === 'admin' ? '1' : '0').then((res) => {
        if (res && res.message === "OK") {
          message.success(t('编辑成功'));
          fetchDataList();
        }
      });
      setAddTeacherVisible(false);
    }
  };

  return (
    <div className="temp_teach_team">
      {
        location.query.type == 'edit' && templateCourseDetail.entityData?.permission == 2 ?
          <div className="heard">
            <Input
              placeholder={t("请输入姓名")}
              suffix={suffix}
              style={{ width: 200 }}
              onChange={(e: any) => { setTeacherName(e.target.value); }}
              onPressEnter={() => searchTeacherHandle()} />

            <div className="right_view">
              <Button block onClick={addteacherBtn}>{t("添加人员")}

              </Button>
              <Button
                block
                style={{ marginLeft: '15px' }}
                disabled={selectedTeacher.length === 0}
                onClick={() => {
                  setDeleteList(selectedTeacher);
                  setIsModalVisible(true);
                }}>{t("删除")}

              </Button>
            </div>
          </div> : <div className="heard"></div>}


      <div className="tabel_content">
        <Table
          rowSelection={{
            selectedRowKeys,
            onChange: (selectedRowKeys, selectedRow) => {
              setSelectedRowKeys((selectedRowKeys as any));
              setSelectedTeacher(selectedRow);
            }
          }}
          dataSource={dataSource}
          columns={(columns as any)}
          rowKey={'userCode'} />

      </div>
      <AddTeacherModal
        visible={addTeacherVisible}
        newOrEdit={newOrEdit}
        editData={editData}
        onCancel={() => setAddTeacherVisible(false)}
        onOk={handleAddTeacherOk} />

      <Modal
        title={t("删除")}
        visible={isModalVisible}
        onOk={deleteTeacher}
        onCancel={() => setIsModalVisible(false)}>
        {t("确定要删除吗")}

      </Modal>
    </div>);

};

export default TeachTeam;