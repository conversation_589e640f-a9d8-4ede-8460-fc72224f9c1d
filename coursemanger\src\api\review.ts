import HTTP from './index';

/**
 * 共享，待我审核
 */
export const auditToMe = (params: any) => {
  return HTTP('/unifiedplatform/v1/audit/owner/examine/instance', {
    method: 'POST',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(err => {
      return err;
    });
};

/**
 * 共享，我发起的审核
 */
export const myInitateAudit = (params: any) => {
  return HTTP(`/unifiedplatform/v1/audit/owner/instance`, {
    method: 'POST',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(err => {
      return err;
    });
};


/**
 * 入库，我发起的审核
 */
export const myAuditResource = (data: any) => {
  return HTTP(`/unifiedplatform/v1/audit/owner/instance/resource`, {
    method: 'POST',
    params: data,
  });
};

/**
 * 查询流程审核实例资源，入库待我审核
 */
export const getAuditInstance = (params: any) => {
  return HTTP(`/unifiedplatform/v1/audit/instance/resource`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(err => {
      return err;
    });
};

/**
 * 批量通过审核
 */

export const opreateAudit = (
  status: '已审核' | '已驳回',
  data: any,
  auditComment?: string,
  instanceId?: string,
) => {
  return HTTP(`/unifiedplatform/v1/audit/bulk/process`, {
    method: 'POST',
    params: { instanceId, status, auditComment },
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(err => {
      return err;
    });
};

/**
 * 查询审核日志
 */

export const getAuditLogs = (contentId: string, page: number, size: number) => {
  return HTTP(`/unifiedplatform/v1/audit/log`, {
    method: 'GET',
    params: { contentId, page, size },
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(err => {
      return err;
    });
};

/**
 * 撤回审核
 */
export const revokeAudit = (instanceId: string, resourceId: string) => {
  return HTTP(`/unifiedplatform/v1/audit/revoke`, {
    method: 'POST',
    params: { instanceId, resourceId },
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(err => {
      return err;
    });
};

// Bpmn审核流程相关,API:http://172.16.151.202/flowbpm/doc.html

export interface BpmnHeaders {
  Authorization: string;
  'sobeycloud-site': 'S1';
}

/**
 * 获取hive token作为用户身份标识
 */
export const getHiveToken = () => {
  return HTTP('/learn/bpm/info', { method: 'GET' })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(err => {
      return err;
    });
};

/**
 * 获取所有分配到自己的任务分页
 */
export const reviewToMeList = (
  params: {
    pageNo: number;
    pageSize: number;
    beginCreateTime?: string;
    endCreateTime?: string;
    name?: string;
    isDesc?: boolean;
  },
  headers: BpmnHeaders,
) => {
  return HTTP('/flowbpm/bpm/task/all-page', {
    method: 'GET',
    params,
    headers,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(err => {
      return err;
    });
};

/**
 * 我发起的实例审核任务
 */
export const reviewFormMeList = (
  params: {
    pageNo: number;
    pageSize: number;
    name?: string;
    isDesc: boolean;
    createTime?: {
      dayOfMonth: number;
      dayOfWeek: string;
      dayOfYear: number;
      hour: number;
      minute: number;
      second: number;
      month: string;
      year: number;
      monthValue: number;
      nano: number;
    };
  },
  headers: BpmnHeaders,
) => {
  return HTTP('/flowbpm/bpm/process-instance/my-page', {
    method: 'GET',
    params,
    headers,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(err => {
      return err;
    });
};

/**
 * 获取指定流程实例
 */
export const getProcessInstace = (id: string, headers: BpmnHeaders) => {
  return HTTP('/flowbpm/bpm/process-instance/get', {
    method: 'GET',
    params: { id },
    headers,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(err => {
      return err;
    });
};

/**
 * 通过任务
 */
export const approveTask = (
  data: {
    id: string;
    reason: string;
  },
  headers: BpmnHeaders,
) => {
  return HTTP('/flowbpm/bpm/task/approve', {
    method: 'POST',
    data,
    headers,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(err => {
      return err;
    });
};

/**
 * 拒绝任务
 */

export const rejectTask = (
  data: {
    id: string;
    reason: string;
  },
  headers: BpmnHeaders,
) => {
  return HTTP('/flowbpm/bpm/task/reject', {
    method: 'POST',
    data,
    headers,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(err => {
      return err;
    });
};

/**
 * 驳回任务到指定节点
 */

export const rebackTask = (
  data: {
    id: string;
    reason: string;
    targetDefinitionKey: string;
  },
  headers: BpmnHeaders,
) => {
  return HTTP('/flowbpm/bpm/task/return', {
    method: 'POST',
    data,
    headers,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(err => {
      return err;
    });
};

/**
 * 获取某个任务所有可回退的节点
 */
export const getCanReturnNodeList = (taskId: string, headers: BpmnHeaders) => {
  return HTTP('/flowbpm/bpm/task/get-return-list', {
    method: 'GET',
    params: { taskId },
    headers,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(err => {
      return err;
    });
};

/**
 * 取消流程实例
 */
export const cancelProcess = (
  data: {
    id: string;
    reason: string;
  },
  headers: BpmnHeaders,
) => {
  return HTTP('/flowbpm/bpm/process-instance/cancel', {
    method: 'POST',
    data,
    headers,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(err => {
      return err;
    });
};

/**
 * 获取流程实例日志
 */
export const getProcessInstanceLogs = (
  processInstanceId: string,
  headers: BpmnHeaders,
) => {
  return HTTP('/flowbpm/bpm/task/list-by-process-instance-id', {
    method: 'GET',
    params: { processInstanceId },
    headers,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(err => {
      return err;
    });
};
export function getAssistantTsak(headers: BpmnHeaders) {
  return HTTP('/flowbpm/bpm/task/todo-page?pageNo=1&pageSize=20', {
    method: 'GET',
    params: {},
    headers,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(err => {
      return err;
    });
}
export interface GetForMeCourseList {
  page: number;
  size: any;
  order: {
    field: string;
    isDesc: boolean;
  }[];
  approvalStatus?: number;
  courseType: number;
  isTop?: any;
  subjectId?: any;
  teacher?: any;
}

/**
 * 获取待我审核的课程列表
 */
export const getForMeCourseList = (data: GetForMeCourseList) => {
  return HTTP('/learn/v1/course/release', {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(err => {
      return err;
    });
};
