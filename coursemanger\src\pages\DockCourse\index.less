.dock-page-wrapper {
  padding: 19px 20px 0px;

  .ant-tabs-nav-wrap {
    border-bottom: 2px solid var(--fourth-color);
  }

  .ant-form {
    // display: flex;
    // justify-content: space-between;
  }

  .reset-wrp {
    color: #525252;
    margin: 6px 18px 0 -6px;

    .anticon {
      margin-left: 5px;
    }
  }

  .ant-tabs-tabpane {
    height: 100%;
  }

  .ant-tabs {
    height: 100%;
  }

  .ant-tabs-tab {
    padding: 0 !important;
    width: 113px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-self: center;
    font-size: 16px;
    font-family: STHeitiSC-Light, STHeitiSC;
    font-weight: 300;
    color: rgba(55, 55, 55, 1);
    text-shadow: 0px 0px 4px rgba(202, 202, 202, 0.5);
    border: none !important;
    background: #fff !important;
  }

  .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    color: #fff;
  }

  .ant-tabs-card>.ant-tabs-nav .ant-tabs-tab-active,
  .ant-tabs-card>div>.ant-tabs-nav .ant-tabs-tab-active {
    background: linear-gradient(var(--third-color),
        var(--fourth-color)) !important;
  }

  .ant-tabs-card>.ant-tabs-nav .ant-tabs-tab,
  .ant-tabs-card>div>.ant-tabs-nav .ant-tabs-tab {
    transition: none;
  }

  .button_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 24px;
    margin-bottom: 24px;
    position: relative;

    .ant-space-item:not(:last-child) {
      .item_ {
        &::after {
          content: "";
          display: inline-block;
          width: 1px;
          height: 16px;
          background: #D8D8D8;
          right: 0;
          top: 8px;
          position: absolute;
        }
      }
    }

    .disabled {
      color: rgba(0, 0, 0, .25);
    }

    .item_ {
      position: relative;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        .ant-btn {
          color: var(--primary-color);
        }

        color:var(--primary-color);
      }

      &.disabled {
        cursor: no-drop;
        color: rgba(0, 0, 0, 0.25) !important;

        .ant-btn {
          color: rgba(0, 0, 0, 0.25) !important;
        }
      }
    }

    //div {
    //  button {
    //    margin-left: 20px;
    //  }
    //}
  }

  .mode_switch_wrapper {
    float: right;
    display: flex;
    align-items: center;

    .mode_switch {
      cursor: pointer;
      font-size: 16px;
      margin-left: 10px;

      .active,
      &:hover {
        color: var(--primary-color);
      }
    }
  }

  .data_wrapper {
    display: flex;
    flex-wrap: wrap;
    height: calc(100vh - 260px);
    overflow-y: auto;
    align-content: flex-start;

    .ant-empty {
      width: 100%;
    }
  }

  .splitLine {
    border-bottom: 2px #F7F9FA solid;
    margin-top: 24px;
  }
}
