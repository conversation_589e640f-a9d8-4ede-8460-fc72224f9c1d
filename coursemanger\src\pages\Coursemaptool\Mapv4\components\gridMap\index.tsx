import React, { useState,useRef, FC, useEffect } from 'react';
import './index.less';
import { Graph, Markup, Model, DataUri, Line, Curve, Path } from '@antv/x6';
import ReactDOM from 'react-dom';
// 引入react插件 这个包必须引用 否则无法使用react组件
import '@antv/x6-react-shape';
import { snakeLayout } from '@/pages/Coursemaptool/Mapv3/components/util';
import { Button, Tooltip } from 'antd';
import {
  PlusOutlined,
  MinusOutlined,
  SyncOutlined
} from
  '@ant-design/icons';
import useLocale from '@/hooks/useLocale';
import { useLocation, useSelector, useDispatch } from 'umi';

import nodeimg5 from '@/assets/imgs/coursemap/v4/snake/node5.png'
import nodeimg4 from '@/assets/imgs/coursemap/v4/snake/node4.png'
import nodeimg3 from '@/assets/imgs/coursemap/v4/snake/node3.png'
import nodeimg2 from '@/assets/imgs/coursemap/v4/snake/node2.png'
import nodeimg1 from '@/assets/imgs/coursemap/v4/snake/node1.png'
import startnodeimg from '@/assets/imgs/coursemap/v4/snake/start.png'
import jiantouimg from '@/assets/imgs/coursemap/v4/snake/jiantou.png'
import { useListener } from 'react-bus';
import { defaultNodeData ,createguid} from '@/pages/Coursemaptool/Editmap/util';
import debounce from 'lodash/debounce';

const GridMap: FC<any> = ({mapdata, initover, nodeClick,laytype,showzoombtn=false,showMinimap=true,showNumerical=true,allnodes,showtype,moudelNodedata,currentLearnNode}) => {
  const { t } = useLocale();
  const container = useRef<any>(null);
  const minimap =  useRef<any>(null);
  const graph = useRef<any>(null); //全局方法
  let contnum: number = 1; //1到5之间  轮流取值
  let colormodel: any = {}; //颜色字典表
  const [selectnode, setselectnode] = useState<boolean>(false); //当前选中的节点
  // 获取url参数
  const { query }: any = useLocation();
  // 当前正在学习节点
  const currtnode = useRef<any>('')
  const { parameterConfig } = useSelector<
      { global: any },
      { buttonPermission: string[]; parameterConfig: any; permission: any }
    >(state => state.global);
  
  //监听节点学习进度更新
  useListener('nodelearning', ({id,finishRate,masterRate}) => {
    let prenodeobj = graph.current.getCellById(id);
      if(prenodeobj){
        let data = prenodeobj.getData();
        let obj = {
          ...data,
          finishRate,
          masterRate,
          tempid:new Date().getTime()
        }
        prenodeobj.updateData(obj)
        // 更新复制出来的节点
        if(data.cid){
          let cnode = graph.current.getCellById(data.cid);
          if(cnode){
            cnode.updateData(obj)
          }
        }
      }      
      if(showtype == 1){
        if(Number(finishRate)==100 || Number(masterRate)==100){
          getprenode(id)
        }
      }
  });

  const getprenode = debounce((id:any)=>{
    // 获取后续节点
    if(graph.current){
      let prenodeobj = graph.current.getCellById(id);
       let pdata =  prenodeobj.getData()  
        if(pdata.iscopy || pdata.iscreate){
          return;
        }
        //获取到当前节点的下一个节点 
        let nextnodes = graph.current.getSuccessors(prenodeobj,{ distance: 1 });
        let prenodes = graph.current.getPredecessors(prenodeobj);
        // 如果有后续节点 并且前面也有节点
        if(nextnodes.length>0 && prenodes.length>0){
          // 找到跟这个节点有关系的节点 1 包含 2等价 3后续  4关联
          let pnode = mapdata.relationVos.filter((item:any)=>{
            let data = JSON.parse(item.data);
            if(data.type == 3 && data.isnew && item.target == nextnodes[0].id && item.source != id){
              let thisnode = graph.current.getCellById(item.source);
              let thisdata = thisnode.getData();              
              if(showtype == 1){
                  if(Number(thisdata.finishRate)<=30){
                    return true
                  }
                }else{
                  if(Number(thisdata.masterRate)<=30){
                    return true
                  }
                }
            }
            return false;
          })
          if(pnode.length){
            let prenode = graph.current.getCellById(pnode[0].source);
            // 计算X轴位置
            const nodeposition = prenodeobj.position();
            const nextnodesposition = nextnodes[0].position();
            // 动态计算X轴位置
            let newx = 0;
            if(nodeposition.x == nextnodesposition.x){
              // 看当前节点是不是10的整数 如果是就在左边
              let nindex = mapdata.nodes.findIndex((item:any)=>item.id == id)
              if((nindex+1)%10 === 0){
                newx = nodeposition.x - 150
              }else{
                newx = nodeposition.x + 150
              }
            }else if(nodeposition.x>nextnodesposition.x){
              newx = nodeposition.x - 150
            }else{
              newx = nodeposition.x + 150
            }

            let data = prenode.getData()            
            const newid = createguid();
            let newnode2 = {
              id: newid,
              data: {
                ...data,
                id: newid,
                oid: prenode.id,
                children_number: 0,
                iscopy:true
              },
              zIndex: 3,
              shape: 'react-shape',
              primer: 'circle', //节点形状
              component: 'react-node-v4',
              type: data.type,
              visible: true,
              width: 70,
              height: 70,
              position:{
                x:newx,
                y:nodeposition.y - 110
              }
            }
            graph.current.addNode(newnode2)
            // 更新点击的节点
            prenodeobj.updateData({
              ...pdata,
              iscreate:true //当前节点已经生成过了 虚拟节点就不允许再次生成了
            })
            prenode.updateData({
              ...data,
              cid:newid
            })

            let newedeg = {
              data: { visible: true, type: 1, isnew: false },
              source: id,
              target: newid,
              type: 1,
              attrs: {
                line: {
                  strokeDasharray: 10,
                  style: {
                    animation: 'ant-line 30s infinite linear',
                  },
                  stroke: '#FBFDFF', // 指定 path 元素的填充色,
                  sourceMarker: null,
                  targetMarker: null,
                  strokeWidth: 10,
                },
              },
              zIndex: 1,
            }
            graph.current.addEdge(newedeg);

            let newedeg2 = {
              data: { visible: true, type: 1, isnew: false },
              source: newid,
              target: nextnodes[0].id,
              type: 1,
              attrs: {
                line: {
                  strokeDasharray: 10,
                  style: {
                    animation: 'ant-line 30s infinite linear',
                  },
                  stroke: '#FBFDFF', // 指定 path 元素的填充色,
                  sourceMarker: null,
                  targetMarker: null,
                  strokeWidth: 10,
                },
              },
              zIndex: 1
            }
            graph.current.addEdge(newedeg2);

          }
        }        
    }
  },500)

  // 注册节点
  const registerstyle = () => {
    try {
      // 注册返回 React 组件的函数
      Graph.registerReactComponent('react-node-v4', (node) => {
        // parameterConfig.target_customer === 'sjtu'&&
        if( node.id == mapdata.nodes[0].id){
          return (
            <div className='node_start_view' style={{filter: node.data.showgrey ? 'grayscale(100%)':''}}>
              <img className='node_bg' src={startnodeimg} />
              {/* <span className='span_number'>{node.data.children_number}</span> */}
              <span className='name'>{node.data.label}</span>
            </div>
          )
        }
        if(node.data.type == 1){
          return (
            <div className='node_fenlei_view' style={{filter: node.data.showgrey ? 'grayscale(100%)':''}}>
              <img className='node_bg' src={nodeimg1} />
              <span className='span_number'>{node.data.children_number}</span>
              <span className='name'>{node.data.label}</span>
            </div>
          )
        }else if(node.data.type == 5){
          return(
            <div className='module_node' style={{filter: node.data.showgrey ? 'grayscale(100%)':''}}>
              <div className='name'>
                <span>{node.data.label}</span>
                <span>{node.data.score || '-'}学分</span>
              </div>
              <div className='time'>
                <span>{node.data?.studytime[0]?.replace('-','.')}~{node.data?.studytime[1]?.replace('-','.')}</span>
              </div>
              <div className='tag_view'>
                {
                  node.data.notNeedToStudy && <div className='tag_node'>免</div>
                }
              </div>
            </div>
          )
        }else{          
          let percentage =  10;          
          if(showtype == 1){
            percentage =  node.data.finishRate || 0;
          }else{
            percentage =  node.data.masterRate || 0;
          }
          let { isyinandian,iscoreknowledge,isexpandknowledge,isexperiment,iscase , bindresource , homework , isDiscriminate ,referenceMaterials,showgrey,notNeedToStudy} = node.data;
          
          // 是否为免考核节点
          let isnone = false;
          if((bindresource.length == 0 &&  referenceMaterials.length == 0 && showtype == 1 ) || (homework.length == 0 && showtype == 2)){
            isnone = true;
          }
          
          return (
            <div className='node_zhishidian_view' style={{filter: showgrey ? 'grayscale(100%)':''}}>
              {
                isnone && <img className='node_bg' src={nodeimg5} />
              }
              {
                !isnone && (Number(percentage) <=30 || percentage == null) &&  <img className='node_bg' src={nodeimg2} />
              }
              {
                !isnone && (Number(percentage) >30 && Number(percentage)<=70) && <img className='node_bg' src={nodeimg3} />
              }
              {
                !isnone && Number(percentage) >70 && <img className='node_bg' src={nodeimg4} />
              }
              {!isnone && <span className='span_number'>{percentage.toFixed(0)}<span style={{fontSize:'12px',marginLeft:'1px'}}>%</span></span>}
              <div className='name_view' style={node.data.isSelect ? {background:'rgb(46, 123, 255)'}:{}}>
                <span title={node.data.label}>{node.data.label}</span>
              </div>
              <div className='tag_view'>
                {
                  isyinandian && <div className='tag_node'>难</div>
                }
                {
                  iscoreknowledge && <div className='tag_node' style={{background:'#FD8059'}} >核</div>
                }
                {
                  isexpandknowledge && <div className='tag_node' style={{background:'#FCBD6F'}} >拓</div>
                }
                {
                  isexperiment && <div className='tag_node' style={{background:'#6CCC85'}} >实</div>
                }
                {
                  isDiscriminate && <div className='tag_node' style={{background: '#34BAD1'}} >辨</div>
                }
                {
                  iscase && <div className='tag_node' style={{background:'#8471FF'}} >案</div>
                }
                {
                  notNeedToStudy && <div className='tag_node' style={{background:'#6E6E6E'}} >免</div>
                }
              </div>
            </div>
          )
        }
      });
    } catch (error) {
      console.log(error);
    }
  }

  // 布局  1是只显示12个的布局   2是全部节点竖直布局
  const layout = (data: any) => {
    let newdata = snakeLayout(data,300,150,5,laytype);
      console.log(newdata)
      graph.current.fromJSON(newdata); 
      // graph.current.zoomToFit({ maxScale: 1 })
      if(newdata.nodes.length >= 8){
        graph.current.centerPoint(newdata.nodes[7].x,newdata.nodes[7].y);
      }else{
        graph.current.centerContent();
      }
      if(query.nodeId || currentLearnNode){
        graph.current.select(query.nodeId || currentLearnNode)
      }
      // graph.current.getContentBBox();
    if (initover) {
      initover(graph.current);
    }
    

  }

  // 初始化图谱数据
  const initmap = ({}) => {
    const containerdom: any = ReactDOM.findDOMNode(container.current);
    const width = containerdom.scrollWidth;
    const height = containerdom.scrollHeight || 800;
    if (graph.current) {
      try {
        graph.current.dispose();
        Graph.unregisterReactComponent('react-node-v4');
      } catch (error) {
        console.log(error);
      }
    }
    // x6 注册react 组件
    registerstyle();
    try {
      // 实例初始化
      graph.current = new Graph({
        container: containerdom,
        autoResize: true,
        // width,
        // height,
        async: true,
        //   frozen: true,
        panning: {
          enabled: true
        },
        // 滚轮放大缩小
        // mousewheel: {
        //   enabled: true,
        //   modifiers: [] //配置快捷键 触发
        //   // minScale: 0.1,                 
        // },
        selecting: {
          enabled: true,
          // rubberband: true, // 启用框选
          // movable: true, //拖动选框框选的节点一起移动
          // showNodeSelectionBox: false, //是否显示节点的选中样式
          // showEdgeSelectionBox: false, //是否显示边的选中样式
          // modifiers:['ctrl']// 快捷键
        },
        minimap: {
          enabled: true,
          container: minimap.current,
          width:190,
          height:126,
          padding:10
        },
        // 自定义交互行为
        interacting: function(cellView: any) {
          // 禁止边移动
          return {
            edgeLabelMovable: false,
            nodeMovable: false,
          };
        },
        onEdgeLabelRendered: (args) => {
          const { selectors } = args
          const content = selectors.foContent as HTMLDivElement
          if (content) {
            // 使用img标签显示箭头图片
            ReactDOM.render(
              <img 
                src={jiantouimg} 
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                  opacity: 0.5
                }}
              />, 
              content
            )
          }
        },
      });

      graph.current.on('node:selected', ({ e, x, y, node, view }: any) => {
          node.updateData({
            ...node.getData(),
            isSelect:true,
            tempid:new Date().getTime()
          })
      });

      graph.current.on('node:unselected', ({ e, x, y, node, view }: any) => {
        node.updateData({
          ...node.getData(),
          isSelect:false,
          tempid:new Date().getTime()
        })
      });

      // 节点点击事件
      graph.current.on('node:click', ({ e, x, y, node, view }: any) => {
        if(nodeClick){
          if(node.data.iscopy){
            nodeClick(graph.current.getCellById(node.data.oid));
          }else{
            nodeClick(node);
          }
        }
      
      });
      graph.current.on('edge:click', ({ e, x, y, edge, view }: any) => {
        setselectnode(true);
      });
      //点击画布空白区域 
      graph.current.on('blank:click', ({ e, x, y, node, view }: any) => {
        // resetstyle();
        setselectnode(false);
      });
      // 执行布局
      layout(mapdata);
    } catch (error) {
      console.log(error);
    }
  };

  // 放大缩小画布   code 有三个参数  init 是初始化   enlarge 是放大   narrow是缩小
  const zoomdom = (code: string) => {
    if (code == 'enlarge') {
      const zoom = parseFloat(graph.current.zoom().toFixed(2));
      graph.current.zoomTo(zoom + 0.1);
    } else if (code == 'narrow') {
      const zoom = parseFloat(graph.current.zoom().toFixed(2));
      graph.current.zoomTo(zoom - 0.1);
    }
  };

  // 初始化数据
  const initdata = ()=>{
    mapdata.nodes = mapdata.nodes.map((item: any) => {
      return {
        ...item,
        data:{
          ...item.data,
          // finishRate:allnodes[item.id] ? allnodes[item.id].finishRate : 0,
          // masterRate:allnodes[item.id] ? allnodes[item.id].masterRate : 0,
          finishRate:item.finishRate ? Number(item.finishRate) : 0,
          masterRate:item.masterRate ? Number(item.masterRate) : 0,
          isSelect: false
        }
      }
    });
    initmap(mapdata); 
  }

  useEffect(() => {
    
    // if(mapdata && allnodes && !graph.current){
    if(mapdata && !graph.current){
      initdata();
    }
    
    // 判断是不是初始化完毕
    // if(mapdata && allnodes && graph.current){
    if(mapdata && graph.current){
      initdata();
    }

    return () => {
      if (graph.current) {
        try {
          graph.current.dispose();
          Graph.unregisterReactComponent('react-node-v4');
          graph.current = null;
        } catch (error) {
          console.log(error);
        }
      }
    }
  },[mapdata,showtype])
  // },[mapdata,allnodes,showtype])

  useEffect(()=>{
    // 判断是不是初始化完毕
    if(graph.current){
      initmap(mapdata);
    }
  },[showtype])


  return (
    <div className='mapv4_x6_view'>
      <div className="map_canvas" ref={container}></div>
      {
        showzoombtn &&
        <div className="mapfunction">
          <Tooltip title={t("放大")} placement="right">
            <Button style={{ backgroundColor: 'rgba(0, 0, 0, 0.2)', border: '0' }} icon={<PlusOutlined style={{ color: '#fff' }} />} onClick={() => zoomdom('enlarge')} />
          </Tooltip>
          <Tooltip title={t("缩小")} placement="right">
            <Button style={{ backgroundColor: 'rgba(0, 0, 0, 0.2)', border: '0', marginTop: '20px' }} icon={<MinusOutlined style={{ color: '#fff' }} />} title={t("缩小")} onClick={() => zoomdom('narrow')} />
          </Tooltip>          
        </div>
      }
      {
        showMinimap && 
        <div className='minmap_box' ref={minimap}></div>
      }
      {
        showNumerical && 
        <div className='numerical_box'>
          <div className='item_box'>
            <span>节点颜色与完成率/掌握率关系</span>
          </div>
          <div className='item_box'>
            <div className='box'></div>
            <span>100%-70%</span>
          </div>
          <div className='item_box'>
            <div className='box' style={{background:'#00AEF7'}}></div>
            <span>70%-30%</span>
          </div>
          <div className='item_box'>
            <div className='box' style={{background:'#F29C2B'}}></div>
            <span>30%-0%</span>
          </div>
          <div className='item_box'>
            <div className='box' style={{background:'#52C6D4'}}></div>
            <span>免考核</span>
          </div>
        </div>
      }
    </div>
  );
};

export default GridMap;