import { saveCourse, courseOfSensitivew } from '@/api/addCourse';
import {
  getCourseList,
  getCoverList,
  publishCourseNew,
  unPublishCourse,
  unPublishCourseNew,
} from '@/api/course';
import recycleBinApi from '@/api/recycleBin';
import CourseBlock from '@/components/CourseBlock/CourseBlock';
import DeleteProcess from '@/components/DeleteProcessModal';
import MobileSearch from '@/components/SearchForm/mobileSearch';
import TurnThePageDataItem from '@/components/formItemBox/turnThePageDataItem';
import { IconFont } from '@/components/iconFont';
import useLocale from '@/hooks/useLocale';
import perCfg from '@/permission/config';
import { confirmModal } from '@/utils';
import { PlusCircleFilled, ReloadOutlined, ExclamationCircleTwoTone } from '@ant-design/icons';
import {
  Button,
  Checkbox,
  DatePicker,
  Empty,
  Form,
  Image,
  Input,
  Modal,
  Pagination,
  Popconfirm,
  Popover,
  Select,
  Space,
  Spin,
  Table,
  Tooltip,
  message,
} from 'antd';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import React, { useEffect, useState, useRef } from 'react';
import { useDispatch, useHistory, useSelector } from 'umi';
import './index.less';

const { Option } = Select;
const { RangePicker } = DatePicker;

function MicroList() {
  const { t } = useLocale();
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [newCourseName, setNewCourseName] = useState('');
  const [courseTip, setCourseTip] = useState('');
  const [getinputvalue, setinputvalue] = useState('');
  const [courseName, setCourseName] = useState<string>(''); //课程名称
  const [courseStatus, setCourseStatus] = useState<any>(null); //课程状态
  const [courseTeacher, setCourseTeacher] = useState<string>(''); //教师
  const [page, setPage] = useState<number>(1); // 页码
  const [releaseLoading, setReleaseLoading] = useState<boolean>(false);
  // const [sensitiveWordInfos, setSensitiveWordInfos] = useState<any>([]); // 敏感词信息
  const [size, setSize] = useState<number>(
    Number(localStorage.getItem('microlist_size') ?? 24),
  ); // 页码
  const [loading, setLoading] = useState<boolean>(false); // 加载效果

  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]); //选中的row

  const [publishButton, setPublishButton] = useState<boolean>(true); // 发布按钮禁用状态
  const [unPublishButton, setUnPublishButton] = useState<boolean>(true); // 取消发布按钮禁用状态
  const [recallButton, setRecallButton] = useState<boolean>(true); // 撤回发布状态
  const [deleteButton, setDeleteButton] = useState<boolean>(true); // 删除按钮禁用状态

  const [listData, setListData] = useState<any[]>([]); // 课程列表数据
  const [total, setTotal] = useState<number>(0); //列表数据总数
  const copyParamRef = useRef<any>(null);
  const [modeSwitch, setModeSwitch] = useState<boolean>(
    JSON.parse(localStorage.getItem('learn_view_mode') || '{}').microlist !=
    '0',
  );
  // 视图切换
  const [newSelectedRows, setNewSelectedRows] = useState([]);
  //个人模式
  const [personalMode, setPersonalMode] = useState('');
  //个人模式搜索
  const [personalSearch, setPersonalSearch] = useState('');
  //总的的列表
  const [indeterminate, setIndeterminate] = useState<boolean>(false);
  const [checkAll, setCheckAll] = useState<boolean>(false);

  const jurisdictionList = useSelector<{ jurisdiction: any }, any>(
    ({ jurisdiction }) => {
      return jurisdiction.jurisdictionList;
    },
  );
  const { location }: any = useHistory();
  //如果是工作台跳转过来打开新建页面
  useEffect(() => {
    if (location.query.openShow == 'true') {
      setIsModalVisible(true);
    }
  }, [location]);
  const { mobileFlag } = useSelector<{ config: any }, any>(
    ({ config }) => config,
  );

  const [operatMenuVisible, setOpreatMenuVisible] = useState<boolean>(false);
  const { userInfo } = useSelector<any, any>(state => state.global);

  const { parameterConfig,buttonPermission } = useSelector<
    { global: any },
    { buttonPermission: string[]; parameterConfig: any; permission: any }
  >(state => state.global);

  const [deleteProcessModalVisible, setDeleteProcessModalVisible] = useState(
    false,
  );
  const [processId, setProcessId] = useState<string>('');
  const [addLoading, setAddLoading] = useState<boolean>(false);


  useEffect(() => {
    if (location.query.isAdd == 'true') {
      setIsModalVisible(true);
    }
  }, []);
  //监听到另外一个页面关闭
  // useEffect(() => {
  //   const handleStorage = (e: StorageEvent | Event) => {
  //     if (e instanceof StorageEvent && e.key === 'close_page') {
  //       if (e.newValue === 'true') {
  //         console.log(1221)
  //             callback();
  //       }
  //     }
  //   };
  //   window.addEventListener('storage', handleStorage);
  //   window.addEventListener('customStorage', handleStorage);
  //
  //   return () => {
  //     window.removeEventListener('storage', handleStorage);
  //     window.removeEventListener('customStorage', handleStorage);
  //   };
  // }, []);

  // 敏感词信息dom
  const sensitiveMsgListDom = (list: any) => {

    return '';
    // if (!list || list.length === 0) return ''

    // return list.map((item: any) => <div style={{ padding: '0 15px' }}>
    //   <div style={{ marginTop: 10 }}><ExclamationCircleTwoTone twoToneColor="#faad14" /> 资源  <span>{item.name}</span>：</div>
    //   <div style={{ paddingLeft: 20, margin: '10px 0' }}>
    //     {
    //       item.sensitiveInfos?.map((ele: any) => <div key={ele.source}>
    //         <span>({ele.source}) - 包含敏感词：</span>
    //         <span>{ele.sensitiveMsgs.join('、')}</span>
    //       </div>)
    //     }
    //   </div>
    // </div>)
  }

  // 发布课程敏感词检测
  const checkCourseContent = (
    id: string[],
    status: number,
    enablePublish?: boolean) => {
    // 仅发布操作：发布前先进行敏感词检测

    function openPublishCourseModal(sensiList: any[]) {
      confirmModal(sensitiveMsgListDom(sensiList), () =>
        publishMicroCourse(id, status, enablePublish), t(`${sensiList?.length > 0 ? '课程包含敏感内容，是否继续发布？' : '确认发布这些课程?'}`)
      )
    }

    const params = {
      coursePublishInfos: id.map(item => ({ courseId: item })),
      courseType: 0,
    }
    setReleaseLoading(true);
    courseOfSensitivew(params).then((res) => {
      if (res?.statusCode === 200) {
        // const data = [
        //   {
        //       "guid_": "b2a59903dfc248a6a67601c48deb9c65",
        //       "resourceId": "80dcd4c5bb0549bdab72468bacd2699f",
        //       "name": "sm",
        //       "sensitiveInfos": [
        //           {
        //               "source": "名称",
        //               "sensitiveMsgs": [
        //                   "sm"
        //               ]
        //           }
        //       ]
        //   }
        // ];
        // setSensitiveWordInfos(res.data || []);
        openPublishCourseModal(res.data || []);
      } else {
        openPublishCourseModal([]);
      }
    })
      .finally(() => { setReleaseLoading(false); })
  }

  useEffect(() => {
    setPublishButton(
      selectedRowKeys.length <= 0 ||
      !newSelectedRows.every(
        (item: { publishStatus: number }) => item.publishStatus === 0,
      ),
    );

    setDeleteButton(
      selectedRowKeys.length <= 0 ||
      !newSelectedRows.every(
        (item: { publishStatus: number }) => item.publishStatus === 0,
      ),
    );

    setUnPublishButton(
      selectedRowKeys.length <= 0 ||
      !newSelectedRows.every(
        (item: { publishStatus: number }) => item.publishStatus === 1,
      ),
    );

    setRecallButton(
      selectedRowKeys.length <= 0 ||
      !newSelectedRows.every(
        (item: { publishStatus: number }) => item.publishStatus === 2,
      ),
    );
  }, [newSelectedRows]);
  useEffect(() => {
    const temp = localStorage.getItem('learn_view_mode') || '{}';
    const value = {
      ...JSON.parse(temp),
      microlist: modeSwitch ? '1' : '0',
    };
    localStorage.setItem('learn_view_mode', JSON.stringify(value));
  }, [modeSwitch]);

  //对象转数组
  const objToArr = (obj: any): { label: string; value: string }[] => {
    let arr = [];
    for (const key in obj) {
      arr.push({
        label: obj[key].split(',')[0],
        value: key,
      });
    }
    return arr;
  };
  // 名字改变
  const searchNameChange = (
    event: React.ChangeEvent<HTMLInputElement>,
  ): void => {
    setCourseName(event.target.value);
  };
  const searchNameEnter = (
    event: React.KeyboardEvent<HTMLInputElement>,
  ): void => {
    const param: MicroCourse.searchParams = {
      keyword: courseName,
      publishStatus: courseStatus,
      teacher: courseTeacher ? [courseTeacher] : undefined,
      page: 1,
      size,
    };
    getListData(param);
  };
  // 教师改变
  const onTeacherChange = (value: any): void => {
    setCourseTeacher(value);
    const param: MicroCourse.searchParams = {
      keyword: courseName,
      publishStatus: courseStatus,
      teacher: value ? [value] : undefined,
      page: 1,
      size,
    };
    getListData(param);
  };
  // 搜索
  const search = (): void => {
    const param: MicroCourse.searchParams = {
      keyword: courseName,
      publishStatus: courseStatus,
      teacher:
        personalMode || courseTeacher
          ? [personalMode || courseTeacher]
          : undefined,
      page: 1,
      size,
    };
    getListData(param);
  };
  //表单搜索
  const onSearch = (value: any): void => {
    const param: MicroCourse.searchParams = {
      ...value,
      teacher: Array.isArray(value.teacher) ? value.teacher : [value.teacher],
      page: 1,
      size,
    };
    getListData(param);
  };
  // 清空
  const clear = (): void => {
    setCourseName('');
    setCourseStatus('');
    setCourseTeacher('');
    setPage(1);
    const param: MicroCourse.searchParams = {
      keyword: '',
      publishStatus: '',
      teacher: [],
      page: 1,
      size,
    };
    getListData(param);
  };
  // 表格colums数据
  const columns: any = [
    {
      title: '',
      dataIndex: 'check',
      key: 'check',
      align: 'center',
      width: 50,
      render: (_text: any, record: any) => (
        <Checkbox value={record.contentId_} />
      ),
    },
    {
      title: t('课程封面'),
      dataIndex: 'cover',
      key: 'cover',
      align: 'center',
      width: 120,
      render: (text: any) => <Image width={'100%'} src={text} />,
    },
    {
      title: t('课程名称'),
      dataIndex: 'name_',
      align: 'center',
      width: 270,
      ellipsis: {
        showTitle: true,
      },
      key: 'name_',
    },
    {
      title: t('教师'),
      align: 'center',
      width: 200,
      dataIndex: 'teacher_names',
      ellipsis: {
        showTitle: true,
      },
      key: 'teacher_names',
      render: (teacher: any): React.ReactNode => {
        return Array.isArray(teacher) ? teacher.join(',') : teacher;
      },
    },
    // {
    //   title: '分类',
    //   dataIndex: 'classification',
    //   align: 'left',
    //   width: 110,
    //   key: 'classification',
    //   render: (text: { value: string }[]): React.ReactNode => {
    //     return text.map((item: any) => item.value.split(',')[0]).join('，')
    //   },
    // },

    {
      title: t('开课学院'),
      dataIndex: 'collegeName',
      align: 'center',
      key: 'college',
      ellipsis: {
        showTitle: false,
      },
      render: (text: any) => text?.join('，'),
      // render: (text: { value: string }[]): React.ReactNode => {
      //   let college = Array.isArray(text)
      //     ? text.map((item: any) => item.value).join(',')
      //     : text;
      //   return (
      //     <Tooltip placement="topLeft" title={college} mouseEnterDelay={0.8}>
      //       {college}
      //     </Tooltip>
      //   );
      // },
    },
    {
      title: t('发布状态'),
      dataIndex: 'publishStatus',
      align: 'center',
      key: 'publishStatus',
      render: (text: number): React.ReactNode => {
        const flag = text === 0;
        // '未发布' : '已发布';
        return (
          <span style={{ color: flag ? '#B5B5B5' : 'rgba(0, 0, 0, 0.85)' }}>
            {flag ? t('未发布') : text === 2 ? t('待审核') : t('已发布')}
          </span>
        );
      },
    },
    {
      title: t('创建人'),
      dataIndex: 'createUserName',
      align: 'center',
    },
    {
      title: t('创建时间'),
      dataIndex: 'createDate_',
      align: 'center',
      key: 'createDate_',
      render: (createDate_: string): React.ReactNode => {
        return createDate_;
      },
    },
    {
      title: t('操作'),
      dataIndex: 'publishStatus',
      align: 'center',
      width: 160,
      key: 'action',
      render: (
        text: number,
        record: { contentId_: string; enablePublish: boolean },
      ): React.ReactNode => {
        return (
          <div>
            <Tooltip title={t('预览')}>
              <Button
                type="text"
                icon={<IconFont type="iconviews" />}
                onClick={() => {
                  window.open(
                    `/learn/course/micro/${record.contentId_}?preview=1`,
                  );
                }}
              />
            </Tooltip>
            <Tooltip title={t('数据')}>
              <Button
                type="text"
                icon={<IconFont type="icondata" />}
                onClick={() => {
                  window.open(`#/course/microData?id=${record.contentId_}`);
                }}
              />
            </Tooltip>
            {text === 0
              ? jurisdictionList.includes(perCfg.microcourse_edit) && (
                <Tooltip title={t('编辑')}>
                  <IconFont
                    style={{ margin: '0 6px' }}
                    type="iconedit"
                    onClick={() => {
                      window.open(
                        `./#/course/resource?${record.contentId_}`,
                        record.contentId_,
                      );
                    }}
                  />
                </Tooltip>
              )
              : null}
            {text === 0 ? (
              jurisdictionList.includes(perCfg.microcourse_publish) &&
                parameterConfig?.microcourse_course_release_review !== 'true' ? (
                <Popconfirm
                  title={t('确认发布该课程?')}
                  placement="topRight"
                  onConfirm={() => {
                    checkCourseContent(
                      [record.contentId_],
                      1,
                      record.enablePublish,
                    );
                  }}
                  onCancel={() => { }}
                  okText={t('是')}
                  cancelText={t('否')}
                >
                  <Tooltip title={t('发布')}>
                    <IconFont style={{ margin: '0 6px' }} type="iconrelease" />
                  </Tooltip>
                </Popconfirm>
              ) : parameterConfig?.microcourse_course_release_review ===
                'true' ? (
                <Popconfirm
                  title={t('确认提交至管理员进行审核发布?')}
                  placement="topRight"
                  onConfirm={() => {
                    checkCourseContent(
                      [record.contentId_],
                      2,
                      record.enablePublish,
                    );
                  }}
                  onCancel={() => { }}
                  okText={t('是')}
                  cancelText={t('否')}
                >
                  <Tooltip title={t('发布')}>
                    <IconFont type="iconrelease" style={{ margin: '0 6px' }} />
                  </Tooltip>
                </Popconfirm>
              ) : null
            ) : null}
            {text === 1
              ? jurisdictionList.includes(perCfg.microcourse_publish) && (
                  <Popconfirm
                    title={t('确认取消发布该课程?')}
                    placement="topRight"
                    onConfirm={() => {
                      unPublishMicroCourse([record.contentId_], 1);
                    }}
                    onCancel={() => {}}
                    okText={t('是')}
                    cancelText={t('否')}
                  >
                    <Tooltip title={t('下架')}>
                      <Button
                        type="text"
                        icon={<IconFont type="iconoffShelf" />}
                        disabled={!buttonPermission.includes('microcourse_remove')}
                      >
                      </Button>
                      {/*<IconFont*/}
                      {/*  style={{ margin: '0 6px' }}*/}
                      {/*  type="iconoffShelf"*/}
                      {/*/>*/}
                    </Tooltip>
                  </Popconfirm>
                )
              : null}
            {text === 2
              ? parameterConfig?.microcourse_course_release_review ===
              'true' && (
                <Popconfirm
                  title={t('确认撤回发布审核这些课程?')}
                  placement="topRight"
                  onConfirm={() => {
                    unPublishMicroCourse([record.contentId_], 2);
                  }}
                  onCancel={() => { }}
                  okText={t('是')}
                  cancelText={t('否')}
                >
                  <Tooltip title={t('撤回')}>
                    <IconFont type="iconrecall" style={{ margin: '0 6px' }} />
                  </Tooltip>
                </Popconfirm>
              )
              : null}
            {text === 0
              ? jurisdictionList.includes(perCfg.microcourse_delete) && (
                <Popconfirm
                  title={t('确认删除该课程?')}
                  placement="topRight"
                  onConfirm={() => {
                    deleteMicroCourse([record.contentId_]);
                  }}
                  onCancel={() => { }}
                  okText={t('是')}
                  cancelText={t('否')}
                >
                  <Tooltip title={t('删除')}>
                    <IconFont style={{ margin: '0 6px' }} type="icondelete" />
                  </Tooltip>
                </Popconfirm>
              )
              : null}
          </div>
        );
      },
    },
  ];

  // 变革数据复选框事件
  const onSelectChange = (
    selectedRowKeys: React.ReactText[],
    selectedRows: { contentId_: string; publishStatus: number }[],
  ): void => {
    setPublishButton(
      selectedRowKeys.length <= 0 ||
      !selectedRows.every(
        (item: { publishStatus: number }) => item.publishStatus === 0,
      ),
    );

    setDeleteButton(
      selectedRowKeys.length <= 0 ||
      !selectedRows.every(
        (item: { publishStatus: number }) => item.publishStatus === 0,
      ),
    );

    setUnPublishButton(
      selectedRowKeys.length <= 0 ||
      !selectedRows.every(
        (item: { publishStatus: number }) => item.publishStatus === 1,
      ),
    );

    setSelectedRowKeys(selectedRowKeys);
  };
  useEffect(() => {
    // 控制非管理员状态下，personalMode初始化导致的重复请求
    if (!isSuper && !personalMode) return;

    const param: MicroCourse.searchParams = {
      keyword: courseName,
      publishStatus: courseStatus,
      teacher:
        personalMode || courseTeacher
          ? [personalMode || courseTeacher]
          : undefined,
      page,
      size,
    };
    getListData(param);
  }, [size, page, personalMode]);
  // 页码改变
  const onPageChange = (page: number, size: any): void => {
    setSize(size);
    setPage(page);
    localStorage.setItem('microlist_size', size);
  };

  const isSuper =
    userInfo.roles
      ?.map((item: any) => item.roleCode)
      ?.includes('r_sys_manager') || //是否是系统管理员
    userInfo.roles
      ?.map((item: any) => item.roleCode)
      ?.includes('r_course_manager') || //是否是课程管理员
    userInfo.roles
      ?.map((item: any) => item.roleCode)
      ?.includes('r_second_manager') || //第二权限
    userInfo.roles?.map((item: any) => item.roleCode)?.includes('admin_S1'); // admin

  useEffect(() => {
    if (!isSuper) {
      setPersonalMode(userInfo?.userCode);
    }
  }, [isSuper, userInfo]);
  //把获取数据的逻辑抽离出来
  const getData = (param: MicroCourse.searchParams) => {
    getCourseList(param).then(res => {
      dispatch({
        type: 'config/changeShowLoading',
        payload: {
          value: false,
        },
      });
      if (res && res.message === 'OK') {
        const { results = [], total, page } = res.data;
        setListData(results);
        setPage(page);
        setTotal(total);
      }
      setLoading(false);
    });
  }



  //详情页面关闭，通知本页面获取列表数据
  const updateList = () => {
    // 使用useRef存储的值来获取最新的参数
    const currentParam = copyParamRef.current;
    if (!currentParam) {
      console.error('No param available for update');
      return;
    }
    getData(currentParam);
  }


  // 监听localStorage中的isClosed属性变化
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'isClosed' && e.newValue === 'true') {
        // 当isClosed值变为true时，重新获取列表数据
        console.log('isClosed detected, updating list')
        updateList();
      }
    };

    // 添加storage事件监听器
    window.addEventListener('storage', handleStorageChange);

    // 组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // 同时检查当前窗口的localStorage值
  useEffect(() => {
    const isClosed = window.localStorage.getItem('isClosed');
    if (isClosed === 'true') {
      updateList();
      window.localStorage.removeItem('isClosed');
    }
  }, []);







  // 获取列表数据
  const getListData = (param: MicroCourse.searchParams): void => {
    setSelectedRowKeys([]);
    setLoading(true);
    copyParamRef.current = param;
    getData(param);
  };
  // 发布课程
  const publishMicroCourse = (
    id: string[],
    status: number,
    enablePublish?: boolean,
  ): void => {
    if (newSelectedRows.length === 0) {
      if (!enablePublish) {
        message.error(t('当前课程信息不完善，不能发布！'));
        return;
      }
    } else {
      const unEnablePublishList = newSelectedRows
        .filter((item: any) => !item.enablePublish)
        .map((item: any) => item.name_);
      if (unEnablePublishList.length > 0) {
        message.error(
          `${t('课程')}${unEnablePublishList.join(',')}${t(
            '信息不完善，不能发布！',
          )}`,
        );
        return;
      }
    }
    publishCourseNew(id, isSuper ? 1 : status)
      .then(res => {
        if (res && res?.data?.message == 'OK') {
          const messageText =
            parameterConfig?.microcourse_course_release_review === 'true'
              ? `${isSuper
                ? t('提交成功，您是管理员，已为您自动完成课程发布审核')
                : t('课程已提交至管理员审核！')
              }`
              : t('课程发布成功！');
          message.success(messageText);
          const param: MicroCourse.searchParams = {
            keyword: courseName,
            publishStatus: courseStatus,
            teacher: personalMode || courseTeacher
              ? [personalMode || courseTeacher]
              : undefined,
            page: page,
            size,
          };
          setTimeout(() => getListData(param), 200);
          initStatus();
        } else {
          message.error(res?.data?.message);
        }
      })
      .catch(error => {
        const messageText =
          parameterConfig?.microcourse_course_release_review === 'true'
            ? t('课程提交至管理员审核失败！')
            : t('课程发布失败！');
        message.error(messageText);
      });
  };
  // 取消发布
  const unPublishMicroCourse = (id: string[], status: number): void => {
    if (status === 1) {
      unPublishCourse(id)
        .then(res => {
          if (res && res.data.message == 'OK') {
            const messageText =
              status === 1 ? t('课程取消发布成功！') : t('课程撤回审核成功！');
            message.success(messageText);
            const param: MicroCourse.searchParams = {
              keyword: courseName,
              publishStatus: courseStatus,
              teacher: personalMode || courseTeacher
                ? [personalMode || courseTeacher]
                : undefined,
              page: page,
              size,
            };
            setTimeout(() => getListData(param), 500);
            initStatus();
          } else {
            const messageText =
              status === 1 ? t('课程取消发布失败！') : t('课程撤回审核失败！');
            message.error(messageText + res.data.message);
          }
        })
        .catch(error => {
          message.error(t('课程取消发布失败！'));
        });
    } else {
      unPublishCourseNew(id, { courseType: 0 })
        .then(res => {
          if (res && res.data.message == 'OK') {
            const messageText =
              status === 1 ? t('课程取消发布成功！') : t('课程撤回审核成功！');
            message.success(messageText);
            const param: MicroCourse.searchParams = {
              keyword: courseName,
              publishStatus: courseStatus,
              teacher: courseTeacher ? [courseTeacher] : undefined,
              page: page,
              size,
            };
            setTimeout(() => getListData(param), 500);
            initStatus();
          } else {
            const messageText =
              status === 1 ? t('课程取消发布失败！') : t('课程撤回审核失败！');
            message.error(messageText + res.data.message);
          }
        })
        .catch(error => {
          message.error(t('课程取消发布失败！'));
        });
    }
  };
  const initCheckbox = () => {
    setCheckAll(false);
    setIndeterminate(false);
    setNewSelectedRows([]);
    setSelectedRowKeys([]);
  };
  // 删除课程
  const deleteMicroCourse = (id: string[]): void => {
    Modal.confirm({
      content: t('确认删除课程?'),
      onOk() {
        recycleBinApi
          .deleteCourse(id, { type: 0 })
          .then(res => {
            if (res && res.status === 200) {
              // setProcessId(res.data.data.process_id);
              // setDeleteProcessModalVisible(true);
              callback();
              initCheckbox();
              message.success('删除成功！');
            } else {
              message.error(t('课程删除失败！') + res.message);
            }
          })
          .catch(error => {
            message.error(t('课程删除失败！') + error);
          });
      },
    });
  };
  const callback = () => {
    const param: MicroCourse.searchParams = {
      keyword: courseName,
      publishStatus: courseStatus,
      teacher: personalMode || courseTeacher
        ? [personalMode || courseTeacher]
        : undefined,
      page: page,
      size,
    };
    getListData(param);
  };
  const initStatus = () => {
    setSelectedRowKeys([]);
    setIndeterminate(false);
    setCheckAll(false);
    setNewSelectedRows([]);
    setPublishButton(true);
    setUnPublishButton(true);
    setRecallButton(true);
  };

  // 全选
  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    // setReleaseDis(false);
    setSelectedRowKeys(
      e.target.checked ? listData.map((item: any) => item.contentId_) : [],
    );

    setNewSelectedRows([...listData]);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };
  // 单选
  const onChange = (check: Array<any>) => {
    setSelectedRowKeys(check);
    setNewSelectedRows(
      [...listData].filter((d: any) => check.includes(d.contentId_)),
    );

    setIndeterminate(!!check.length && check.length < listData.length);
    setCheckAll(check.length === listData.length);
  };

  const changenewinput = (e: any) => {
    // 添加限制：输入字符不超过50字符
    const input = e.target.value;
    setinputvalue(input);

    if (input.length > 50) {
      setCourseTip('名称最多50个字符');
      return;
    } else {
      setCourseTip('');
      setNewCourseName(input);
    }
  };

  const handleOk = () => {
    setAddLoading(true);
    getCoverList()
      .then(res => {
        if (res.status == 200) {
          const data = res.data;
          if (data.length > 0) {
            const param = {
              contentId: '',
              name: getinputvalue,
              entityData: {
                contentId: '',
                courseType: 0,
                publishStatus: 0,
                classification: [],
                top: 0,
                describe: '',
                teacher: [],
                school: '',
                college: [],
                subject: [],
                is_attachment_ownload: 1,
                major: [],
                cover: data.map((item: any) => item.url)[
                  Math.floor(Math.random() * data.length)
                ],
                courseResources: [],
                name_: getinputvalue,
                check: 0,
                teacher_names: [],
                collegeName: [],
                majorName: [],
                subjectName: [],
                classificationName: [],
              },
              fileGroups: [
                {
                  groupType: 'other',
                  status: 'ready',
                  groupName: 'attachmentgroup',
                  fileItems: [],
                },
              ],
            };
            return param;
          }
        } else {
          return false;
        }
      })
      .then((param: any) => {
        if (param) {
          return saveCourse(param).then(res => {
            if (res.message === 'OK') {
              setNewCourseName('');
              setIsModalVisible(false);
              window.open(`./#/course/resource?${res.data}`);
            } else {
              message.error(res.message);
            }
          });
        }
      })
      .finally(() => {
        setAddLoading(false);
      });
  };
  const reset = () => {
    form.resetFields();
    search();
  };
  //按钮列表
  let btn_list: any = [];
  if (
    jurisdictionList.includes(perCfg.microcourse_publish) &&
    parameterConfig?.microcourse_course_release_review !== 'true'
  ) {
    btn_list.push({
      title: t('发布'),
      disabled: publishButton,
      func: () => {
        // confirmModal(sensitiveMsgListDom(sensitiveWordInfos), () =>
        //   publishMicroCourse(selectedRowKeys, 1), t(`${sensitiveWordInfos?.length > 0 ? '课程包含敏感内容，是否继续发布？' : '确认发布这些课程?'}`)
        // ),
        checkCourseContent(selectedRowKeys, 1);
      },
      dom: (
        <Button
          type="link"
          ghost
          icon={<IconFont type="iconrelease" />}
          onClick={() => {
            // confirmModal(sensitiveMsgListDom(sensitiveWordInfos), () =>
            //   publishMicroCourse(selectedRowKeys, 1), t(`${sensitiveWordInfos?.length > 0 ? '课程包含敏感内容，是否继续发布？' : '确认发布这些课程?'}`)
            // );
            checkCourseContent(selectedRowKeys, 1);
          }
          }
          disabled={publishButton}
        >
          {t('发布')}
        </Button>
      ),
    });
  }
  if (parameterConfig?.microcourse_course_release_review === 'true') {
    btn_list.push({
      title: t('发布'),
      disabled: publishButton,
      func: () => {
        // confirmModal(sensitiveMsgListDom(sensitiveWordInfos), () => {
        //   publishMicroCourse(selectedRowKeys, 1);
        // }, t(`${sensitiveWordInfos?.length > 0 ? '课程包含敏感内容，是否继续发布？' : '确认发布这些课程?'}`));
        checkCourseContent(selectedRowKeys, 1);
      },
      dom: (
        <Button
          type="link"
          ghost
          icon={<IconFont type="iconrelease" />}
          disabled={publishButton}
          onClick={() => {
            // confirmModal(sensitiveMsgListDom(sensitiveWordInfos), () =>
            //   publishMicroCourse(selectedRowKeys, 1), t(`${sensitiveWordInfos?.length > 0 ? '课程包含敏感内容，是否继续发布？' : '确认发布这些课程?'}`)
            // );
            checkCourseContent(selectedRowKeys, 1);
          }
          }
        >
          {t('发布')}
        </Button>
      ),
    });
    btn_list.push({
      title: t('撤回'),
      disabled: recallButton,
      func: () => {
        confirmModal('确认撤回发布审核这些课程?', () =>
          unPublishMicroCourse(selectedRowKeys, 2),
        );
      },
      dom: (
        <Button
          type="link"
          ghost
          icon={<IconFont type="iconrecall" />}
          disabled={recallButton}
        >
          {t('撤回')}
        </Button>
      ),
    });
  }
  if (jurisdictionList.includes(perCfg.microcourse_publish)) {
    btn_list.push({
      title: t('下架'),
      disabled: unPublishButton ||  !buttonPermission.includes('microcourse_remove'),
      func: () => {
        confirmModal('确认取消发布这些课程?', () =>
          unPublishMicroCourse(selectedRowKeys, 1),
        );
      },
      dom: (
        <Button
          type="link"
          ghost
          icon={<IconFont type="iconoffShelf" />}
          disabled={unPublishButton ||  !buttonPermission.includes('microcourse_remove')}
          onClick={() =>
            confirmModal('确认取消发布这些课程?', () =>
              unPublishMicroCourse(selectedRowKeys, 1),
            )
          }
        >
          {t('下架')}
        </Button>
      ),
    });
  }
  if (jurisdictionList.includes(perCfg.microcourse_delete)) {
    btn_list.push({
      title: t('删除'),
      disabled: deleteButton,
      func: () => {
        deleteMicroCourse(selectedRowKeys);
      },
      dom: (
        <Button
          type="link"
          ghost
          icon={<IconFont type="icondelete" />}
          disabled={deleteButton}
          onClick={() => deleteMicroCourse(selectedRowKeys)}
        >
          {t('删除')}
        </Button>
      ),
    });
  }
  const handleAddCourse = () => {
    if (newCourseName) {
      handleOk();
    } else {
      setNewCourseName('');
      setIsModalVisible(false);
      message.error('请填写课程名称');
    }
  };
  return (
    <div className="micro-list">
      <Spin spinning={loading}>
        {mobileFlag ? (
          <MobileSearch
            resourceSearch={onSearch}
            selected={'micro'}
            form={form}
            reset={reset}
            showPersonal={isSuper && !!personalMode}
            isShowSemester={false}
          />
        ) : (
          <div className="micro-mode-wrap">
            {!!personalMode ? (
              <div />
            ) : (
              <div className="search-group">
                <Input
                  autoComplete="off"
                  placeholder={t('请输入课程名')}
                  value={courseName}
                  onChange={searchNameChange}
                  onPressEnter={searchNameEnter}
                  style={{ width: 250 }}
                  allowClear
                />

                <TurnThePageDataItem
                  message={t('请选择教师')}
                  type={0}
                  value={courseTeacher ? courseTeacher : undefined}
                  onTeacherChange={onTeacherChange}
                />

                <Select
                  placeholder={t('请选择发布状态')}
                  allowClear
                  style={{ width: 180 }}
                  value={courseStatus}
                  onChange={value => {
                    setCourseStatus(value);
                  }}
                >
                  <Option value="">{t('全部')}</Option>
                  <Option value="0">{t('未发布')}</Option>
                  <Option value="1">{t('已发布')}</Option>
                  {parameterConfig?.microcourse_course_release_review ===
                    'true' && <Option value="2">{t('待审核')}</Option>}
                </Select>
                <div className="reset-wrp" onClick={clear}>
                  <span>{t('清空')}</span>
                  <ReloadOutlined />
                </div>
                <Button
                  type="primary"
                  onClick={search}
                  style={{ marginLeft: 16 }}
                >
                  {t('搜索')}

                  <IconFont type="iconsousuo2" />
                </Button>
                {/* <Button onClick={clear}>清空</Button> */}
              </div>
            )}
            {isSuper && !personalMode && (
              <div
                className="mode-btn"
                onClick={() => {
                  setPersonalMode(pre => {
                    return !!pre ? '' : userInfo?.userCode;
                  });
                  setCourseTeacher('');
                  setCourseName('');
                  setCourseStatus('');
                  setPersonalSearch('');
                  setPage(1);
                  if (!personalMode) {
                    setModeSwitch(true);
                  }
                }}
              >
                {t('切换')}
                {!!personalMode ? t('管理') : t('个人')}
                {t('模式')}
              </div>
            )}
          </div>
        )}

        {!personalMode ? <div className="splitLine"></div> : null}
        <Space className="action-button">
          <Checkbox
            indeterminate={indeterminate}
            onChange={onCheckAllChange}
            checked={checkAll}
          >
            {t('全部')}
          </Checkbox>
          <div className="btn-wrp">
            {jurisdictionList.includes(perCfg.microcourse_new) && (
              <Button
                type="primary"
                shape="round"
                icon={<PlusCircleFilled />}
                onClick={() => {
                  if (mobileFlag) {
                    message.info('暂不支持手机端，请前往电脑端操作');
                  } else {
                    setIsModalVisible(true);
                  }
                }}
              >
                {t('新建')}
              </Button>
            )}

            {!mobileFlag ? (
              btn_list.map((item: any, index: number) => {
                return (
                  <div
                    className={`item_${item.disabled ? ' disabled' : ''}`}
                    key={index}
                  >
                    {item.dom}
                  </div>
                );
              })
            ) : (
              //移动端取前几个展示即可
              <>
                {!isSuper &&
                  btn_list.slice(0, 1).map((item: any, index: number) => {
                    return (
                      <div
                        className={`item_${item.disabled ? ' disabled' : ''}`}
                        key={index}
                      >
                        {item.dom}
                      </div>
                    );
                  })}

                {btn_list.slice(1, btn_list.length).length > 0 && (
                  <Popover
                    className="mobile_btns_popover"
                    onOpenChange={(newOpen: boolean) =>
                      setOpreatMenuVisible(newOpen)
                    }
                    open={operatMenuVisible}
                    content={
                      <div className="mobile_btns">
                        {btn_list
                          .slice(1, btn_list.length)
                          .map((item: any, index: number) => {
                            return (
                              <div
                                key={index}
                                className={item.disabled ? 'disabled' : ''}
                                onClick={() => {
                                  if (!item.disabled) {
                                    debugger;
                                    setOpreatMenuVisible(false);
                                    item.func();
                                  }
                                }}
                              >
                                {item.dom}
                              </div>
                            );
                          })}
                      </div>
                    }
                  >
                    <Button
                      onClick={(e: any) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setOpreatMenuVisible(!operatMenuVisible);
                      }}
                    >
                      <IconFont type="iconziyuanku1" />
                      {t('管理')}
                    </Button>
                  </Popover>
                )}
                {mobileFlag && isSuper && !personalMode && (
                  <div
                    className="mode-btn"
                    onClick={() => {
                      setPersonalMode(pre => {
                        return !!pre ? '' : userInfo?.userCode;
                      });
                      setCourseTeacher('');
                      setCourseName('');
                      setCourseStatus('');
                      setPersonalSearch('');
                      setPage(1);
                      if (!personalMode) {
                        setModeSwitch(true);
                      }
                    }}
                  >
                    {t('切换')}
                    {!!personalMode ? t('管理') : t('个人')}
                    {t('模式')}
                  </div>
                )}
              </>
            )}
          </div>
          {!personalMode ? (
            <div className="mode_switch_wrapper">
              <div onClick={() => setModeSwitch(true)} className="mode_switch">
                <Tooltip title={t('图例模式')}>
                  <IconFont
                    type="iconhebingxingzhuangfuzhi2"
                    className={modeSwitch ? 'active' : ''}
                  />
                </Tooltip>
              </div>
              <div onClick={() => setModeSwitch(false)} className="mode_switch">
                <Tooltip title={t('列表模式')}>
                  <IconFont
                    type="iconliebiao"
                    className={modeSwitch ? '' : 'active'}
                  />
                </Tooltip>
              </div>
            </div>
          ) : (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Input
                placeholder={t('搜索课程名称')}
                onChange={(e: any) => setPersonalSearch(e.target.value)}
                suffix={
                  <IconFont
                    type="iconsousuo2"
                    onClick={(e: any) => {
                      getListData({
                        keyword: personalSearch,
                        publishStatus: '',
                        teacher: [personalMode],
                        page: 1,
                        size,
                      });
                    }}
                  />
                }
                autoComplete="off"
                style={{ width: 200, marginRight: '20px' }}
                onPressEnter={(e: any) => {
                  getListData({
                    keyword: personalSearch,
                    publishStatus: '',
                    teacher: [personalMode],
                    page: 1,
                    size,
                  });
                }}
              />
              {isSuper && (
                <div
                  className="mode-btn"
                  onClick={() => {
                    setPersonalMode(pre => {
                      return !!pre ? '' : userInfo?.userCode;
                    });
                    setCourseTeacher('');
                    setCourseName('');
                    setCourseStatus('');
                    setPersonalSearch('');
                    setPage(1);
                    if (!personalMode) {
                      setModeSwitch(true);
                    }
                  }}
                >
                  {t('切换')}
                  {!!personalMode ? t('管理') : t('个人')}
                  {t('模式')}
                </div>
              )}
            </div>
          )}
        </Space>

        <div className="course-list">
          <Checkbox.Group
            value={selectedRowKeys}
            onChange={onChange}
            style={{ width: '100%' }}
          >
            {modeSwitch ? (
              <div className="data_wrapper">
                {listData.length > 0 ? (
                  listData?.map((item: any) => (
                    <CourseBlock
                      key={item.contentId_}
                      item={item}
                      onEdit={() => {
                        if (mobileFlag) {
                          message.info('暂不支持手机端，请前往电脑端操作');
                        } else {
                          window.open(
                            `./#/course/resource?${item.contentId_}`,
                            item.contentId_,
                          );
                        }
                      }}
                      onData={() => {
                        window.open(`#/course/microData?id=${item.contentId_}`);
                      }}
                      onDelete={() => {
                        deleteMicroCourse([item.contentId_]);
                      }}
                      onPublish={() => {
                        const status =
                          parameterConfig?.microcourse_course_release_review ===
                            'true'
                            ? 2
                            : 1;
                        checkCourseContent(
                          [item.contentId_],
                          1,
                          item.enablePublish,
                        );
                      }}
                      onUnPublish={() => {
                        unPublishMicroCourse(
                          [item.contentId_],
                          item.publishStatus,
                        );
                      }}
                      onPreview={() =>
                        window.open(
                          `/learn/course/micro/${item.contentId_}?preview=1`,
                        )
                      }
                    />
                  ))
                ) : (
                  <Empty style={{ width: '100%' }} />
                )}
              </div>
            ) : (
              <Table
                rowKey="contentId_"
                // rowSelection={{
                //   selectedRowKeys,
                //   onChange: onSelectChange,
                // }}
                columns={columns}
                dataSource={listData}
                scroll={{ y: 'calc(100vh - 320px)' }}
                size="small"
                pagination={false}
              />
            )}
          </Checkbox.Group>
          {total > 0 && (
            <Pagination
              style={{ textAlign: 'center', marginTop: 10 }}
              {...{
                size: 'small',
                showQuickJumper: true,
                showTotal: total => t('共{name}条', String(total)),
                current: page,
                total: total,
                pageSize: size,
                showSizeChanger: true,
                pageSizeOptions: ['24', '36', '48', '60'],
                onChange: onPageChange,
              }}
            />
          )}

          <Modal
            title={t('添加课程')}
            visible={isModalVisible}
            onOk={handleAddCourse}
            onCancel={() => {
              setIsModalVisible(false);
              setNewCourseName('');
              setCourseTip('');
            }}
            className="Mooc-addcourse"
            confirmLoading={addLoading}
          >
            <div className="course_name_container">
              <label>
                <span className="course_label">{t('课程名称：')}</span>
                <Input
                  style={{ width: 300 }}
                  placeholder={t('请输入课程名称')}
                  value={newCourseName}
                  onChange={changenewinput}
                  required
                  onKeyDown={(e: any) => {
                    e.keyCode === 13 && handleAddCourse();
                  }}
                />
              </label>
              {/* <div className="course_tip">{courseTip}</div> */}
            </div>
          </Modal>
        </div>
      </Spin>
      <DeleteProcess
        title={t('删除进度')}
        // key={processId}
        visible={deleteProcessModalVisible}
        processId={processId}
        closeModal={() => setDeleteProcessModalVisible(false)}
        callback={callback}
      />
    </div>
  );
}

export default MicroList;
