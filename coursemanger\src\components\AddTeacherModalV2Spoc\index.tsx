import React, {
useState,
useEffect,
KeyboardEvent,
FocusEvent,
useRef, 
useMemo} from
'react';
import {
Modal,
Input,
Button,
Tree,
message,
Form,
Table,
Space,
Select,
Checkbox,
Avatar, 
Tabs} from
'antd';
const { Option } = Select;
import { useIntl, useSelector, useDispatch, useHistory } from 'umi';
import { TeamOutlined, UserOutlined } from '@ant-design/icons';
import './index.less';
import { addmapteacher, deletemapteacher, querymapteacher } from '@/api/coursemap';
import { addteacher, deleteteacher, getteacherlist } from '@/api/teacher';
import useLocale from '@/hooks/useLocale';
import TeacherList from './TeacherList';
import { microRoles, normalRoles } from '@/pages/TeachingTeam';
// import IStudent from '@/types/student';
const { DirectoryTree } = Tree;
interface CreateModalProps {
  courseModules?: any[];
  addOrEdit: string;
  modalVisible: boolean;
  modalClose: () => void;
  refresh: () => void; // 刷新
  relationPeople: any;
  ismapa?: boolean; //是否是地图的添加
}
interface ITreeItemProps {
  code: string;
  title: string;
  key: string;
  children?: Array<ITreeItemProps>;
  icon?: React.ReactNode;
  parentName?: string;
  parentId?: number;
  parentCode?: string;
}
const AddTeacherModalV2Spoc: React.FC<CreateModalProps> = (props) => {
  const { t } = useLocale();

  const history: any = useHistory();;
  const [step, setStep] = useState(1);
  const { modalClose, modalVisible, refresh, addOrEdit, ismapa, courseModules } = props;
  const [selectedTeacher, setSelectedTeacher] = useState<any[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Array<any>>([]);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [initialRowKeys, setInitialRowKeys] = useState<string[]>([]);
  const [relationPeople, setRelationPeople] = useState<string[]>([]);
  const isMicro = history.location.query.type === "microMajor"
  const roles = isMicro ? microRoles : normalRoles;
  useEffect(() => {
    if (modalVisible) {
      // 课程图谱的添加管理员
      if (ismapa) {
        // 查询所有的管理员
        querymapteacher({
          mapId: history.location.query.id
        }).then((res: any) => {
          if (res && res.status === 200) {
            setRelationPeople(res.data);
            setSelectedRows(res.data);
            setSelectedRowKeys(
            res.data.map((n: any) => {
              return n.userCode;
            }));

            setInitialRowKeys(
            res.data.map((n: any) => {
              return n.userCode;
            }));

          }
        });
      } else {
        getteacherlist({ id: history.location.query.id }).then((res: any) => {
          if (res && res.status === 200) {
            setRelationPeople(res.data);
            setSelectedRows(res.data);
            setSelectedRowKeys(
            res.data.map((n: any) => {
              return n.userCode;
            }));

            setInitialRowKeys(
            res.data.map((n: any) => {
              return n.userCode;
            }));

          }
        });
      }
    }
  }, [modalVisible]);

  const handleNextStep = () => {
    let selected = selectedRows.filter((n: any) => n);
    let addKey = selected.filter(
    (code: any) =>
    initialRowKeys.indexOf(code.userCode ? code.userCode : code.user_code) <
    0);

    if (addKey.length === 0) {
      message.error(t('请选择教师'));
      return;
    }
    setStep(2);
    let data: any = {
      modifyBasicInfo: 0,
      chapterRevision: 0,
      modifyCourseNotice: 0,
      studentManagement: 0,
      teachingTeamManage: 0,
      modifySettings: 0
    };
    setSelectedTeacher(
    selectedRows.
    filter((item: any) => item).
    filter(
    (code: any) =>
    initialRowKeys.indexOf(
    code.userCode ? code.userCode : code.user_code) <
    0).
    map((item: any) => ({ ...item, type: roles.filter(item => item !== "课程负责人")[0], authority: data })));

  };
  const modalOk = () => {
    const deleteKey = relationPeople.filter(
    (code: any) => selectedRows.map((item: any) => item.userCode ?? item.user_code).indexOf(code.userCode) < 0);

    // console.log(deleteKey)
    // let selected = selectedRows.filter((n: any) => n);
    // let addKey = selected.filter(
    //   (code: any) =>
    //     initialRowKeys.indexOf(code.userCode ? code.userCode : code.user_code) <
    //     0,
    // );
    let addKey = selectedTeacher;
    if (addKey.length) {
      let unSetAuthArr: string[] = [];
      addKey = addKey.map((item: any) => {
        const unSetAuth: boolean = Object.keys(item.authority).every((el) => item.authority[el] === 0);
        if (unSetAuth) {
          unSetAuthArr.push(item.nick_name);
        }
        return {
          userCode: item.user_code,
          name: item.nick_name,
          jobNumber: item.extend.account,
          college: item.extend.college,
          school: item.extend.school,
          position: item.extend.position,
          // major: item.extend.major,
          sex: item.extend.sex,
          type: item.type,
          authority: item.authority,
          avatarUrl: item.avatar_url,
          ...(isMicro ? {          
            teachingModuleNodeId: item.teachingModuleNodeId?.join(","),
            teachingModuleNodeName: item.teachingModuleNodeId?.map((item_: any) => courseModules?.find((i: any) => i.nodeId === item_)?.nodeName)?.join(",") ?? "",
          } : {})
        };
      });
      //后台默认给予全部权限 不在手动勾选
      // if (unSetAuthArr.length > 0) {
      //   message.error(`${unSetAuthArr.join(",")} 没有设置权限，无法保存`);
      //   return
      // }
      if (ismapa) {
        let newarr = addKey.map((item: any) => {
          let role = 2; //1 管理员 2//编辑者 
          if (item.type == t("管理员")) {
            role = 1;
          }
          return {
            college: item.college,
            cover: item.avatarUrl,
            mapId: history.location.query.id,
            role: role,
            userCode: item.userCode,
            userName: item.name
          };
        });
        addmapteacher(newarr).then((res: any) => {
          if (res && res.status === 200) {
            message.success(t('添加成功'));
            if (deleteKey.length) {
              let deletedata: string[] = [];
              deleteKey.forEach((item: any) => {
                deletedata.push(item.id);
              });
              deletemapteacher(deletedata).then((res: any) => {
                if (res && res.status === 200) {
                  message.success(t('删除成功'));
                  refresh();
                }
                handleCancel();
              });
            } else {
              handleCancel();
            }
          } else if (res && res.status === 400) {
            message.error(res.message);
          } else {
            message.error(t('添加失败'));
          }
          refresh();
          handleCancel();
        });
      } else {
        addteacher(history.location.query.id, addKey).then((res: any) => {
          if (res && res.status === 200) {
            message.success(t('添加成功'));
            if (deleteKey.length) {
              let deletedata: string[] = [];
              deleteKey.forEach((item: any) => {
                deletedata.push(item.id);
              });
              deleteteacher(history.location.query.id, deletedata).then((res: any) => {
                if (res && res.status === 200) {
                  message.success(t('删除成功'));
                  refresh();
                }
                handleCancel();
              });
            } else {
              handleCancel();
            }
          } else if (res && res.status === 400) {
            message.error(res.message);
          } else {
            message.error(t('添加失败'));
          }
          refresh();
          handleCancel();
        });
      }

    } else {
      if (deleteKey.length) {
        let deletedata: string[] = [];
        deleteKey.forEach((item: any) => {
          deletedata.push(item.id);
        });
        deleteteacher(history.location.query.id, deletedata).then((res: any) => {
          if (res && res.status === 200) {
            message.success(t('删除成功'));
            refresh();
          }
          handleCancel();
        });
      } else {
        handleCancel();
      }
    }

  };
  const handleRoleChange = (value: string[], code: string, key: string) => {
    const newTeacher = [...selectedTeacher];
    const index = newTeacher.findIndex((item) => item.user_code === code);
    if (index > -1) {
      newTeacher.splice(index, 1, {
        ...newTeacher[index],
        ...(key === "type" ? { type: value } : {          
          teachingModuleNodeId: value,
          teachingModuleNodeName: courseModules?.find((item_: any) => value === item_.nodeId)?.nodeName
        })
      });
      setSelectedTeacher(newTeacher);
    }
  };
  const handleCancel = () => {
    setStep(1);
    modalClose();
  }
  const handleSelected = (rows: any, roleCode: string) => {
    if (isMicro) {
      setSelectedRows(rows)
    } else {
      setSelectedRows((r_: any) => {
        return r_.filter((row: any) => row.roleCode !== roleCode).concat(rows);
      });
    }
    
  };
  const items = useMemo(() => 
    [
      {
        key: "1",
        label: "教师",
        children: <TeacherList
          roleCode='r_teacher'
          choseTeacher={relationPeople.filter((item: any) => item.roleCode !== "r_student")}
          open={modalVisible}
          onSelected={handleSelected}
        />
      },
      {
        key: "2",
        label: "学生",
        children: <TeacherList
          roleCode='r_student'
          choseTeacher={relationPeople.filter((item: any) => item.roleCode === "r_student")}
          open={modalVisible}
          onSelected={handleSelected}
        />
      },
    ], [relationPeople, modalVisible])
  return (
    <Modal
    destroyOnClose
    title={t("添加人员")}
    open={modalVisible}
    onCancel={handleCancel}
    wrapClassName='add-teacher-modal'
    // closable={false}
    width={step == 1 ? 1200 : 600}
    // footer={[
    //   <Button key="submit" type="primary" onClick={modalOk}>
    //     确定
    //   </Button>,
    //   <Button key="back" onClick={modalClose}>
    //     取消
    //   </Button>,
    // ]}
    footer={
    <div style={{ textAlign: 'center' }}>
          {step === 1 && addOrEdit === 'add' ?
      <Button onClick={handleNextStep}>{t("下一步")}</Button> :

      <Space>
              {addOrEdit === 'add' ?
        <Button onClick={() => setStep(1)}>{t("上一步")}</Button> :
        null}
              <Button onClick={modalOk} type="primary">{t("确定")}

        </Button>
            </Space>}
      
        </div>}>

      
      {addOrEdit === 'add' && step === 1 ?
      // 公开课 图谱课 班级课 知识地图进入都可以选择学生
      (history.location.query.type === "mooc" || history.location.query.type === "spoc" || history.location.query.type === "map" || 
        history.location.pathname.includes("/mapv4/teachingteam" )? <Tabs defaultActiveKey="1" items={items} /> : 
        <TeacherList
          roleCode='r_teacher'
          choseTeacher={relationPeople}
          open={modalVisible}
          onSelected={handleSelected}
        />) :
        <div className="permissions_setting">
          {
            <div className="teacher_block">
              {selectedTeacher.map((teacher) =>
                <div className="teacher-item" key={teacher.nick_name}>
                  <div className="teacher-item-left">
                    {teacher.avatarUrl ?
                      <Avatar
                        shape="circle"
                        size={64}
                        src={teacher.avatarUrl}
                        icon={<UserOutlined />} /> :
                      <Avatar
                        shape="circle"
                        size={64}
                        icon={<UserOutlined />} />}
                    <div className="teacher_baseInfo">
                      <h2>
                        {teacher.nick_name}
                        {/* <span>
                     {teacher.extend?.school || '——'}/
                     {teacher.extend?.college || '——'}
                    </span> */}
                      </h2>
                      <p>{t("工号：")}{teacher.extend.account}</p>
                      {/* <p className='auth-required'>
                   权限：
                   <Checkbox.Group
                     onChange={(value: any) =>
                       handlePermChange(value, teacher.user_code)
                     }
                     options={options}
                     // defaultValue={['Pear']}
                   />
                  </p> */}
                    </div>
                  </div>
                  <div className='input-wrp'>
                    <label>{t("角色：")}
                    </label>
                    <Select
                      value={ismapa ? t("编辑者") : teacher.type}
                      onChange={(value) =>
                        handleRoleChange(value, teacher.user_code, "type")}
                      style={{ width: 150 }}>
                      {ismapa ?
                        <>
                          {/* <Option value="管理员">管理员</Option> */}
                          <Option value={t("编辑者")}>{t("编辑者")}</Option>
                        </> :
                        <>
                          {roles.filter(item => item !== "课程负责人" && !(teacher.roles?.map((item: any) => item.code)?.includes('r_sys_manager') && ["专业负责管理员", "授课教师"].includes(item))).map(item => <Option value={item} key={item}>{item}</Option>)}
                        </>}
                    </Select>
                    {(isMicro && teacher.type === "授课教师") && <div className='input-wrp' style={{ marginTop: "10px" }}>
                      <label>{t("授课模块：")}</label>
                      <Select
                        value={teacher.teachingModuleNodeId}
                        mode="multiple"
                        onChange={(value) => handleRoleChange(value, teacher.user_code, "teachingModuleNodeId")}
                        style={{ width: 200 }} placeholder={t("请选择授课模块")}
                      >
                        {courseModules?.map((item: any) => <Option value={item.nodeId} key={item.nodeId}>{item.nodeName}</Option>)}
                      </Select>
                    </div>}
                  </div>
                </div>)}
            </div>}
        
          {/* <Form labelCol={{ span: 7 }} form={editform}>
           <Form.Item
             label="类别"
             name="category"
             rules={[{ required: true, message: '请选择类别' }]}
           >
             <Select style={{ width: 200 }} placeholder="请选择类别">
               <Option value="教师">教师</Option>
               <Option value="助教">助教</Option>
             </Select>
           </Form.Item>
           <Form.Item
             label="权限"
             name="jurisdiction"
             rules={[{ required: true, message: '请至少选择一个权限' }]}
           >
             <Checkbox.Group
               options={options}
               // defaultValue={['Pear']}
             />
           </Form.Item>
          </Form> */}
        </div>}
      
    </Modal>);

};

export default AddTeacherModalV2Spoc;