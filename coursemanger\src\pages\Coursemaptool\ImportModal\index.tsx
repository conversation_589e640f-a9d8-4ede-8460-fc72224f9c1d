import React, { FC, useRef, useState } from 'react';
import { Modal, Upload, Button, message } from 'antd';
import Icon, { DeleteOutlined } from '@ant-design/icons';
import { ReactComponent as plus_icon } from "@/assets/imgs/icon/plus_icon.svg";
import chapterApis from "@/api/chapter";
import { downloadtemplate, importword, importexcel } from '@/api/coursemap';
import { useLocation } from "umi";
import "./index.less";
import useLocale from '@/hooks/useLocale';

const { Dragger } = Upload;
const { confirm } = Modal;
interface IWordImport {
  visible: number;
  onClose: () => void;
  onSuccess: () => void;
  perviewtype: number;
  mapid: any;
}

const PlusIcon = (props: any) => <Icon component={plus_icon} {...props} />;

const ImportModal: FC<IWordImport> = ({ visible, onClose, onSuccess, perviewtype, mapid }) => {
  const { t } = useLocale();
  const [fileData, setFileData] = useState<any>(null);
  //  文件后缀
  const [fileType, setFileType] = useState<string>("");
  const { query }: any = useLocation();
  const [loading, setLoading] = useState<boolean>(false);

  const props = {
    name: 'file',
    accept:'.doc,.docx,.xls,.xlsx',
    showUploadList: false,
    // action: '/rman/v1/upload/reference/material/import',
    beforeUpload(file: any) {
      const nameArr = file.name.split('.');
      if (nameArr[nameArr.length - 1] === 'doc' || nameArr[nameArr.length - 1] === 'docx' || nameArr[nameArr.length - 1] === 'xls' || nameArr[nameArr.length - 1] === 'xlsx') {
        setFileData(file);
        setFileType(nameArr[nameArr.length - 1]);
      } else {
        message.warning(t("只能上传excel和word文件！"));
      }
      return false;
    },
    onDrop(e: any) {
      console.log('Dropped files', e.dataTransfer.files);
    }
  };


  const handleImport = () => {
    confirm({
      title: t(`导入数据会覆盖当前编辑内容，是否确定？`),
      onOk() {
        if (!fileData.name) {
          message.warning(t("请先上传文件再导入！"));
          return;
        }
        setLoading(true);
        const formData = new FormData();
        formData.append("file", fileData);
        let courseId = null;
        if (perviewtype == 1) {
          courseId = query.id;
        }
        if (fileType == 'doc' || fileType == 'docx') {
          importword(mapid, courseId, formData).then((res: any) => {
            if (res.status === 200) {
              message.success(t("导入成功"));
            } else {
              message.error(t("导入失败：") + res.message);
              // 下载base64文件       
              base64ToExcel(res.data.excelMessage, t('图谱导入错误信息.xlsx'));
            }
          }).finally(() => {
            onSuccess();
            handleClose();
            setLoading(false);
          });
        } else if (fileType == 'xls' || fileType == 'xlsx') {
          importexcel(mapid, courseId, formData).then((res: any) => {
            if (res.status === 200) {
              message.success(t("导入成功"));
            } else {
              message.error(t("导入失败：") + res.message);
              base64ToExcel(res.data.excelMessage, t('图谱导入错误信息.xlsx'));
            }
          }).finally(() => {
            handleClose();
            onSuccess();
            setLoading(false);
          });
        } else {
          message.warning(t("只能上传excel和word文件！"));
          setLoading(false);
        }
      },
      onCancel() { }
    });
  };

  function base64ToExcel(base64Data: any, fileName: String) {
    // 解码 Base64 字符串为二进制数据
    const binaryString = atob(base64Data);
    // 将二进制数据转换为字节数组
    const byteNumbers = new Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      byteNumbers[i] = binaryString.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);

    // 创建 Blob 对象
    const blobData = new Blob([byteArray], { type: 'application/vnd.ms-excel' });

    // 创建 data URI
    const url = URL.createObjectURL(blobData);

    // 设置下载链接的 href 属性为 data URI
    const downloadLink: any = document.createElement('a');
    downloadLink.href = url;
    downloadLink.download = fileName;
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
  }

  const handleClose = () => {
    setFileData(null);
    onClose();
  };
  const handleDelete = () => {
    setFileData(null);
  };

  const handleDownload = (e: any, type: number) => {
    e.stopPropagation();
    downloadtemplate(type).then((res) => {
      const blobURL = window.URL.createObjectURL(res);
      // 创建a标签，用于跳转至下载链接
      const tempLink = document.createElement('a');
      tempLink.style.display = 'none';
      tempLink.href = blobURL;
      if (type === 0) {
        tempLink.setAttribute('download', t('图谱导入模板.xls'));
      } else {
        tempLink.setAttribute('download', t('图谱导入模板.doc'));
      }
      // 兼容：某些浏览器不支持HTML5的download属性
      if (typeof tempLink.download === 'undefined') {
        tempLink.setAttribute('target', '_blank');
      }
      // 挂载a标签
      document.body.appendChild(tempLink);
      tempLink.click();
      document.body.removeChild(tempLink);
      // 释放blob URL地址
      window.URL.revokeObjectURL(blobURL);
    });
  };

  return <Modal title={t("导入图谱")} open={visible == 7} footer={null} onCancel={handleClose}>
    <div className="import-map-chapter">
      {
        fileData?.name ? <div className='file-name'>{fileData?.name}<DeleteOutlined onClick={handleDelete} /></div> : <Dragger {...props}>
          <p className="ant-upload-drag-icon">
            <PlusIcon />
          </p>
          <p className="ant-upload-text">{t("将文件拖拽至此处，或")}<a>{t("点击上传")}</a></p>
          {/* <p className="ant-upload-text">上传成功的文件可在个人资源-课程上传资源文件夹里找到</p> */}
          <a className='download-text' onClick={(e) => handleDownload(e, 0)}>{t("下载excel导入模板")}</a>
          <a className='download-text' style={{ marginLeft: '30px' }} onClick={(e) => handleDownload(e, 1)}>{t("下载word导入模板")}</a>
        </Dragger>}

      <div className="btn-group">
        <Button type="primary" loading={loading} disabled={!fileData} onClick={handleImport}>{t("确认导入")}</Button>
      </div>
    </div>

  </Modal>;
};

export default ImportModal;