import { publishCourse } from '@/api/course';
import { deleteHomeworks } from '@/api/homework';
import {
  deleteCourse,
  fetchOtherCourse,
  fetchSemeter,
  getCourse_floor,
  offShelfThirdCourse,
  queryUserDetailInfo,
  updataDockCourse
} from '@/api/mooclist';
import CourseBlockForOther from '@/components/CourseBlock/CourseBlockForOther';
import DeleteProcess from '@/components/DeleteProcessModal';
import { IconFont } from '@/components/iconFont';
import MobileSearch from '@/components/SearchForm/mobileSearch';
import AddThirdcourseModal from '@/components/AddThirdcourseModal'
import useLocale from '@/hooks/useLocale';
import { IGlobal, IGlobalModelState } from '@/models/global';
import { COURSE_TYPE } from '@/permission/moduleCfg';
import { PlusCircleFilled, ReloadOutlined } from '@ant-design/icons';
import {
  Button,
  Checkbox,
  DatePicker,
  Empty,
  Form,
  Input,
  Modal,
  Pagination,
  Popover,
  Select,
  Space,
  message,
} from 'antd';
import moment from 'moment';
import React, { FC, useEffect, useRef, useState } from 'react';
import { useDispatch, useLocation, useRouteMatch, useSelector } from 'umi';
import './index.less';

const { Option } = Select;
const { RangePicker } = DatePicker;

const DockCourseList: FC = () => {
  const { t } = useLocale();
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [selectKey, setSelectKey] = useState<string[]>([]);
  const [dataSource, setDataSource] = useState<any>([]);
  const [tableLoading, setTableLoading] = useState(false);
  const [total, setTotal] = useState<number>(0);
  const [newSelectedRows, setNewSelectedRows] = useState<any>([]);
  const [releaseDis, setReleaseDis] = useState(true);
  const [offShelfDis, setOffShelfDis] = useState(true);
  const [oneOrBatch, setOneOrBatch] = useState<boolean>(true);
  const [operationData, setOperationData] = useState<any>();
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteProcessModalVisible, setDeleteProcessModalVisible] = useState(
    false,
  );
  const [processId, setProcessId] = useState<string>('');
  const [releaseOrNot, setReleaseOrNot] = useState<boolean>(false);
  const [release, setRelease] = useState<boolean>(false);
  const [offlineCourse, setOfflineCourse] = useState<any[]>([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const offlineId = useRef('');
  const [contentId, setContentId] = useState<string>('');
  const [indeterminate, setIndeterminate] = useState<boolean>(false);
  const [checkAll, setCheckAll] = useState<boolean>(false);
  const [query, setQuery] = useState({
    pageIndex: 1,
    pageSize: 24,
    courseType: 2,
  });
  // const history = useHistory();
  const { mobileFlag } = useSelector<{ config: any }, any>(
    ({ config }) => config,
  );

  const [operatMenuVisible, setOpreatMenuVisible] = useState<boolean>(false);
  const { userInfo } = useSelector<any, IGlobal>(state => state.global);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  useEffect(() => {
    // 初始化
    if (localStorage.getItem('docklist_size')) {
      setQuery({
        ...query,
        pageSize: Number(localStorage.getItem('docklist_size')),
      });
    }
  }, []);

  const [createForm] = Form.useForm();
  const { parameterConfig } = useSelector<
    { global: IGlobalModelState },
    IGlobalModelState
  >(state => state.global);
  const onSearch = (value: any) => {
    setQuery({
      ...query,
      ...value,
      startTime: value.startTime ? value.startTime[0].format('x') : undefined,
      endTime: value.startTime ? value.startTime[1].format('x') : undefined,
    });
  };
  const onReset = () => {
    form.resetFields();
    setQuery({
      pageIndex: 1,
      pageSize: query.pageSize,
      courseType: 3,
    });
  };
  const { params }: any = useRouteMatch();
  useEffect(() => {
    fetchDataList();
  }, [params.type, query]);
  const location = useLocation()
  // 使用useRef保存原始标题，防止被路由配置覆盖
  const originalTitle = useRef(document.title);

  useEffect(() => {
    // 保存原始标题
    if (!originalTitle.current) {
      originalTitle.current = document.title;
    }

    // 从localStorage获取自定义标题
    const storedTitle = JSON.parse(window.sessionStorage.getItem('title') || '""');

    if (storedTitle) {
      // 使用setTimeout确保我们的标题设置在路由系统之后执行
      setTimeout(() => {
        document.title = storedTitle;
      }, 0);
    }

    // 创建MutationObserver监听title变化
    const titleObserver = new MutationObserver(() => {
      const currentTitle = document.title;
      const customTitle = JSON.parse(window.sessionStorage.getItem('title') || '""');

      // 如果当前标题不是我们想要的自定义标题，则重新设置
      if (customTitle && currentTitle !== customTitle) {
        document.title = customTitle;
      }
    });

    // 监听document.title的变化
    titleObserver.observe(document.querySelector('title'), { childList: true });

    return () => {
      titleObserver.disconnect();
      // 恢复原始标题
      if (originalTitle.current) {
        document.title = originalTitle.current;
      }
    };
  }, [location.pathname]);

  const fetchDataList = () => {
    setTableLoading(true);
    // const pathname = history.location.pathname;
    let courseType = params.type;
    // if (pathname.includes("/dockcourse")) {
    //   courseType = "爱课堂";
    // } else if (pathname.includes("/rainCourse")) {
    //   courseType = "雨课堂";
    // } else if (pathname.includes("/superstarCourse")) {
    //   courseType = "超星";
    // } else if (pathname.includes("/schoolMooc")) {
    //   courseType = "中国大学MOOC";
    // } else if (pathname.includes("/pmphmoocCourse")) {
    //   courseType = "人卫慕课";
    // } else if (pathname.includes("/umoocCourse")) {
    //   courseType = "优慕课";
    // } else if (pathname.includes("/zhihuishuCourse")) {
    //   courseType = "智慧树";
    // } else if (pathname.includes("/schoolOnline")) {
    //   courseType = "学堂在线";
    // } else if (pathname.includes("/silverLearning")) {
    //   courseType = "学银在线";
    // }
    fetchOtherCourse(courseType, { ...query, teacherName: userInfo.nickName })
      .then(res => {
        dispatch({
          type: 'config/changeShowLoading',
          payload: {
            value: false,
          },
        });
        setTableLoading(false);
        console.log(res);
        if (res.message === 'OK') {
          const { results, total } = res.data;
          setTotal(total);
          const data = results.map((item: any) => {
            let {
              name,
              contentId_,
              subject,
              end_time,
              start_time,
              publishStatus,
              teacher,
              publishTime,
              createDate_,
            } = item;
            let subjectName: string[] = [];
            let teacherName: string[] = [];
            if (subject) {
              subject.forEach((element: any) => {
                subjectName.push(element.value);
              });
            }
            if (teacher && teacher instanceof Array) {
              teacher.forEach((element: any) => {
                teacherName.push(element.value);
              });
            } else if (teacher) {
              teacherName.push(teacher);
            }
            return {
              ...item,
              name,
              key: contentId_,
              subject: subjectName && subjectName.join(),
              opentime: end_time
                ? `${moment(start_time).format('YYYY-MM-DD')}undefined${moment(
                  end_time,
                ).format('YYYY-MM-DD')}`
                : '',
              publishStatus,
              teacher: teacherName && teacherName.join(),
              updatetime: publishTime
                ? moment(publishTime).format('YYYY-MM-DD HH:mm:ss')
                : createDate_,
            };
          });
          setDataSource(data);
        }
      })
      .catch(() => setTableLoading(false));
  };
  useEffect(() => {
    if (createModalVisible && offlineCourse.length === 0) {
      queryUserDetailInfo().then(res => {
        console.log(res);
        //查询当前学期
        fetchSemeter().then(ress => {
          console.log(ress);
          getCourse_floor(
            res.extendMessage.user_code === 'admin' ||
              res.extendMessage.user_code === 'sys'
              ? {
                page: 1,
                size: 50,
                course_form: t('班级课'),
                semester: ress.extendMessage.name,
              }
              : {
                page: 1,
                size: 50,
                course_form: t('班级课'),
                teacher: res.extendMessage.user_code,
                semester: ress.extendMessage.name,
                // teacher:'zj2018326050004'
              },
          ).then(res => {
            if (res.error_code === 'cloud_sc.0000.0000' && res.extend_message) {
              setOfflineCourse(res.extend_message.results);
            }
          });
        });
      });
    }
  }, [createModalVisible]);

  useEffect(() => {
    let offshelf = newSelectedRows.some((item: any) => {
      return item.publishStatus === 0;
    });
    setOffShelfDis(offshelf);
    let release = newSelectedRows.some((item: any) => {
      return item.publishStatus !== 0;
    });
    setReleaseDis(release);
  }, [newSelectedRows]);

  //记住该页码大小
  const pageChange = (pageIndex: any, pageSize: any) => {
    setQuery({ ...query, pageIndex, pageSize });
    localStorage.setItem('docklist_size', pageSize);
  };
  //爱课堂跳转
  const openDockCourse = () => {
    // window.open('')
  };
  const handleOffCourse = () => {
    if (!oneOrBatch) {
      const params = {
        contentId: operationData.contentId_,
        publishStatus: releaseOrNot ? 1 : 0,
      };
      offShelfThirdCourse(params).then(res => {
        if (res.errorCode === 'success') {
          // fetchDataList();
          const data = dataSource.map((item: any) => {
            if (item.contentId_ === operationData.contentId_) {
              return {
                ...item,
                publishStatus: releaseOrNot ? 1 : 0,
              };
            } else {
              return item;
            }
          });
          setOperationData(null);
          setDataSource(data);
          setRelease(false);
          message.success(
            `${t('课程')}${releaseOrNot ? t('发布') : t('下架')}${t('成功')}`,
          );
        } else {
          message.error(
            `${t('课程')}${releaseOrNot ? t('发布') : t('下架')}${t('失败')}`,
          );
        }
      });
    } else if (selectKey.length > 0) {
      publishCourse(selectKey, releaseOrNot ? 1 : 0).then(res => {
        if (res.data?.status === 200) {
          const data = dataSource.map((item: any) =>
            selectKey.includes(item.contentId_)
              ? { ...item, publishStatus: releaseOrNot ? 1 : 0 }
              : item,
          );
          setSelectKey([]);
          setNewSelectedRows([]);
          setDataSource(data);
          setRelease(false);
          message.success(
            `${t('课程')}${releaseOrNot ? t('发布') : t('下架')}${t('成功')}`,
          );
        } else {
          message.error(
            res.data?.message ||
            `${t('课程')}${releaseOrNot ? t('发布') : t('下架')}${t('失败')}`,
          );
        }
      });
    }
  };
  const batchDelete = () => {
    if (selectKey.length) {
      setOneOrBatch(false);
      setIsDeleteModalVisible(true);
    } else {
      message.info(t('请选择课程'));
    }
  };
  const handleDeleteOk = () => {
    setDeleteLoading(true);
    let param: any = [];
    if (oneOrBatch) {
      param.push(operationData.key);
    } else {
      param = [...selectKey];
    }
    console.log(
      'xxxxxxxxxxxxxxxxxxxxx',
      param,
      oneOrBatch,
      operationData,
      selectKey,
    );
    deleteCourse(param).then(res => {
      if (res && res.message === 'OK') {
        deleteHomeworks(param);
        setProcessId(res.data);
        setDeleteProcessModalVisible(true);
      } else {
        message.error(t('删除失败'));
      }
      // setTimeout(() => getlist(), 500)
      setSelectKey([]);
      setDeleteLoading(false);
      setIndeterminate(false);
      setIsDeleteModalVisible(false);
    });
  };

  const onCheckGroupChange = (check: any[]) => {
    setSelectKey(check);
    setNewSelectedRows(
      [...dataSource].filter((d: any) => check.includes(d.contentId_)),
    );
    // console.log([...dataSource].filter((d: any) => check.includes(d.contentId_)).filter((d:any)=>d.publishStatus == 1))
    setIndeterminate(!!check.length && check.length < dataSource.length);
    setCheckAll(check.length === dataSource.length);
  };
  const onCheckAllChange = (e: any) => {
    setSelectKey(
      e.target.checked ? dataSource.map((item: any) => item.contentId_) : [],
    );
    setNewSelectedRows([...dataSource]);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
    setReleaseDis(false);
  };
  //按钮列表
  let btn_list: any = [];
  btn_list.push({
    title: t('发布'),
    disabled: releaseDis || !selectKey.length,
    func: () => {
      setOneOrBatch(true);
      setReleaseOrNot(true);
      setRelease(true);
    },
    dom: (
      <Button
        type="text"
        ghost
        icon={<IconFont type="iconrelease" />}
        onClick={() => {
          setOneOrBatch(true);
          setReleaseOrNot(true);
          setRelease(true);
        }}
        disabled={releaseDis || !selectKey.length}
      >
        {t('发布')}
      </Button>
    ),
  });
  btn_list.push({
    title: t('下架'),
    disabled: offShelfDis || !selectKey.length,
    func: () => {
      setOneOrBatch(true);
      setReleaseOrNot(false);
      setRelease(true);
    },
    dom: (
      <Button
        type="text"
        ghost
        icon={<IconFont type="iconoffShelf" />}
        onClick={() => {
          setOneOrBatch(true);
          setReleaseOrNot(false);
          setRelease(true);
        }}
        disabled={offShelfDis || !selectKey.length}
      >
        {t('下架')}
      </Button>
    ),
  });
  btn_list.push({
    title: t('删除'),
    disabled: releaseDis || !selectKey.length,
    func: () => batchDelete,
    dom: (
      <Button
        type="text"
        ghost
        onClick={batchDelete}
        icon={<IconFont type="icondelete" />}
        disabled={releaseDis || !selectKey.length}
      >
        {t('删除')}
      </Button>
    ),
  });

  // 处理表单提交
  const handleSubmit = async (values: MicroCourse.FormValues) => {
    const data = {
      ...values,
      platform_name: params.type
    }
    try {
      const res = await updataDockCourse(data);
      if (res?.status === 200) {
        fetchDataList()
        return res.data;
      } else {
        throw new Error('请求失败');
      }
    } catch (error) {
      throw error;
    }
  };

  return (
    <div className="dock-page-wrapper">
      <div className="sp-page-content">
        {mobileFlag ? (
          <MobileSearch
            resourceSearch={onSearch}
            selected={'other'}
            form={form}
            reset={onReset}
          />
        ) : (
          <Form layout="inline" name="basic" form={form} onFinish={onSearch}>
            <Form.Item name="courseName">
              <Input
                placeholder={t('请输入课程名')}
                autoComplete="off"
                onPressEnter={() => {
                  form.submit();
                }}
              />
            </Form.Item>

            {/* <Form.Item name="teacherName">
             <Input placeholder="请输入教师名" autoComplete="off" />
            </Form.Item> */}
            <div className="reset-wrp" onClick={onReset}>
              <span>{t('清空')}</span>
              <ReloadOutlined />
            </div>
            <Button type="primary" htmlType="submit">
              {t('搜索')}
            </Button>
          </Form>
        )}

        <div className="splitLine"></div>
        <div className="button_box">
          <Space size={0}>
            <Checkbox
              indeterminate={indeterminate}
              onChange={onCheckAllChange}
              checked={checkAll}
            >
              {t('全部')}
            </Checkbox>
            {
              !mobileFlag && <Button
                shape="round"
                type="primary"
                icon={<PlusCircleFilled />}
                onClick={() => setModalVisible(true)}
              >
                {t('新建')}
              </Button>
            }
            {!mobileFlag ? (
              btn_list.map((item: any, index: number) => {
                return (
                  <div
                    onClick={() => item.func()}
                    className={`item_${item.disabled ? ' disabled' : ''}`}
                    key={index}
                  >
                    {item.dom}
                  </div>
                );
              })
            ) : (
              //移动端取前两个展示即可
              <>
                {btn_list.slice(0, 4).map((item: any, index: number) => {
                  return (
                    <div
                      className={`item_${item.disabled ? ' disabled' : ''}`}
                      key={index}
                    >
                      {item.dom}
                    </div>
                  );
                })}

                {btn_list.slice(4, btn_list.length).length > 0 && (
                  <Popover
                    className="mobile_btns_popover"
                    onOpenChange={(newOpen: boolean) =>
                      setOpreatMenuVisible(newOpen)
                    }
                    open={operatMenuVisible}
                    content={
                      <div className="mobile_btns">
                        {btn_list
                          .slice(4, btn_list.length)
                          .map((item: any, index: number) => {
                            return (
                              <div
                                key={index}
                                className={item.disabled ? 'disabled' : ''}
                                onClick={() => {
                                  if (!item.disabled) {
                                    setOpreatMenuVisible(false);
                                    item.func();
                                  }
                                }}
                              >
                                {item.dom}
                                {item.title}
                              </div>
                            );
                          })}
                      </div>
                    }
                  >
                    <Button
                      onClick={(e: any) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setOpreatMenuVisible(!operatMenuVisible);
                      }}
                    >
                      <IconFont type="iconziyuanku1" />
                    </Button>
                  </Popover>
                )}
              </>
            )}
          </Space>
        </div>
        <Checkbox.Group
          value={selectKey}
          onChange={onCheckGroupChange}
          style={{ width: '100%' }}
        >
          <div className="data_wrapper">
            {dataSource && dataSource.length > 0 ? (
              dataSource.map((item: any) => (
                <CourseBlockForOther
                  key={item.contentId_}
                  item={item}
                  courseClick={() => window.open(item.jump_address)}
                  onPublish={() => {
                    setOneOrBatch(false);
                    setOperationData(item);
                    setReleaseOrNot(true);
                    setRelease(true);
                  }}
                  onUnPublish={() => {
                    setOneOrBatch(false);
                    setOperationData(item);
                    setReleaseOrNot(false);
                    setRelease(true);
                  }}
                  onPreview={() =>
                    window.open(
                      `/learn/course/preview/${COURSE_TYPE[item.courseType]}/${item.contentId_
                      }?preview=1&show=1&type=released`,
                    )
                  }
                  onDelete={() => {
                    setOneOrBatch(true);
                    setOperationData(item);
                    setIsDeleteModalVisible(true);
                  }}
                />
              ))
            ) : (
              <Empty style={{ width: '100%' }} />
            )}
          </div>
        </Checkbox.Group>
        {total > 0 && (
          <Pagination
            style={{ textAlign: 'center', marginTop: 10 }}
            {...{
              current: query.pageIndex,
              pageSize: query.pageSize,
              total: total,
              onChange: pageChange,
              showQuickJumper: true,
              defaultCurrent: 1,
              size: 'small',
              showTotal: total => t('共{name}条', String(total)),
              showSizeChanger: true,
              pageSizeOptions: ['24', '36', '48', '60'],
            }}
          />
        )}
      </div>
      <Modal
        title={releaseOrNot ? t('发布') : t('下架')}
        visible={release}
        onOk={handleOffCourse}
        onCancel={() => setRelease(false)}
      >
        {t('确定要')}
        {releaseOrNot ? t('发布') : t('下架')}
        {t('该课程吗？')}
      </Modal>
      <Modal
        title={t('删除')}
        visible={isDeleteModalVisible}
        onOk={handleDeleteOk}
        onCancel={() => setIsDeleteModalVisible(false)}
        confirmLoading={deleteLoading}
      >
        {t('确定要删除该课程吗？')}
      </Modal>
      <DeleteProcess
        title={t('删除进度')}
        visible={deleteProcessModalVisible}
        processId={processId}
        closeModal={() => setDeleteProcessModalVisible(false)}
        callback={fetchDataList}
      />

      {/*  第三方课程新增课程*/}
      <AddThirdcourseModal
        modalVisible={modalVisible}
        modalClose={() => setModalVisible(false)}
        onSubmit={handleSubmit}
      />
    </div >
  );
};

export default DockCourseList;
