/*
 * @Author: 李武林
 * @Date: 2021-12-03 12:18:28
 * @LastEditors: 李武林
 * @LastEditTime: 2022-04-07 15:24:47
 * @FilePath: \coursemanger\src\api\student.ts
 * @Description: 
 * 
 * Copyright (c) 2022 by 李武林/索贝数码科技股份有限公司, All Rights Reserved. 
 */
import HTTP from './index';
// 查询列表
export function getstudentlist(data: string) {
  return HTTP.get(`/learn/v1/teaching/course/get/student/list?${data}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
// 查询列表
export function getmicroMajorStudentlist(data: string) {
  return HTTP.get(`/learn/map/resource/study/student/list?${data}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
export function getStudentCodes(params: any) {
  return HTTP(`/learn/v1/teaching/course/get/mooc/student/code`, {
    method: 'GET',
    params
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
// 查询mooc学生列表
export function getmoocstudentlist(data: any) {
  return HTTP.get(`/learn/v1/teaching/course/get/mooc/student/list`,{
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
// 查询图谱课学生列表
export function getmapstudentlist(data: any) {
  return HTTP.get(`/learn/map/resource/study/student/list`,{
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 添加
export function addstudent(
  params: {
    typeId: string;
    courseType: string;
    courseSemester?: number;
  },
  data: Array<IStudent.IaddParams>,
) {
  return HTTP.post(
    `/learn/v1/teaching/course/add/student`,
    data,
    { params }
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
// /cvod​/v1​/teaching​/course​/delete​/student
// 删除
export function deletestudent(code: string, data: string[], courseSemester?: number) {
  return HTTP.post(
    `/learn/v1/teaching/course/delete/student?typeId=${code}&courseSemester=${courseSemester}`,
    {data},
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function exportMicroMajorStudent(params: any, data: any, onUploadProgress?: any) {
  return HTTP("/learn/map/resource/study/student/import/", {
    method: "POST",
    data,
    params,
    onUploadProgress
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
  .catch(error => {
    console.error(error);
  });
}
export function exportStudent(typeId: string, courseType: number, data: any, courseSemester?: number,onUploadProgress?: any) {
  return HTTP.post(
    `/learn/v1/teaching/course/add/student/${typeId}/${courseType}/${courseSemester}`,
    data,
    {
      onUploadProgress
    }
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getStudentTemplate() {
  return HTTP.get(`/learn/v1/teaching/course/load/template`, { responseType: "blob" })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
export function getMicroPermission(params: any) {
  return HTTP.get(`/learn/v1/teaching/course/get/microProfession/teacher/permission`,{
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}