/*
 * @Author: lijin
 * @Description: 回复输入框
 * @Date: 2022-02-24 14:12:45
 * @LastEditTime: 2022-02-28 14:02:11
 * @LastEditors: lijin
 * @FilePath: \coursemanger\src\pages\CourseQA\components\replyInput.tsx
 */

import { IconFont } from '@/components/iconFont';
import { Button, Col, Input, Row, Tooltip } from 'antd';
import { EnterOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { FC } from 'react';
import useLocale from '@/hooks/useLocale';
interface ReplyInputProps {
  onReply: (replyMsg: string) => Promise<boolean>;
  permissions?: string;
  canReply?: boolean;
}
const ReplyInput: FC<ReplyInputProps> = ({ onReply, canReply, permissions }) => {
  const [input, setInput] = useState('');
  const [replyLoading, setReplyLoading] = useState(false);
  const { t } = useLocale();

  const handleReply = async () => {
    if (!canReply) return;
    setReplyLoading(true);
    const ifSuccess = await onReply(input);
    if (ifSuccess) {
      setInput('');
    }
    setReplyLoading(false);
  };
  return (
    <Row gutter={16} style={{ margin: '12px 0' }}>
      <Col flex="auto">
        <Input
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder={t("写下你的回复")}
          onPressEnter={handleReply}
          prefix={
            <IconFont
              type="iconbianji-heise"
              style={{ color: 'rgba(0, 0, 0, 0.45)' }} />} />



      </Col>
      <Col flex="none">
        <Tooltip title={`${!canReply && permissions == "1" ? t("仅教师团队可继续回复") : !canReply && permissions == "2" ? t("仅提问人和教师团队可继续回复") : ""}`}>
          <Button
            disabled={!canReply}
            type="primary"
            onClick={handleReply}
            loading={replyLoading}
            icon={<EnterOutlined />}>
            {t("回复")}

          </Button>
        </Tooltip>

      </Col>
    </Row>);

};
export default ReplyInput;