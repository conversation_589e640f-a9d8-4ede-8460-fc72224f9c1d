import React, { useEffect, useState } from "react";
import "./index.less"
import {
  Button, Col, Form, Input, Row, Tabs, Select, Table, Space,
  Pagination, Checkbox, Steps, message, Modal, Drawer,
  Skeleton,
  Empty
} from "antd";
import { auditToMe, getAuditInstance, opreateAudit, getAuditLogs, myInitateAudit, revokeAudit, myAuditResource } from "@/api/review";
import useLocale from "@/hooks/useLocale";
import { IconFont } from "@/components/iconFont";
import { ReloadOutlined, CheckCircleOutlined, CloseCircleOutlined, LeftOutlined } from "@ant-design/icons";
import { CheckboxChangeEvent } from "antd/lib/checkbox";
import { getUserJurisdictionV2 } from "@/api/course";
import { useHistory } from "umi";

const { Option } = Select

const Share = () => {

  const [form] = Form.useForm()
  const [detailForm] = Form.useForm()
  const { t } = useLocale()
  const [activeTab, setActiveTab] = useState<string>('1')
  const [dataSource, setDataSource] = useState([])
  const [loading, setLoading] = useState(false)
  const [pager, setPager] = useState({
    page: 1,
    size: 10
  })
  const [detailPager, setDetailPager] = useState({
    page: 1,
    size: 10
  })
  const [total, setTotal] = useState(0)
  const [detailTotal, setDetailTotal] = useState(0)
  const [allChecked, setAllChecked] = useState<boolean>(false)
  const [selcetedRows, setSelectedRows] = useState<any[]>([])
  const [selectedList, setSelectedList] = useState<any[]>([])
  const [showDetail, setShowDetail] = useState(false)
  const [detailList, setDetailList] = useState<any[]>([])
  const [loadDetailing, setLoadDetailing] = useState(false)
  const [instanceId, setInstanceId] = useState('')
  const [disableAllCheck, setDisableAllCheck] = useState(false)
  const [indeterminate, setIndeterminate] = useState(false)
  const [auditComment, setAuditComment] = useState('')
  const [commentModal, setCommentModal] = useState(false)
  const [openLogs, setOpenLogs] = useState(false)
  const [loadingLos, setLoadingLogs] = useState(false)
  const [logsStep, setLogsStep] = useState<any[]>([])
  const history = useHistory()

  const statusOpts = [
    { label: t('待审核'), value: '待审核' },
    { label: t('审核中'), value: '审核中' },
    { label: t('已审核'), value: '已审核' },
    { label: t('已驳回'), value: '已驳回' }
  ]
  const createDateOpts = [
    {
      label: t('发起时间'),
      value: 'down',
      icon: <IconFont type="iconchangjiantou-xia" className="icon" />,
    },
    {
      label: t('发起时间'),
      value: 'up',
      icon: <IconFont type="iconchangjiantou-shang" className="icon" />,
    }
  ]

  useEffect(() => {
    getAuditList()
  }, [activeTab])

  const pickListFn = () => {
    const hash = window.location.hash
    // 入库审核
    if (hash.includes('reviewcenter/import')) {
      if (activeTab == '1') {
        return getAuditInstance
      } else {
        return myAuditResource
      }
    }
    // 共享审核
    if (hash.includes('/reviewcenter/share')) {
      if (activeTab == '1') {
        return auditToMe
      } else {
        return myInitateAudit
      }
    }
    return auditToMe
  }

  useEffect(() => {
    if (location.hash.includes('reviewcenter/import')) {
      const wiatList = dataSource.filter((item: any) => !['已审核', '已撤销', '已驳回'].includes(item.auditState))
      setDisableAllCheck(wiatList.length === 0)
    }
  }, [dataSource])

  const getAuditList = async () => {
    const formData = history.location.pathname == "/reviewcenter/import" ? detailForm.getFieldValue()  :form.getFieldsValue()
    let instanceType = "共享"
    if (history.location.pathname == "/reviewcenter/import") {
      instanceType = "入库"
    }
    const data = {
      ...formData,
      page: pager.page,
      size: pager.size,
      isDesc: formData.sort ? formData.sort === 'down' ? true : false : true, // 发起时间排序：false由远到近，升序；true由近到远，降序。
      instanceType // inventoryVerify：入库、shareVerify：共享
    }
    delete data.sort // isDesc来代表排序
    const fn = pickListFn()
    setLoading(true)
    fn(data).then((res: any) => {
      if (res.errorMsg === "Success" && res.extendMessage) {
        setTotal(res.extendMessage.recordTotal)
        setDataSource(res.extendMessage.results)
      }
    }).catch(err => {

    }).finally(() => {
      setLoading(false)
    })
  }

  useEffect(() => {
    getAuditList()
  }, [pager])

  useEffect(() => {
    getDetailList(instanceId, detailPager.page, detailPager.size, detailForm.getFieldValue('resourceName'), detailForm.getFieldValue('status'))
  }, [pager])

  const statusColor: any = {
    '待审核': '#525252',
    '审核中': '#FF8750',
    '已审核': '#549CFF',
    '已撤销': '#FF8750',
    '已驳回': '#FF4F4F',
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0 || !bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    const size = (bytes / Math.pow(k, i)).toFixed(2);
    return `${size} ${sizes[i]}`;
  }

  const getDetailList = (instanceId: string, page: number, size: number, resourceName?: string, status?: string) => {
    if (location.hash.includes('reviewcenter/import')) return
    setLoadDetailing(true)
    getAuditInstance({
      instanceId,
      resourceName,
      status,
      page,
      size,
      isDesc: true
    }).then((res: any) => {
      if (res.errorMsg === "Success" && res.extendMessage) {
        console.log()
        setDetailTotal(res.extendMessage.recordTotal)
        setDetailList(res.extendMessage.results)
        const wiatList = res.extendMessage.results.filter((item: any) => !['已审核', '已撤销', '已驳回'].includes(item.auditState))
        setDisableAllCheck(wiatList.length === 0)
      } else {
        setDetailTotal(0)
        setDetailList([])
        setDisableAllCheck(true)
      }
    }).catch(() => {
      message.error('加载失败')
    }).finally(() => {
      setLoadDetailing(false)
    })
  }

  const toAudit = (e: any, record: any) => {
    e.preventDefault()
    setShowDetail(true)
    setInstanceId(record.instanceId)
    getDetailList(record.instanceId, detailPager.page, detailPager.size)
  }

  const columns = [
    {
      dataIndex: 'instanceName',
      title: '名称',
      width: 500
    },
    {
      dataIndex: 'totalFileSize',
      title: '大小',
      width: 200,
      render: (text: string) => {
        return formatFileSize(parseInt(text))
      }
    },
    {
      dataIndex: 'status',
      title: '审核状态',
      width: 200,
      render: (text: string) => {
        return <div style={{ color: statusColor[text] }}>{t(text)}</div>
      }
    },
    {
      dataIndex: 'createUserName',
      width: 200,
      title: '发起人'
    },
    {
      dataIndex: 'createdDate',
      width: 200,
      title: '审核发起时间'
    },
    {
      title: '操作',
      render: (text: string, record: any) => {
        const disabled = ['已审核', '已驳回'].includes(record.status)
        return <Space>
          <Button
            type="text"
            className={disabled ? '' : 'opt_btn'}
            disabled={disabled}
            onClick={(e) => toAudit(e, record)}>{t(activeTab === '1' ? '去审核' : '去撤回')}
          </Button>
        </Space>
      }
    },
  ]

  const detailColumns = [
    {
      dataIndex: 'resourceName',
      title: '名称',
      render: (text: string, record: any) => {
        const url = `/rman/#/basic/rmanDetail/${record.resourceId}?isaudit=true&showBtn=true&instanceId=${record.instanceId}&page=1&size=30`
        return <Space size="small">
          <img className="keyframe" src={record.keyframe} alt="img" />
          <a href={url} target="_blank">{text}</a>
        </Space>
      }
    },
    {
      dataIndex: 'auditState',
      title: '审核状态'
    },
    {
      dataIndex: 'auditCreator',
      title: '审核人'
    },
    {
      dataIndex: 'auditTime',
      title: '审核时间'
    },
    {
      title: "操作",
      render: (text: any, record: any) => {
        return <Button type="text" className="opt_btn" onClick={() => openDrawer(record.resourceId)}>审核日志</Button>
      }
    }
  ]

  const openDrawer = (contentId: string) => {
    setOpenLogs(true)
    setLoadingLogs(true)
    getAuditLogs(contentId, 1, 99).then((res: any) => {
      if (res.errorCode === "success" && res.extendMessage) {
        let temp: any[] = []
        res.extendMessage.results.forEach((item: any) => {
          temp.push({
            title: item.createTime,
            subTitle: item.username,
            description: item.description
          })
        })
        setLogsStep(temp)
      }
    }).catch(() => {
      message.error('加载日志失败')
    }).finally(() => {
      setLoadingLogs(false)
    })
  }

  const onPageChange = (page: number, size: number) => {
    setPager({ page, size })
  }

  const onPageSizeChange = (current: number, size: number) => {
    const page = 1
    setPager({ page, size })
  }

  const onDetailPageChange = (page: number, size: number) => {
    setDetailPager({ page, size })
  }

  const onDetailPageSizeChange = (current: number, size: number) => {
    const page = 1
    setDetailPager({ page, size })
  }

  const onCheckChange = (e: CheckboxChangeEvent) => {
    setAllChecked(e.target.checked)
    const source = location.hash.includes('reviewcenter/share') ? detailList : dataSource
    const list: any = [], checkList: any = [];
    if (e.target.checked) {
      source.forEach((item: any) => {
        // 已经审核或者驳回了的不能再操作
        if (!['已审核', '已撤销', '已驳回'].includes(item.auditState)) {
          list.push(item.resourceId)
          checkList.push(item)
        }
      })
    }
    setSelectedRows(list)
    setSelectedList(checkList)
  }

  const onReset = () => {
    form.resetFields()
    getAuditList()
  }

  useEffect(() => {
    if (selcetedRows.length === 0) {
      setIndeterminate(false)
      setAllChecked(false)
      return
    }
    const source = location.hash.includes('reviewcenter/share') ? detailList : dataSource
    const waitAuditList = source.filter(item => !['已审核', '已撤销', '已驳回'].includes(item.auditState))
    setAllChecked(selcetedRows.length === waitAuditList.length)
    setIndeterminate(selcetedRows.length > 0 && (selcetedRows.length !== waitAuditList.length))
  }, [selcetedRows.length])

  const rowSelection = {
    onSelect: (record: any, selected: boolean,) => {
      const temp = [...selcetedRows]
      if (temp.includes(record.resourceId)) {
        setSelectedRows(temp.filter(i => i !== record.resourceId))
      } else {
        temp.push(record.resourceId)
        setSelectedRows(temp)
      }
      const source = location.hash.includes('reviewcenter/share') ? detailList : dataSource
      const list = source.filter(item => temp.includes(item.resourceId))
      setSelectedList(list)
    },
    hideSelectAll: true,
    selectedRowKeys: selcetedRows,
    getCheckboxProps: (record: any) => {
      return {
        disabled: ['已审核', '已撤销', '已驳回'].includes(record.auditState)
      }
    }
  }

  const generateSteps = (all: number, finised: number) => {
    const temp = []
    for (let index = 0; index < all; index++) {
      temp.push({
        title: `任务${index + 1}`
      })
    }
    // 有的节点只有一个审核，统一加入开始、结束节点
    const start = { title: '开始审核' }
    const end = { title: '审核完毕' }
    temp.unshift(start)
    temp.push(end)
    return temp
  }

  const setExpand = (record: any) => {
    let all = record.resourceCount, finised = record.auditCount;
    const items = generateSteps(all, finised)
    let current = 0
    if (all === finised) {
      current = finised + 2
    } else {
      current = finised + 1
    }
    return <Steps size="small" current={current} items={items} />
  }

  const queryPart = <Form form={form}>
    <Row gutter={24}>
      <Col span={5}>
        <Form.Item name='instanceName' >
          <Input placeholder={t('输入资源名称')} />
        </Form.Item>
      </Col>
      <Col span={5}>
        <Form.Item name='status' >
          <Select placeholder={t('审核状态')} options={statusOpts} onChange={getAuditList} />
        </Form.Item>
      </Col>
      {
        activeTab === '1' ? <Col span={5}>
          <Form.Item name='initiator' >
            <Input placeholder={t('发起人')} />
          </Form.Item>
        </Col> : null
      }
      <Col span={5}>
        <Form.Item name='sort' initialValue="down">
          <Select placeholder={t('发起时间')} onChange={getAuditList}>
            {
              createDateOpts.map(item => <Option value={item.value} key={item.value}>
                {item.label}
                {item.icon}
              </Option>)
            }
          </Select>
        </Form.Item>
      </Col>
      <Col span={4}>
        <Space size='middle'>
          <Button type="primary" onClick={getAuditList}>{t('搜索')}<IconFont type="iconsousuo2" /></Button>
          <Button type="text" onClick={onReset}>{t('清空')}<ReloadOutlined /></Button>
        </Space>
      </Col>
    </Row>
  </Form>

  const handleDetailFormChange = () => {

    const formData = detailForm.getFieldsValue()
    getAuditList()
    // getDetailList(instanceId, detailPager.page, detailPager.size, formData.resourceName, formData.status)
  }

  const onResetDetail = () => {
    detailForm.resetFields()
    getDetailList(instanceId, detailPager.page, detailPager.size)
  }

  const agreetAudit = () => {
    const flowNodeIds = selectedList.map(item => item?.flowInfo?.id)
    const resourceIds = selectedList.map(item => item.resourceId)
    const instanceIds = selectedList.map(item => item.instanceId)
    opreateAudit('已审核', { flowNodeIds, resourceIds, instanceIds }).then(res => {
      if (res.errorMsg === 'Success') {
        message.success('审核通过')
        if (location.hash.includes('reviewcenter/share')) {
          getDetailList(instanceId, detailPager.page, detailPager.size, detailForm.getFieldValue('resourceName'), detailForm.getFieldValue('status'))
        } else {
          getAuditList()
        }
        setSelectedRows([])
        setSelectedList([])
      }
    }).catch(() => {
      message.success('审核失败')
    })
  }

  const handleWithdraw = () => {
    console.log('selectedList', selectedList)
    const keys = selcetedRows.join(',')
    revokeAudit(instanceId, keys).then(res => {
      if (res.errorMsg === 'Success') {
        message.success('撤回成功')
        if (location.hash.includes('reviewcenter/share')) {
          getDetailList(instanceId, detailPager.page, detailPager.size, detailForm.getFieldValue('resourceName'), detailForm.getFieldValue('status'))
        } else {
          getAuditList()
        }
        setSelectedRows([])
        setSelectedList([])
      }
    }).catch(() => {
      message.error('撤回失败')
    })
  }

  const rejectAudit = () => {
    if (!auditComment) return message.warning('请输入驳回理由')
    const flowNodeIds = selectedList.map(item => item?.flowInfo?.id)
    const resourceIds = selectedList.map(item => item.resourceId)
    const instanceIds = selectedList.map(item => item.instanceId)
    opreateAudit('已驳回', { flowNodeIds, resourceIds, instanceIds }, auditComment).then(res => {
      if (res.errorMsg === 'Success') {
        message.success('驳回成功')
        setCommentModal(false)
        if (location.hash.includes('reviewcenter/share')) {
          getDetailList(instanceId, 1, 10, detailForm.getFieldValue('resourceName'), detailForm.getFieldValue('status'))
        } else {
          getAuditList()
        }
        setSelectedRows([])
        setSelectedList([])
      }
    }).catch(() => {
      message.success('驳回失败')
    })
  }

  const detailQueryPart = <Form form={detailForm}>
    <Row gutter={24}>
      <Col span={5}>
        <Form.Item name='resourceName' >
          <Input placeholder={'输入资源关键字'} />
        </Form.Item>
      </Col>
      <Col span={5}>
        <Form.Item name='status' >
          <Select placeholder={t('审核状态')} options={statusOpts} onChange={handleDetailFormChange} />
        </Form.Item>
      </Col>
      <Col span={4}>
        <Space size='middle'>
          <Button type="primary" onClick={handleDetailFormChange}>{t('搜索1')}<IconFont type="iconsousuo2" /></Button>
          <Button type="text" onClick={onResetDetail}>{t('清空')}<ReloadOutlined /></Button>
        </Space>
      </Col>
    </Row>
  </Form>

  const opreatBtns = <div className="opts">
    <Space>
      <Checkbox disabled={disableAllCheck} indeterminate={indeterminate} checked={allChecked} onChange={onCheckChange}>{t('全选')}</Checkbox>
      {
        activeTab === '1' ? <>
          <Button disabled={selcetedRows.length === 0} type="text" onClick={agreetAudit}><CheckCircleOutlined />{t('通过')}</Button>
          <Button disabled={selcetedRows.length === 0} type="text" onClick={() => setCommentModal(true)}><CloseCircleOutlined />{t('驳回')}</Button>
        </> : <Button disabled={selcetedRows.length === 0} type="text" onClick={handleWithdraw}><IconFont type="iconrecall" />{t('撤回')}</Button>
      }
    </Space>
  </div>

  const itemsCommon = <div>
    {
      location.hash.includes('reviewcenter/share') ? <>
        {queryPart}
        <Table
          bordered={false}
          columns={columns}
          dataSource={dataSource}
          pagination={false}
          loading={loading}
          rowKey={(item: any) => item.instanceId}
          expandable={{
            expandedRowRender: (record) => setExpand(record),
            expandIcon: () => null,
            expandRowByClick: true
          }}
        />
        <Pagination
          current={pager.page}
          pageSize={pager.size}
          total={total}
          showSizeChanger
          showQuickJumper
          onChange={onPageChange}
          onShowSizeChange={onPageSizeChange}
          showTotal={(total) => `共${total}条`}
          hideOnSinglePage
          className="pagination"
        /></> : <>
        {detailQueryPart}
        {opreatBtns}
        <Table
          bordered={false}
          columns={detailColumns}
          dataSource={dataSource}
          pagination={false}
          loading={loading}
          rowKey={(item: any) => item.resourceId}
          rowSelection={disableAllCheck ? undefined : rowSelection}
        />
        <Pagination
          current={pager.page}
          pageSize={pager.size}
          total={total}
          showSizeChanger
          showQuickJumper
          onChange={onPageChange}
          onShowSizeChange={onPageSizeChange}
          showTotal={(total) => `共${total}条`}
          hideOnSinglePage
          className="pagination"
        />
        <Modal open={commentModal} title="驳回理由" onOk={rejectAudit} onCancel={() => setCommentModal(false)}>
          <Input.TextArea
            required
            value={auditComment}
            placeholder="请输入驳回理由,最多30字"
            maxLength={30}
            onChange={(e) => setAuditComment(e.target.value)}
          ></Input.TextArea>
        </Modal>
        <Drawer title="审核日志" open={openLogs} onClose={() => setOpenLogs(false)}>
          {
            loadingLos ? <Skeleton /> : logsStep.length ? <Steps items={logsStep} current={logsStep.length} direction="vertical" /> : <Empty />
          }
        </Drawer>
      </>
    }
  </div>

  const allItems = [
    {
      key: '1',
      label: t('待我审核'),
      children: itemsCommon
    },
    {
      key: '2',
      label: t('我发起的'),
      children: itemsCommon
    },
  ]

  const onTabChange = (e: string) => {
    setActiveTab(e)
    setPager({
      page: 1,
      size: 10
    })
  }

  const handleBack = () => {
    setShowDetail(false)
    setSelectedRows([])
    setSelectedList([])
    getAuditList()
  }

  return (
    <div className="share">
      {
        !showDetail ?
          <Tabs items={allItems} activeKey={activeTab} onChange={(e) => onTabChange(e)} />
          : <>
            <Button icon={<LeftOutlined />} type="text" onClick={handleBack} style={{ marginBottom: '20px' }}>返回</Button>
            {detailQueryPart}
            {opreatBtns}
            <Table
              bordered={false}
              columns={detailColumns}
              dataSource={detailList}
              pagination={false}
              loading={loadDetailing}
              rowKey={(item: any) => item.resourceId}
              rowSelection={disableAllCheck ? undefined : rowSelection}
            />
            <Pagination
              current={detailPager.page}
              pageSize={detailPager.size}
              total={detailTotal}
              showSizeChanger
              showQuickJumper
              onChange={onDetailPageChange}
              onShowSizeChange={onDetailPageSizeChange}
              showTotal={(total) => `共${total}条`}
              hideOnSinglePage
              className="pagination"
            />
            <Modal open={commentModal} title="驳回理由" onOk={rejectAudit} onCancel={() => setCommentModal(false)}>
              <Input.TextArea
                required
                value={auditComment}
                placeholder="请输入驳回理由,最多30字"
                maxLength={30}
                onChange={(e) => setAuditComment(e.target.value)}
              ></Input.TextArea>
            </Modal>
            <Drawer title="审核日志" open={openLogs} onClose={() => setOpenLogs(false)}>
              {
                loadingLos ? <Skeleton /> : logsStep.length ? <Steps items={logsStep} current={logsStep.length} direction="vertical" /> : <Empty />
              }
            </Drawer>
          </>
      }
    </div>
  )
}

export default Share
