/*
 * @Description: 布局、用户信息
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-04-26 10:24:07
 * @LastEditors: 李武林
 * @LastEditTime: 2022-07-04 19:01:34
 */
import { Effect, Subscription } from 'dva';
import { flattenDeep, map, values } from 'lodash';
import { Reducer } from 'redux';
// import getMenuList from '../layout/publisher/WorkbenchLayout/menuList';
// import {
//   getUserInfo,
//   handleLogOut,
//   getTeacherMaterialMenu,
//   getPermission,
//   login,
//   getUserCode,
//   getOrganization,
//   queryUserList,
// } from '@/api/publisherApi/globalApi';
import { getSemesters, userInfo } from '@/api/course';
import themeService from '@/api/theme';


interface IObj<T> {
  [propsName: string]: T;
}

/**
 * 参数设置
 */
export interface ParameterSet {
  /**
   * 主键
   */
  id?: string;
  /**
   * 参数名称
   */
  name?: string;
  /**
   * 参数编码
   */
  code?: string;
  /**
   * 值
   */
  value?: string;
  /**
   * 是否是系统内置
   */
  isSystem?: boolean;
  /**
   * 模块
   */
  moduleID?: string;
  /**
   * 字段不映射
   */
  moduleCode?: string;
}

export interface IGlobalModelState {
  code: any;
  userInfo: any; // 用户信息
  permission: any[]; // 原始的权限数组
  buttonPermission: any[]; // 按钮的权限
  permissionNav: any[]; // 导航的权限
  parameterConfig: IObj<'true' | 'false' | string>; // 全局参数对象V1 {参数code:'true'|'false'}
  parameterConfigObj: IObj<string[]>; // 全局参数对象V2 {模块code:[参数code,参数code]}
  permissionCodes: string[]; // 权限codes
  rmanGlobalParameter: any[];
  homePageConfig: any; //存储首页配置
  fileMap: any;
  semesterMap: any;
}
export interface IGlobal {
  userInfo: {
    avatar: string;
    disabled: boolean;
    email: string;
    loginName: string;
    nickName: string;
    phone: string;
    userCode: string;
    roles: { code: string; name: string; description: string, roleCode: string }[];
    extend: {
      [propsName: string]: string;
    };
  };
  showLoading: boolean;
}

export interface CourseType {
  namespace: 'global';
  state: IGlobalModelState;
  effects: {
    fetchUserInfo: Effect;
    fetchLoginOut: Effect;
    fetchLogin: Effect;
    fetchMenu: Effect;
    initParameterConfig: Effect;
    initPermissions: Effect;
    fetchUserCode: Effect;
    fetchOrganization: Effect;
    fetchAllUserByOrg: Effect;
    fetchGlobalParameter: Effect;
    fetchCurrentEnv: Effect;
    fetchSemester: Effect;
  };
  reducers: {
    handlePublicChange: Reducer<any>;
  };
  subscriptions: {
    setup: Subscription;
  };
}

const CourseType: CourseType = {
  namespace: 'global',
  state: {
    code: '',
    userInfo: {},
    permission: [],
    buttonPermission: [],
    permissionNav: [],
    parameterConfig: {}, // 之前的权限判断对象
    parameterConfigObj: {}, // 之后的权限判断对象, 保留整个对象，因为要判断moduleCode（只包含value==="true"的权限）
    permissionCodes: [],
    rmanGlobalParameter: [], //rman的配置权限
    homePageConfig: {}, //存储首页配置
    fileMap: {}, // 文件支持类型
    semesterMap: {}, //学期配置
  },
  subscriptions: {
    setup({ dispatch, history }) {
      const pathname = history.location.pathname;
      if (pathname == '/offlinesignin') {
        return
      }
      console.info(history.location.pathname)
      dispatch({
        type: 'fetchUserInfo',
      });
      dispatch({
        type: 'fetchCurrentEnv',
      });
      dispatch({
        type: 'fetchGlobalParameter',
      });
      dispatch({
        type: 'fetchSemester',
      });
      // 这个是那几个新开的页面 需要去重新获取权限
      const zlRouter = [
        'course',
        'organization',
        'editcourse',
        'coursetemplate',
        'tempatedetail',
        "home",
        'coursemap/minemap',
        '/mapdetail/editmap',
        '/mapv3',
        '/perviewemap',
        '/mapv4',
        "agent",
        '/perviewemap',
        "/tccaselibrary",
        "/micromajor",
        "/atlasmap",
        "/editmicromajor",
        "/reviewcenter/import",
        "/reviewcenter/share",
        "/reviewcenter/process",
        "/reviewcenter/course",
        "/reviewcenter/call",
        '/othereditmap',
        "/stuTextbook",
        "personalstatistic",
        "/microprofessional",
        "/mapCourse",
        "/canvasmap"
        // "announcement"
      ];
      // 知了获取配置页面
      if (zlRouter.some(r => pathname.indexOf(r) > -1) || pathname === "/") {
        dispatch({
          type: 'initParameterConfig',
        });
        dispatch({
          type: 'initPermissions',
        });
      }
    },
  },
  effects: {
    *fetchUserInfo({ payload, callback }, { call, put }): any {
      // const res = yield call(getUserInfo);
      const res = yield call(userInfo);
      // console.log(res,'fetchUserInfo')
      yield put({
        type: 'handlePublicChange',
        payload: {
          userInfo: res.extendMessage,
        },
      });
      callback?.(res);
    },
    // *fetchLoginOut({ payload, callback }, { call, put }) {
    //   const res = yield call(handleLogOut);
    //   callback?.(res);
    // },

    // *fetchLogin({ payload, callback }, { call, put }) {
    //   const { params } = payload;
    //   const res = yield call(login, params);
    //   callback?.(res);
    // },

    // *fetchMenu({ payload, callback }, { call, put }) {
    //   const [res1, res2] = yield [
    //     call(getTeacherMaterialMenu),
    //     call(getPermission),
    //   ];

    //   const teacherMaterialMenu = map(res1.data, item => ({
    //     name: item?.type_name,
    //     path: `/textmange/institutions/${item?.id}`,
    //   }));

    //   // 导航的权限 和 二级菜单的权限
    //   const permission = res2.extendMessage.modules;
    //   // 按钮的权限
    //   const permissionBtn = res2.extendMessage.moduleFeatures;

    //   // 筛选出有权限的导航
    //   const permissionNav = filter(
    //     getMenuList(teacherMaterialMenu),
    //     item => includes(permission, item.authority) || isEmpty(item.authority),
    //   );

    //   yield put({
    //     type: 'handlePublicChange',
    //     payload: {
    //       permission,
    //       permissionNav,
    //       buttonPermission: flattenDeep(
    //         map(permissionBtn, item => values(item)),
    //       ), // 按钮的权限
    //     },
    //   });
    //   callback?.(teacherMaterialMenu, permission);
    // },
    *initParameterConfig({ payload }, { call, put }): any {
      const res = yield call(themeService.fetchParameterConfigs);
      if (res.errorCode === 'success' && res.extendMessage.length > 0) {
        const parameterConfig: IObj<'true' | 'false'> = {};
        const parameterConfigObj: IObj<string[]> = {};
        res.extendMessage.forEach((r: any) => {
          parameterConfig[r.code] = r.value;
          if (r.value === 'true') {
            parameterConfigObj[r.moduleCode] = parameterConfigObj[r.moduleCode]
              ? [...parameterConfigObj[r.moduleCode], r.code]
              : [r.code];
          }
        });
        yield put({
          type: 'handlePublicChange',
          payload: {
            parameterConfig,
            parameterConfigObj,
          },
        });
      }
    },
    *initPermissions({ payload }, { call, put }): any {
      const res = yield call(themeService.fetchUserPermissionsV2);
      console.log('zhutise111');
      if (
        res.errorCode === 'success' &&
        typeof res.extendMessage === 'object'
      ) {
        const permissionCodes = Object.keys(res.extendMessage)
          .map(key => res.extendMessage[key])
          .flat(2);
        const permission = res.extendMessage.modules;
        const permissionBtn = res.extendMessage.moduleFeatures;
        yield put({
          type: 'handlePublicChange',
          payload: {
            permissionCodes,
            permission,
            buttonPermission: flattenDeep(
              map(permissionBtn, item => values(item)),
            ), // 按钮的权限
          },
        });
      }
    },
    // *fetchUserCode({ payload, callback }, { call, put }) {
    //   const res = yield call(getUserCode, { ...payload });
    //   callback?.(res);
    // },

    // *fetchOrganization({ payload, callback }, { call, put }) {
    //   const res = yield call(getOrganization, payload);
    //   callback?.(res);
    // },

    // *fetchAllUserByOrg({ payload, callback }, { call, put }) {
    //   const res = yield call(queryUserList, { ...payload });
    //   callback?.(res);
    // },
    //获取rman全局配置参数
    *fetchGlobalParameter(_: any, { call, put }: any) {
      const {
        errorCode,
        extendMessage,
      } = yield call(
        themeService.fetchRmanGlobalParam,
      );
      if (errorCode === 'success') {
        if (Array.isArray(extendMessage)) {
          const rmanGlobalParameter = extendMessage
            .filter(item => item.value === "true")
            .map(item => item.code);
          yield put({
            type: 'handlePublicChange',
            payload: {
              rmanGlobalParameter
            },
          });
        }
      }
    },
    //获取当前环境
    *fetchCurrentEnv(_: any, { call, put }: any) {
      const {
        errorCode,
        extendMessage,
      } = yield call(
        themeService.fetchCurrentEnv,
      );
      if (errorCode === 'success') {
        if (Array.isArray(extendMessage)) {
          const homePageConfig: any = {};
          extendMessage
            .map(item => {
              homePageConfig[item.code] = item.value
            });
          yield put({
            type: 'handlePublicChange',
            payload: {
              homePageConfig
            },
          });
        }
      }
    },
    //获取学期showName配置
    *fetchSemester(_: any, { call, put }: any) {
      const {
        status,
        data,
      } = yield call(
        getSemesters,
      );
      const {semesterDataList = []} = data || {}
      if (status === 200) {
        let semesterMap: any = {}
        semesterDataList.map((el: any)=>{
          semesterMap[el.name] = el.showSemester ?? el.name
        })
        // 使用 Proxy 包装 semesterMap
        semesterMap = new Proxy(semesterMap, {
          get: function(target, prop) {
            // 如果属性存在，则返回该属性值
            if (prop in target) {
              return target[prop];
            }
            // 如果属性不存在，则返回属性名
            return prop;
          }
        });
        yield put({
          type: 'handlePublicChange',
          payload: {
            semesterMap
          },
        });
      }
    },
  },

  reducers: {
    handlePublicChange: (state: IGlobalModelState, { payload }) => {
      return {
        ...state,
        ...payload,
      };
    },
  },
};

export default CourseType;
