import React, { FC, useState, useEffect } from 'react';
import './index.less';
import baseInfo from '@/api/baseInfo';
import { releaseCourse, offShelfCourse, updataCourse } from '@/api/mooclist';
import { useSelector, useHistory, useDispatch } from 'umi';
import { Menu, message, Button, Modal, Radio } from 'antd';
import { IconFont } from '@/components/iconFont';
import courseTemplate from '@/api/courseTemplate';
import Loading from '@/components/loading/loading';
import { IGlobalModelState } from '@/models/global';
import Icon, { LeftOutlined, LeftCircleFilled } from '@ant-design/icons';
import { ReactComponent as homework_icon } from '@/assets/imgs/icon/homework.svg';
import Header from '@/components/Header';
import NPUHeader from "@/components/NPUHeader/index.tsx";
import { CUSTOMER_NPU } from '@/permission/moduleCfg';
import useLocale from '@/hooks/useLocale';
import TplShareModal from '@/components/TemplateShareModal';

const shareTypes = ["校内共享", "院内共享", "自定义共享"]

interface IauthorityList {
  icon: any;
  name: string;
  path: string;
  authorit: string;
  key: number;
}

const HomeworkIcon = (props: any) => <Icon component={homework_icon} {...props} />;

const TemplateDeatilLayout: FC<{}> = ({ children }) => {
  const { t } = useLocale();
  let history: any = useHistory();
  const dispatch = useDispatch();
  const [courseData, setCourseData] = useState<any>();
  const [basicCollege, setBasicCollege] = useState<string>('');
  const [basicMajors, setBasicMajors] = useState<string>('');
  const [basicCover, setBasicCover] = useState<string>('');
  const [publishCount, setPublishCount] = useState<string>('');
  const [resourcesCount, setResourcesCount] = useState<string>('');
  const [peopleCount, setPeoplCount] = useState<string>('');
  const [publishStatus, setPublishStatus] = useState<number>(0);
  const [authorityList, setAuthorityList] = useState<IauthorityList[]>([]);
  const [releaseValue, setReleaseValue] = useState<string>('no');
  const [release, setRelease] = useState<boolean>(false);
  const [releaseOrNot, setReleaseOrNot] = useState<boolean>(false);
  const [selectedKeys, setSelectedKeys] = useState<any[]>([]);
  const [shareLoading, setShareLoading] = useState<boolean>(false);
  const [tplShareVisible, setTplShareVisible] = useState<boolean>(false);
  const queryData = history.location.query;
  const layouechange: any[] = useSelector<any, any>(
    (state) => state.updata.layoutUpdata);

  const { parameterConfigObj } = useSelector<
    { global: IGlobalModelState; },
    IGlobalModelState>(
      (state) => state.global);
  // document.title = '教学空间';

  useEffect(() => {
    // history.push(
    //   `/tempatedetail/courseInfo?id=${queryData.id}&type=${queryData.type}&myOrShare=${queryData.myOrShare}`,
    // );
    getName();
    getAct();
  }, [layouechange]);

  useEffect(() => {
    const key = history.location.pathname.replace("/tempatedetail/", "");
    setSelectedKeys([key]);
  }, [history.location]);

  // 发布下架课程
  const handleReleaseOK = (data?: any) => {
    let num = releaseOrNot ? 1 : 0;
    setShareLoading(true);
    if (num == 1) {
      courseTemplate.personaltoshare(queryData.id, data).then((res) => {
        if (res && res.message === 'OK') {
          message.success(t('共享成功！'));
          courseTemplate.copyhomework({
            "orgCourseId": queryData.id,
            "targetCourseId": res.data
          });
        } else {
          message.error(res.message);
        }
        getName();
        setRelease(false);
        dispatch({
          type: 'updata/changeCourse',
          payload: {}
        });
      }).finally(() => {
        setShareLoading(false);
      });
    } else {
      courseTemplate.releasestatus(num, [queryData.id]).then((res: any) => {
        if (res && res.message === 'OK') {
          getName();
          message.success(t('取消共享成功！'));
          setRelease(false);
        } else {
          getName();
          message.error(t('取消共享失败！'));
          setRelease(false);
        }
        dispatch({
          type: 'updata/changeCourse',
          payload: {}
        });
      }).finally(() => {
        setShareLoading(false);
      });
    }
  };

  const getName = () => {
    courseTemplate.getTemplateDetail(queryData.id).then((res) => {
      if (res && res.message === 'OK') {
        setCourseData(res.data);
        setPublishStatus(res.data.entityData.release_type === "public" ? 1 : 0);
        setBasicCollege(
          res.data.entityData.college ?
            res.data.entityData.college.map((item: any) => item.value) :
            '');

        let majors: string[] = [];
        res.data.entityData.major &&
          res.data.entityData.major.forEach((item: any) => {

            if (Array.isArray(res.data.entityData.major)) {
              majors.push(item.value);
            } else {
              majors.push(item.split(',')[1]);
            }
          });
        setBasicMajors(majors.join('，'));
        setBasicCover(res.data.entityData.cover);
        dispatch({
          type: 'moocCourse/updateState',
          payload: {
            templateCourseDetail: res.data
          }
        });
      }
    });
  };
  // const updataCourseState = () => {
  //   courseTemplate.updatatemCourse([queryData.id]).then(res => {
  //     if (res && res.message === 'OK') {
  //       message.success('课程更新成功');
  //       getName();
  //       dispatch({
  //         type: 'updata/changeCourse',
  //         payload: {},
  //       });
  //     } else {
  //       message.error(res.errorMsg);
  //     }
  //   });
  // };
  const getAct = () => {
    baseInfo.getactivities(queryData.id ?? 1).then((res) => {
      if (res && res.message === 'OK') {
        // console.log(res)
        setPublishCount(res.data.publishCount);
        setResourcesCount(res.data.resourcesCount);
      }
    });
  };
  const { parameterConfig } = useSelector<{ global: any; }, { parameterConfig: any; }>((state) => state.global);


  return (
    <div className="edit-course">
      {parameterConfig.target_customer === CUSTOMER_NPU ? <NPUHeader /> : <Header />}
      <div className="edit-top">
        <div className="top-right">
          <div className="info_box">
            <a onClick={() => window.open(`#/coursetemplate/${queryData.myOrShare ? "my" : "share"}template`, "_self")}>
              {parameterConfig.target_customer === CUSTOMER_NPU ? <LeftCircleFilled /> : <><LeftOutlined />{t("返回")}</>}
            </a>
            <span className="course-title">{courseData?.name}</span>
            {publishStatus === 1 && <span className="publishStatus published">{t("已共享")}：{shareTypes?.[courseData?.entityData?.sharedType]}</span>}
            {/* <div className="course-data">
               <span>发布数：</span>
               <div>
                 <span className="publish">{publishCount}</span> /{' '}
                 {resourcesCount}
               </div>
              </div> */}
          </div>
          {queryData.type == 'edit' ?
            <div className="buttom_box">
              {/* <Button
               type="primary"
               disabled={publishStatus !== 1}
               onClick={updataCourseState}
              >
               更新课程
              </Button> */}
              {publishStatus === 1 ?
                <Button
                  type={`${parameterConfig.target_customer === CUSTOMER_NPU ? 'default' : 'primary'}`}
                  onClick={() => {
                    setReleaseOrNot(false);
                    setRelease(true);
                  }}>
                  {t("取消共享")}

                </Button> :

                <Button
                  type={`${parameterConfig.target_customer === CUSTOMER_NPU ? 'default' : 'primary'}`}
                  onClick={() => {
                    setReleaseOrNot(true);
                    setTplShareVisible(true);
                  }}>
                  {t("共享")}

                </Button>}

            </div> :

            ''}

        </div>
        {/* <div className='top-left'>
                     <div className='publish course-data'>
                         <div><img src={require('@/assets/imgs/EditCourse/release.svg')}></img></div>
                         <div className='data'>
                             <span>{publishCount}/{resourcesCount}</span>
                             <span>发布数</span>
                         </div>
                     </div>
                     <div className='participatein course-data'>
                         <div><img src={require('@/assets/imgs/EditCourse/participatein.svg')}></img></div>
                         <div className='data'>
                             <span>{peopleCount}人</span>
                             <span>参与人数</span>
                         </div>
                     </div>
                     <div className='browse course-data'>
                         <div><img src={require('@/assets/imgs/EditCourse/browse.svg')}></img></div>
                         <div className='data'>
                             <span>86次</span>
                             <span>开课次数</span>
                         </div>
                     </div>
                 </div> */}
      </div>
      <div className="edit-detail">
        <div className="edit-menu">
          <div className="course-img">
            <img src={basicCover}></img>
          </div>
          <Menu
            style={{ width: 230 }}
            defaultSelectedKeys={[queryData.key || 'courseInfo']}
            selectedKeys={selectedKeys}
            mode="inline"
          // onSelect={(e: any) => setSelectedKeys(e.selectedKeys)}
          >
            <Menu.Item
              key="courseInfo"
              icon={<IconFont type="iconkechengxinxi" />}
              onClick={() =>
                history.push(
                  `/tempatedetail/courseInfo?id=${queryData.id}&type=${queryData.type}&myOrShare=${queryData.myOrShare}&key=1`)}>


              {t("基本信息")}

            </Menu.Item>
            <Menu.Item
              key="chapter"
              icon={<IconFont type="iconwenzhangpailie2-22" />}
              onClick={() =>
                history.push(
                  `/tempatedetail/chapter?id=${queryData.id}&type=${queryData.type}&myOrShare=${queryData.myOrShare}&key=2`)}>


              {t("章节内容")}

            </Menu.Item>
            <Menu.Item
              key="teachingteam"
              icon={<IconFont type="iconshoukejiaoshi" />}
              onClick={() =>
                history.push(
                  `/tempatedetail/teachingteam?id=${queryData.id}&type=${queryData.type}&myOrShare=${queryData.myOrShare}&key=5`)}>


              {t("教学团队")}

            </Menu.Item>
            <Menu.Item
              key="resources"
              icon={<IconFont type="iconziyuan1" />}
              onClick={() =>
                history.push(
                  `/tempatedetail/resources?id=${queryData.id}&type=${queryData.type}&myOrShare=${queryData.myOrShare}&key=6`)}>


              {t("资源")}

            </Menu.Item>
            {parameterConfigObj.kczx?.includes('course_library_school_assignment_display') && <Menu.Item
              key="homework"
              icon={<HomeworkIcon />}
              onClick={() =>
                history.push(
                  `/tempatedetail/homework?id=${queryData.id}&type=${queryData.type}&myOrShare=${queryData.myOrShare}&key=3`)}>


              {t("作业")}

            </Menu.Item>}
            {queryData.type === 'edit' ?
              <Menu.Item
                key="usage"
                icon={<IconFont type="iconshiyongqingkuang1" />}
                onClick={() =>
                  history.push(
                    `/tempatedetail/usage?id=${queryData.id}&type=${queryData.type}&myOrShare=${queryData.myOrShare}&key=7`)}>


                {t("使用情况")}

              </Menu.Item> :

              ''}

            {/* soso统计 */}
            {/* <Menu.Item
               key="8"
               icon={<IconFont type="iconshezhi7" />}
               onClick={() =>
                 history.push(
                   `/tempatedetail/statistics?id=${queryData.id}&type=${queryData.type}&myOrShare=${queryData.myOrShare}`,
                 )
               }
              >
               统计
              </Menu.Item> */}
          </Menu>
        </div>
        <div className="edit-content">{children}</div>
      </div>
      <TplShareModal visible={tplShareVisible} onClose={() => setTplShareVisible(false)} onConfirm={handleReleaseOK} />
      <Modal
        title={releaseOrNot ? t("共享") : t("取消共享")}
        visible={release}
        onOk={handleReleaseOK}
        onCancel={() => setRelease(false)}
        confirmLoading={shareLoading}>

        {/* {releaseOrNot ? (
           <div>
             <Radio.Group
               onChange={(e: any) => setReleaseValue(e.target.value)}
               value={releaseValue}
             >
               <Radio value="share">共享</Radio>
               <Radio value="no">不共享</Radio>
             </Radio.Group>
           </div>
          ) : (
           // `确定要${releaseOrNot ? '发布' : '下架'}该课程吗`
           `确定要下架该课程吗`
          )} */}
        {releaseOrNot ? t("确定将课程资源包至共享课程资源中？") : t("确定将课程资源包移除共享课程资源？")}
      </Modal>
      <Loading />
    </div>);

};

export default TemplateDeatilLayout;