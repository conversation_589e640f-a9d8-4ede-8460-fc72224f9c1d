.main_content{
    // background-color: rgb(220,238,252);
    height: 100vh;
    overflow: auto;
    padding: 0 15px;
    border-radius: 5px;
    .select-container {
        .ant-select-selection-item {
            font-weight: bold;
        }
        .micro-select {
          .ant-select-selector {
            padding: 0px;
          }
        }
    }
    .title{
        font-size: 18px;
        font-weight: 700;
    }
    .top_content{
        border-radius: 7px;
        margin-top: 15px;
        display: flex;
        align-items: center;
        gap: 12px;
        // justify-content: space-between;
        .block1{
            flex: 1;
            height: 110px;
            background: rgba(255,255,255,0.6);
            box-shadow: 0px 10px 20px 0px rgba(119,110,245,0.05);
            border-radius: 10px;
            border: 2px solid #FFFFFF;
            backdrop-filter: blur(4px);
            padding: 15px 20px;
            border-radius: 7px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            img{
                width: 88px;
                height: 88px;

            }
            .info{
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                .name{
                    font-size: 14px;
                    white-space: nowrap;
                    color: rgb(111,113,114);
                }
                .number{
                    font-size: 24px;
                    font-weight: 700;
                    color: rgb(59,146,233);
                }
                .color1{
                    color: rgb(190, 135, 234);
                }
                .color2{
                    color: rgb(252,134,66);
                }
            }
            .info1{
                margin-left: 15px;
                padding-left: 15px;
                position: relative;
                &:before {
                    content: "";
                    display: block;
                    width: 1px;
                    height: 100%;
                    background-color: #F2F4F5;
                    position: absolute;
                    bottom: 0px;
                    left: 0px;
                }
            }
        }
        .block2{
            flex: 2;
            height: 110px;
            background: rgba(255,255,255,0.6);
            box-shadow: 0px 10px 20px 0px rgba(119,110,245,0.05);
            border-radius: 10px;
            border: 2px solid #FFFFFF;
            backdrop-filter: blur(4px);
            padding: 15px 20px;
            border-radius: 7px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .panel{
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                position: relative;
                .number{
                    font-size: 24px;
                    font-weight: 700;
                    color: rgb(59,146,233);
                }
                .name{
                    font-size: 14px;
                    color: rgb(111,113,114);
                }
                .split:before{
                    content: "";
                    display: block;
                    width: 1px;
                    height: 100%;
                    background-color: #F2F4F5;
                    position: absolute;
                    bottom: 0px;
                    left: 0px;
                }
            }
        }
    }
    .center_content{
        border-radius: 7px;
        margin-top: 15px;
        background: rgba(255,255,255,0.6);
        box-shadow: 0px 10px 20px 0px rgba(119,110,245,0.05);
        border-radius: 10px;
        border: 2px solid #FFFFFF;
        backdrop-filter: blur(4px);
        margin-top: 15px;
        padding: 15px;
        height: 400px;
        .top{
            display: flex;
            justify-content: space-between;
            .header{
                font-size: 15px;
                font-weight: 600;
                position: relative;
                margin-left: 10px;
                &:before {
                    content: "";
                    display: block;
                    width: 3px;
                    height: 50%;
                    background-color: rgb(59,146,233);
                    position: absolute;
                    bottom: 25%;
                    left: -10px;
                }
            }
        }
        .main_content{
            background-color: transparent;
            height: 358px;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            // margin-top: 10px;
            &::-webkit-scrollbar {
                display: none ; /* 隐藏滚动条 */
            }
            .left{
                width: 230px;
                height: 260px;
                margin-top: 20px;
                // padding: 35px 55px;
                .image_content{
                    position: relative;
                    .image{
                        width: 230px;
                        height: 300px;
                        margin-top: -40px;
                    }
                    .bg_content{
                        width: 230px;
                        height: 300px;
                        position: absolute;
                        top: -40px;
                        // background-image: url('../../../assets/imgs/EditCourse/info_background.png');
                        // background-size: 100% 100%;
                        // background-repeat: no-repeat;
                        color: white;
                        display: flex;
                        align-items: center;
                        flex-direction: column;
                        justify-content: center;
                        z-index: 999;
                        .mychart{
                            height: 100px;
                            width: 100px;
                        }
                        .number{
                            font-size: 28px;
                            span{
                                font-size: 16px;
                            }
                        }
                    }
                }


            }
            .center{
                width: 20%;
                height: 320px;
                margin-top: -30px;
            }
            .right{
                width: 45%;
                height: 320px;
                // margin-top: 10px;
                padding-right: 20px;
                margin-bottom: 13px;
                overflow: auto;
                // &::-webkit-scrollbar {
                //     display: none ; /* 隐藏滚动条 */
                // }
                .top_right_container{
                    height: auto;
                    .header{
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin: 3px 0px;
                        .text{
                            .text1{
                                background-color: #E5F5FF;
                                color: #76A2D2;
                                padding: 1px 6px;
                                border-radius: 5px;
                                font-size: 15px;
                                display: inline-block;
                                min-width: 60px;
                                text-align: center;
                                max-width: 100px;
                                text-wrap: nowrap;
                                text-overflow: ellipsis;
                                overflow: hidden;
                            }
                            .active{
                                background-color: rgb(87, 155, 255);
                                color: white;
                            }
                            .text2{
                                font-size: 14px;
                                margin-left: 15px;
                                display: inline-block;
                                max-width: 500px;
                                text-wrap: nowrap;
                                text-overflow: ellipsis;
                                overflow: hidden;
                            }
                        }
                        .end{
                            color: #76A2D2;
                            font-size: 13px;
                            cursor: pointer;
                            img{
                                width: 7px;
                                height: 7px;
                                margin: 7px 0px 0px 8px;
                            }
                        }
                    }
                    .detail{
                        height: 160px;
                        overflow: hidden;
                        transition: height 0.3s ease;

                        .echarts-for-react{
                            height: 160px!important;
                        }
                    }

                    .hidden{
                        height: 0;
                    }
                }
            }
        }
    }
    .bottom_content{
        height: calc(100% - 206px);
        margin-top: 15px;
        display: flex;
        justify-content: space-between;
        .bottom_left{
            width: 53%;
            background: rgba(255,255,255,0.6);
            box-shadow: 0px 10px 20px 0px rgba(119,110,245,0.05);
            border-radius: 10px;
            border: 2px solid #FFFFFF;
            backdrop-filter: blur(4px);
            border-radius: 7px;
            padding: 15px;
            .antd_space{
                margin-bottom: 20px;
                .btn{
                    position: relative;
                    font-size: 15px;
                    font-weight: 600;
                    cursor: pointer;
                }
                .active{
                    color: rgb(85,155,242);
                    &:after {
                      content: "";
                      display: block;
                      width: 50%;
                      height: 2px;
                      background-color: rgb(85,155,242); /* 设置横线颜色 */
                      position: absolute;
                      bottom: -10px;
                      left: 25%;
                    }
                }
            }
            .container{
                width: 100%;
                height: calc(100% - 23px);
                .echarts-for-react{
                    height: 450px;
                }
            }
        }
        .bottom_right{
            width: calc(47% - 15px);
            background: rgba(255,255,255,0.6);
            box-shadow: 0px 10px 20px 0px rgba(119,110,245,0.05);
            border-radius: 10px;
            border: 2px solid #FFFFFF;
            backdrop-filter: blur(4px);
            border-radius: 7px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            .antd_space{
                margin-bottom: 20px;
                .btn{
                    position: relative;
                    font-size: 15px;
                    font-weight: 600;
                    cursor: pointer;
                }
                .active{
                    color: rgb(85,155,242);
                    &:after {
                      content: "";
                      display: block;
                      width: 50%;
                      height: 2px;
                      background-color: rgb(85,155,242); /* 设置横线颜色 */
                      position: absolute;
                      bottom: -10px;
                      left: 25%;
                    }
                }
            }
            .container{
                flex: 1;
                display: flex;
                justify-content: center;
                align-items: center;
            }
          .study_bag {
            flex-direction: column;
            &_item {
              position: relative;
              display: flex;
              align-items: center;
              padding-left: 16px;
              margin-bottom: 12px;
              &_title {
                font-weight: 600;
              }
              &_title::before{
                content: '';
                position: absolute;
                left: 0px;
                top:5px;
                width: 3px;
                height: 14px;
                background-color: rgb(87, 155, 255);
              }
            }
            &_item:not(:first-child) {
              margin-top: 16px;
            }
            .scroll_view {
              //flex:1;
              //overflow-y: scroll;
              padding-bottom: 30px;
            }
            //.scroll_view::-webkit-scrollbar {
            //  width: 6px;
            //  height: 6px;
            //}
            .scroll_view::-webkit-scrollbar {
              width: 6px;
              height: 6px;
            }

            .scroll_view::-webkit-scrollbar-thumb {
              width: 5px !important;
              background: #888;
              border-radius: 10px;

            }
          }
        }
    }
}
