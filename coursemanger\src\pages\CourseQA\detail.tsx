/*
 * @Author: lijin
 * @Description: 问答详情
 * @Date: 2022-02-23 10:14:34
 * @LastEditTime: 2022-04-14 15:32:22
 * @LastEditors: 李武林
 * @FilePath: \coursemanger\src\pages\CourseQA\detail.tsx
 */
import CourseHeader from '@/components/CourseHeader/CourseHeader';
import {
  Breadcrumb,
  Button,
  Col,
  Empty,
  Input,
  List,
  message,
  Modal,
  Row,
  Spin
} from
  'antd';
import React, { useEffect, useRef, useState } from 'react';
import qs from 'qs';
import { FC } from 'react';
import { useDispatch, useLocation, useSelector } from 'umi';
import './detail.less';
import QAService from '@/api/qa';
import TopicItem from './components/topicItem';
import { IconFont } from '@/components/iconFont';
import CustomComment from './components/customComment';
import ReplyInput from './components/replyInput';
import { LeftOutlined } from '@ant-design/icons';
import { queryNodeinfo } from '@/api/coursemap';
import Item from 'antd/lib/list/Item';
import { getteacherlist } from '@/api/teacher';
import QAPublicModal from './components/QAPublicModal';
import { getSensitiveWord } from '@/utils';
import useLocale from '@/hooks/useLocale';
import { CUSTOMER_NPU } from '@/permission/moduleCfg';
import QAReplyModal from './components/QAReplyModal';
interface IQADetailQuery {
  id: string; // 课程id
  type: 'spoc' | 'mooc';
  topicid: string; // 主题id
  isJoin?: 'false' | 'true'; // 是否加入
  iflearn: '1' | undefined; // 是否是learn嵌入界面
  ifcanvas?: 'true' | undefined; // 是否是canvas嵌入界面
  sm: number; // 期次
  courseid?: string; // 课程id
  r?: '0' | '1'; //  0 教师 1 学生
  courseName?: string; // 课程名称
}
interface CourseQADetailProps {
  ismap?: boolean;
  topicid?: string;
  onback?: any;
}

const CourseQADetail: FC<CourseQADetailProps> = (props) => {
  const { t } = useLocale();
  const location: any = useLocation();
  const query: IQADetailQuery = location.query;
  const dispatch = useDispatch();
  const { userInfo, parameterConfig } = useSelector<any, any>((state) => state.global);
  const topicParam = useRef({
    link_id: query.id || query.courseid,
    topic_id: props.ismap ? props.topicid : query.topicid,
    height: 2,
    user_id_of_unread: userInfo.userCode
  });
  const [replyParam, setReplyParam] = useState<QA.ReplySearch>({
    link_id: query.id || query.courseid,
    height: 1,
    page: 1,
    size: 12,
    topic_id: props.ismap ? props.topicid : query.topicid,
    user_id_of_unread: userInfo.userCode
  });
  const [total, setTotal] = useState(0);

  const [replyLoading, setReplyLoading] = useState<boolean>(false);
  const [topic, setTopic] = useState<QA.Topic | null>(null);
  const [replys, setReplys] = useState<QA.Reply[]>([]);
  const [userDic, setUserDic] = useState<{
    [propName: string]: QA.User;
  } | null>(null);
  const [currentItem, setCurrentItem] = useState<QA.Comment | QA.Reply | null>(
    null);

  const [currentItemType, setCurrentItemType] = useState<
    'reply' | 'comment' | undefined>(
      undefined);

  const [knowledge, setKnowledge] = useState(null);
  const [type, setType] = useState<'01' | '02'>('02');
  const [teachersCode, setTeachersCode] = useState<any>([]);
  const [openVisible, setOpenVisible] = useState<boolean>(false);
  const [replyOpen, setReplyOpen] = useState<boolean>(false);
  const [curComment, setCurComment] = useState<any>({});


  useEffect(() => {
    getteacherlist({ id: location.query.id || query.courseid }).then((res: any) => {
      if (res.status === 200) {
        const codeArr = res.data?.map((item: any) => item.userCode);
        setTeachersCode(codeArr ?? []);
      }
    });
  }, []);

  const canDelete = (id: string): boolean => {
    return userInfo.userCode == id ||
      userInfo.roles?.map((item: any) => item.roleCode)?.includes('r_sys_manager') || //是否是系统管理员
      userInfo.roles?.map((item: any) => item.roleCode)?.includes('r_course_manager') || //是否是课程管理员
      teachersCode.includes(userInfo.userCode);
  };
  // 获取知识点详情
  const getnodedetail = (id: string) => {



    // queryNodeinfo(id).then(res => {
    //   setKnowledge(res.data);
    // });
  }; //---------主题
  const getTopic = async () => {
    let params: QA.TopicSearch = topicParam.current; const result = await QAService.fetchTopicList(params);
    if (
      result.error_code === 'forumservice.0000.0000' &&
      result.extend_message?.count === 1) {
      let topic: QA.Topic = result.extend_message.results[0];
      // setType(topic.extend_type)
      setTopic(topic);
      // topic.extend_link_id && topic.extend_type === '01' && getnodedetail(topic.extend_link_id);
      if (topic.unread_id != null) {
        readTopic(topic);
      }
    }
  };

  /**
   * @description: 设置主题已读
   * @param {*}
   * @return {*}
   */
  const readTopic = (topic: QA.Topic) => {
    let param: QA.TopicReadParam = { link_id: query.id || query.courseid, ids: topic.unread_id };
    QAService.readTopic(param).then((res) => {
      if (res.error_code === 'forumservice.0000.0000') {
        dispatch({
          type: 'updata/changeQAlayout'
        });
        if (window.location.href.includes("out/courseqa")) {
          window.parent.postMessage(
            JSON.stringify({ action: 'changeQAUnread' }),
            window.location.origin);

        }
      }
    });
  };
  //---------回复
  const getReply = async () => {
    setReplyLoading(true);
    const result = await QAService.fetchReplyList(replyParam);
    if (result.error_code === 'forumservice.0000.0000') {
      setReplys(result.extend_message.results || []);
      setTotal(result.extend_message.count || 0);
    }
    setReplyLoading(false);
  };

  const addReply = async (replyMsg: string) => {
    if (replyMsg === '') {
      message.info(t('内容不能为空'));
      return false;
    }
    const canAdd: any = await getSensitiveWord(replyMsg, "回复", () => true, () => false);
    if (canAdd) {
      let topicid: any = props.ismap ? props.topicid : query.topicid;
      const result = await QAService.addReply({
        link_id: query.id || query.courseid,
        content: replyMsg,
        user_id: userInfo.userCode,
        user_name: userInfo.nickName,
        user_roles: userInfo.roles?.map((item: any) => item.roleCode),
        topic_id: topicid
      });
      if (result.error_msg === 'Success') {
        message.success(t('回复成功'));
        getReply();
        getTopic();
        return true;
      }
    }
    return false;
  };

  const deleteReply = async (reply: QA.Reply) => {
    const result = await QAService.deleteReply([reply.comment_id], location.query.id || location.query.courseid);
    if (result.error_msg === 'Success') {
      message.success(t('删除成功'));
      getReply();
      return true;
    }
    return false;
  };
  //--------------评论
  const addComment = async (
    replyMsg: string,
    replyItem: QA.Reply,
    commentItem?: QA.Comment) => {
    if (replyMsg === '') {
      // 评论不为空
      message.info(t('内容不能为空'));
      return false;
    }
    const canAdd: any = await getSensitiveWord(replyMsg, "回复", () => true, () => false);
    if (canAdd) {
      const result = await QAService.addComment({
        link_id: query.id || query.courseid,
        content: replyMsg,
        user_id: userInfo.userCode,
        user_name: userInfo.nickName,
        user_roles: userInfo.roles?.map((item: any) => item.roleCode),
        comment_id: replyItem.comment_id,
        target_reply_id: commentItem ? commentItem.reply_id : undefined
      });
      if (result.error_msg === 'Success') {
        message.success(t('评论成功'));
        getReply();
        getTopic();
        return true;
      }
    }
    return false;
  };

  const deleteComment = async (comment: QA.Comment) => {
    const result = await QAService.deleteComment([comment.reply_id], location.query.id || location.query.courseid);
    if (result.error_msg === 'Success') {
      message.success(t('删除成功'));
      getReply();
      return true;
    }
    return false;
  };

  /**
   * @description: 获取用户信息
   * @param {string} userIds
   * @return {*}
   */
  const getUsers = async (userIds: string[]) => {
    const result = await QAService.fetchUser(userIds, location.query.id || location.query.courseid);
    if (result.error_code === 'forumservice.0000.0000') {
      let data = result.extend_message.user_dict;
      setUserDic(data);
    }
  };

  const handleCommentShow = (
    item: QA.Comment | QA.Reply,
    type: 'comment' | 'reply') => {
    if (
      type === currentItemType && (
        type === 'comment' &&
        (item as QA.Comment).reply_id ===
        (currentItem as QA.Comment)?.reply_id ||
        type === 'reply' && item.comment_id === currentItem?.comment_id)) {
      // 再次点击关闭评论
      setCurrentItem(null);
      setCurrentItemType(undefined);
      return;
    }
    setCurrentItem(item);
    setCurrentItemType(type);
  };

  /**
   * @description: 获取回复及其评论下的用户ids
   * @param {QA} replys
   * @return {*}
   */
  const handleGetIds = (
    replys: QA.Reply[])
    : { [propName: string]: string[]; } => {
    let userIds: string[] = [];
    let theReplys: QA.Reply[] = [];
    let theComments: QA.Comment[] = [];
    replys.forEach((item: QA.Reply) => {
      userIds.push(item.user_id);
      theReplys.push(item);
      item.reply_list &&
        item.reply_list.forEach((item: QA.Comment) => {
          userIds.push(item.user_id);
          theComments.push(item);
        });
    });
    // 去重
    userIds = [...new Set(userIds)];
    let unReadReplyIds: any = theReplys.
      filter((item: QA.Reply) => item.unread_id != null).
      map((item: QA.Reply) => item.unread_id);
    let unReadCommentIds: any = theComments.
      filter((item: QA.Comment) => item.unread_id != null).
      map((item: QA.Comment) => item.unread_id);
    return { userIds, unReadReplyIds, unReadCommentIds };
  };

  useEffect(() => {
    getTopic();
  }, []);

  useEffect(() => {
    getReply();
  }, [replyParam]);

  useEffect(() => {
    if (replys.length > 0) {
      let {
        userIds,
        unReadReplyIds: replyIds,
        unReadCommentIds: commentIds
      } = handleGetIds(replys);
      // 获取用户头像
      getUsers(userIds);
      // // 更改layout
      // dispatch({
      //   type: 'updata/changeQAlayout',
      // });
      // 已读reply
      QAService.readReply(userInfo.userCode, replyIds)?.then((res) => {
        if (res.error_code === 'forumservice.0000.0000') {
          dispatch({
            type: 'updata/changeQAlayout'
          });
          if (window.location.href.includes("out/courseqa")) {
            window.parent.postMessage(
              JSON.stringify({ action: 'changeQAUnread' }),
              window.location.origin);

          }
        }
      });;
      // 已读comment
      QAService.readComment(userInfo.userCode, commentIds)?.then((res) => {
        if (res.error_code === 'forumservice.0000.0000') {
          dispatch({
            type: 'updata/changeQAlayout'
          });
          if (window.location.href.includes("out/courseqa")) {
            window.parent.postMessage(
              JSON.stringify({ action: 'changeQAUnread' }),
              window.location.origin);

          }
        }
      });;
    }
  }, [replys]);

  const handlePublic = (id: string, isPublic: boolean) => {
    if (isPublic) {
      setOpenVisible(true);
    } else {
      Modal.confirm({
        content: t("确认取消公开该问答？"),
        onOk() {
          QAService.setPublic({ link_id: location.query.id || location.query.courseid, topic_id: id, permissions: "0" }).then((res: any) => {
            if (res.error_msg === "Success") {
              message.success(t("取消公开成功！"));
              setTopic({ ...topic, permissions: "0" });
            } else {
              message.error(t("取消公开失败!"));
            }
          });
        }
      });
    }
  };

  return (
    <div className="courseqa_detail">
      {props.ismap ?
        <div style={{ cursor: 'pointer' }} onClick={() => props.onback()}>
          <LeftOutlined style={{ color: '#828282' }} />
          <span style={{ color: '#828282', marginLeft: '5px' }}>{t("返回")}</span>
        </div> :

        Reflect.ownKeys(parameterConfig).length > 0 && <CourseHeader
          title={
            <Breadcrumb separator=">">
              <Breadcrumb.Item
                href={(()=>{
                  if(query.ifcanvas == 'true'){
                    return `#/canvasmap/courseqa?${qs.stringify({
                      id: query.id || query.courseid,
                      type: query.type,
                      ifcanvas: query.ifcanvas,
                      sm: query.sm,
                      isJoin: query.isJoin,
                      r:query.r,
                      courseName: query.courseName
                    })}`
                  }else{
                    return `#${query.iflearn !== '1' ? '/editcourse' : '/out'
                  }/${query.type === 'mooc' && query.iflearn !== '1' ? 'mooc' : ''}courseqa?${qs.stringify({
                    id: query.id || query.courseid,
                    type: query.type,
                    iflearn: query.iflearn,
                    sm: query.sm
                  })}`
                  }
                })()}>
                {parameterConfig.target_customer === CUSTOMER_NPU ? t("问学区") : t("问答区")}

              </Breadcrumb.Item>
              <Breadcrumb.Item>{t("提问详情")}</Breadcrumb.Item>
            </Breadcrumb>}

          className="qadetail_header"
          ifDivider={true} />
      }


      {topic &&
        <TopicItem onTopicDelete={() => props.onback()} topic={topic} ifDelete={canDelete(topic.user_id)} knowledge={knowledge} isDetail={true} canOpen={canDelete("") && location.query.type === "mooc"} onPublicClick={handlePublic} />}

      <div className="comment_container">
        <div className="reply_count">{`${topic?.unread_subtotal ||
          0}${t("条新回复")}`}</div>
        <ReplyInput canReply={topic?.can_reply} permissions={topic?.permissions} onReply={addReply} />
        <List
          itemLayout="vertical"
          pagination={{
            size: 'small',
            showQuickJumper: true,
            showTotal: (total) => t("共{name}条", String(total)),
            current: replyParam.page,
            total: total,
            pageSize: replyParam.size,
            showSizeChanger: true,
            hideOnSinglePage: true, //只有一页隐藏分页
            pageSizeOptions: ['12', '24', '36', '48', '60'],
            onChange: (page: number, pageSize?: number) => {
              setReplyParam({ ...replyParam, page: page, size: pageSize || 0 });
            }
          }}
          loading={replyLoading}
          dataSource={replys}
          renderItem={(reply: QA.Reply) =>
            <CustomComment
              canDelete={canDelete(reply.user_id)}
              avatar={userDic ? userDic[reply.user_id]?.avatar_url : undefined}
              ifTeacher={reply.user_roles.
                map((item) => item?.code ?? item).
                includes('r_teacher')}
              comment={reply}
              ifCommenting={
                currentItem?.comment_id === reply.comment_id &&
                currentItemType === 'reply'}

              ifEdit={true}
              onComment={(reply) => handleCommentShow(reply, 'reply')}
              onReply={addComment}
              onDelete={deleteReply}
              key={reply.comment_id}>

              {reply.reply_list &&
                reply.reply_list.length > 0 &&
                reply.reply_list
                // .slice(0, 3)
                .map((comment) =>
                  <CustomComment
                    canDelete={canDelete(comment.user_id)}
                    avatar={
                      userDic ? userDic[comment.user_id]?.avatar_url : undefined}

                    ifTeacher={comment.user_roles.
                      map((item) => item?.code ?? item).
                      includes('r_teacher')}
                    type="comment"
                    ifCommenting={
                      (currentItem as QA.Comment)?.reply_id === comment.reply_id}

                    comment={comment}
                    onComment={(comment) => handleCommentShow(comment, 'comment')}
                    onDelete={(item: QA.Comment | QA.Reply) =>
                      deleteComment((item as QA.Comment))}

                    onReply={(
                      replyMsg: string,
                      currentItem: QA.Comment | QA.Reply) =>
                      addComment(replyMsg, reply, (currentItem as QA.Comment))}
                    key={comment.reply_id}>
                  </CustomComment>)}
              {/* {
                reply.reply_list && reply.reply_list?.length > 3 && <div className='show-more-btn' onClick={() => {
                  setCurComment(reply);
                  setReplyOpen(true);
                }}>查看全部{reply.reply_list.length}条回复 {'>'}</div>
              } */}

            </CustomComment>} />


      </div>
      <QAPublicModal topicId={topic?.topic_id} visible={openVisible} permission={topic?.permissions} onClose={() => setOpenVisible(false)} onOk={(permissions?: string) => setTopic({ ...topic, permissions })} />
      {/* <QAReplyModal open={replyOpen} comment={curComment} teachersCode={teachersCode} usersAvatar={userDic} onClose={() => setReplyOpen(false)} refresh={() => {
        getReply();
        getTopic();
      }} /> */}
    </div>);

};
export default CourseQADetail;