import React, { FC, useRef, useState } from 'react';
import { Modal, Upload, Button, message } from 'antd';
import Icon, { DeleteOutlined } from '@ant-design/icons';
import { ReactComponent as plus_icon } from "@/assets/imgs/icon/plus_icon.svg";
import chapterApis from "@/api/chapter";
import { useLocation } from "umi";
import "./WordImportModal.less";
import useLocale from '@/hooks/useLocale';

const { Dragger } = Upload;

interface IWordImport {
  visible: boolean;
  onClose: () => void;
}

const PlusIcon = (props: any) => <Icon component={plus_icon} {...props} />;

const WordImportModal: FC<IWordImport> = ({ visible, onClose }) => {
  const { t } = useLocale();
  const location: any = useLocation();
  const [fileData, setFileData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const props = {
    name: 'file',
    showUploadList: false,
    // action: '/rman/v1/upload/reference/material/import',
    beforeUpload(file: any) {
      const nameArr = file.name.split('.');
      if (nameArr[nameArr.length - 1] === 'docx') {
        setFileData(file);
      } else {
        message.warning("只能上传word文件！");
      }
      return false;
    },
    onDrop(e: any) {
      console.log('Dropped files', e.dataTransfer.files);
    }
  };
  const handleImport = () => {
    if (!fileData.name) {
      message.warning(t("请先上传文件再导入！"));
      return;
    }
    setLoading(true);
    const formData = new FormData();
    formData.append("file", fileData);
    chapterApis.importChapter({ contentId: location.query.id, courseSemester: location.query.sm ?? 1 }, formData).then((res: any) => {
      if (res.status === 200) {
        message.success(t("导入成功"));
      } else {
        message.error(t("导入失败"));
      }
    }).finally(() => {
      handleClose();
      setLoading(false);
    });
  };
  const handleClose = () => {
    setFileData(null);
    onClose();
  };
  const handleDelete = () => {
    setFileData(null);
  };
  const handleDownload = (e: any) => {
    e.stopPropagation();
    chapterApis.downloadChapterModal().then((res) => {
      const blobURL = window.URL.createObjectURL(res);
      // 创建a标签，用于跳转至下载链接
      const tempLink = document.createElement('a');
      tempLink.style.display = 'none';
      tempLink.href = blobURL;
      tempLink.setAttribute('download', '导入模板.docx');
      // 兼容：某些浏览器不支持HTML5的download属性
      if (typeof tempLink.download === 'undefined') {
        tempLink.setAttribute('target', '_blank');
      }
      // 挂载a标签
      document.body.appendChild(tempLink);
      tempLink.click();
      document.body.removeChild(tempLink);
      // 释放blob URL地址
      window.URL.revokeObjectURL(blobURL);
    });
  };
  return <Modal title={t("章节导入")} visible={visible} footer={null} onCancel={handleClose}>
    <div className="import-chapter">
      {
        fileData?.name ? <div className='file-name'>{fileData?.name}<DeleteOutlined onClick={handleDelete} /></div> : <Dragger {...props}>
          <p className="ant-upload-drag-icon">
            <PlusIcon />
          </p>
          <p className="ant-upload-text">{t("将文件拖拽至此处，或")}<a>{t("点击上传")}</a></p>
          {/* <p className="ant-upload-text">上传成功的文件可在个人资源-课程上传资源文件夹里找到</p> */}
          <a className='download-text' onClick={handleDownload}>{t("下载导入模板")}</a>
        </Dragger>}

      <div className="btn-group">
        <Button type="primary" loading={loading} onClick={handleImport}>{loading ? t("导入中") : t("开始导入")}</Button>
      </div>
    </div>

  </Modal>;
};

export default WordImportModal;