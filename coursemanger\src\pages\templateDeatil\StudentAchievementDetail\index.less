.student-achievement-container {
  .back-wrp {
    border-bottom: 1px solid #e3e3e3;
    // padding: 6px 0;

    button {
      font-size: 16px;
    }
  }
  .detail_content {
    margin: -15px;
    padding: 15px;
    // background: rgb(248, 250, 251);
  }
  .top_content {
    margin-bottom: 10px;
    margin-top: 10px;
    margin-left: 20px;
    font-size: 20px;
    font-weight: bold;
    .ant-select-selection-item {
      font-size: 20px;
      font-weight: bold;
    }
  }
  .main_content {
    background-color: rgb(220, 238, 252);
    padding: 15px;
    border-radius: 5px;
    .header_content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: white;
      border-radius: 7px;
      padding: 15px;
      .block {
        flex-basis: 15%;
        height: 90px;
        border-radius: 5px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .student_info {
          height: 100%;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0px 10px;
          .avatar {
            flex-basis: 45%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .detail {
            flex-basis: 55%;
            .name {
              font-weight: 600;
              font-size: 16px;
            }
            .code {
              color: #808080;
            }
            .college {
              color: #696969;
            }
          }
        }
        .title {
          font-size: 15px;
          img {
            margin-top: 3px;
            margin-right: 5px;
          }
        }
        .dept {
          span {
            font-size: 20px;
            font-weight: 700;
          }
        }
      }
      .active_bg {
        background-color: rgb(220, 238, 252);
        min-width: 264px;
      }
    }
    .radar {
      margin-top: 15px;
      background-color: white;
      border-radius: 5px;
      padding: 15px;
      padding-bottom: 25px;
      display: flex;
      justify-content: space-between;
      .custom-tooltip {
        display: flex;
        .tooltip_left {
          display: flex;
          flex-direction: column;
          .row_left {
            display: flex;
            justify-content: space-between;
            .dot {
              color: rgb(55, 153, 245);
            }
            .name {
              color: rgb(133, 133, 133);
            }
            .value {
              margin: 0 10px;
              font-weight: bold;
              color: rgb(133, 133, 133);
            }
          }
        }
      }
      .top_left {
        width: 45%;
        height: 320px;
        margin-top: 20px;
      }
      .top_right {
        width: 45%;
        height: 320px;
        margin-top: 30px;
        padding-right: 20px;
        overflow: auto;
        &::-webkit-scrollbar {
          display: none; /* 隐藏滚动条 */
        }
        .top_right_container {
          height: auto;
          .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 7px 0px;
            .text {
              .text1 {
                background-color: #e5f5ff;
                color: #76a2d2;
                padding: 1px 6px;
                border-radius: 5px;
                font-size: 15px;
                display: inline-block;
                min-width: 60px;
                text-align: center;
                max-width: 100px;
                text-wrap: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              }
              .active {
                background-color: rgb(87, 155, 255);
                color: white;
              }
              .text2 {
                font-size: 14px;
                margin-left: 15px;
                display: inline-block;
                max-width: 500px;
                text-wrap: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              }
            }
            .end {
              color: #76a2d2;
              font-size: 13px;
              cursor: pointer;
              img {
                width: 7px;
                height: 7px;
                margin: 7px 0px 0px 8px;
              }
            }
          }
          .detail {
            height: 160px;
            overflow: hidden;
            transition: height 0.3s ease;

            .echarts-for-react {
              height: 160px !important;
            }
          }

          .hidden {
            height: 0;
          }
        }
      }
    }
    .center {
      margin-top: 15px;
      background-color: white;
      border-radius: 7px;
      padding: 15px;
      height: 400px;
      .mode_switch_wrapper {
        float: right;
        display: flex;
        align-items: center;

        .mode_switch {
          cursor: pointer;
          font-size: 16px;
          margin-left: 10px;

          .active,
          &:hover {
            color: var(--primary-color);
          }
        }
      }
      .antd_space {
        margin-bottom: 20px;
        .btn {
          position: relative;
          font-size: 15px;
          font-weight: 700;
          cursor: pointer;
        }
        .active {
          color: rgb(85, 155, 242);
          &:after {
            content: '';
            display: block;
            width: 50%;
            height: 2px;
            background-color: rgb(85, 155, 242); /* 设置横线颜色 */
            position: absolute;
            bottom: -10px;
            left: 25%;
          }
        }
      }
    }
    .bottom_table {
      margin-top: 15px;
      background-color: white;
      border-radius: 7px;
      padding: 15px;
      .antd_space {
        margin-bottom: 20px;
        .btn {
          position: relative;
          font-size: 16px;
          font-weight: 700;
        }
        .active {
          color: rgb(85, 155, 242);
          &:after {
            content: '';
            display: block;
            width: 50%;
            height: 2px;
            background-color: rgb(85, 155, 242); /* 设置横线颜色 */
            position: absolute;
            bottom: -10px;
            left: 25%;
          }
        }
      }
    }
  }
}
