import React, { FC, useState, useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'umi';
import './AddCourseForm.less';
import TagInput from './TagInput';
import {
  Form,
  Input,
  Button,
  Select,
  Table,
  Space,
  Upload,
  TreeSelect,
  message,
  Modal,
  Spin,
  Switch,
  Image as Img,
  Tooltip,
  Checkbox,
  Popconfirm,
  Progress,
  Popover,
  Tree
} from
  'antd';
import Icon, {
  ExclamationCircleOutlined,
  LoadingOutlined,
  EditOutlined,
  QuestionCircleOutlined,
  MenuOutlined,
  DeleteOutlined,
  FormOutlined,
  CheckOutlined,
  CloseOutlined,
  LeftOutlined,
  PlusCircleFilled
} from
  '@ant-design/icons';
import { ReactComponent as plus_icon } from "@/assets/imgs/icon/plus_icon.svg";
import { IconFont } from '@/components/iconFont';
import { ColumnsType } from 'antd/es/table';
import { useHistory } from 'react-router-dom';
import ResourceModal from '../ResourceModal';
import {
  saveCourse,
  resourceDetail,
  courseDetail,
  deleteAttchmentApi,
  storageConfig,
  getSinedUrl,
  uploadImport,
  commonUpload,
  reqAllPlate,
  reqPlateAuditor,
  reqAllSubject,
  reqLogs,
  sourceOfSensitivew
} from
  '@/api/addCourse';
import axios from 'axios';
import { getPagingData, publishCourse, publishCourseNew, uploadFile, getCoverList, queryColleges, queryResourceLabel, queryIndustryCategoryList } from '@/api/course';
import { getTreebylevel } from '@/api/addCourse';
import EditFileInfoModal from '@/components/AddCourseForm/editFileInfoModal';
import FilePreviewModal from "@/components/FilePreviewModal";
import MetaEnums from '@/constant/meta';
import perCfg from '@/permission/config';
import chapterApis from '@/api/chapter';
import ResourcePreviewModal from '../ResourcePreviewModal';
import { ReactComponent as video_icon } from '@/assets/imgs/icon/video_icon.svg';
import { ReactComponent as audio_icon } from '@/assets/imgs/icon/audio_icon.svg';
import { ReactComponent as doc_icon } from '@/assets/imgs/icon/doc_icon.svg';
import { ReactComponent as pic_icon } from '@/assets/imgs/icon/pic_icon.svg';
import { ReactComponent as icon_text } from '@/assets/imgs/icon/icon_text.svg';
import CoverModal from "@/components/CoverModal";
import OOSDK from '@/otherStorage/ctyun';
import aliyun from '@/otherStorage/aliyun';
import { SortableContainer, SortableElement, SortableHandle, arrayMove } from 'react-sortable-hoc';
import Header from '@/components/Header';
import Editor from '@/pages/CourseSetting/components/Editor';
import AddTeachModal from '@/pages/CourseSetting/components/AddTeachModal';
import { getLabels, getSensitiveWord, getUuid } from '@/utils';
import useCover from '@/hooks/useCover';
import NPUHeader from '../NPUHeader';
import { CUSTOMER_CDOU, CUSTOMER_NPU, logTypeText } from '@/permission/moduleCfg';
import useLocale from '@/hooks/useLocale';
import AddHyperlinkModal from './AddHyperlinkModal';
import LoggerModal from '../LoggerModal';
import { useRefresh } from '@/hooks/useRefresh';


const { Dragger } = Upload;
const { TextArea } = Input;
const { Option } = Select;
const { confirm } = Modal;
const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;
const DragHandle = SortableHandle(() => <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />);

const SortableItem = (props: {
  item: any;
  onDelete?: () => void;
  editStateChange?: (state: boolean) => void;
  knowledge?: any[];
}) => {
  const { item, onDelete, editStateChange, knowledge } = props;
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [value, setValue] = useState<string>('');
  const { parameterConfigObj } = useSelector<any, any>(state => state.global);
  const [from, setFrom] = useState<string>('');
  const [visible, setVisible] = useState<boolean>(false);
  const { t } = useLocale();
  const [current, setCurrent] = useState<any>(null);
  const [resourceModalVisible, setResourceModalVisible] = useState<boolean>(
    false,
  );
  const inputRef = useRef<any>(null);
  useEffect(() => {
    setValue(item.name);
    setFrom(item.sourceFrom);
    if (item.edit) {
      //添加栏目默认编辑并选中默认标题
      setIsEdit(true);
      setTimeout(() => {
        inputRef.current?.focus();
        inputRef.current?.select();
      }, 0);
    }
  }, [item]);

  useEffect(() => {
    editStateChange && editStateChange(isEdit);
  }, [isEdit]);

  const handleEdit = () => {
    setIsEdit(true);
  };
  const handleConfirm = () => {
    getSensitiveWord(value, '视频名称', () => {
      item.name = value;
      if (item.name_) {
        item.name_ = value;
      }
      if (item.edit) {
        delete item.edit;
      }
      setIsEdit(false);
    });
  };
  const handleCancel = () => {
    setIsEdit(false);
  };
  const handleDelete = () => {
    onDelete && onDelete();
  };
  const handleNote = () => {
    getSensitiveWord(from, '视频来源', () => {
      setVisible(false);
      item.sourceFrom = from;
      message.success('操作成功！');
    });
  };

  // 敏感词信息dom
  const sensitiveMsgListDom = (list: any) => {
    return list.map((ele: any) => <div>
      <span>{ele.source}：</span>
      <span>{ele.sensitiveMsgs.join('、')}（敏感词）</span>
    </div>)
  }

  return (
    <li className="video-list-item drag-visible" style={{ zIndex: 1001 }}>
      <div className="list-content-wrp">
        <div className="item-content">
          {/* <span className="sort-num">{idx + 1}</span> */}
          {isEdit ? (
            <div className="input-container" onClick={e => e.stopPropagation()}>
              <Input
                value={value}
                onChange={(e: any) => {
                  setValue(e.target.value);
                }}
                ref={inputRef}
              />
              <div className="confirm-container">
                <CheckOutlined onClick={handleConfirm} />
                <CloseOutlined onClick={handleCancel} />
              </div>
            </div>
          ) : (
            <span
              className="item-name"
              onClick={() => {
                if (item.type == 'column') {
                  return;
                } else if (item.type == 'hyperlink') {
                  return window.open(item.link);
                }
                let typeSplit = item.type_.split('_');
                setCurrent({
                  id: item.contentId,
                  name: item.name,
                  type: typeSplit[typeSplit.length - 1],
                  knowledge:
                    knowledge?.length &&
                      knowledge?.[0]?.videoId == item.contentId
                      ? knowledge?.[0]
                      : null,
                });
                setResourceModalVisible(true);
              }}
            >
              {item.name}
              {
                item?.sensitive_info && <Tooltip title={sensitiveMsgListDom(JSON.parse(item.sensitive_info))}>
                  <span className={`learn-tag sensitive-words`}>敏感词（{JSON.parse(item.sensitive_info).length}）</span>
                </Tooltip>
              }
            </span>
          )}
        </div>
        <div className="btn-container" onClick={e => e.stopPropagation()}>
          <Tooltip title={t('编辑')}>
            <FormOutlined onClick={handleEdit} />
          </Tooltip>
          {item.creator && (
            <Tooltip title={t('备注来源')}>
              <IconFont type="iconqingdan" onClick={() => setVisible(true)} />
            </Tooltip>
          )}
          <Tooltip title={t('删除')}>
            <Popconfirm
              title={
                t('确定删除该条') +
                t(
                  item.type == 'column'
                    ? t('栏目')
                    : item.type == 'hyperlink'
                      ? t('超链接')
                      : t('内容'),
                ) +
                '？'
              }
              onConfirm={handleDelete}
            >
              <DeleteOutlined />
            </Popconfirm>
          </Tooltip>
        </div>
      </div>
      {parameterConfigObj.zyzx?.includes('voicetext_check_display') && (
        <div className="label-wrp">
          {item.knowledge > 0 && (
            <div className="label-item knowledge">
              {t('知识点(')}
              {item.knowledge})
            </div>
          )}
          {item.labels > 0 && (
            <div className="label-item label">
              {t('标签')}({item.labels})
            </div>
          )}
          {item.haveVideoText ? (
            <Tooltip
              title={
                !item.textCheck ? t('未确认过的语音文本，课程中不会显示') : null
              }
            >
              <div
                className={`label-item ${!item.textCheck ? 'no-check-text' : 'text'
                  }`}
              >
                {t('语音文本')}
                {!item.textCheck && (
                  <span
                    onClick={() => {
                      if (!item.textCheck)
                        window.open(
                          `/rman/#/basic/rmanDetail/${item.contentId_}`,
                        );
                    }}
                  >
                    {t('[待确认]')}
                  </span>
                )}
              </div>
            </Tooltip>
          ) : (
            ''
          )}
        </div>
      )}
      <Modal
        wrapClassName="note-video-from-modal"
        title={t('视频来源')}
        open={visible}
        onCancel={() => {
          setVisible(false);
          setFrom(item.sourceFrom);
        }}
        onOk={handleNote}
      >
        <div className="label">
          {t('填写后学生在观看视频时可看到此视频来源备注')}
        </div>
        <Input
          value={from}
          onChange={(e: any) => setFrom(e.target.value)}
          maxLength={50}
          showCount
        />
      </Modal>
      <ResourcePreviewModal
        modalVisible={resourceModalVisible}
        modalClose={() => setResourceModalVisible(false)}
        resource={current}
      />
    </li>
  );
};


const courseContents = [
  'is_attachment_ownload',
  'attachmentResources',
  'resources'];


const getType = (filename: string) => {
  const fileArr = filename?.split('.');
  return fileArr ? fileArr[fileArr.length - 1].split('?')[0]?.toLocaleLowerCase() : '';
};

const AddCourseForm = () => {
  const { t } = useLocale();
  const history: any = useHistory();
  const dispatch = useDispatch();
  const { handleOperate } = useRefresh();


  const jurisdictionList = useSelector<{ jurisdiction: any; }, any>(
    ({ jurisdiction }) => {
      return jurisdiction.jurisdictionList;
    });

  const { userInfo } = useSelector<any, any>((state) => state.global);
  const { parameterConfig } = useSelector<
    { global: any; },
    { buttonPermission: string[]; parameterConfig: any; permission: any; }>(
      (state) => state.global);
  const PlusIcon = (props: any) => <Icon component={plus_icon} {...props} />;
  const [isAddHyperlink, setIsAddHyperlink] = useState<boolean>(true);
  const [open, setOpen] = useState<boolean>(false);
  const [contentId, setContentId] = useState<string>('');
  const [saveLoading, setSaveLoading] = useState<boolean>(false); //保存按钮的Loading
  const [videoUrl, setVideoUrl] = useState(''); //课程内容的url
  const [attachmentList, setAttachmentList] = useState<Array<any>>([]); //课程附件列表数据
  const [isPublish, setIsPublish] = useState(false); //课程附件列表数据
  const [uploading, setUploading] = useState(false);

  const [modalLoading, setModalLoading] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalTreeData, setModalTreeData] = useState<ResourceModal.treeItem[]>(
    []);

  // 资源预览相关state
  const [entityModalVisible, setEntityModalVisible] = useState(false);
  const [entityPreview, setEntityPreview] = useState<any>(null);

  // 是否是附件
  const [isAttachment, setIsAttachment] = useState(false);

  const [fileInfoModalVisible, setFileInfoModalVisible] = useState(false);
  const [fileInfo, setFileInfo] = useState<MicroCourse.IAttachmentRenameForm>();
  const courseData = useSelector<Models.Store, any>(
    (state) => state.microCourse.courseData);
  const formList = useSelector<Models.Store, Array<any>>(
    (state) => state.microCourse.formList);


  const [teacherList, setTeacherList] = useState<any>([]);
  const [thKeyword, setKeyword] = useState<string>('');
  const [thPage, setPage] = useState<number>(1);
  const [thTotalPage, setTotalPage] = useState<number>(1);
  const [teacherName, setTeacherName] = useState([]);
  const [collegeName, setCollegeName] = useState([]);
  const [classificationName, setClassificationName] = useState([]);
  const [majorName, setMajorName] = useState([]);
  const [subjectName, setSubjectName] = useState<any>([]);
  const subjectObject = useRef<any>({
    primary_classification: '',
    secondary_classification: ''
  });
  const [isSave, setIsSave] = useState<any>(true);

  const [getFile, setFile] = useState<any>([]);
  const [fileObj, setFileObj] = useState<any>({});
  const [previewVisible, setPreviewVisible] = useState<boolean>(false);
  const [coverModalVisible, setCoverModalVisible] = useState<boolean>(false);
  const [showDragger, setShowDragger] = useState<boolean>(false);
  const [collegeList, setCollegeList] = useState<any[]>([]);
  const [industryCategoryList, setIndustryCategoryList] = useState<any[]>([]);
  const [courseResources, setCourseResources] = useState<any[]>([]);
  const [tableLoading, setTableLoading] = useState<boolean>(false);
  const [isFirst, setIsFirst] = useState<boolean>(true); // 用于判断是否首次获取资源，否则会再刚进时调取保存（除非重构）
  const { coverSetting, setCoverSetting, handleChangeCover } = useCover({ id: history.location.search?.replace("?", "") });
  const [nameChangeTag, setNameChangeTag] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [uploadStatus, setUploadStatus] = useState<"success" | "exception" | "active">("active");
  const [uploadVis, setUploadVis] = useState<boolean>(false);
  const [uploadFile, setUploadFile] = useState<any>({});
  // 板块元数据
  const [plateData, setPlateData] = useState<any[]>([]);
  // 当前所有的板块
  const [allPlate, setAllPlate] = useState<any[]>([]);
  // 当前所有的专题
  const [allSubject, setAllSubject] = useState<any[]>([]);
  // 所有的专题
  const [subjectData, setSubjectData] = useState<any[]>([]);
  //添加超链接弹框
  const [hyperlinkModalVisible, setHyperlinkModalVisible] = useState<boolean>(
    false,
  );
  const [hyperlinkData, setHyperlinkData] = useState<any>({});
  const [expandKeys, setExpandKeys] = useState<any[]>([]);
  const [selectColumn, setSelectColumn] = useState<string>('');

  useEffect(() => {
    console.info(history);
    //当页面携带contendId，则获取课程详情数据回填到表单中
    if (history.location.search) {
      getCourseDetail(history.location.search.substr(1));

    }
    // setDefaultCover();
  }, []);
  useEffect(() => {
    if (courseData.contentId) {
      form.setFieldsValue({ name_: courseData.name });
      setClassificationName(courseData.classificationName);
      setTeacherName(courseData.teacher_names);
      setCollegeName(courseData.collegeName);
      setMajorName(courseData.majorName);
      setSubjectName(courseData.subjectName);
    }
  }, [courseData]);

  const flatCourseResource = (arr: any[], res: any = []) => {
    arr.forEach((item: any) => {
      if (item.type != 'column' && item.type != 'hyperlink') {
        res.push(item);
      } else if (item.children && item.children.length) {
        flatCourseResource(item.children, res);
      }
    });
    return res;
  };

  const [tagLabels, setTagLabels] = useState<string[]>([]);
  useEffect(() => {
    if (courseResources.length > 0) {
      // form.setFieldsValue({ resources: courseResources });没有用到resources字段，故去掉
      getLabels(
        flatCourseResource(courseResources)?.map(
          (item: any) => item.contentId_,
        ),
      ).then(labels => {
        setTagLabels(labels?.slice(0, 12));
      });
    }
  }, [courseResources]);

  useEffect(() => {
    setAttachmentList(courseData.attachResources[0]?.fileItems.concat(hyperlinkData.fileGUID ? [] : courseData.resource_link.filter(item => item.type === 1 && item.fileGUID !== hyperlinkData.fileGUID)));
    if ((courseData.attachResources[0]?.fileItems.concat(courseData.resource_link)).length && !isFirst) {
      setIsFirst(false);
      handleSave(true, false, false);
    }
  }, [courseData.attachResources, courseData.resource_link]);

  // 查询所有板块
  const getAllPlate = (entityData: any) => {
    reqAllPlate().then((res) => {
      if (res.status === 200) {
        setAllPlate(res.data);
        // 这里是数据回显
        if (entityData.plate_id?.length) {
          let arr = res.data.filter((item: any) => item.id == entityData.plate_id[0]);
          setAllSubject(JSON.parse(arr[0].thematic_ids)?.filter((item: any) => item.is_enable));
        } else {
          reqAllSubject().then((res) => {
            setSubjectData(res.data?.filter((item: any) => item.is_enable));
            setAllSubject(res.data?.filter((item: any) => item.is_enable));
          });
        }
      }
    });

  };

  const getCollegeList = (data: any) => {
    queryColleges().then((res) => {
      if (res.status === 200) {
        const colleges = res.data?.organization;
        const collegeCodes = colleges.map((item: any) => item.code);
        form.setFieldsValue({ college: data.college.filter((item: any) => collegeCodes.includes(item)) });
        setCollegeList(colleges);
        const data_ = data?.teacherOrg ? JSON.parse(data?.teacherOrg) : {}
        setTeacherData({
          avatarUrl: data.teacherCover,
          name: data_.name ? data_.name : (data.teacher_names.length > 1 ? data.teacher_names?.[0] : ""),
          orgCode: data_?.value ?? []
        });
      }
    });
  };

  const getIndustryCategoryList = () => {
    queryIndustryCategoryList().then((res:any) => {
        setIndustryCategoryList(res?.data);
    });
  };
  const filterByBNotEqualTo = (data, valueToExclude) => {
    return data.map(item => {
      const filteredA = item.fileItems.filter(subItem => subItem.filePath !== valueToExclude);
      return { ...item, fileItems: filteredA };
    });
  }

  const formatCourseChapter: any = (arr: any[]) => {
    return arr.map((item: any) => {
      const { contentId, name, duration, type, sourceFrom, link } = item;
      return {
        contentId,
        name,
        duration,
        type,
        sourceFrom,
        link,
        children: item.children ? formatCourseChapter(item.children) : [],
      };
    });
  };
  // 清理富文本内容，计算真实长度
  const calculateRealLength = (html: string) => {
    // 1. 移除所有 HTML 标签
    const textWithoutTags = html.replace(/<[^>]+>/g, "");
    // 2. 移除所有空白字符（空格、换行、制表符等）
    const cleanText = textWithoutTags.replace(/\s/g, "");
    // 3. 返回长度
    return cleanText.length;
  };
  const handleSave = (
    isFile: boolean,
    isComplete: boolean,
    isPublish: boolean,
    showMsg: boolean = true,
  ) => {
    const formObj = form.getFieldsValue();
    const key = "loading";
    let { name, attachResources, ...entityData } = courseData;
    if (!isFile) {
      const describe_name = formList.find((item: any) => item.fieldName == 'describe')?.showName;
      const txtLength = formObj.describe.length
      console.log('txtLength',txtLength)
      // console.log('什么都没处理',formObj.describe)
      // const reg = new RegExp("<.+?>", "g");
      // if (formObj.describe === "<p><br data-mce-bogus=\"1\"></p>") {
      //   formObj.describe = ""
      // }
      // let txt = formObj.describe.replace(reg, '');
      // console.log('处理前',txt.length)
      // let txtLength = calculateRealLength(formObj.describe)
      // const text = txt.replace(/\n/g, '').replace(/&nbsp;/g, ').replace(/\s/g, '')
      // txt = txt.replace(/\n/g, '');
      // console.log('处理后',txtLength)
      // // txt = txt.replace(/&nbsp;/g, '');
      // // txt = txt.replace(/\s/g, '');
      // const text = new DOMParser()
      // const textVal = text.parseFromString(formObj.describe,"text/html")
      // const textContent = formObj.describe.textContent;
      // console.log(textVal,textContent)
      // const wordCount = textContent.trim().split(/\s+/).filter(Boolean).length;

      if (formObj.describe && txtLength > 30000) {
        message.error(`${describe_name}${t("文本长度不能超过30000")}`);
        return;
      }
      let name_obj = formList.filter((item: any) => item.fieldName == 'name_');
      if (name_obj.length > 0) {
        if (!formObj.name_ || formObj.name_ == '') {
          message.error({ content: `${t("请填写")}${name_obj[0].showName}` });
          return;
        } else if (formObj.name_.length > name_obj[0].maxLength) {
          message.error({
            content: `${name_obj[0].showName}${t("不能超过")}${name_obj[0].maxLength}${t("字")}`
          });
          return;
        }
      }
      setSaveLoading(true);
      if (showMsg) {
        if (isPublish) {
          const isReview = parameterConfig?.microcourse_course_release_review === 'true';
          message.loading({ content: `${t("正在")}${isReview ? t("提交至管理员审核") : t("发布课程")}...`, key });
        } else {
          message.loading({ content: t("正在保存课程..."), key });
        }
      }



      entityData.check = isComplete ? 1 : 0;
      entityData.course_level = formObj.course_level;
    }
    // if (!isComplete) {
    for (let key in formObj) {
      if (key === "teacher" || key === "assistant") {
        entityData[key] = formObj[key]?.value ? [formObj[key]?.value] : [];
      } else if (key == "major" || key == "college" || key == "subject" || key == "classification") {
        entityData[key] = formObj[key] ? Array.isArray(formObj[key]) ? formObj[key] : [formObj[key]] : [];
      } else {
        entityData[key] = formObj[key] ?? null;
      }
    }
    const hasTeacher = formList.filter((item: any) => item.fieldName == 'teacher').length > 0;
    if (!hasTeacher) {
      const value = entityData.teacher?.value || entityData.teacher?.[0]?.value;
      entityData.teacher = value ? [value] : [];
      entityData.teacher_names = Array.isArray(formObj.teacher_names) ? formObj.teacher_names : [formObj.teacher_names];
    } else {
      const value = Array.isArray(formObj.teacher?.label) ? formObj.teacher?.label?.[0] : formObj.teacher?.label;
      entityData.teacher_names = value ? Array.isArray(value) ? value : [value] : [];
    }
    if (teacherData?.name) {
      entityData.teacher_names = [teacherData.name];
    }
    if (teacherData?.college) {
      entityData.teacherOrg = JSON.stringify({ value: teacherData.orgCode, name: teacherData.name, college: teacherData.college });
    }
    if (teacherData?.avatarUrl) {
      entityData.teacherCover = teacherData.avatarUrl;
    }
    entityData.collegeName = Array.isArray(collegeName) ?
      collegeName :
      [collegeName];
    entityData.majorName = Array.isArray(majorName) ? majorName : [majorName];
    entityData.subjectName = Array.isArray(subjectName) ?
      subjectName :
      [subjectName];
    entityData.classificationName = Array.isArray(classificationName) ?
      classificationName :
      [classificationName];
    let arr = allPlate.filter((item: any) => item.name == entityData.plate);
    if (arr.length > 0) {
      entityData.plate_id = arr.map((item) => item.id);
      entityData.plate = Array.isArray(entityData.plate) ?
        entityData.plate :
        [entityData.plate];
    }
    entityData.subject = Array.isArray(entityData.subject) ?
      entityData.subject :
      [entityData.subject];
    entityData.is_attachment_ownload = entityData.is_attachment_ownload ? 1 : 0;

    //使用course_chapter储存树结构数据，courseResources储存资源数据
    entityData.course_chapter = JSON.stringify(
      formatCourseChapter(courseResources),
    );
    const onlyResourses: any[] = [];
    //过滤出树结构的资源数据
    const getResource = (data: any[], arr: any[]) => {
      data.map((item: any) => {
        if (item.type_) {
          arr.push(item);
        }
        if (item.children && item.children.length) {
          getResource(item.children, arr);
        }
      });
    };
    getResource(courseResources, onlyResourses);
    entityData.courseResources = onlyResourses;

    if (entityData.courseResources?.length > 0) {
      entityData.videoDuration =
        entityData.courseResources[0].duration?.toString() || '';
    }
    //省平台
    // if(['3','4'].includes(homePageConfig.banner_plate_type)){
    entityData.primary_classification = subjectObject.current.primary_classification;
    entityData.secondary_classification = subjectObject.current.secondary_classification;
    // }else{
    //   entityData.primary_classification = subjectObject.current.primary_classification
    // }
    entityData.createUser = courseData.createUser;
    entityData.attachmentResources = entityData.attachmentResources?.filter(item => item.filePath !== '超链接') || []
    let param = {
      contentId: courseData.contentId || contentId,
      name: formObj.name_,
      fileGroups: filterByBNotEqualTo(attachResources, '超链接'),
      entityData
    };
    return getSensitiveWord(formObj.name_ + formObj.describe + (formObj.tag?.join() ?? ""), "课程名称或简介", () => {
      return new Promise((resolve) => {
        if (nameChangeTag) {
          Modal.confirm({
            content: t("检测到课程名已改变，是否按当前课程名更新课程封面？"),
            onOk() {
              const setting = {
                ...coverSetting,
                courseName: formObj.name_
              };
              handleChangeCover(setting).then((res) => {
                if (res) {
                  param.entityData.cover = res;
                  dispatch({
                    type: 'microCourse/updateCourseProps',
                    payload: {
                      cover: res
                    }
                  });
                  form.setFieldsValue({
                    cover: res
                  });
                  resolve(param);
                } else {
                  throw new Error();
                }
              }).catch(() => {
                resolve(false);
                setSaveLoading(false);
                setTableLoading(false);
                if (!isFile && !isPublish) {
                  message.error({
                    content: t("课程保存失败"),
                    key,
                    duration: 2
                  });
                }
              });
            },
            onCancel() {
              resolve(param);
            }
          });
        } else {
          resolve(param);
        }
      }).then((data: any) => {
        if (!data) return;
        return saveCourse(data).then((res) => {
          if (res.message == 'OK') {
            setSaveLoading(false);
            if (!isPublish) {
              dispatch({
                type: 'microCourse/updateState',
                payload: {
                  activeKey: '1'
                }
              });
              dispatch({
                type: 'microCourse/updateSelectProps',
                payload: {
                  publishStatus: 0
                }
              });
              if (!isFile && showMsg) {
                message.success({
                  content: t("课程保存成功！"),
                  key,
                  duration: 2
                });
                clearForm();
                // localStorage.setItem('refresh', JSON.stringify(true));
                window.onbeforeunload = null;
                // setTimeout(() => {
                //   window.close();
                //   // window.open("/learn/workbench/#/course");
                // }, 2000);
              } else {
                setContentId(res.data);
              }
            } else {
              return res.data;
            }
          } else {
            setSaveLoading(false);
            setTableLoading(false);
            if (!isFile && !isPublish) {
              message.error({
                content: t("课程保存失败"),
                key,
                duration: 2
              });
            }
          }
          // throw new Error();
        });
      }).
        finally(() => {
          setTableLoading(false);
        });
    }, () => {
      setSaveLoading(false);
      setTableLoading(false);
      return Promise.reject("error");
    });

  };

  useEffect(() => {
    getTeacherList();
  }, [thKeyword, thPage]);
  const getTeacherList = () => {
    getPagingData(
      `sourceTpye=0&school=EDU&pageIndex=${thPage}&pageSize=30${thKeyword ? '&keyWord=' + thKeyword : ''
      }`).
      then((res) => {
        if (res && (res.errorCode === 'success' || res.errorCode === '200')) {
          setTotalPage(res.extendMessage.pageTotal);
          if (thPage === 1) {
            setTeacherList(res.extendMessage.results);
          } else {
            setTeacherList([...teacherList, ...res.extendMessage.results]);
          }
        }
      });
  };
  const handleSearch = (value: string) => {
    setPage(1);
    setKeyword(value);
  };
  const handleChange = (value: any, key: string) => {
    let newValue = value?.value ?? value;
    if (!newValue || newValue.length) {
      setPage(1);
      setKeyword('');
      if (key === "teacher") {
        setTeacherName(
          teacherList.filter((t: any) => t.fieldCode === newValue)[0]?.fieldValue);

      }
    }
  };
  const onSelectChange = (value: string, label: any, flag: any) => {
    console.log(value, label, flag);
    const label_: any = Array.isArray(label) ? label : [label]; //避免有些是单选的
    if (flag === 'classification') {
      //classification
      setClassificationName(label_.map((item: any) => item.children));
    } else if (flag === 'college') {
      setCollegeName(label_.map((item: any) => item.children));
    } else if (flag === 'subject') {
      //是对象 单独处理
      setSubjectName(label_.map((item: any) => item.children));
      subjectObject.current.primary_classification = label_[0].children;
    } else if (flag === 'plate') {
      let thisplate = allPlate.filter((item: any) => item.name == value);
      if (thisplate.length > 0) {
        setAllSubject(JSON.parse(thisplate[0].thematic_ids));
      } else {
        setAllSubject(subjectData);
      }
      // 重置
      form.setFieldsValue({ classification: [] });
    }
  };
  const handleScroll = (e: any) => {
    const { target } = e;
    const total = target.scrollTop + target.offsetHeight;
    const scrollHeight = target.scrollHeight;
    if (
      total >= scrollHeight - 1 &&
      total < scrollHeight + 1 &&
      thPage < thTotalPage) {
      setPage(thPage + 1);
    }
  };

  const getResourceLabel = (ids: string | string[] | any[]) => {
    let idL;
    if (typeof ids === 'string') {
      idL = [ids];
    } else if (typeof ids[0] === 'string') {
      idL = ids;
    } else {
      //树结构，递归出所有资源contentId
      idL = flatCourseResource(ids).map((el: any) => el.contentId);
    }
    return queryResourceLabel(idL).then((res: any) => {
      if (res.status === 200) {
        if (typeof ids === "string" || typeof ids?.[0] === "string") {
          return res.data;
        }
        if (typeof ids[0] !== 'string') {
          //回填数据到courseResources
          let data = res.data.map((el: any, index: number) => ({
            ...el,
            contentId: idL[index],
          }));
          const fillLabelData: any = (arr: any[], confArr: any) => {
            return arr.map((el: any) => {
              let curr = confArr.find(
                (item: any) => item.contentId === el.contentId,
              );
              if (curr) {
                return {
                  ...el,
                  children:
                    el.children && el.children.length
                      ? fillLabelData(el.children, confArr)
                      : [],
                  ...curr,
                };
              } else {
                return {
                  ...el,
                  children:
                    el.children && el.children.length
                      ? fillLabelData(el.children, confArr)
                      : [],
                };
              }
            });
          };
          const temp = fillLabelData(ids, data);
          setCourseResources(temp);
        }
      }
    });
  };

  //获取课程详情 回填数据
  const getCourseDetail = (id: string | any) => {
    courseDetail(id).
      then((res) => {
        if (res.message === 'OK') {
          let data = res.data;
          let { contentId, name, entityData, attachResources, createUser } = data;
          subjectObject.current = {
            primary_classification: entityData.primary_classification,
            secondary_classification: entityData.secondary_classification
          };
          let courseResources_: any[] = [];
          if (entityData.course_chapter) {
            try {
              courseResources_ = JSON.parse(entityData.course_chapter);
              if (entityData.courseResources?.length) {
                //将资源完整数据回填树
                const fillResources = (arr1: any, arr2: any) => {
                  return arr1.map((item1: any) => {
                    let children = [];
                    if (item1.children?.length) {
                      children = fillResources(
                        item1.children,
                        entityData.courseResources,
                      );
                    }
                    let resource = arr2.find(
                      (item2: any) => item2.contentId === item1.contentId,
                    );
                    if (resource) {
                      return {
                        ...item1,
                        children,
                        ...resource,
                      };
                    } else {
                      return {
                        ...item1,
                        children,
                      };
                    }
                  });
                };
                courseResources_ = fillResources(
                  courseResources_,
                  entityData.courseResources,
                );
              }
            } catch (err) {
              console.error(err);
            }
          } else if (entityData.courseResources) {
            courseResources_ = entityData.courseResources;
          }
          setCourseResources(courseResources_);
          getResourceLabel(courseResources_);
          //默认展开所有栏目节点
          setExpandKeys(courseResources_.map((el: any) => el.contentId));
          const temp = {
            ...entityData,
            resource_link: entityData.resource_link?.map((item: any) => JSON.parse(item)) || [],
            college: entityData?.college,
            teacher_names: Array.isArray(entityData?.teacher_names) ? entityData?.teacher_names?.join("") : entityData?.teacher_names,
            major: entityData?.major,
            classification: entityData?.classification,
            // classificationName: entityData?.classificationName,
            // plate: entityData?.plate,
            // plate_id: entityData?.plate_id,
            subject:
              entityData?.subject || [],
            teacher: entityData?.teacher ? { value: entityData?.teacher[0], label: entityData?.teacher_names?.[1] || entityData?.teacher_names?.[0] } : [],
            assistant:
              entityData?.assistant?.map((item: any, index: number) => ({
                value: item,
                label: entityData?.assistant_name[index]
              }))[0] || [],
            is_attachment_ownload:
              entityData?.is_attachment_ownload === 1 ? true : false,
            createUser,
            contentId,
            name,
            createDate: data.createDate,
            attachResources,
            knowledgeResources: entityData.knowledgeResources
          };
          setClassificationName(entityData?.classificationName || []);
          form.setFieldsValue(temp);
          // 查询板块
          getAllPlate(entityData);
          dispatch({
            type: 'microCourse/updateState',
            payload: {
              courseData: temp
            }
          });
          return entityData;
        } else {
          message.error('课程详情查询失败！');
        }
      }).then((data: any) => {
        if (data) {
          getCollegeList(data);
          getIndustryCategoryList();
        }

      }).
      catch((error) => {
        console.error(error);
        message.error('课程详情查询失败！');
      });
  };

  //表单必填验证
  const validateMessages = {
    required: '请填写${label}',
  };

  //上传封面
  const onCoverChange = (file: any) => {
    const formData = new FormData();
    formData.append("file", file, file.name);
    uploadFile(formData).then((res: any) => {
      if (res.message === 'OK') {
        try {
          dispatch({
            type: 'microCourse/updateCourseProps',
            payload: {
              cover: res.data.httpPath
            }
          });
          form.setFieldsValue({
            cover: res.data.httpPath
          });
          setCoverModalVisible(false);
          message.success('课程封面上传成功！');
        } catch (error) {
          message.error('课程封面上传失败！');
        }
      } else {
        message.error(res.message);
      }
    }).catch(() => {
      message.error('课程封面上传失败！');
    });

  };
  //专业TreeSelect的change函数
  const onProfessionChange = (value: any, label: any, extra: any, fieldName: any) => {
    console.log(value, label, extra, fieldName);
    if (fieldName === 'subject') {
      setSubjectName(label);
      const triggerNode = extra.triggerNode;
      if (triggerNode) {
        const parentName = triggerNode?.props?.parentName;
        subjectObject.current = {
          primary_classification: parentName || triggerNode.props.title,
          secondary_classification: parentName ? triggerNode.props.title : ''
        };
      } else {
        subjectObject.current = {
          primary_classification: "",
          secondary_classification: ""
        };
      }


      // return;
    } else {
      setMajorName(label);
    }

    // let major = getAllChildren(extra.allCheckedNodes);
    // dispatch({
    //   type: 'microCourse/updateCourseProps',
    //   payload: {
    //     major: major,
    //   },
    // });
    // dispatch({
    //   type: 'microCourse/updateState',
    //   payload: {
    //     courseData: {
    //       profession: profession,
    //     },
    //   },
    // });
  };

  //获取所有的子节点
  // const getAllChildren = (parent: Array<any>) => {
  //   // let childrenArr: ConcatArray<any> = []
  //   let arr: any = [];
  //   parent.map(item => {
  //     if (item.children.length > 0) {
  //       arr.push(...getAllChildren(item.children));
  //     } else {
  //       arr.push(item.node.props.value);
  //     }
  //   });
  //   return arr;
  // };

  const [form] = Form.useForm();

  //清空表单 重置redux中的courseData
  const clearForm = () => {
    // 清空
    dispatch({
      type: 'microCourse/initCourse'
    });
  };


  // 发布 发布函数
  const publish = async () => {
    const key = 'loading';
    handleSave(false, true, true)?.then((data) => {
      setSaveLoading(true);
      // setTimeout(() => {
      const isReview = parameterConfig?.microcourse_course_release_review === 'true';
      const isSuper = userInfo.roles?.map((item: any) => item.roleCode)?.includes('r_sys_manager') || //是否是系统管理员
        userInfo.roles?.map((item: any) => item.roleCode)?.includes('r_course_manager') || //是否是课程管理员
        userInfo.roles?.map((item: any) => item.roleCode)?.includes('r_second_manager') || //第二权限
        userInfo.roles?.map((item: any) => item.roleCode)?.includes('admin_S1'); // admin
      let { ...entityData } = courseData;
      // 发布
      publishCourseNew([data], 1, {
        "classification": entityData.classification || [],
        "plate": entityData.plate || [],
        "plate_id": entityData.plate_id || []
      }).then((response) => {
        if (response.data.message === 'OK') {
          setSaveLoading(false);
          const text = isReview ? `${isSuper ? t("提交成功，您是管理员，已为您自动完成课程发布审核") : t("课程已提交至管理员审核")}` : t("课程发布成功");
          message.success({
            content: `${text}${t("，页面即将关闭！")}`,
            key,
            duration: 2
          });
          dispatch({
            type: 'microCourse/updateState',
            payload: {
              activeKey: '2'
            }
          });
          dispatch({
            type: 'microCourse/updateSelectProps',
            payload: {
              publishStatus: 1
            }
          });
          clearForm();
          // localStorage.setItem('refresh', JSON.stringify(true));
          window.onbeforeunload = null;
          setTimeout(() => {
            window.close();
          }, 2000);
          handleOperate();
        } else {
          message.error(response.data.message);
          throw new Error();
        }
      });
      // }, 500);
    }).catch(() => {
      setSaveLoading(false);
      message.error({
        content: t("课程保存失败"),
        key,
        duration: 2
      });
    }).finally(() => {
      setSaveLoading(false);
    });
  };

  useEffect(() => {
    let isClosed = window.localStorage.getItem('isClosed');
    if (isClosed === "true") {
      window.localStorage.removeItem('isClosed');
    }
  }, []);


  const showPublishConfirm = async () => {
    if (parameterConfig?.microcourse_course_special_review === 'true') {
      let content: any = '';
      const formObj = form.getFieldsValue();
      // if(!formObj.plate || formObj.plate.length == ''){
      //   message.info('请先选择板块！');
      //   return;
      // }
      if (formObj.classification.length == 0) {
        message.info('请先选择专题！');
        return;
      }
      let arr = allPlate.filter((item: any) => item.name == formObj.plate);
      let plate = Array.isArray(formObj.plate) ? formObj.plate : formObj.plate ? [formObj.plate] : [];
      reqPlateAuditor({
        "classification": formObj.classification || [],
        "plate": plate,
        "plate_id": arr.map((item: any) => item.id)
      }).then((res: any) => {
        if (res.status == 200) {
          content =
            <div>
              <p>{t("您选择了")}<span style={{ fontWeight: 'bold' }}>{res.data.plate.join(',')}</span>{res.data.plate.length > 0 ? t("板块") : ''}
                <span style={{ fontWeight: 'bold' }}>{res.data.classificationName.join(',')}</span>{t("专题，将提交给管理员")}
                <span style={{ fontWeight: 'bold' }}>{res.data.courseCheck_userName.join(',')}</span>{t("进行审核发布，是否确认发布？")}</p>
            </div>;

          confirm({
            title: t("提示"),
            icon: <ExclamationCircleOutlined />,
            content: content,
            okText: t("是"),
            okType: 'danger',
            cancelText: t("否"),
            onOk() {
              publish();
            },
            onCancel() {
              buttonStatus(false);
            }
          });
        } else {
          message.error('获取审核人失败！');
        }
      });
    } else {
      const sensitiviWords = Array.isArray(courseResources) ? courseResources.filter((item: any) => Number(item.sensitivity_label) === 1) : [];
      const noticeSensitivityDom = sensitiviWords.length > 0 ? <div>{t("课程中包含敏感词，请谨慎发布！")}</div> : '';
      const showPulishDom = parameterConfig?.microcourse_course_release_review === 'true' ?
        <div>
          {noticeSensitivityDom}
          {t("发布后将提交至管理员进行审核发布，是否确认提交？")}
        </div> : <div>{noticeSensitivityDom}{t("确认发布该课程！")}</div>;

      confirm({
        title: t("提示"),
        icon: <ExclamationCircleOutlined />,
        content: showPulishDom,
        okText: t("是"),
        okType: 'danger',
        cancelText: t("否"),
        onOk() {
          publish();
        },
        onCancel() {
          buttonStatus(false);
        }
      });
    }


  };

  //对象转数组
  const objToArr = (obj: any, type: string) => {
    let arr = [];
    for (const key in obj) {
      if (type === 'classification') {
        arr.push({
          label: obj[key].split(',')[0],
          value: key
        });
      } else {
        arr.push({
          label: obj[key],
          value: key
        });
      }
    }
    return arr;
  };

  // 遍历目录树 为了把父亲节点的name带上
  const forTree = (tree: any, parent?: any, isMultiSelect?: boolean) => {
    return tree.map((item: any) => {
      return {
        key: item.categoryCode,
        title: item.categoryName,
        parentName: parent ? parent.categoryName : '',
        value: item.categoryCode,
        disabled: isMultiSelect && item.categoryType !== "major",
        children: item.children ? forTree(item.children, item, isMultiSelect) : []
      };
    });
  };

  //显示Modal
  const showModal = (type: string) => {
    setModalLoading(true);
    const getTreeData = () => {
      getTreebylevel().then((res) => {
        if (res && res.success) {
          let treeData = forTree(res.data, []);
          setModalTreeData(treeData);
          setTimeout(() => {
            setModalLoading(false);
            setModalVisible(true);
          }, 100);
        } else {
          console.error(res);
        }
      });
    };
    // 遍历目录树
    const forTree = (tree: any, parentsKeys: string[]) => {
      return tree.map((item: any) => {
        return {
          key: item.id,
          parentsKeys,
          title: item.name,
          path: item.path,
          children: item.children ?
            forTree(item.children, [...parentsKeys, item.code]) :
            []
        };
      });
    };

    getTreeData();
    // setResType(type);
  };
  //检查文件大小
  const checkFileSize = (file: any) => {
    if (file.size / 1024 / 1024 > 100) {
      // message.error('100M以上的文件请先上传至资源中心');
      return false;
    }
  };
  const uploadDefault = (info: any) => {
    console.log('阻止upload的默认上传', info);
    return true;
  };
  //上传完成回调
  const completeUpload = (params: any) => {
    uploadImport(
      {
        oosPath: `http://${params.bucket}.${params.endpoint}/${params.key}`,
        fileLength: params.file.size as number,
        courseName: `${courseData?.name}-${courseData.createDate.replace(
          /[\-:\s]/g,
          '',
        )}-${t('微课')}`,
        courseId: courseData.contentId,
      },
      onUploadProgress).
      then((dd) => {
        if (dd && dd.data && dd.success) {
          const name_ = params.file.name;
          const nameArr = name_.split(".");
          const n = nameArr.length > 1 ? nameArr.slice(0, nameArr.length - 1)?.join(".") : name_;
          let attchmentItem = [
            {
              extraData: n,
              fileState: 'ready',
              fileSize: params.file.size,
              filePath: dd.data.httpPath,
              fileLength: 2,
              fileGUID: getUuid(),
              notSynced: true,
              fileNumber: 0,
              resourceId: dd.data.contentId
            }];

          console.log(attchmentItem);

          dispatch({
            type: 'microCourse/updateCourseProps',
            payload: {
              attachResources: [
                {
                  groupType: 'other',
                  status: 'ready',
                  groupName: 'attachmentgroup',
                  fileItems: (courseData.attachResources?.[0]?.fileItems ?? []).concat(
                    attchmentItem)

                }]

            }
          });
          setShowDragger(false);
          setUploadStatus("success");
          setUploadProgress(100);

          form.setFieldsValue({
            attachmentResources: courseData.attachResources[0].fileItems.concat(
              attchmentItem)

          });
          message.success(`${params.file.name} ${t("上传成功")}`);
        } else {
          setUploadStatus("exception");
          message.error(dd?.error?.title || t("上传失败"));
        }
        setUploading(false);
        setUploadVis(false);
      });
  };
  //其他存储
  const otherPlatform = async (params: any) => {
    console.log('otherPlatform', params);
    if (params.product === 'ctyun') {
      const client = OOSDK.init(params); //先初始化
      console.log('client', client);
      const res: any = await OOSDK.putObject({
        Bucket: params.bucket,
        Body: params.file.source,
        Key: params.key
      });
      console.log('无分片上传', res);
      if (res?.ETag) {
        completeUpload(params);
      }
    } else if (params.product === 'aliyun') {
      aliyun.init(params);
      const result = await aliyun.putObject(params);
      if (result.url) {
        console.log('aliyun', result.url);
        completeUpload(params);
      }
    } else if (params.product === 'amazon' || params.product === 'huawei') {
      getSinedUrl({
        bucket: params.bucket,
        objectPath: params.key,
        requestOper: 1,
        queryParams: {},
        product: params.product
      }).then(res => {
        // for (let index = 0; index < lists.length; index++) {
        const { signUrl, actualSignedRequestHeaders } = res?.data
        const reopt = {
          method: 'PUT',
          url: signUrl,
          withCredentials: false,
          headers: actualSignedRequestHeaders || {},
          maxRedirects: 0,
          responseType: 'text',
          data: params.file.originFileObj,
        };
        axios(reopt).then(function (response) {
          if (response.status < 300) {
            completeUpload(params);
          }
        })
      })
    }
  };
  const onUploadProgress = (e: any) => {
    setUploadStatus("active");
    if (e.lengthComputable) {
      const percentComplete = Math.ceil(e.loaded / e.total * 100); // 获取进度
      setUploadProgress(Math.min(percentComplete, 99));
    }
  };

  //附件上传
  const uploadAttachment = async (info: any) => {
    if (info.file.size / 1024 / 1024 > 100) {
      message.error('100M以上的文件请先上传至资源库');
      setUploading(false);
      return false;
    }
    const type = getPreviewType(info.file.name)
    if (!type) {
      message.error("暂不支持该类型文件上传！");
      return false;
    }
    setUploading(true);
    setUploadVis(true);
    setUploadFile(info.file);
    //判断存储方式
    const canUpload: any = await getSensitiveWord(info.file.name, "文件名", () => true, () => false);
    if (!canUpload) {
      setUploading(false);
      return false;
    }
    const ress: any = await storageConfig([{
      fileName: info.file.name,
      fileLength: info.file.size,
      fileType: info.type,
      poolType: window.localStorage.getItem('upform_platform') === 'Lark' ? 'ROLE' : '',
      pathType: 1
    }]);
    if (!ress?.success) {
      message.error('存储初始化失败，请重试');
      return;
    };
    const storage_: any = ress.data[0];
    if (storage_.access_type === 'NAS') {
      const formData = new FormData();
      const file = info.file.originFileObj || info.file;
      formData.append('file', file, file.name);
      const res: any = await commonUpload(
        formData,
        {
          courseName: `${courseData?.name}-${courseData.createDate.replace(
            /[\-:\s]/g,
            '',
          )}-${t('微课')}`,
          courseId: courseData.contentId,
        },
        onUploadProgress,
      );
      if (res.success) {
        const name_ = info.file.name;
        const nameArr = name_.split(".");
        const n = nameArr.length > 1 ? nameArr.slice(0, nameArr.length - 1)?.join(".") : name_;
        let attchmentItem = [
          {
            extraData: n,
            fileState: 'ready',
            fileSize: info.file.size,
            filePath: res.data.httpPath,
            fileLength: 2,
            fileGUID: getUuid(),
            notSynced: true,
            fileNumber: 0,
            resourceId: res.data.contentId
          }];

        console.log(attchmentItem);

        dispatch({
          type: 'microCourse/updateCourseProps',
          payload: {
            attachResources: [
              {
                groupType: 'other',
                status: 'ready',
                groupName: 'attachmentgroup',
                fileItems: (courseData.attachResources?.[0]?.fileItems ?? []).concat(
                  attchmentItem)

              }]

          }
        });
        setUploadStatus("success");
        setUploadProgress(100);
        setShowDragger(false);
        form.setFieldsValue({
          attachmentResources: courseData.attachResources[0].fileItems.concat(
            attchmentItem)
        });
        message.success(`${info.file.name} ${t("上传成功")}`);
      } else {
        setUploadStatus("exception");
        message.success(res?.error?.title || t("上传失败"));
      }
      setUploading(false);
      setUploadVis(false);
    } else if (storage_.access_type === 'OSS') {
      otherPlatform({ ...storage_, file: info.file });
    }
  };


  //获取 课程内容 资源详情


  //删除附件事件
  const deleteAttachment = (text: any, record: any) => {
    setTableLoading(true);
    if (history.location.search && !record.notSynced) {
      deleteAttchmentApi(history.location.search.substr(1), record.fileGUID).
        then((res) => {
          if (res?.data?.message === "OK") {
            dispatch({
              type: 'microCourse/updateCourseProps',
              payload: {
                attachResources: [
                  {
                    groupType: 'other',
                    status: 'ready',
                    groupName: 'attachmentgroup',
                    fileItems: arrRemove(
                      attachmentList,
                      record.fileGUID)

                  }]

              }
            });
            message.success('附件删除成功！');
          } else {
            message.error('附件删除失败');
          }
        }).
        catch((error) => {
          message.error(t("附件删除失败，") + error);
        }).finally(() => {
          setTableLoading(false);
        });
    } else {
      setTableLoading(false);
      dispatch({
        type: 'microCourse/updateCourseProps',
        payload: {
          attachResources: [
            {
              groupType: 'other',
              status: 'ready',
              groupName: 'attachmentgroup',
              fileItems: arrRemove(
                attachmentList,
                record.fileGUID)

            }]

        }
      });
      message.success('附件删除成功！');
    }
  };

  //删除数组中指定元素
  const arrRemove = (arr: Array<any>, val: any) => {
    const newArr = [...arr];
    const index = arr.findIndex((item) => item.fileGUID === val);
    if (index > -1) {
      newArr.splice(index, 1);
    }
    return [...newArr];
  };
  const clickNameHandle = (file: any) => {
    setFileObj(file);
    setPreviewVisible(true);
  };

  interface attachmentResourcesList {
    title: string;
    dataIndex: string;
    align: string;
    key: string;
    width: number;
    render: () => React.ReactElement;
  }

  //表格列数据
  const columns: ColumnsType<attachmentResourcesList> = [
    {
      title: t("资源名"),
      dataIndex: 'extraData',
      align: 'left',
      // width: 200,
      key: 'name',
      ellipsis: true,
      render: (text: string, record: any) => {
        return <span className='filename' onClick={() => { clickNameHandle(record); }} title={text}>{getIconByType(record.filePath)} {text}</span>;
      }
    },
    {
      title: t("资源类型"),
      dataIndex: 'filePath',
      align: 'center',
      width: 100,
      key: 'type',
      render: (text: string) => text === '超链接' ? text : getType(text)
    },
    {
      title: t("大小"),
      dataIndex: 'fileSize',
      align: 'center',
      width: 100,
      key: 'fileSize',
      render: (text: number) => {
        if (!text) {
          return '--'
        }
        if (text / 1024 > 1024) {
          return `${(text / 1024 / 1024).toFixed(1)}M`;
        } else {
          return `${(text / 1024).toFixed(1)}KB`;
        }

      }
    },
    // {
    //   title: '资源来源',
    //   dataIndex: 'filePath',
    //   align: 'center',
    //   width: 100,
    //   key: 'resourceFrom',
    //   render: (text: string) => text.includes("local") ? '本地上传' : '资源库'
    // },
    {
      title: t("操作"),
      dataIndex: 'action',
      align: 'center',
      key: 'action',
      width: 100,
      render: (text: any, record: any) => {
        return (
          <Space size="middle">
            <EditOutlined
              title={t("编辑")}
              onClick={() => {
                if (record.filePath === '超链接') {
                  setHyperlinkModalVisible(true)
                  setIsAddHyperlink(false)
                  setHyperlinkData(record)
                }
                else {
                  setFileInfoModalVisible(true);
                  setFileInfo({
                    fileName: record.extraData,
                    fileguid: record.fileGUID,
                    courseId: history.location.search.substr(1),
                    notSynced: record.notSynced
                  });
                }

              }} />

            <IconFont
              title={t("删除")}
              type="iconshanchu4"
              onClick={() => {
                setHyperlinkData(record.filePath === '超链接' ? record : {})
                deleteAttachment(text, record);
              }} />

          </Space>);

      }
    }];


  const onFinish = (values: any, isPublish: boolean) => {
    if (isPublish) {
      showPublishConfirm();
    } else {
      handleSave(false, true, false);
    }
  };
  const buttonStatus = (bool: boolean) => {
    setIsPublish(bool);
  };

  // 敏感词信息dom
  const sensitiveMsgListDom = (list: any) => {
    return list.map((item: any) => <div>
      <div style={{ fontSize: 16 }}>资源《<span>{item.name}</span>》：</div>
      <div style={{ paddingLeft: 20, margin: '10px 0' }}>
        {
          item.sensitiveInfos?.map((ele: any) => <div>
            <span>({ele.source}) - 包含敏感词：</span>
            <span>{ele.sensitiveMsgs.join('、')}</span>
          </div>)
        }
      </div>
    </div>)
  }

  // 资源铭感词检测
  const checkSensitiveWord = (list: any, callback: (mingans: any[]) => void) => {
    const params = list.map((item: any) => ({ contentId: item.contentId_, name: item.name_, type: item.type_ }));

    // 先调用敏感词资源查询，检测引用资源是否含有敏感词
    sourceOfSensitivew(params).then((res: any) => {
      if (res?.statusCode === 200) {
        // 当前存在敏感词
        if (res.data && res.data?.length > 0) {
          Modal.confirm({
            title: t("引用资源包含敏感词，是否继续上传？"),
            // content: res.data?.map((item: any) => item.name).join('、') + t("包含敏感词，是否继续上传？"),
            content: sensitiveMsgListDom(res.data),
            onOk() {
              const data = list.map((item: any) => {
                const sensitiveWord = res.data.find((ele: any) => ele.resourceId === item.contentId_)?.sensitiveInfos;
                return {
                  ...item,
                  sensitive_info: sensitiveWord ? JSON.stringify(sensitiveWord) : '',
                  sensitivity_label: sensitiveWord ? 1 : 0,
                }
              })
              form.setFieldsValue({
                attachmentResources: courseData.attachResources[0].fileItems.concat(
                  attachmentList)

              });
              callback(data);
            },
            onCancel() { }
          });
        } else {
          // 不存在敏感词敏感词
          callback(list);
        }
      };
    });
  }

  const resourceModalConfirm = (resource: any[]) => {
    checkSensitiveWord(resource, (mingans: any[]) => { handleSave(mingans) });

    function handleSave(mingans: any[]) {

      if (isAttachment) {
        if (mingans.length > 0) {
          const promise: any[] = [];
          mingans.forEach((item: any) => {
            promise.push(resourceDetail(item.contentId, 4));
          });
          Promise.all(promise).then((res) => {
            const files = res.filter((r) => r.success && r.data).map((r) => r.data);
            const attachmentList = getAttachmentList(files, mingans.map((item: any) => item.contentId));

            dispatch({
              type: 'microCourse/updateCourseProps',
              payload: {
                attachResources: [
                  {
                    groupType: 'other',
                    status: 'ready',
                    groupName: 'attachmentgroup',
                    fileItems: courseData.attachResources[0].fileItems.concat(
                      attachmentList)

                  }]

              }
            });
            form.setFieldsValue({
              attachmentResources: courseData.attachResources[0].fileItems.concat(
                attachmentList)

            });
          });
        }
      } else {
        // dispatch({
        //   type: 'microCourse/updateCourseProps',
        //   payload: {
        //     courseResources: mingans,
        //   },
        // });
        getResourceLabel(mingans.map((item: any) => item.contentId)).then((data: any) => {
          const newResources = mingans.map((item: any, index: number) => ({ ...item, fileGUID: getUuid(), ...(data[index] ?? {}), nowKey: Date.now() }));
          setCourseResources([...courseResources, ...newResources]);
        });

      }
      setModalVisible(false);
    }

  };
  // 获取附件列表
  const getAttachmentList = (
    files: MicroCourse.IFileEntity[],
    contentIds: string[])
    : MicroCourse.IAttachmentFile[] =>
    files.map((item, index) => {
      const fileInfo = getFileItem(item);
      return {
        extraData: item.entityName,
        fileState: 'ready',
        fileSize: fileInfo.fileSize,
        filePath: fileInfo.displayPath,
        fileLength: 0,
        fileGUID: getUuid(),
        notSynced: true,
        resourceId: contentIds?.[index]
      };
    });

  // 获取文件信息
  const getFileItem = (
    data: any)
    : MicroCourse.IFileItem => {
    const { fileGroups } = data;
    const preIndex = fileGroups.findIndex(
      (file: any) => file.typeCode === 'previewfile');

    const souIndex = fileGroups.findIndex(
      (file: any) => file.typeCode === 'sourcefile');

    if (data.type.includes("document")) {
      return fileGroups[souIndex].fileItems[0];
    }
    return preIndex > -1 ?
      fileGroups[preIndex].fileItems[0] :
      fileGroups[souIndex].fileItems[0];
  };

  const linkModalConfirm = (link: any) => {
    if (isAddHyperlink) {
      if (!isAttachment) {
        setCourseResources([...courseResources, { name: link, link, type: 0, fileGUID: getUuid(), filePath: '超链接', nowKey: Date.now() }]);
      }
      else {
        let attchmentItem = [
          {
            extraData: link,
            link,
            type: 1,
            fileState: 'ready',
            fileSize: '',
            fileGUID: getUuid(),
            filePath: '超链接',
            notSynced: true,
            fileLength: 2,
          }];

        dispatch({
          type: 'microCourse/updateCourseProps',
          payload: {
            attachResources: [
              {
                groupType: 'other',
                status: 'ready',
                groupName: 'attachmentgroup',
                fileItems: attachmentList.concat(
                  attchmentItem)

              }]
          }
        });
        form.setFieldsValue({
          attachmentResources: attachmentList.concat(
            attchmentItem)
        });
      }
    }
    else {
      const newList = [...attachmentList];
      const index = attachmentList.findIndex((item) => item.fileGUID === hyperlinkData.fileGUID);
      if (index > -1) {
        newList[index] = {
          ...newList[index],
          extraData: link
        };
      }
      dispatch({
        type: 'microCourse/updateCourseProps',
        payload: {
          attachResources: [
            {
              groupType: 'other',
              status: 'ready',
              groupName: 'attachmentgroup',
              fileItems: newList
            }]

        }
      });
      form.setFieldsValue({
        attachmentResources: newList
      });
    }
    setHyperlinkModalVisible(false);
  }
  const handleEditFileOk = (fileName: string, fileId: string) => {
    getSensitiveWord(fileName, "资源名称", () => {
      setFileInfoModalVisible(false);
      // 更新表格数据
      const newList = [...attachmentList];

      const index = attachmentList.findIndex((item) => item.fileGUID === fileId);
      if (index > -1) {
        newList[index] = {
          ...newList[index],
          extraData: fileName
        };
      }
      dispatch({
        type: 'microCourse/updateCourseProps',
        payload: {
          attachResources: [
            {
              groupType: 'other',
              status: 'ready',
              groupName: 'attachmentgroup',
              fileItems: newList
            }]

        }
      });
      form.setFieldsValue({
        attachmentResources: newList
      });
    });
  };
  const onSortEnd = ({ oldIndex, newIndex }: { oldIndex: number; newIndex: number; }) => {
    setCourseResources(arrayMove(courseResources, oldIndex, newIndex));
  };

  const [editTeacherVisible, setEditTeacherVisible] = useState<boolean>(false);
  const [teacherData, setTeacherData] = useState<any>(null);
  const handleOKTeacher = (data: any) => {
    setTeacherData(data);
    message.success("设置成功");
  };

  const { fileMap } = useSelector<Models.Store, any>(
    (state) => state.global);

  const getIconByType = (filePath: string) => {
    const type = getPreviewType(filePath);
    const VideoIcon = (props: any) => <Icon component={video_icon} {...props} />;
    const AudioIcon = (props: any) => <Icon component={audio_icon} {...props} />;
    const DocIcon = (props: any) => <Icon component={doc_icon} {...props} />;
    const PicIcon = (props: any) => <Icon component={pic_icon} {...props} />;
    const IconText = (props: any) => <Icon component={icon_text} {...props} />;
    switch (type) {
      case 'video':
        return <VideoIcon />;
      case 'audio':
        return <AudioIcon />;
      case 'picture':
        return <PicIcon />;
      case 'document':
        return <DocIcon />;
      default:
        return <IconText />;
    }

  };

  type tFileType = "video" | "audio" | "picture" | "document";
  const getPreviewType = (filePath: string): tFileType => {
    const fileSuffix = getType(filePath);
    const typeArr = Object.keys(fileMap).filter((item: string) => fileMap[(item as tFileType)].includes(fileSuffix));
    const type = typeArr.length > 0 ? typeArr[0] : '';
    return (type as tFileType);
  };
  const handleCoverConfirm = (images: string, setting: any) => {
    dispatch({
      type: 'microCourse/updateCourseProps',
      payload: {
        cover: images
      }
    });
    form.setFieldsValue({
      cover: images
    });
    setCoverSetting(setting);
    setCoverModalVisible(false);
  };
  const [logOpen, setLogOpen] = useState<boolean>(false);

  const handleLogShow = () => {
    setLogOpen(true);
  };
  useEffect(() => {
    if (parameterConfig?.microcourse_course_release_review === 'true' && history.location.search) {
      getLog(history.location.search.substr(1));
    }
  }, [parameterConfig])
  const [logList, setLogList] = useState<any>([]);

  const getLog = (courseId: string) => {
    reqLogs({ courseId }).then((res: any) => {
      if (res.status === 200) {
        setLogList(res.data?.map((item: any) => ({
          title: `${moment(item.approvalTime).format('YYYY-MM-DD HH:mm:ss') || '-'} ${item.processName ?? ""}`,
          subTitle: item.approvalUserName,
          description: <>
            <div className="review-des">{logTypeText[item.operateType]}</div>
            {item.operateType === 3 && <div className="review-des">驳回理由：{item.rejectReason || "无"}</div>}
          </>
        })) ?? []);
      }
    });
  };
  //操作之后保存一个标志符号
  const handlePageOperate = () => {
    window.localStorage.setItem("close_page", "true")
  }
  //添加超链接回调
  const onHyperlinkModalOk = (params: any) => {
    addCourseResource([params]);
  };

  //添加资源
  const addCourseResource = (newResources: any) => {
    if (
      courseResources.length &&
      courseResources[courseResources.length - 1].type === 'column'
    ) {
      if (selectColumn && courseResources.find((item: any) => item.contentId == selectColumn)) {
        //有选中栏目，新资源添加到选中栏目
        setCourseResources(
          courseResources.map((item: any, index: number) => {
            if (item.contentId == selectColumn) {
              return {
                ...item,
                children: [...item.children, ...newResources],
              };
            } else {
              return item;
            }
          }),
        );
      } else {
        //否则添加到末尾栏目
        setCourseResources([
          ...courseResources.slice(0, courseResources.length - 1),
          {
            ...courseResources[courseResources.length - 1],
            children: [
              ...courseResources[courseResources.length - 1].children,
              ...newResources,
            ],
          },
        ]);
      }
    } else {
      setCourseResources([...courseResources, ...newResources]);
    }
  };

  //添加栏目
  const handleAddColumn = () => {
    const contentId = getUuid();
    if (courseResources.find(el => el.type == 'column')) {
      //存在栏目，在尾部添加新栏目
      setCourseResources([
        ...courseResources,
        {
          contentId,
          name: '新栏目',
          type: 'column',
          children: [],
          edit: true,
        },
      ]);
    } else {
      //将当前添加的资源全放入当前添加栏目
      setCourseResources([
        {
          contentId,
          name: '新栏目',
          type: 'column',
          children: [...courseResources],
          edit: true,
        },
      ]);
    }
  };

  //资源拖拽排序
  const onCourseResourceDrop = (info: any) => {
    console.log(info);
    const dropKey = info.node.contentId;
    const dragKey = info.dragNode.contentId;
    const dropPos = info.node.pos.split('-');
    const dropPosition =
      info.dropPosition - Number(dropPos[dropPos.length - 1]);

    const loop = (
      data: any[],
      contentId: React.Key,
      callback: (node: any, i: number, data: any[]) => void,
    ) => {
      for (let i = 0; i < data.length; i++) {
        if (data[i].contentId === contentId) {
          return callback(data[i], i, data);
        }
        if (data[i].children) {
          loop(data[i].children!, contentId, callback);
        }
      }
    };
    const data = [...courseResources];
    // Find dragObject
    let dragObj: any;
    loop(data, dragKey, (item, index, arr) => {
      arr.splice(index, 1);
      dragObj = item;
    });

    if (!info.dropToGap) {
      // Drop on the content
      loop(data, dropKey, item => {
        item.children = item.children || [];
        // where to insert 示例添加到头部，可以是随意位置
        item.children.unshift(dragObj);
      });
    } else if (
      ((info.node as any).children || []).length > 0 && // Has children
      (info.node as any).expanded && // Is expanded
      dropPosition === 1 // On the bottom gap
    ) {
      loop(data, dropKey, item => {
        item.children = item.children || [];
        // where to insert 示例添加到头部，可以是随意位置
        item.children.unshift(dragObj);
        // in previous version, we use item.children.push(dragObj) to insert the
        // item to the tail of the children
      });
    } else {
      let ar: any[] = [];
      let i: number;
      loop(data, dropKey, (_item, index, arr) => {
        ar = arr;
        i = index;
      });
      if (dropPosition === -1) {
        ar.splice(i!, 0, dragObj!);
      } else {
        ar.splice(i! + 1, 0, dragObj!);
      }
    }

    setCourseResources(data);
  };
  const handleAllowDrop = (info: any) => {
    //只允许
    const { dropNode, dragNode, dropPosition } = info;
    // console.log('dragInfo', dragNode, dropNode, dropPosition);
    // 栏目-只能放在第一层
    if (dragNode.type == 'column') {
      return dropPosition == 1 && dropNode.type === 'column';
    } else if (dropNode.type != 'column') {
      //只能放到同级
      return dropPosition != 0;
    }
    return false;
  };
  const onDragStart = (info: any) => {
    const { node } = info;
    if (node.type == 'column') {
      //收起所有栏目
      setExpandKeys([]);
    }
  };
  const onTreeExpand = (selectedKeys: any, info: any) => {
    if (!info.node.children?.length) return;
    if (expandKeys.includes(info.node?.contentId)) {
      setExpandKeys(
        expandKeys.filter((item: any) => item !== info.node?.contentId),
      );
    } else {
      setExpandKeys([...expandKeys, info.node.contentId]);
    }
  };

  const onCourseResourceDelete = (contentId: string) => {
    let arr = [...courseResources];
    const deleteItem = (arr: any[], contentId: string) => {
      console.log(arr, contentId);
      return arr.filter((item: any) => {
        if (item.children?.length) {
          item.children = deleteItem(item.children, contentId);
        }
        return item.contentId !== contentId;
      });
    };
    setCourseResources(deleteItem(arr, contentId));
  };
  return (
    <div className="add-course-form">
      {parameterConfig.target_customer === CUSTOMER_NPU ? <NPUHeader /> : <Header />}
      <div className='header'>
        <div className="info_box">
          <a onClick={() => {
            const isLeave = window.confirm("你所做的更改可能未保存，确认离开？");
            if (isLeave) {
              window.open(`#/course/microcourse`, "_self");
            }
          }}><LeftOutlined />{t("返回")}</a>
          <span className='title_name' title={courseData?.name}>{courseData?.name}</span>
        </div>
        <div className="top-right-opt">
          <Button
            ghost
            type="primary"
            style={{ marginRight: '30px' }}
            onClick={() => {
              window.open(`/learn/course/micro/${history.location.search.substring(1)}`);
              handleSave(true, false, false, false);
            }}>
            {t("预览")}

          </Button>
          <Button
            ghost
            style={{ marginRight: '30px' }}
            type="primary"
            loading={saveLoading}
            onClick={() => {
              form.submit();
              setIsSave(true);
            }}>
            {t("保存")}

          </Button>
          {jurisdictionList.includes(perCfg.microcourse_publish) && parameterConfig?.microcourse_course_release_review !== 'true' &&
            <Button
              type="primary"
              // shape="round"
              onClick={() => {
                form.submit();
                buttonStatus(true);
                setIsSave(false);
              }}>
              {t("发布")}

            </Button>}

          {parameterConfig?.microcourse_course_release_review === 'true' &&
            <Button
              type="primary"
              // shape="round"
              onClick={() => {
                form.submit();
                buttonStatus(true);
                setIsSave(false);

              }}>
              {t("发布")}

            </Button>}
          {parameterConfig?.microcourse_course_release_review === 'true' && logList.length > 0 && <Button
            style={{ marginLeft: "30px" }}
            type='primary'
            ghost
            onClick={handleLogShow}>
            {t("审核日志")}
          </Button>}
        </div>
      </div>
      <Spin tip={t("资源加载中...")} spinning={modalLoading}>
        <Spin indicator={antIcon} tip={t("上传中...")} spinning={uploading}>
          <Form
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 16 }}
            validateMessages={validateMessages}
            form={form}
            onFinish={(values) => {
              onFinish(values, isPublish);
            }}
            onFinishFailed={() => {
              if (isSave) {
                handleSave(false, false, false);
              }
            }}
            onValuesChange={(values: any) => {
              if (values.name_ != undefined) {
                setNameChangeTag(true);
              }
            }}
            name="addCourseForm"
          // onValuesChange={valChange}
          >
            {/* <div className="basic-information">课程基本信息</div> */}
            <div className='basic-information-content'>
              <div className='basic-information-left'>
                <div className="title-wrp">{t("课程内容")}</div>
                {formList ?
                  formList.map((item, index) => {
                    if (
                      item.controlType == MetaEnums.DataType.resource &&
                      item.fieldName == 'resources') {
                      return (
                        <>
                          <Form.Item
                            key={item.fieldName}
                            name={item.fieldName}
                            label={item.showName}
                            labelCol={{ span: 3 }}
                            wrapperCol={{ span: 18 }}
                            className="form-item-left"
                          // rules={[{ required: item.isMustInput }]}
                          >
                            <div style={{ textAlign: 'left' }}>
                              {courseResources.length > 0 ?
                                <>
                                  <Tree
                                    className="video-list"
                                    expandedKeys={expandKeys}
                                    draggable
                                    blockNode
                                    fieldNames={{
                                      title: 'name',
                                      key: 'contentId',
                                      children: 'children',
                                    }}
                                    titleRender={(node: any) => (
                                      <SortableItem
                                        item={node}
                                        onDelete={() =>
                                          onCourseResourceDelete(
                                            node.contentId,
                                          )
                                        }
                                        editStateChange={(
                                          state: boolean,
                                        ) => { }}
                                        knowledge={
                                          courseData.knowledgeResources
                                        }
                                      />
                                    )}
                                    onDrop={onCourseResourceDrop}
                                    allowDrop={handleAllowDrop}
                                    onDragStart={onDragStart}
                                    onSelect={(selectedKeys: any, info: any) => {
                                      if (info.node.type === 'column') {
                                        setSelectColumn(info.node.contentId);
                                      } else {
                                        setSelectColumn('');
                                      }
                                    }}
                                    onExpand={onTreeExpand}
                                    treeData={courseResources}
                                  />
                                  <div className='add-btn-group'>
                                    <div
                                      className="add-btn"
                                      onClick={() => {
                                        showModal('resources');
                                        setIsAttachment(false);
                                      }}
                                    >
                                      <PlusCircleFilled />
                                      {t('内容')}
                                    </div>
                                    {parameterConfig.target_customer === CUSTOMER_CDOU && <div className="add-btn"
                                      onClick={() => {
                                        setHyperlinkModalVisible(true);
                                        setIsAddHyperlink(true);
                                        setIsAttachment(false);
                                        setHyperlinkData({})
                                      }}>
                                      <PlusCircleFilled />{t("添加超链接")}
                                    </div>}
                                    <div
                                      className="add-btn"
                                      onClick={handleAddColumn}
                                    >
                                      <PlusCircleFilled />
                                      {t('栏目')}
                                    </div>
                                  </div>
                                  <AddHyperlinkModal
                                    data={hyperlinkData}
                                    visible={hyperlinkModalVisible}
                                    onClose={() => {
                                      setHyperlinkModalVisible(false);
                                    }}
                                    onOK={onHyperlinkModalOk}
                                  />
                                </> :

                                <div
                                  className='video-empty'
                                  onClick={() => {
                                    showModal('resources');
                                    setIsAttachment(false);
                                  }}>
                                  <PlusCircleFilled />
                                  <div>
                                    {t("添加内容")}
                                    {parameterConfig.target_customer === CUSTOMER_CDOU && <span onClick={(e) => {
                                      e.stopPropagation();
                                      setHyperlinkModalVisible(true);
                                      setIsAddHyperlink(true)
                                      setIsAttachment(false)
                                    }} className='addLink'>【添加视频超链接】</span>}
                                  </div>
                                </div>}
                            </div>
                          </Form.Item>
                          {formList.find(
                            item =>
                              item.fieldName === 'showWatermark' ||
                              item.fieldName === 'allowed_drag' ||
                              item.fieldName === 'allowed_double_speed',
                          ) && (
                              <Form.Item
                                label={t('视频设置')}
                                labelCol={{ span: 3 }}
                                wrapperCol={{ span: 18 }}
                                className="form-item-left"
                                style={{ marginBottom: 0 }}
                              >
                                <div className="checkbox-container">
                                  {formList.map((item_: any) => {
                                    if (
                                      item_.fieldName === 'showWatermark' ||
                                      item_.fieldName === 'allowed_drag' ||
                                      item_.fieldName === 'allowed_double_speed'
                                    ) {
                                      return (
                                        <Form.Item
                                          key={item_.fieldName}
                                          name={item_.fieldName}
                                          wrapperCol={{ span: 32 }}
                                          valuePropName="checked"
                                          // style={{ width: '50%' }}
                                          rules={[
                                            { required: item_.isMustInput },
                                          ]}
                                        >
                                          <Checkbox defaultChecked={false}>
                                            {item_.showName}
                                            {item_.fieldName !==
                                              'showWatermark' && (
                                                <Tooltip
                                                  title={`${t(
                                                    '勾选则学生在第一次学习完成前，无法',
                                                  )}${item_.fieldName ===
                                                    'allowed_drag'
                                                    ? t(
                                                      '拖拽进度条，同时无法使用知识点列表快速定位至知识点',
                                                    )
                                                    : t('调整倍速')
                                                    }`}
                                                >
                                                  <QuestionCircleOutlined />
                                                </Tooltip>
                                              )}
                                          </Checkbox>
                                        </Form.Item>
                                      );
                                    }
                                  })}
                                </div>
                              </Form.Item>
                            )}
                        </>
                      );
                    } else {
                      if (
                        item.controlType == MetaEnums.DataType.resource &&
                        item.fieldName == 'attachmentResources'
                      ) {
                        return (
                          <div style={{ display: 'flex' }} key={index}>
                            <Form.Item
                              key={item.fieldName}
                              name={item.fieldName}
                              label={item.showName}
                              wrapperCol={{ span: 32 }}
                              style={{ width: '50%' }}
                              rules={[{ required: item.isMustInput }]}>
                              <Popover
                                open={open}
                                onOpenChange={(open: boolean) => {
                                  setOpen(open)
                                }}
                                content={
                                  <>
                                    <div
                                      className='pop-item'
                                      onClick={() => {
                                        showModal('resources');
                                        setIsAttachment(true);
                                        setOpen(false)
                                      }}>
                                      {t("从资源库选择")}

                                    </div>
                                    <div className='pop-item' onClick={() => {
                                      setShowDragger(true)
                                      setOpen(false)
                                    }}>{t("从本地上传")}</div>
                                    <div className='pop-item'
                                      onClick={() => {
                                        setHyperlinkModalVisible(true);
                                        setOpen(false);
                                        setIsAddHyperlink(true)
                                        setIsAttachment(true);
                                      }}>{t("添加超链接")}</div>
                                  </>
                                }
                                getPopupContainer={(e: any) => e.parentElement}
                                placement="bottom"
                                trigger="click"
                              >
                                <Button
                                  type="primary"
                                  icon={<PlusCircleFilled />}
                                  onClick={(e: any) => e.stopPropagation()}>
                                  {t("添加资料")}

                                </Button>
                              </Popover>
                            </Form.Item>
                            {
                              formList.map((item: any, index: number) => {
                                if (item.controlType == MetaEnums.DataType.input &&
                                  item.fieldName == 'is_attachment_ownload') {
                                  return <div
                                    style={{ width: '50%' }} key={index}>

                                    <Form.Item
                                      key={item.fieldName}
                                      name={item.fieldName}
                                      initialValue={true}
                                      label={item.showName}
                                      labelCol={{ span: 16 }}
                                      wrapperCol={{ span: 6 }}
                                      rules={[{ required: item.isMustInput }]}
                                      valuePropName="checked"
                                      tooltip={t("若选择“否”，则学生只能够在进入课程页面后观看附件，无法下载，对于压缩包等无法直接观看课件，将无法进行任何操作。")}
                                    // extra="若选择“否”，则学生只能够在进入课程页面后观看附件，无法下载，对于压缩包等无法直接观看课件，将无法进行任何操作。"
                                    >
                                      <Switch checkedChildren={t("是")} unCheckedChildren={t("否")} />
                                    </Form.Item>
                                  </div>;
                                }
                              })}

                          </div>);
                      }
                    }
                  }) :
                  ''}
                <div className='table-container'>
                  {showDragger ? <div className='dragger-upload-container'>
                    <Dragger
                      name="file"
                      action="/rman/v1/upload/reference/material/import"
                      headers={{ authorization: 'authorization-text' }}
                      onChange={uploadAttachment}
                      customRequest={uploadDefault}
                      showUploadList={false}
                      beforeUpload={(checkFileSize as any)}>

                      <p className='close-container'><CloseOutlined onClick={(e) => { e.stopPropagation(); setShowDragger(false); }} /></p>
                      <p className="ant-upload-drag-icon">
                        <PlusIcon />
                      </p>
                      <p className="ant-upload-text">{t("将文件拖拽至此处，或")}<a>{t("点击上传")}</a></p>
                      <p className="ant-upload-text">{t("上传成功的文件可在个人资源-上传文件夹里找到")}</p>
                    </Dragger>
                  </div> : ""}
                  <Table<attachmentResourcesList>
                    bordered
                    loading={tableLoading}
                    columns={columns}
                    dataSource={attachmentList}
                    pagination={{
                      position: ['bottomCenter'],
                      hideOnSinglePage: true
                    }}
                    size="small" />

                </div>

              </div>
              <div className='basic-information-right'>
                <div className="title-wrp">{t("基本信息")}</div>
                {formList ?
                  [...formList].
                    sort((a, b) => a.order - b.order).
                    filter((item) => !courseContents.includes(item.fieldName)).
                    map((item, index) => {
                      if (
                        item.controlType == MetaEnums.DataType.input &&
                        item.fieldName === 'name_') {
                        return (
                          <Form.Item
                            key={item.fieldName}
                            name={item.fieldName}
                            label={item.showName}
                            rules={[
                              { required: item.isMustInput },
                              { type: 'string', max: item.maxLength }]}>


                            <Input autoComplete="off" showCount maxLength={50} onChange={(e: any) => {
                              dispatch({
                                type: 'microCourse/updateCourseProps',
                                payload: {
                                  name: e.target.value
                                }
                              });
                            }} />
                          </Form.Item>);

                      } else if (item.fieldName === 'tag') {
                        return <Form.Item
                          key={item.fieldName}
                          name={item.fieldName}
                          label={item.showName}>

                          <TagInput tagLabels={tagLabels} />
                        </Form.Item>;
                      } else if (
                        item.controlType == MetaEnums.DataType.input &&
                        item.fieldName !== 'teacher' && item.fieldName !== 'assistant' && item.fieldName !== 'unLoginShow' && item.fieldName !== "showWatermark" && item.fieldName !== "allowed_double_speed" && item.fieldName !== "allowed_drag") {
                        return (
                          <Form.Item
                            key={item.fieldName}
                            name={item.fieldName}
                            label={item.showName}
                            rules={[{ required: item.isMustInput }]}>

                            <Input autoComplete="off" allowClear />
                          </Form.Item>);

                      } else if (
                        item.controlType == MetaEnums.DataType.textArea) {
                        return (
                          <Form.Item
                            key={item.fieldName}
                            name={item.fieldName}
                            label={item.showName}
                            rules={[{ required: item.isMustInput }]}>

                            <Editor  height={300} name={item.fieldName} onlyText wordlimit={30000} />
                            {/* <TextArea placeholder='请输入简介' allowClear rows={4} showCount maxLength={300} /> */}
                          </Form.Item>);

                      } else if (item.fieldName == 'college') {
                        return (
                          <Form.Item
                            key={item.fieldName}
                            name={item.fieldName}
                            label={item.showName}
                            wrapperCol={{ span: 16 }}
                            rules={[{ required: item.isMustInput }]}>

                            <Select
                              showSearch
                              mode="multiple"
                              style={{ width: '100%' }}
                              showArrow
                              value={form.getFieldValue('college')}
                              suffixIcon={(() => {
                                let collegelength = form.getFieldValue('college')?.length || 0;
                                return <span style={{ color: '#9D9D9D', fontSize: '14px' }}>{collegelength}/10</span>;
                              })()}
                              placeholder={`${t("请选择")}${item.showName}`}
                              optionFilterProp="children"
                              onChange={(e: any, label: any) => {
                                if (e.length > 10) {
                                  message.error('最多选择10个开课学院');
                                  let value = form.getFieldValue('college');
                                  // 截取前10个
                                  form.setFieldValue('college', value.slice(0, 10));
                                } else {
                                  onSelectChange(e, label, item.fieldName);
                                }
                              }}
                              // allowClear
                              filterOption={(input, option: any) =>
                                option.children.
                                  toLowerCase().
                                  indexOf(input.toLowerCase()) >= 0}

                              getPopupContainer={(triggerNode) =>
                                triggerNode.parentNode}>


                              {collegeList.map((item: any) =>
                                <Option key={item.code} value={item.code}>
                                  {item.name}
                                </Option>)}

                            </Select>
                          </Form.Item>);

                      } else if (
                        item.controlType == MetaEnums.DataType.select) {
                        if (item.fieldName == 'classification') {
                          return (
                            <Form.Item
                              key={item.fieldName}
                              name={item.fieldName}
                              label={item.showName}
                              wrapperCol={{ span: 16 }}
                              rules={[{ required: item.isMustInput }]}>

                              <Select
                                showSearch
                                mode={item.isMultiSelect ? 'multiple' : undefined}
                                style={{ width: '100%' }}
                                placeholder={`${t("请选择")}${item.showName}`}
                                optionFilterProp="children"
                                onChange={(e: any, label: any) =>
                                  onSelectChange(e, label, item.fieldName)}

                                // onChange={handleChange}
                                // onFocus={onSelectFocus}
                                // onBlur={onSelectBlur}
                                // onSearch={onSelectSearch}
                                allowClear
                                filterOption={(input, option: any) =>
                                  option.children.
                                    toLowerCase().
                                    indexOf(input.toLowerCase()) >= 0}

                                getPopupContainer={(triggerNode) =>
                                  triggerNode.parentNode}>


                                {allSubject.map((item: any) =>
                                  <Option key={item.code} value={item.code}>
                                    {item.name}
                                  </Option>)}

                              </Select>
                            </Form.Item>);

                        } else if (item.fieldName == 'subject') {
                          return (
                            <Form.Item
                              key={item.fieldName}
                              name={item.fieldName}
                              label={item.showName}
                              wrapperCol={{ span: 16 }}
                              rules={[{ required: item.isMustInput }]}>

                              <Select
                                showSearch
                                mode={item.isMultiSelect ? 'multiple' : undefined}
                                style={{ width: '100%' }}
                                placeholder={`${t("请选择")}${item.showName}`}
                                optionFilterProp="children"
                                onChange={(e: any, label: any) =>
                                  onSelectChange(e, label, item.fieldName)}

                                // onChange={handleChange}
                                // onFocus={onSelectFocus}
                                // onBlur={onSelectBlur}
                                // onSearch={onSelectSearch}
                                allowClear
                                filterOption={(input, option: any) =>
                                  option.children.
                                    toLowerCase().
                                    indexOf(input.toLowerCase()) >= 0}

                                getPopupContainer={(triggerNode) =>
                                  triggerNode.parentNode}>


                                {objToArr(
                                  JSON.parse(item.controlData),
                                  item.fieldName).
                                  map((item: any) =>
                                    <Option key={item.label} value={item.value}>
                                      {item.label}
                                    </Option>)}

                              </Select>
                            </Form.Item>);

                        } else if (item.fieldName === 'course_level') {
                          return (
                            <Form.Item
                              key={item.fieldName}
                              name={item.fieldName}
                              label={item.showName}
                              wrapperCol={{ span: 16 }}
                              rules={[{ required: item.isMustInput }]}>

                              <Select
                                showSearch
                                mode={item.isMultiSelect ? 'multiple' : undefined}
                                style={{ width: '100%' }}
                                placeholder={`${t("请选择")}${item.showName}`}
                                optionFilterProp="children"
                                onChange={(e: any, label: any) =>
                                  onSelectChange(e, label, item.fieldName)}

                                allowClear
                                getPopupContainer={(triggerNode) =>
                                  triggerNode.parentNode}>


                                {[t("校级"), t("省级"), t("国家级")].map((item: any, index: number) =>
                                  <Option key={index} value={item}>
                                    {item}
                                  </Option>)}

                              </Select>
                            </Form.Item>);

                        } else if (item.fieldName == 'teacher' || item.fieldName == 'assistant') {
                          return (
                            <div className='teacher-wrp'>
                              <Form.Item
                                key={item.fieldName}
                                name={item.fieldName}
                                label={item.showName}
                                wrapperCol={{ span: 16 }}
                                rules={[{ required: item.isMustInput }]}
                                tooltip={item.fieldName == 'assistant' ? t("助教可帮助进行课程编辑，但名字不会显示在开课老师中") : null}>

                                <Select
                                  showSearch
                                  style={{ width: '100%' }}
                                  placeholder={`${t("请选择")}${item.showName}`}
                                  optionFilterProp="children"
                                  labelInValue
                                  // onChange={onSelectChange}
                                  // onFocus={onSelectFocus}
                                  // onBlur={onSelectBlur}
                                  // onSearch={onSelectSearch}
                                  allowClear
                                  // filterOption={(input, option: any) =>
                                  //   option.children
                                  //     .toLowerCase()
                                  //     .indexOf(input.toLowerCase()) >= 0
                                  // }
                                  filterOption={false}
                                  getPopupContainer={(triggerNode) =>
                                    triggerNode.parentNode}

                                  onSearch={handleSearch}
                                  onChange={(value: any) => handleChange(value, item.fieldName)}
                                  onPopupScroll={handleScroll}>

                                  {teacherList.map((item: any, index: number) => {
                                    return (
                                      <Option
                                        key={item.fieldCode}
                                        value={item.fieldCode}>

                                        {item.fieldValue}（{item.extendedValue}，{item.fieldCode}）
                                      </Option>);

                                  })}
                                </Select>
                              </Form.Item>
                              {item.fieldName == 'teacher' &&
                                <div className='edit-teacher-btn'>
                                  <a onClick={() => setEditTeacherVisible(true)}>{t("修改教师信息")}</a>
                                  <Tooltip title={t("修改开课教师在课程中展示的头像、姓名、所属学院等信息")}><QuestionCircleOutlined /></Tooltip>
                                </div>}
                            </div>);

                        } else if (item.fieldName == 'plate') {
                          return (
                            <Form.Item
                              key={item.fieldName}
                              name={item.fieldName}
                              label={item.showName}
                              wrapperCol={{ span: 16 }}
                              rules={[{ required: item.isMustInput }]}>

                              <Select
                                showSearch
                                style={{ width: '100%' }}
                                placeholder={`${t("请选择")}${item.showName}`}
                                optionFilterProp="children"
                                onChange={(e: any, label: any) =>
                                  onSelectChange(e, label, item.fieldName)}

                                // onFocus={onSelectFocus}
                                // onBlur={onSelectBlur}
                                // onSearch={onSelectSearch}
                                allowClear
                                filterOption={(input, option: any) =>
                                  option.children.
                                    toLowerCase().
                                    indexOf(input.toLowerCase()) >= 0}

                                getPopupContainer={(triggerNode) =>
                                  triggerNode.parentNode}>


                                {allPlate.map((item: any) =>
                                  <Option key={item.id} value={item.name}>
                                    {item.name}
                                  </Option>)}

                              </Select>
                            </Form.Item>);

                        } else if (item.fieldName === 'industryCategory') {
                          //行业门类
                          return (
                            <Form.Item
                              key={item.fieldName}
                              name={item.fieldName}
                              label={item.showName}
                              wrapperCol={{ span: 16 }}
                              rules={[{ required: item.isMustInput }]}>

                              <Select
                                showSearch
                                allowClear
                                style={{ width: '100%' }}
                                showArrow
                                value={form.getFieldValue('industryCategory')}
                                placeholder={`${t("请选择")}${item.showName}`}
                                onChange={(e: any, label: any) => {
                                  onSelectChange(e, label, item.fieldName);
                                }}>
                                {industryCategoryList.map((item: any) =>
                                  <Option key={item} value={item}>
                                    {item}
                                  </Option>)}
                              </Select>
                            </Form.Item>);
                        } else {
                          return (
                            <Form.Item
                              key={item.fieldName}
                              name={item.fieldName}
                              label={item.showName}
                              wrapperCol={{ span: 16 }}
                              rules={[{ required: item.isMustInput }]}>

                              <Select
                                showSearch
                                style={{ width: '100%' }}
                                placeholder={`${t("请选择")}${item.showName}`}
                                optionFilterProp="children"
                                onChange={(e: any, label: any) =>
                                  onSelectChange(e, label, item.fieldName)}

                                // onFocus={onSelectFocus}
                                // onBlur={onSelectBlur}
                                // onSearch={onSelectSearch}
                                allowClear
                                filterOption={(input, option: any) =>
                                  option.children.
                                    toLowerCase().
                                    indexOf(input.toLowerCase()) >= 0}

                                getPopupContainer={(triggerNode) =>
                                  triggerNode.parentNode}>


                                {objToArr(
                                  JSON.parse(item.controlData),
                                  item.fieldName).
                                  map((item: any) =>
                                    <Option key={item.label} value={item.value}>
                                      {item.label}
                                    </Option>)}

                              </Select>
                            </Form.Item>);

                        }

                      } else if (
                        item.controlType == MetaEnums.DataType.treeSelect) {
                        if (item.fieldName == 'major') {
                          return (
                            <Form.Item
                              key={item.fieldName}
                              name={item.fieldName}
                              label={item.showName}
                              wrapperCol={{ span: 16 }}
                              rules={[{ required: item.isMustInput }]}>

                              <TreeSelect
                                showArrow
                                allowClear
                                showSearch
                                value={form.getFieldValue(item.fieldName)}
                                suffixIcon={(() => {
                                  let majorslength = form.getFieldValue(item.fieldName)?.length || 0;
                                  return item.isMultiSelect ? <span style={{ color: '#9D9D9D', fontSize: '14px' }}>{majorslength}/10</span> : null;
                                })()}
                                treeData={forTree(JSON.parse(item.controlData), undefined, item.isMultiSelect)}
                                // value={courseData.profession}
                                onChange={(value: any, label: any, extra: any) => {
                                  if (value.length > 10 && item.isMultiSelect) {
                                    message.error('最多选择10个适用专业');
                                    let value = form.getFieldValue(item.fieldName);
                                    // 截取前10个
                                    form.setFieldValue(item.fieldName, value.slice(0, 10));
                                  } else {
                                    onProfessionChange(value, label, extra, item.fieldName);
                                  }
                                }}
                                treeCheckable={item.isMultiSelect}
                                // showCheckedStrategy={SHOW_PARENT}
                                placeholder={`${t("请选择")}${item.showName}`}
                                // allowClear={true}
                                treeNodeFilterProp="title"
                                // defaultValue={[]}
                                getPopupContainer={(triggerNode) =>
                                  triggerNode.parentNode} />


                            </Form.Item>);

                        } else {
                          return (
                            <Form.Item
                              key={item.fieldName}
                              name={item.fieldName}
                              label={item.showName}
                              wrapperCol={{ span: 16 }}
                              rules={[{ required: item.isMustInput }]}>

                              <TreeSelect
                                treeData={forTree(JSON.parse(item.controlData))}
                                // value={courseData.profession}
                                onChange={(value: any, label: any, extra: any) => onProfessionChange(value, label, extra, item.fieldName)}
                                treeCheckable={item.isMultiSelect}
                                // showCheckedStrategy={SHOW_PARENT}
                                placeholder={`${t("请选择")}${item.showName}`}
                                allowClear={true}
                                treeNodeFilterProp="title"
                                // defaultValue={[]}
                                getPopupContainer={(triggerNode) =>
                                  triggerNode.parentNode} />


                            </Form.Item>);

                        }
                      } else if (item.controlType == 23) {
                        return (
                          <Form.Item
                            key={item.fieldName}
                            name="cover"
                            label={t("课程封面")}
                            wrapperCol={{ span: 16 }}
                            rules={[{ required: item.isMustInput }]}>
                            <div style={{ display: "flex" }}>
                              <div className="cover-wrapper">
                                <Img src={courseData.cover}
                                  width={'200px'}
                                  height={112} />
                              </div>
                              <div className="upload-btn-wrapper">
                                <Button type='primary' onClick={() => setCoverModalVisible(true)}>{t("编辑封面")}</Button>
                              </div>
                            </div>
                          </Form.Item>);

                      } else if (item.fieldName === 'unLoginShow') {
                        return <Form.Item
                          key={item.fieldName}
                          name={item.fieldName}
                          initialValue={false}
                          label={item.showName}
                          wrapperCol={{ span: 16 }}
                          rules={[{ required: item.isMustInput }]}
                          tooltip={t("勾选则未登录的游客也可直接加入课程，进行课程学习")}
                          valuePropName="checked"
                        // extra="若选择“否”，则学生只能够在进入课程页面后观看附件，无法下载，对于压缩包等无法直接观看课件，将无法进行任何操作。"
                        >
                          <Switch checkedChildren={t("是")} unCheckedChildren={t("否")} />
                        </Form.Item>;

                      }
                    }) :
                  ''}
              </div>
            </div>
            <Form.Item wrapperCol={{ span: 24 }}>
              <div className="form-botton">
                {/* {jurisdictionList.includes(perCfg.microcourse_publish) && (
                   <Button
                     type="primary"
                     loading={publishLoading}
                     htmlType="submit"
                     onClick={() => {
                       buttonStatus(true);
                       setIsSave(false);
                     }}
                   >
                     发布
                   </Button>
                  )} */}
                {/* <Button
                   loading={saveLoading}
                   htmlType="submit"
                   onClick={() => {
                     buttonStatus(false);
                   }}
                  >
                   保存
                  </Button> */}

                {/* <Button onClick={back}>返回</Button> */}
              </div>
            </Form.Item>
          </Form>
        </Spin>
      </Spin>
      <Modal
        wrapClassName='upload-progress-modal'
        title={t("上传进度")}
        open={uploadVis}
        footer={null}
        closeIcon={null}
        maskClosable={false}>

        <div>{uploadFile.name}{t("正在上传...")}</div>
        <Progress percent={uploadProgress} status={uploadStatus} />
      </Modal>
      <ResourceModal
        treeData={modalTreeData}
        visible={modalVisible}
        onConfirm={resourceModalConfirm}
        onCancel={() => setModalVisible(false)}
        onShowDetail={(id, detail) => {
          setEntityPreview({
            id: id,
            name: detail.name,
            type: detail.type
          });
          setEntityModalVisible(true);
        }}
        // fileType={isAttachment ? undefined : ['biz_sobey_video']}
        multi />
      {/* 如果只让选择视频类型，需要传这个值给子组件fileType={isAttachment ? undefined : ['biz_sobey_video']} */}
      <EditFileInfoModal
        visible={fileInfoModalVisible}
        onCancel={() => setFileInfoModalVisible(false)}
        initialValues={fileInfo}
        onOk={handleEditFileOk} />

      {/* 资源预览modal */}
      <ResourcePreviewModal
        modalVisible={entityModalVisible}
        modalClose={() => setEntityModalVisible(false)}
        resource={entityPreview} />

      <FilePreviewModal fileType={getPreviewType(fileObj.filePath)} file={fileObj} visible={previewVisible} onClose={() => { setPreviewVisible(false); }} />
      <CoverModal visible={coverModalVisible} name={form.getFieldValue("name")} coverSetting={coverSetting} teacher={teacherName} coverConfirm={handleCoverConfirm} coverCancel={() => { setCoverModalVisible(false); }} />
      <AddTeachModal isMicro orgList={collegeList} visible={editTeacherVisible} data={teacherData} onClose={() => setEditTeacherVisible(false)} onOk={handleOKTeacher} />
      <LoggerModal list={logList} open={logOpen} onClose={() => setLogOpen(false)} />
      {hyperlinkModalVisible && <AddHyperlinkModal data={hyperlinkData} onConfirm={linkModalConfirm} isAdd={isAddHyperlink} visible={hyperlinkModalVisible} onClose={() => { setHyperlinkModalVisible(false) }} />}
    </div>);

};

export default AddCourseForm;
