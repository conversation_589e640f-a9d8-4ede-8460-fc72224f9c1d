import { updatamapstatus } from '@/api/coursemap';
import { IconFont } from '@/components/iconFont';
import useLocale from '@/hooks/useLocale';
import { IGlobalModelState } from '@/models/global';
import {
  DisconnectOutlined,
  EyeOutlined,
  SaveOutlined,
  ExportOutlined
} from '@ant-design/icons';
import { Button, Modal, message, Checkbox } from 'antd';
import React, { FC, useEffect, useState } from 'react';
import { useBus, useListener } from 'react-bus';
import { history, useDispatch, useLocation, useSelector } from 'umi';
import './index.less';
import NPUHeader from '@/components/NPUHeader';
import { CUSTOMER_NPU } from '@/permission/moduleCfg';
const Mapv4Layout: FC<any> = ({ children }) => {
  const parameterConfig = useSelector<any, any>(
    ({ global }) => global.parameterConfig,
  );
  const mapinfo: any = useSelector<any, any>(
    (state: { coursemap: { mapinfo: any } }) => state.coursemap.mapinfo,
  );
  const { t } = useLocale();
  const [selectbtn, setSelectbtn] = useState<string>('/mapv4/editmap');
  // 获取url参数
  const { query, pathname }: any = useLocation();
  const dispatch = useDispatch();
  const bus = useBus();

  // 发布范围
  const [shareRange, setShareRange] = useState<any[]>([]);
  const [releaseValue, setReleaseValue] = useState<string>('1'); 
  const [release, setRelease] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  // 预览loading
  const [perviewLoading, setPerviewLoading] = useState<boolean>(false);
  // 判断当前路由是否是  /mapdetail/editmap
  const [isEditMap, setIsEditMap] = useState<boolean>(false);

  // 监听地图保存完毕事件
  useListener('mapsaveover', (val: any) => {
    // 保存完毕后，更新地图封面
    bus.emit('saveCover');
  });

  // 监听问题地图保存完毕事件
  useListener('ProbleMapsaveover', (val: any) => {
    setLoading(false);
  });

  // 监听预览保存完毕事件
  useListener('perviewsaveover', (val: any) => {
    setPerviewLoading(false);
    window.open(`${window.location.pathname}#/perviewemap?mapid=${query.id}`);
  });

  // 监听封面保存完毕事件
  useListener('saveCoverover', (val: any) => {
    setLoading(false);
  });

  useEffect(() => {
    setSelectbtn(pathname);
    if (pathname == '/mapv4/editmap' || pathname == '/mapv4/problemap') {
      setIsEditMap(true);
    } else {
      setIsEditMap(false);
    }
  }, [pathname]);

  useEffect(() => {
    // 获取地图信息
    dispatch({
      type: 'coursemap/fetchMapInfo',
      payload: {
        params: {
          mapId: query.id,
        },
      },
    });
  }, []);

  // 发布弹窗 发布范围
  const changeShareParams = (value: any) => {
    setShareRange(value);
  }

  // 发布弹窗内容
  const modalShareContainer = () => {
    return (
      <div className='share-content-style'>
        <div className='title'>{t('请选择发布范围 :')}</div>
        <div className='checkbox-style'>
          <Checkbox.Group onChange={changeShareParams}>
            <Checkbox value={0}>{t('仅门户查看')}</Checkbox>
            <Checkbox value={1}>{t('可引用编辑')}</Checkbox>
          </Checkbox.Group>

        </div>
        <div className='tip'>{t('(修改配置可前往“权限管理-发布权限”)')}</div>
      </div>
    )
  }

  // 修改地图状态 0 下架 1发布 2发布
  const updatamap = (flag?: string) => {
    let status: any = flag || 0;

    if (shareRange.length === 0 && status == 2) {
      message.error(t('请选择发布范围！'));
      return;
    }

    const paramShareRange = shareRange.join(',');
    const params = status == 2 ? `${status}&releaseScope=${paramShareRange},2` : status; // 默认勾选 2：修改同步至发布对象

    updatamapstatus([mapinfo.id], params).then((res: any) => {
      if (res.status === 200) {
        if (status == '1') {
          // message.success('课程地图保存成功！');
        } else if (status == '0') {
          message.success(t('下架成功！'));
        } else {
          message.success(t('发布成功！'));
        }
        // 去更新dva的数据
        dispatch({
          type: 'coursemap/fetchMapInfo',
          payload: {
            params: {
              mapId: query.id,
            },
          },
        });
        setRelease(false);
      } else {
        message.error(t('操作失败！'));
      }
    }).finally(() => { setShareRange([])});
  };

  return (
    <div className="mapv4Layout_view">
      {parameterConfig.target_customer === CUSTOMER_NPU && (
        <NPUHeader />
      )}
      <div className="heard_view">
        <div className="map_name">
          <span className="name_span">{mapinfo?.mapName}</span>
          {mapinfo.isShow === 2 ? (
            <span className={`publishStatus published`}>{t('已发布')}</span>
          ) : null}
        </div>
        <div className="tabs_view">
          <div
            className={
              selectbtn == '/mapv4/editmap'
                ? 'tabs_item selectbtn'
                : 'tabs_item'
            }
            onClick={() => {
              history.push(`/mapv4/editmap?id=${query.id}&key=3`);
            }}
          >
            {t('编辑地图')}
          </div>
          {parameterConfig?.show_problem_map === 'true' && <div
            className={
              selectbtn == '/mapv4/problemap'
                ? 'tabs_item selectbtn'
                : 'tabs_item'
            }
            onClick={() => {
              history.push(`/mapv4/problemap?id=${query.id}&key=3`);
            }}
          >
            {t('问题图谱')}
          </div>}
          <div
            className={
              selectbtn == '/mapv4/teachingteam'
                ? 'tabs_item selectbtn'
                : 'tabs_item'
            }
            onClick={() => {
              history.push(`/mapv4/teachingteam?id=${query.id}&key=2`);
            }}
          >
            {parameterConfig.target_customer == 'npu' ? t('维护权限') : t('权限管理')}
          </div>
          <div
            className={
              selectbtn == '/mapv4/mapdetail'
                ? 'tabs_item selectbtn'
                : 'tabs_item'
            }
            onClick={() => {
              history.push(`/mapv4/mapdetail?id=${query.id}&key=1`);
            }}
          >
            {t('基本信息')}
          </div>
          {parameterConfig.show_curriculum_objectives == 'true' && <div
            className={
              selectbtn == '/mapv4/bind' ? 'tabs_item selectbtn' : 'tabs_item'
            }
            onClick={() => {
              history.push(`/mapv4/bind?id=${query.id}&key=1`);
            }}
          >
            {t('课程目标绑定')}
          </div>}
        </div>
        <div className="rigth_view">         
          {isEditMap ? (
            <Button
              icon={<SaveOutlined style={{ fontSize: '18px' }} />}
              style={{ marginRight: '20px' }}
              loading={loading}
              type="primary"
              onClick={() => {
                if(pathname == '/mapv4/problemap'){
                  setLoading(true);
                  bus.emit('saveProbleMap', {
                    exitEditmode: false
                  });
                }else{
                  setLoading(true);
                  bus.emit('saveMap', true);
                  setReleaseValue('1');
                  // updatamap(mapinfo.isShow || 1);
                }
              }}
            >
              {t('保存')}
            </Button>
          ) : null}

          <Button
            type="primary"
            icon={<EyeOutlined style={{ fontSize: '18px' }} />}
            style={{ marginRight: '20px' }}
            loading={perviewLoading}
            onClick={() => {
              // 判断当前路由是否是/mapdetail/editmap
              if (history.location.pathname == '/mapdetail/editmap') {
                setPerviewLoading(true);
                bus.emit('saveMapbyperview');
              } else {
                window.open(
                  `${window.location.pathname}#/perviewemap?mapid=${query.id}`,
                );
              }
            }}
          >
            {t('预览')}
          </Button>
          {mapinfo.isShow == 2 ? (
            <Button
              icon={<DisconnectOutlined style={{ fontSize: '18px' }} />}
              type="primary"
              onClick={() => {
                setRelease(true);
                setReleaseValue('0');
              }}
            >
              {t('下架')}
            </Button>
          ) : (
            <Button
              icon={
                <IconFont
                  type="iconfabu"
                  style={{ fontSize: '16px' }}
                ></IconFont>
              }
              type="primary"
              onClick={() => {
                setReleaseValue('2');
                setRelease(true);
              }}
            >
              {t('发布')}
            </Button>
          )}
        </div>
      </div>
      <div className="mapv4_content">{children}</div>
      <Modal
        title={releaseValue != '0' ? t('发布') : t('下架')}
        visible={release}
        destroyOnClose
        onOk={() => updatamap(releaseValue)}
        onCancel={() => {setRelease(false); setShareRange([]); }}
      >
        {releaseValue != '0' ? modalShareContainer() : t('确定要下架该图谱吗')}
      </Modal>
    </div>
  );
};

export default Mapv4Layout;
