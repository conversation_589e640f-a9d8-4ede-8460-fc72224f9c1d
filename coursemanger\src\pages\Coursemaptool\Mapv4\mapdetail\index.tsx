import React, { useEffect, useRef, useState } from 'react';
import './index.less'
import { Drawer, Input, Select, Tabs, message } from 'antd';
import {querymapbycourse,checkjoin } from '@/api/coursemap';
import { useHistory, useLocation, useSelector } from 'umi';
import useLocale from '@/hooks/useLocale';
import Atlasmap from '../components/Atlasmap';
import Editmap from '../components/Editmap';
import Persopathmap from '../components/Persopathmap';
import Problemap from '../components/Problemap';


const MapDetail: React.FC<any> = ({defaultActiveKey}) => {
  const { t } = useLocale();
  let history: any = useHistory();
  // 获取url参数
  const { query }: any = useLocation();
  const [mapinfo, setMapinfo] = useState<any>(null);

  // 当前选中的tab 的cLass
  const [activeclass, setActiveclass] = useState<string>('');
  const parameterConfig = useSelector<any, any>(
    ({ global }) => global.parameterConfig,
  );

  //查询课程关联的地图
  const initcoursemap = () => {
    if(query.isJoin == "true" || query.isJoin == "undefined"){
      checkjoin({
        courseId: query.id
      }).then((res:any)=>{
        if(res.data.isJoin || query.isJoin == "undefined"){
          querymapbycourse({
              courseId: query.id,
              isShow: 2,
              courseSemester: query.sm
          }).then(({data}:any)=>{
              if(data.length == 0){
                message.info('当前课程暂未关联图谱！');
                return;
              }
              setMapinfo(data[0]);
          })
        }else{
          message.info('加入课程后才可以查看图谱！');
          history.goBack();
          // history.push(`/mapv4?id=${query.id}&sm=${query.sm}&type=${query.type}`)
        }
      })
    }else{
      querymapbycourse({
          courseId: query.id,
          isShow: 2,
          courseSemester: query.sm
      }).then(({data}:any)=>{
          if(data.length == 0){
            message.info('当前课程暂未关联图谱！');
            return;
          }
          setMapinfo(data[0]);
      })
    }
  };

  useEffect(() => {
    initcoursemap();
  },[])

  return (
    <div className={`mapv4_mapdetail_view ${activeclass} ${query.type === 'microMajor' && 'microMajor_detail_background'}`} >
        <span className='mapName_span'>{mapinfo?.mapName}</span>
        <Tabs centered size="large" tabBarGutter={100} style={{width:'100%',height:'100%'}} destroyInactiveTabPane={true} className="tabs_nav" defaultActiveKey={defaultActiveKey}  onChange={(e)=>{
          if(e == '1'){
            setActiveclass('')
          }else if(e == '2'){
            setActiveclass('new_tab2_view')
          }else{
            setActiveclass('new_tab3_view')
          }
        }}>
            <Tabs.TabPane tab="学习路径" key="1">
                <Persopathmap mapid={mapinfo?.id} courseid={query.id} coursename={mapinfo?.mapName}></Persopathmap>
            </Tabs.TabPane>
            <Tabs.TabPane tab="图谱模式" key="2">
                <Atlasmap newmapid={mapinfo?.id} courseid={query.id} coursename={mapinfo?.mapName}></Atlasmap>
            </Tabs.TabPane>
            <Tabs.TabPane tab="导图模式" key="3">
                <Editmap mapid={mapinfo?.id} perviewtype={2} courseid={query.id} showrete={true} coursename={mapinfo?.mapName}></Editmap>
            </Tabs.TabPane>
            {parameterConfig?.show_problem_map === 'true' && (query.type != 'micromajor' && query.type != 'microMajor') && <Tabs.TabPane tab="问题图谱" key="4">
                <Problemap mapid={mapinfo?.id} perviewtype={2} courseid={query.id} coursename={mapinfo?.mapName}></Problemap>
            </Tabs.TabPane>}
        </Tabs>
    </div>
  );
};

export default MapDetail;
