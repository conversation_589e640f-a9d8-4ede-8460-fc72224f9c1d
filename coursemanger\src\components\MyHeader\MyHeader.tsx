import React, { useEffect } from 'react';
import { useSelector } from 'umi';
import img_clip from '../../assets/imgs/myHeader/clip.png';
import img_course from '../../assets/imgs/myHeader/course.png';
import img_user from '../../assets/imgs/myHeader/user.png';
import { IconFont } from '../iconFont/index';
import './MyHeader.less';
import { Layout, Avatar } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import { useDispatch, useLocation, useHistory } from 'umi';
import useLocale from '@/hooks/useLocale';

const { Header } = Layout;

function MyHeader() {
  let history: any = useHistory();
  let path = useLocation().pathname;
  const userInfo = useSelector<Models.Store, any>(
    (state) => state.microCourse.userInfo);
  const { t } = useLocale();

  return (
    <div className="my-header">
      <Header>
        <div className="left-button">
          <div className="logo">
            <img src={require(`../../assets/imgs/myHeader/logo.png`)} alt="" />
            <span className="logo-text">{t("智慧在线教学平台")}</span>
          </div>
          <div className="button-box">
            <div
              className={
                path.indexOf('/course') !== -1 ?
                  'button-item active' :
                  'button-item'}

              onClick={() => history.push(`/course`)}>

              <span>{t("发布管理")}</span>
            </div>
            <div
              className={
                path.indexOf('/portalconfiguration') !== -1 ?
                  'button-item active' :
                  'button-item'}

              onClick={() => history.push(`/portalconfiguration`)}>

              <span>{t("门户配置")}</span>
            </div>
          </div>
        </div>
        <div className="right-button">
          <div
            className="button-item"
            onClick={() => {
              window.open('/learn');
            }}>

            <IconFont type="iconzaixianjiaoyu" />
            <span>{t("在线教学")}</span>
          </div>
          <div className="button-item">
            {userInfo.avatar ?
              <Avatar src={userInfo.avatar} /> :

              <Avatar icon={<UserOutlined />} />}

            <span>{userInfo.nickName}</span>
          </div>
        </div>
      </Header>
    </div>);

}

export default MyHeader;