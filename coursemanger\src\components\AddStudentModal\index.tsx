import React, {
useState,
useEffect,
KeyboardEvent,
FocusEvent,
useRef } from
'react';
import { Modal, Input, Button, Tree, message, Form, Table } from 'antd';
import { useIntl, useSelector, useDispatch, useHistory } from 'umi';
import { TeamOutlined } from '@ant-design/icons';
import './index.less';
import {
childOrganization,
codeOrganization,
getAllUserByOrg,
login } from
'@/api/choicepeople';
import { addstudent, deletestudent, getStudentCodes, getstudentlist } from '@/api/student';
import useLocale from '@/hooks/useLocale';
import InfiniteScrollSelect from '../InfiniteScrollSelect';
// import IStudent from '@/types/student';
const { DirectoryTree } = Tree;
interface CreateModalProps {
  modalVisible: boolean;
  modalClose: () => void;
  courseSemester?: number;
  refresh: () => void; // 刷新
  specialFlag?: boolean; //单独给课堂回看权限设置用
}
interface ITreeItemProps {
  code: string;
  title: string;
  key: string;
  children?: Array<ITreeItemProps>;
  icon?: React.ReactNode;
  parentName?: string;
  parentId?: number;
  parentCode?: string;
}
const AddStudentModal: React.FC<CreateModalProps> = (props) => {
  const { t } = useLocale();
  const history: any = useHistory();
  const dispatch = useDispatch();
  const [searchform] = Form.useForm();
  const { modalClose, modalVisible, refresh, specialFlag, courseSemester } = props;
  const [loading, setLoading] = useState<boolean>(false);
  const [rootKeys, setRootKeys] = useState<Array<string>>([]);
  const [treeData, setTreeData] = useState<Array<ITreeItemProps>>([]);
  const [selectCode, setSelectCode] = useState<string>('');
  const [current, setCurrent] = useState<number>(1);
  const [keyword, setKeyword] = useState<string>('');
  const [roleCode, setRoleCode] = useState<string>('');
  const [total, setTotal] = useState<number>(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Array<any>>([]);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [userList, setUserList] = useState<Array<any>>([]);
  const [initialRowKeys, setInitialRowKeys] = useState<string[]>([]);
  const [relationPeople, setRelationPeople] = useState<string[]>([]);
  const [size, setSize] = useState<number>(24);
  useEffect(() => {
    // login()
    if (modalVisible) {
      getRoot();
      if (specialFlag) {
        const arr: any = localStorage.getItem('visibleSettingUserCode');
        setSelectedRowKeys(JSON.parse(arr)?.map((n: any) => {
          return n.userCode;
        }));
      } else {
        getStudentCodes({ courseId: history.location.query.id }).then((res: any) => {
          if (res && res.status === 200) {
            setRelationPeople(res.data);
            setSelectedRows(res.data.map((row: any) => ({ userCode: row })));
            setSelectedRowKeys(res.data);
            setInitialRowKeys(res.data);
          }
        });
      }
    }
  }, [modalVisible]);
  useEffect(() => {
    if (selectCode) {
      fetchAllUser();
    }
  }, [current, keyword, selectCode, roleCode, size]);
  const modalOk = () => {
    const deleteKey = relationPeople.filter(
    (code: any) => selectedRowKeys.indexOf(code) < 0);

    // console.log(deleteKey)
    let selected = selectedRows.filter((n: any) => n);
    let addKey = selected.filter(
    (code: any) =>
    initialRowKeys.indexOf(code.userCode ? code.userCode : code.user_code) <
    0);

    if (specialFlag) {
      let tempArray: any = [];
      tempArray = selected.map((item: any) => {
        return {
          userCode: item.user_code,
          name: item.nick_name,
          userType: 0, //非本班同学标记
          jobNumber: item.extend.account,
          college: item.extend.college,
          major: item.extend.major,
          sex: item.extend.sex,
          avatarUrl: item.avatar_url
        };
      });
      // console.log(selected,tempArray);
      localStorage.setItem('visibleSettingUserCode', JSON.stringify(tempArray));
      refresh();
      handleCancel();
      return;
    }
    if (addKey.length) {
      addKey = addKey.map((item: any) => {
        return {
          userCode: item.user_code,
          name: item.nick_name,
          jobNumber: item.extend.account,
          college: item.extend.college,
          major: item.extend.major,
          sex: item.extend.sex,
          avatarUrl: item.avatar_url
        };
      });
      // 参数2为课程类型，只有spoc：2 能添加学生
      addstudent({
        typeId: history?.location?.query?.id,
        courseType: history?.location?.query.type === "map" ? "4" : "2",
        courseSemester
      }, addKey).then((res: any) => {
        if (res && res.status === 200) {
          message.success('添加成功');
        }
        refresh();
        handleCancel();
      });
    } else {
      handleCancel();
    }
    if (deleteKey.length) {
      let deletedata: string[] = [];
      deleteKey.forEach((item: any) => {
        deletedata.push(item.id);
      });
      deletestudent(history.location.query.id, deletedata, Number(history.location.query.sm ?? 1)).then((res: any) => {
        if (res && res.status === 200) {
          message.success('删除成功');
          refresh();
        } else {
          message.error(res.message);
        }
        handleCancel();
      });
    } else {
      handleCancel();
    }
  };
  const getRoot = () => {
    codeOrganization(specialFlag ? '' : `org_student`).then((res: any) => {//课堂回看弹框 老师学生都取
      if (res && res.errorCode === 'success') {
        const rootData = res.extendMessage.map((item: any) => {
          return {
            title: item.organizationName,
            key: item.id,
            code: item.organizationCode
          };
        });
        setTreeData(rootData);
        setSelectCode(rootData[0].code);
        if (rootData.length) {
          onLoadChild(rootData[0], true);
        }
      }
    });
  };
  const onLoadChild = (node: any, isRoot: boolean = false) => {
    const { key, children, code, title } = node;
    return new Promise(async (resolve) => {
      if (children) {
        resolve();
        return;
      }
      function updateTreeData(
      list: ITreeItemProps[],
      key: React.Key,
      children: ITreeItemProps[])
      : ITreeItemProps[] {
        return list.map((node) => {
          if (node.key === key) {
            return {
              ...node,
              children
            };
          } else if (node.children) {
            return {
              ...node,
              children: updateTreeData(node.children, key, children)
            };
          }
          return node;
        });
      }

      const res: any = await childOrganization(code);
      if (res && res.errorCode === 'success') {
        setTreeData((origin) =>
        updateTreeData(
        origin,
        key,
        res.extendMessage.map((item: any) => {
          return {
            title: item.organizationName,
            key: item.id,
            code: item.organizationCode,
            parentName: title,
            parentId: key,
            parentCode: code
          };
        })));


        if (isRoot) {
          setRootKeys([key]);
        }
        resolve();
      }
    });
  };
  // 目录树选择
  const onSelect = (selectedKeys: any, e: any) => {
    setCurrent(1);
    setSelectCode(e.node.code);
  };
  const searchRole = () => {
    setKeyword(searchform.getFieldsValue().keyword);
    setRoleCode(searchform.getFieldsValue().roleCode);
    setCurrent(1);
    // setCurrent(1);
  };
  const columns = (specialFlag ? [
  {
    title: t("姓名"),
    dataIndex: 'nick_name'
  },
  {
    title: t("学号"),
    dataIndex: 'accountshow'
  },
  {
    title: t("学院"),
    dataIndex: 'college'
  },
  {
    title: t("专业"),
    dataIndex: 'major'
  }
  // {
  //   title: '角色',
  //   dataIndex: 'roles',
  //   ellipsis: true,
  //   render: (roles: any) => {
  //     let temp:any = [];
  //     roles?.forEach((item: any) => {
  //         if(item.code==='r_student'
  //           ||item.code==='r_teacher'||
  //           item.code==='r_offcampusteam'
  //           ){
  //             temp.push(item.name)
  //         }
  //       })
  //     return temp?.sort()?.join('，');
  //   },
  // },
  ] :
  [
  {
    title: t("姓名"),
    width: '15%',
    dataIndex: 'nick_name'
  },
  {
    title: t("学号"),
    width: '15%',
    dataIndex: 'accountshow'
  },
  {
    title: t("性别"),
    width: '5%',
    dataIndex: 'sexshow'
  },
  {
    title: t("学院"),
    dataIndex: 'college'
  },
  {
    title: t("专业"),
    dataIndex: 'major'
  }
  ]).concat(history.location.query.type === "training" ? [{
    title: '角色',
    dataIndex: 'roles',
    ellipsis: true,
    render: (roles: any) => {
      return roles
        ?.map((item: any) => item.name)
        ?.sort()
        ?.join('，');
    },
  }]: []);
  const pagination = {
    total,
    current,
    pageSize: size,
    onChange: (page: number, size: number) => handleTableChnage(page, size),
    showSizeChanger: true,
    pageSizeOptions: ['24', '36', '48', '60'],
    showTotal: (total: number) => t("共{name}页", String(Math.ceil(total / size))),
    defaultPageSize: 24
  };
  const rowSelection = {
    onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
      setSelectedRowKeys(newSelectedRowKeys);
      setSelectedRows(newSelectedRows);
    },
    preserveSelectedRowKeys: true,
    selectedRowKeys
  };
  // 切换页码
  const handleTableChnage = (params: any, size: number) => {
    setSize(size);
    setCurrent(params);
  };
  // 获取列表
  const fetchAllUser = async () => {
    setLoading(true);
    let param: any = specialFlag ? {
      organizationCode: selectCode,
      keyword,
      page: current,
      size,
      // include_roles: true,
      isIncludeSubUser: true
    } : {
      organizationCode: selectCode,
      keyword,
      page: current,
      size,
      // include_roles: true,
      isIncludeSubUser: true,
      roleCode: history.location.query.type === "training" ? (roleCode || null) : 'r_student'
    };
    // if (account) {
    //   param = {
    //     organizationCode: selectCode,
    //     extend: {
    //       account
    //    page: current,
    //     size,
    //     include_roles: true,
    //       },
    //     keyword,
    //    fill_detail: false
    //   };
    // }
    const res = await getAllUserByOrg(param);
    if (res && res.errorCode === 'success') {
      let data = res.extendMessage.results;
      // console.log('前',data,data.length)
      let tempArray: any = [];
      data.map((item: any) => {
        if (specialFlag) {
          if (item.roles) {//只给课堂回看返回有角色属性的数据
            item.sexshow = item.extend.sex;
            item.accountshow = item.extend.account;
            item.college = item.extend.college;
            item.major = item.extend.major;
            item.roles = item.roles;
            tempArray.push(item);
            return item;
          }
        } else {
          item.sexshow = item.extend.sex;
          item.accountshow = item.extend.account;
          item.college = item.extend.college;
          item.major = item.extend.major;
          item.roles = item.roles;
          return item;
        }
      });
      // console.log('后',tempArray,tempArray.length)
      setUserList(specialFlag ? tempArray : data);
      // setTotal(res.extendMessage.recordTotal);
      setTotal(specialFlag ? res.extendMessage.recordTotal + tempArray.length - data.length : res.extendMessage.recordTotal);
    }
    setLoading(false);
  };
  const handleCancel = () => {
    setCurrent(1);
    setKeyword("");
    searchform.setFieldsValue({ keyword: "", roleCode: null });
    modalClose();
  }
  const filterRole = (data: any) => {
    return data.map((item: any) => ({
      label: item.roleName,
      value: item.roleCode
    }))
  }
  const filterPage = ({pageIndex, pageSize}: { pageIndex: number; pageSize: number; }) => ({page: pageIndex, size: pageSize});
  return (
    <Modal
    destroyOnClose
    title={specialFlag ? t("添加人员") : t("添加学生")}
    visible={modalVisible}
    closable={false}
    width={1200}
    footer={[
    <Button key="submit" type="primary" onClick={modalOk}>{t("确定")}

    </Button>,
    <Button key="back" onClick={handleCancel}>{t("取消")}

    </Button>]}>

      
      <div className="add-student">
        <div className="tree">
          {!!rootKeys.length &&
          <Tree
          icon={<TeamOutlined />}
          defaultExpandedKeys={rootKeys}
          defaultSelectedKeys={[rootKeys[0]]}
          loadData={(onLoadChild as any)}
          treeData={treeData}
          onSelect={onSelect} />}

          
        </div>
        <div className="content">
          <div className="search_student">
            <Form layout={'inline'} form={searchform}>
              {/* <Form.Item
                                 label="学号"
                                 name="account"
                             >
                                 <Input autoComplete="off" placeholder='请输入学号' />
                             </Form.Item> */}
              <Form.Item label={t("姓名")} name="keyword">
                <Input autoComplete="off" placeholder={t("请输入姓名")} />
              </Form.Item>
              {history.location.query.type === "training" && <Form.Item label={t("角色")} name="roleCode">
                <InfiniteScrollSelect
                  url="/unifiedplatform/v1/role/search"
                  otherParams={{
                    count_users: true,
                    includeEA: true,
                  }}
                  filterOption={false}
                  style={{ width: "200px" }}
                  showSearch
                  requestType='POST'
                  placeholder="请选择角色"
                  searchKey="keyword"
                  allowClear
                  filterData={filterRole}
                  filterPage={filterPage}
                />
              </Form.Item>}
              <Form.Item>
                <Button type="primary" onClick={searchRole}>{t("检索")}

                </Button>
              </Form.Item>
            </Form>
          </div>
          <Table
          loading={loading}
          dataSource={userList}
          columns={columns}
          rowKey="user_code"
          size="small"
          rowSelection={{
            ...rowSelection
          }}
          pagination={{
            ...pagination
          }}
          scroll={{ y: 350 }} />
          
        </div>
      </div>
    </Modal>);

};

export default AddStudentModal;