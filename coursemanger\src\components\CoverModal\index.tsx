
import { FC, useEffect, useState, useRef } from 'react';
import React from "react";
import "./index.less";
import { Modal, Button, Select, Input, Upload, message, Spin } from "antd";
import html2canvas from "html2canvas";
import ImgCrop from 'antd-img-crop';
import ResourceModal from "@/components/ResourceModal";
import { getTreebylevel } from '@/api/addCourse';
import { changeCover, getCoverList, uploadFile } from "@/api/course";
import ResourcePreviewModal from "@/components/ResourcePreviewModal";
import CropperModal from "@/components/CropperModal";
// @ts-ignore
import $ from "jquery";
import { getSensitiveWord } from '@/utils';
import useLocale from '@/hooks/useLocale';
import RenderHtml from '../renderHtml';


const { Option } = Select;
const { TextArea } = Input;

interface CoverModalProps {
  name: string;
  visible: boolean;
  teacher: Array<string>;
  treeData?: ResourceModal.treeItem[];
  coverSetting?: any;
  coverConfirm: (image: string, setting: any) => void;
  coverCancel: () => void;
}

const fontList = [
  { ch: '宋体', en: 'SimSun' },
  { ch: '黑体', en: 'SimHei' },
  { ch: '微软雅黑', en: 'Microsoft Yahei' },
  { ch: '微软正黑体', en: 'Microsoft JhengHei' },
  { ch: '楷体', en: 'KaiTi' },
  { ch: '新宋体', en: 'NSimSun' },
  { ch: '仿宋', en: 'FangSong' }
];

const textToHtml = (text: string) => text.replace(/\n/g, "<br/>").replace(/\r/, "&nbsp;");
const limitRows = (value: string, limitLength: number, limitRow: number) => {
  const row = value.split("\n");
  if (value.includes("\n") && (row[1].length >= limitLength || row.length > limitRow)) {
    return row[0] + "\n" + row[1].slice(0, limitLength);
  } else {
    return value;
  }
};




const CoverModal: FC<CoverModalProps> = ({ name, teacher, treeData, visible, coverSetting, coverConfirm, coverCancel }) => {
  const { t } = useLocale();
  const [defaultImages, setDefaultImages] = useState<string[]>([]); // 默认的图片列表，左侧的9张
  // const [imageIndex, setImageIndex] = useState<number>(-1); // 选中图片的索引
  const imageIndex = useRef<number>(-1);
  const [cover, setCover] = useState<string>(""); // 右侧封面的背景图
  const [titleValue, setTitleValue] = useState<string>(""); // 右侧封面的名字
  const [teacherValue, setTeacherValue] = useState<string>(""); // 右侧封面的teacher
  const [color, setColor] = useState<string>("#ffffff"); // 字体颜色
  const [font, setFont] = useState<string>("Microsoft Yahei"); // 字体
  const [blur, setBlur] = useState<boolean>(false);
  const [resourceVisible, setResourceVisible] = useState<boolean>(false); // 资源库弹窗visible
  const [entityVisible, setEntityVisible] = useState<boolean>(false); // 资源详情visible
  const [entityPreview, setEntityPreview] = useState<any>(null); // 资源详情数据
  const [modalTreeData, setModalTreeData] = useState<ResourceModal.treeItem[]>([]); // 资源树数据
  const [resourceImage, setResourceImage] = useState<string>("");
  const [cropperVisible, setCropperVisible] = useState<boolean>(false);
  // const [newCover, setNewCover] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    getCovers();
  }, []);
  useEffect(() => {
    if (coverSetting && Reflect.ownKeys(coverSetting).length > 0) {
      if (coverSetting.uploadUrl) {
        setCover(coverSetting.uploadUrl);
        imageIndex.current = 12;
      } else {
        imageIndex.current = coverSetting.coverType || 0;
        setCover(defaultImages?.[imageIndex.current]);
      }
      setTitleValue(coverSetting.courseName || "");
      setTeacherValue(coverSetting.teacherName || "");
      setColor(coverSetting.color || "#ffffff");
      setFont(fontList[coverSetting.type ?? 2].en);
    } else {
      setCover(defaultImages[0]);
    }
  }, [coverSetting]);

  // useEffect(() => {
  //   if (defaultImages.length > 0) {
  //     setCover();
  //   }
  // }, [imageIndex]);

  const getCovers = () => {
    getCoverList().then((res) => {
      if (res.status == 200) {
        const images = res.data.map((item: any) => item.url);
        setDefaultImages(images);
        if (imageIndex.current < 12) {
          if (imageIndex.current === -1) {
            imageIndex.current = 0;
          }
          setCover(images[coverSetting.coverType || 0]);
        }
        // if (res.data.length > 0) {
        //   setImageIndex(Math.floor(Math.random() * res.data.length));
        // }
      }
    });
  };

  const forTree = (tree: any, parentsKeys: string[]) => {
    return tree.map((item: any) => {
      return {
        key: item.id,
        parentsKeys,
        title: item.name,
        path: item.path,
        children: item.children ?
        forTree(item.children, [...parentsKeys, item.code]) :
        []
      };
    });
  };
  const getTreeData = () => {
    getTreebylevel().then((res) => {
      if (res && res.success) {
        let treeData = forTree(res.data, []);
        setModalTreeData(treeData);
        setTimeout(() => {
          setResourceVisible(true);
        }, 100);
      } else {
        console.error(res);
      }
    });
  };
  const confirmHandle = () => {
    if (!cover) {
      message.warning("请选择封面！");
      return;
    }
    setLoading(true);
    // 处理上传图片
    const canvas:any = canvasRef.current;
    const dataURL = canvas.toDataURL('image/png');
    const file = convertBase64UrlToBlob(dataURL)
    const formData = new FormData();
    formData.append("file", file, "cover.png");
    uploadFile(formData).then((res: any) => {
      if (res.message === 'OK') {
        // setCover(res.data.httpPath);
        imageIndex.current = 12;
        // isEdit = true;
        // setCover(false);
        message.success(t("封面上传成功！"));
        // handleChangeCover(res.data.httpPath);
        const data = {
          ...coverSetting,
          uploadUrl: imageIndex.current === 12 ? cover : "",
          coverType: imageIndex.current,
          courseName: titleValue,
          type: fontList.findIndex((item) => item.en === font) || 0,
          color,
          cover: res.data.httpPath,
          teacherName: teacherValue
        };
        coverConfirm(res.data.httpPath, data);
        coverCancel();
      } else {
        message.error(res.message);
      }
    }).catch(() => {
      // setUploadLoading(false);
      message.error(t("封面上传失败！"));
    }).finally(() => {
      setLoading(false);
    });
    // handleChangeCover();
  };
  const handleChangeCover = () => {
    setLoading(true);
    const data = {
      ...coverSetting,
      uploadUrl: imageIndex.current === 12 ? cover : "",
      coverType: imageIndex.current,
      courseName: titleValue,
      type: fontList.findIndex((item) => item.en === font) || 0,
      color,
      teacherName: teacherValue
    };
    getSensitiveWord(titleValue + teacherValue, "课程名或教师", () => {
      changeCover(data).then((res: any) => {
        if (res.status === 200) {
          coverConfirm(res.data, data);
          coverCancel();
        } else {
          message.warning('封面变更失败！');
        }
      }).finally(() => {
        setLoading(false);
      });
    }, () => {
      setLoading(false);
    });

  };
  const selectDefaultCover = (index: number, image: string) => {
    imageIndex.current = index;
    setCover(image);
  };
  const colorChange = (el: any) => {
    setColor(el.target?.value || "#fff");
  };
  // const drawCanvas = () => {
  //   setBlur(true);
  //   setTimeout(() => {
  //     html2canvas((document.querySelector(".cover-content") as HTMLElement), {
  //       scale: 2
  //     }).then(canvas => {
  //       // const width = canvas.width;
  //       // const height = canvas.height;
  //       // console.info(width, height);
  //       // const image = new Image();
  //       // image.src = canvas.toDataURL("image/jpg");
  //       // document.querySelector(".left-wrapper")?.append(image);
  //       setBlur(false);
  //       // coverConfirm(canvas.toDataURL("image/jpg"));
  //       const image = !teacherValue && !titleValue && newCover ? newCover : canvas.toDataURL("image/jpeg");
  //       const bytes = window.atob(image.split(',')[1]);
  //       // 处理异常,将ascii码小于0的转换为大于0
  //       const ab = new ArrayBuffer(bytes.length);
  //       const ia = new Uint8Array(ab);
  //       for (let i = 0; i < bytes.length; i++) {
  //         ia[i] = bytes.charCodeAt(i);
  //       }
  //       const file: any = new Blob([ab], { type: 'image/jpeg' });
  //       file.name = `${new Date().valueOf()}.jpg`;
  //       coverConfirm(file);
  //     });
  //   });
  // };
  const compressImage = (file?: any) => {
    // 图片小于5M不压缩
    const name = file.name; //文件名
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (e: any) => {
      const src = e.target.result;
      const img = new Image();
      img.src = src;
      if (file.size <= Math.sqrt(1024) * 5) {
        setCover(src);
      }
      img.onload = (e: any) => {
        const w = img.width;
        const h = img.height;
        // const quality = (Math.pow(1024, 2) * 5 / file.size).toFixed(1);  // 默认图片质量为0.92
        // 生成canvas
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        // 创建属性节点
        const anw = document.createAttribute("width");
        anw.nodeValue = w.toString();
        const anh = document.createAttribute("height");
        anh.nodeValue = h.toString();
        canvas.setAttributeNode(anw);
        canvas.setAttributeNode(anh);

        // 铺底色 PNG转JPEG时透明区域会变黑色
        (ctx as CanvasRenderingContext2D).fillStyle = "#fff";
        (ctx as CanvasRenderingContext2D).fillRect(0, 0, w, h);

        (ctx as CanvasRenderingContext2D).drawImage(img, 0, 0, w, h);
        // quality值越小，所绘制出的图像越模糊
        const base64 = canvas.toDataURL('image/jpeg'); // 图片格式jpeg或webp可以选0-1质量区间
        base64ToFile(base64, name);

        // setNewCover(base64);
      };
    };
  };
  const base64ToFile = (base64: string, name: string) => {
    // 返回base64转blob的值
    // 去掉url的头，并转换为byte
    const bytes = window.atob(base64.split(',')[1]);
    // 处理异常,将ascii码小于0的转换为大于0
    const ab = new ArrayBuffer(bytes.length);
    const ia = new Uint8Array(ab);
    for (let i = 0; i < bytes.length; i++) {
      ia[i] = bytes.charCodeAt(i);
    }
    const file: any = new Blob([ab], { type: 'image/jpeg' });
    file.name = name;
    if (file.size > Math.sqrt(1024) * 5) {
      setCover(base64);
    }
    handleUpload(file);
  };
  const beforeUpload = (file: any) => {
    compressImage(file);
    return false;
  };
  const clickResourceHandle = () => {
    if (!treeData?.length) {
      getTreeData();
    }

  };

  const resourceModalConfirm = (resource: Array<any>) => {
    if (resource.length > 0) {
      setResourceImage(resource[0].keyframe_);
      setResourceVisible(false);
      setCropperVisible(true);
    }
  };
  const handleUpload = (file: any) => {
    setLoading(true);
    const formData = new FormData();
    formData.append("file", file, file.name);
    uploadFile(formData).then((res: any) => {
      if (res.message === 'OK') {
        setCover(res.data.httpPath);
        imageIndex.current = 12;
        // isEdit = true;
        // setCover(false);
        message.success(t("封面上传成功！"));
      } else {
        message.error(res.message);
      }
    }).catch(() => {
      // setUploadLoading(false);
      message.error(t("封面上传失败！"));
    }).finally(() => {
      setLoading(false);
    });
  };
  const updateHandle = () => {
    titleValueChange(name);
    console.info(teacher);
    const teachers = (teacher instanceof Array ? teacher?.join("、") : teacher) ?? "";
    setTeacherValue(teachers);
  };
  const resourceCropperHandle = (image: string) => {
    base64ToFile(image, `${Date.now().valueOf()}.jpg`);
    setCropperVisible(false);

  };
  const titleValueChange = (value: string) => {
    setTitleValue(limitRows(value, 12, 2));
    // console.info($(".title-input").height())
    setTimeout(() => {
      if ($(".title-input").height() > 70) {
        $(".cover-content").css("padding-top", "61px");
      } else {
        $(".cover-content").css("padding-top", "100px");
      }
    });
  };

  const canvasRef = useRef(null);
  const divRef = useRef(null);
  const [imageURL, setImageURL] = useState(''); // 替换为你的背景图片链接

  useEffect(() => {
    setImageURL(cover);
  }, [cover]);
  useEffect(() => {
    drawImageWithText();
  }, [imageURL,titleValue, teacherValue]);

  const drawImageWithText = () => {
    const canvas:any = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    const img = new Image();
    img.src = imageURL;

    img.onload = () => {
      // 清空画布
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      // 绘制背景图片
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
      // 设置文字样式
      ctx.font = '600 50px Microsoft Yahei';
      ctx.fillStyle = 'white';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle'; 
      
      // 绘制第一行文字
      const newTitleValue = JSON.parse(JSON.stringify(titleValue))?.split('\n')??[]
      const newTeacherValue = JSON.parse(JSON.stringify(teacherValue))?.split('\n') ?? []
      // 计算总高度
      const totalHeight = 57 * newTitleValue.length + 42 + 24 * newTeacherValue.length; // 第一行高度 + 间距 + 第二行高度
      const centerY = (canvas.height - totalHeight) / 2 - 18; // 计算垂直居中的起始 y 坐标  向上偏移18px
      // const centerY = (canvas.height - totalHeight) / 2; // 计算垂直居中的起始 y 坐标  垂直剧中显示
      // 计算文字的中心位置
      const x1 = canvas.width / 2;
      let y1 = centerY + 50 / 2;
      newTitleValue?.forEach((item:any)=>{
        ctx.fillText(item, x1, y1); // 第一行文字的 y 坐标
        y1 += 70;
      })
      // ctx.fillText(titleValue, x1, centerY + 30 / 2); // 第一行文字的 y 坐标

      ctx.font = '400 24px Microsoft Yahei';
      // 绘制第二行文字
      let y2 = centerY + 50 * newTitleValue.length + 42 + 24 / 2
      newTeacherValue?.forEach((item: any) => {
        ctx.fillText(item, x1, y2); // 第一行文字的 y 坐标
        y2 += 30;
      })
      // ctx.fillText(teacherValue, x1, centerY + 50 * newTitleValue.length + 50 + 24 / 2); // 第二行文字的 y 坐标
    };
  };

  /**
     * 将base64编码转换为二进制文件
     * @param {*} base64
     */
  const convertBase64UrlToBlob = (urlData: any) => {
    //去掉url的头，并转换为byte 
    var bytes = window.atob(urlData.split(',')[1]);
    //处理异常,将ascii码小于0的转换为大于0  
    var ab = new ArrayBuffer(bytes.length);
    var ia = new Uint8Array(ab);
    for (var i = 0; i < bytes.length; i++) {
      ia[i] = bytes.charCodeAt(i);
    }
    let blob = new Blob([ab], { type: 'image/png' })
    return blob;
  }

  // const handleSaveImage = () => {
  //   const canvas:any = canvasRef.current;
  //   const dataURL = canvas.toDataURL('image/png');

  //   // 创建一个链接元素并下载图片
  //   const link = document.createElement('a');
  //   link.href = dataURL;
  //   link.download = 'edited_image.png';
  //   link.click();
  // };
  return (
    <Modal title={t("批量设置公开画面")} wrapClassName="cover-modal" width={1238} onCancel={coverCancel} onOk={confirmHandle} open={visible} confirmLoading={loading}>
      <Spin spinning={loading}>
        <div className="left-wrapper">
          <div className="left-header">
            <div className="title">{t("封面图模板")}</div>
            <div className="btn-wrapper">
              <Button type="primary" ghost onClick={clickResourceHandle}>{t("资源库选择")}</Button>
              <ImgCrop aspect={16 / 9} quality={0.8} rotate>
                <Upload
                accept='.png,.jpg,.gif,.svg'
                beforeUpload={beforeUpload}
                showUploadList={false}>
                  
                  <Button>{t("本地上传")}</Button>
                </Upload>
              </ImgCrop>
            </div>
          </div>
          <div className="left-content">
            {defaultImages.map((image: string, index: number) =>
            <div className={`image-wrapper ${imageIndex.current === index ? 'active' : ''}`} key={index} onClick={() => selectDefaultCover(index, image)}>
                <img src={image} alt="" />
              </div>)}
            
          </div>

        </div>
        <div className="right-wrapper">
          <div className="right-header">{t("效果预览")}</div>
          <div className="right-opt-wrapper">
            <Select value={font} onChange={(value: string) => setFont(value)}>
              {fontList.map((font) =>
              <Option value={font.en} key={font.en}>{t(font.ch)}</Option>)}
              
            </Select>
            <div className="color-wrapper">
              <div className="text">{t("颜色")}</div>
              <input type="color" value={color} className="color" onChange={(el: any) => colorChange(el)} />
            </div>

          </div>
          <div className="cover-wrapper-box">
            {/* <img src={cover} alt="" /> */}
            <div ref={divRef} className='cover-content' style={{ backgroundImage: `url(${cover})` }}>
              {blur ?
              <>
                  <RenderHtml className={`title-content ${titleValue ? "" : "no-title"}`} style={{ color, fontFamily: font }} dangerouslySetInnerHTML={{ __html: textToHtml(titleValue) }}></RenderHtml>
                  <RenderHtml className='teacher-content' style={{ color, fontFamily: font }} dangerouslySetInnerHTML={{ __html: textToHtml(teacherValue) }}></RenderHtml>
                </> :

              <>
                  <TextArea
                className="title-input"
                style={{ color, fontFamily: font }}
                maxLength={24}
                onChange={({ target: { value } }) => titleValueChange(value)}
                value={titleValue}
                autoSize={{ minRows: 1, maxRows: 2 }} />
                
                  <TextArea
                className="teacher-input"
                maxLength={66}
                style={{ color, fontFamily: font }}
                onChange={({ target: { value } }) => setTeacherValue(limitRows(value, 33, 2))}
                value={teacherValue}
                autoSize={{ minRows: 1, maxRows: 2 }} />
                
                </>}
              <canvas ref={canvasRef} width={640} height={360} style={{ display: 'none' }} />
            </div>
          </div>
          <div className="right-btn-wrapper">
            <Button onClick={updateHandle}>{t("添加文字内容")}</Button>
            <div className="tips">{t("将会把当前课程名及教师名自动填入")}</div>
          </div>
        </div>
      </Spin>
      <ResourceModal
      treeData={modalTreeData}
      visible={resourceVisible}
      onConfirm={resourceModalConfirm}
      onCancel={() => setResourceVisible(false)}
      onShowDetail={(id, detail) => {
        setEntityPreview({
          id: id,
          name: detail.name,
          type: detail.type
        });
        setEntityVisible(true);
      }}
      fileType={["biz_sobey_picture"]}
      multi={false} />
      
      <ResourcePreviewModal
      modalVisible={entityVisible}
      modalClose={() => setEntityVisible(false)}
      resource={entityPreview} />
      
      <CropperModal visible={cropperVisible} image={resourceImage} cropperOk={resourceCropperHandle} cropperCancel={() => {setCropperVisible(false);}} />
    </Modal>);
};

export default CoverModal;