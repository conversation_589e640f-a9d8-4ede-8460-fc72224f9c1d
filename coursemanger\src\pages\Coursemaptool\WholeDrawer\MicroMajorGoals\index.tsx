import {
  queryKnowledgeWeightByMicaro
} from '@/api/coursemap';
import React, { useEffect, useState } from 'react';
import TextMore from '../Textmore';
import styles from './index.less';

const MicroMajorGoals: React.FC<{
  planId: string;
  mapId: string;
  nodeId: string;
  courseId: string;
  sm: number|string
}> = ({ mapId, nodeId, planId, courseId, sm }) => {
  const [skillRequireList, setSkillRequireList] = useState<any>([]);
  const getSkillList = (plan_id: string, node_id: string, course_id: string) => {
    queryKnowledgeWeightByMicaro({ planId: plan_id, nodeId: node_id, courseId: course_id, courseSemester: sm || 1 }).then(
      (res: any) => {
        if (res?.status == 200) {
          const requireList = res?.data?.data?.reachDetailVOS || [];
          setSkillRequireList(
            requireList.filter(
              (item: any) =>
                item?.isCheck === true ||
                item?.lmh !== null ||
                Number(item?.percentage || 0) > 0 ,
            ),
          );
        }
      },
    );
  };
  useEffect(() => {
    if (!planId || !nodeId || !courseId) return;
    getSkillList(planId, nodeId, courseId);
  }, [planId, nodeId]);

  return (
    <div className={styles['micromajor-container']}>
      <div className={styles.title}>
        <span className={styles.span1}>能力要求</span>
      </div>
      <div className={styles.target_list}>
        {skillRequireList?.map((item: any,index:number) => (
          <div className={styles.target_item} key={index}>
            <div className={styles.text}>{item?.ability}</div>
            <div className={styles.desc}>
              <TextMore rows={1} text={item?.abilityDesc}></TextMore>
              <div>{item?.rate}%</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MicroMajorGoals;
