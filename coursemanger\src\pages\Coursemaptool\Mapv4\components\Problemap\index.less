#problemapContainer{
    position: relative;
    
    .status_box{
        position: absolute;
        width: 100%;
        height: 100%;
        background: #fff;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
    }

     // 连线流动效果
     .jtk-hover {
       
        &:hover{
            z-index: 2;
            path {
                stroke-dasharray: 10 5;
                stroke-dashoffset: 0;
                animation: ant-line 1s linear infinite;
                cursor: pointer;
            }
        }
    }

    @keyframes ant-line {
        0% {
            stroke-dashoffset: 0;
        }
        100% {
            stroke-dashoffset: -15;
        }
    }

    .ant-modal{
        position: absolute;
        top: 0;
        left: 0;
        width: 100% !important;
        max-width: 100% !important;
        height: 100%;
        z-index: 1000;
        padding-bottom: 0px;

        .ant-modal-content{
            height: 100%;

            .ant-modal-body{
                height: 96%;
            }
        }
    }
}

.problemap-wrp{
    width: 96%;
    margin-left: 2%;
    // height: calc(100% - 10px);
    display: flex;
    justify-content: space-between;

    .block_view{
        width: 30%;
        height: 100%;

        .top_heard_bg{
            
            // 第一个
            &:first-child{  
                background: linear-gradient( 270deg, #FEBF61 0%, #F5673B 100%);
            }
            // 第二个
            &:nth-child(2){
                background: linear-gradient( 270deg, #C2DC85 0%, #1FC5B6 100%);
            }
            // 第三个
            &:nth-child(3){
                background: linear-gradient( 270deg, #84EDE4 0%, #2587C0 100%);
            }
            
        }
    }
   
}