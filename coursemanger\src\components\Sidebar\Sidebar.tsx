import { getCourseListNew } from '@/api/course';
import { IconFont } from '@/components/iconFont';
import useLocale from '@/hooks/useLocale';
import { IGlobalModelState } from '@/models/global';
import ModuleCfg, {CUSTOMER_HNLG, CUSTOMER_NPU, CUSTOMER_UTCM} from '@/permission/moduleCfg';
import { useDispatch, useSelector } from '@@/plugin-dva/exports';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Badge, Menu, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import { Link, useHistory } from 'react-router-dom';
import { history } from 'umi';
import './Sidebar.less';

interface IItemProps {
  code: string;
  clickCallback: (item: any) => void;
}

const Sidebar: React.FC<IItemProps> = props => {

  const { t } = useLocale();
  let key = useHistory().location.pathname;
  const [selectedKey, setSelectKey] = useState(
    key !== '/resource' && key !== '/' ? key : '/course/classreview',
  );

  const permissionModules = useSelector<any, any>(
    ({ jurisdiction }) => jurisdiction.modules,
  );

  const parameterConfig = useSelector<any, any>(
    ({ global }) => global.parameterConfig,
  );

  const jurisdictionList = useSelector<{ jurisdiction: any }, any>(
    ({ jurisdiction }) => {
      return jurisdiction.jurisdictionList;
    },
  );

  const myReviewCount = useSelector<{ microCourse: any }, any>(
    ({ microCourse }) => {
      return microCourse.myReviewCount;
    },
  );
  const [isHnLg,setIsHnLg] = useState(false)
  const { userInfo } = useSelector<any, any>(state => state.global);
  const [reviewCount, setReviewCount] = useState<number>(0);
  const dispatch = useDispatch();
  const { parameterConfigObj } = useSelector<
    { global: IGlobalModelState },
    IGlobalModelState
  >(state => state.global);

  const [showtip, setShowtip] = useState<boolean>(false);

  function changeMenu(item: any) {
    // console.log("item", item);
    props.clickCallback(item);
  }
  function locationChange() {
    if (key === '/course/classreview') {
      if (permissionModules.includes(ModuleCfg.classreview)) {
        return;
      } else if (permissionModules.includes(ModuleCfg.micro)) {
        history.push('/course/microcourse');
      } else if (permissionModules.includes(ModuleCfg.mooc)) {
        history.push('/course/mooccourse');
      } else if (permissionModules.includes(ModuleCfg.spoc)) {
        history.push('/course');
      } else if (permissionModules.includes(ModuleCfg.training)) {
        history.push('/course/trainingCourse');
      } else {
        history.push('/course/space');
      }
    }
    if (key === '/course') {
      if (permissionModules.includes(ModuleCfg.spoc)) {
        return;
      } else if (permissionModules.includes(ModuleCfg.mooc)) {
        history.push('/course/mooccourse');
      } else if (permissionModules.includes(ModuleCfg.micro)) {
        history.push('/course/microcourse');
      } else if (permissionModules.includes(ModuleCfg.training)) {
        history.push('/course/trainingCourse');
      } else if (permissionModules.includes(ModuleCfg.map)) {
        history.push('/course/mapCourse');
      }
    }
  }
  // // console.log(useHistory().location)
  // useHistory().listen(location => {
  //   if (location.pathname !== '/resource' && location.pathname !== '/') {
  //     setSelectKey(location.pathname);
  //   } else {
  //     setSelectKey('/course');
  //   }
  // });

  useEffect(() => {
    if (key !== '/resource' && key !== '/') {
      setSelectKey(key);
    } else {
      // setSelectKey('/course');
      setSelectKey('/course/classreview');
    }
    if (permissionModules.length) {
      locationChange();
    }
  }, [permissionModules, key]);

  useEffect(() => {
    console.log(userInfo.roles?.map((item: any) => item.roleCode))
    const isHave =  userInfo.roles?.map((item: any) => item.roleCode)?.includes('r_sys_manager') ||
      userInfo.roles?.map((item: any) => item.roleCode)?.includes('r_course_manager')
    console.log('parameterConfig.target_customer',parameterConfig.target_customer,isHave)
    setIsHnLg(isHave)
    if (
      (userInfo.roles
        ?.map((item: any) => item.roleCode)
        ?.includes('r_sys_manager') || //是否是系统管理员
        userInfo.roles
          ?.map((item: any) => item.roleCode)
          ?.includes('r_course_manager') || //是否是课程管理员
        userInfo.roles
          ?.map((item: any) => item.roleCode)
          ?.includes('r_second_manager') || //第二权限
        userInfo.roles
          ?.map((item: any) => item.roleCode)
          ?.includes('admin_S1')) && // admin_S1
      (parameterConfig?.microcourse_course_release_review === 'true' ||
        parameterConfig?.mooc_course_release_review === 'true' ||
        parameterConfig?.training_course_release_review === 'true' ||
        parameterConfig?.mooc_course_release_review === 'true')
    ) {
      getCourseListNew({
        courseType: -1,
        approvalStatus: 1,
        page: 1,
        size: 24,
        subjectId: [],
        teacher: [],
      }).then((res: any) => {
        if (res.status === 200) {
          setReviewCount(res.data.total);
          dispatch({
            type: 'microCourse/updateState',
            payload: {
              myReviewCount: res.data.total,
            },
          });
        }
      });
    }
  }, [parameterConfig, userInfo]);
  useEffect(() => {
    setReviewCount(myReviewCount);
  }, [myReviewCount]);
  console.log
  const handleTitleChange = (title: string) => {
    // document.title = title
    window.sessionStorage.setItem('title', JSON.stringify(title))
  }

  const getMenu = () => {
    let menuItem;
    if (props.code === 'portal') {
      menuItem = (
        <Menu
          selectedKeys={[selectedKey]}
          mode="inline"
          theme="light"
          inlineIndent={16}
        >
          <Menu.Item key="/portalconfiguration">
            <Link to="/portalconfiguration">{t('Banner配置')}</Link>
          </Menu.Item>
          <Menu.Item key="/portalconfiguration/classifiedconfiguration">
            <Link to="/portalconfiguration/classifiedconfiguration">
              {t('分类配置')}
            </Link>
          </Menu.Item>
        </Menu>
      );
    } else {
      menuItem = (
        <Menu
          selectedKeys={[selectedKey]}
          defaultOpenKeys={['mycourse']}
          mode="inline"
          inlineIndent={16}
          theme="light"
          onClick={changeMenu}
        >
          <Menu.SubMenu
            key="mycourse"
            title={
              <>
                <IconFont type="iconweikechengguanli" />
                {t('我的课程')}
              </>
            }
          >
            {parameterConfig.target_customer === CUSTOMER_UTCM && (
              <Menu.Item>
                <div
                  onClick={() =>
                    window.open(
                      'https://sso.cdutcm.edu.cn/esc-sso/oauth2.0/authorize?client_id=07e86d9d897cfb46b01b&response_type=code&redirect_uri=https%3A%2F%2Fzsjk.cddmi.cn%2Fzxjy%2Fzyd_sso%2FcheckAuthCode%3Fredirect%3D%2Fuser%2Fmanagement%2Fteacher%2FcourseClass%2FcourseManagement',
                    )
                  }
                >
                  {t('校内spoc')}
                </div>
              </Menu.Item>
            )}
            {permissionModules.includes(ModuleCfg.spoc) && (
              <Menu.Item key="/course" style={{ position: 'relative' }}>
                <Link to="/course">
                  {/* <IconFont type="iconSPOCkechengguanli" /> */}
                  <Tooltip
                    placement="right"
                    title={`${t('班级课')}${t(
                      '是同步课表开设的线上课程，同原课程平台课程开设逻辑',
                    )}`}
                    open={showtip}
                  >
                    <ExclamationCircleOutlined
                      onMouseEnter={() => setShowtip(true)}
                      onMouseLeave={() => setShowtip(false)}
                      style={{
                        fontSize: "14px",
                        position: 'absolute',
                        left: '10px',
                        top: '12px',
                        color:
                          selectedKey === '/course'
                            ? 'var(--primary-color)'
                            : '',
                      }}
                    />
                    {t('班级课')}
                  </Tooltip>
                </Link>
              </Menu.Item>
            )}

            {permissionModules.includes(ModuleCfg.mooc) && (
              <Menu.Item key="/course/mooccourse">
                <Link to="/course/mooccourse">
                  {/* <IconFont type="iconMOOCkechengguanli" /> */}
                  {t('公开课')}
                </Link>
              </Menu.Item>
            )}

            {permissionModules.includes(ModuleCfg.micro) && (
              <Menu.Item key="/course/microcourse">
                <Link to="/course/microcourse">
                  {/* <IconFont type="iconweikechengguanli" /> */}
                  {t('微课')}
                </Link>
              </Menu.Item>
            )}

            {permissionModules.includes(ModuleCfg.training) && (
              <Menu.Item key="/course/trainingCourse">
                <Link to="/course/trainingCourse">
                  {/* <IconFont type="iconweikechengguanli" /> */}
                  {t('培训课')}
                </Link>
              </Menu.Item>
            )}
            {permissionModules.includes(ModuleCfg.map) && (
              <Menu.Item key="/course/mapCourse">
                <Link to="/course/mapCourse">
                  {/* <IconFont type="iconweikechengguanli" /> */}
                  {t('图谱课')}
                </Link>
              </Menu.Item>
            )}
            {parameterConfig?.show_workbench_videos === 'true' && (
              <Menu.Item key="/course/videolist">
                <Link to="/course/videolist">{t('视频')}</Link>
              </Menu.Item>
            )}

            {parameterConfig?.show_third_party_courses === 'true' && (parameterConfig.target_customer !== CUSTOMER_HNLG || (parameterConfig.target_customer === CUSTOMER_HNLG && isHnLg)) && (
              <>
                {JSON.parse(
                  parameterConfig?.show_third_party_courses_list ?? '[]',
                ).map((item: string) => (
                  <Menu.Item key={`/course/dockcourse/${item}`}>
                    <Link onClick={() => handleTitleChange(item)} to={`/course/dockcourse/${item}`}>{item}</Link>
                  </Menu.Item>
                ))}
              </>
            )}
            {parameterConfigObj.spoc_kcgl?.includes(
              'spoc_course_live_display',
            ) &&
              jurisdictionList.includes('spoc_live_look') && (
                <Menu.Item key="/course/liveCourse">
                  <Link to="/course/liveCourse">{t('直播课程信息')}</Link>
                </Menu.Item>
              )}
          </Menu.SubMenu>
          {permissionModules.includes(ModuleCfg.classreview) && (
            <Menu.Item key="/course/classreview">
              <Link to="/course/classreview">
                <IconFont type="iconketanghuikan" />
                {t('在线课堂')}
              </Link>
            </Menu.Item>
          )}
          {permissionModules.includes(ModuleCfg.courseMap) && parameterConfig.target_customer === CUSTOMER_NPU && <Menu.SubMenu
            key="courseMap"
            title={
              <>
                <IconFont type="icona-ditulei_ditu1" />
                {t('课程地图')}
              </>
            }
          >
            <Menu.Item key="/course/minemap">
              <Link to="/course/minemap">
                我的地图
              </Link>
            </Menu.Item>
            <Menu.Item key="/course/sharetemap">
              <Link to="/course/sharetemap">
                共享地图
              </Link>
            </Menu.Item>
            <Menu.Item key="/course/map_recycle">
              <Link to="/course/map_recycle">
                回收站
              </Link>
            </Menu.Item>
          </Menu.SubMenu>}
          {
            parameterConfig.target_customer === CUSTOMER_NPU && permissionModules.includes(ModuleCfg.aiAgent) && <Menu.Item key="/course/agent">
              <Link to="/course/agent">
                <IconFont type="icontikuguanli" />
                {t('AI Agent')}
              </Link>
            </Menu.Item>
          }

          {(userInfo.roles
            ?.map((item: any) => item.roleCode)
            ?.includes('r_sys_manager') || //是否是系统管理员
            userInfo.roles
              ?.map((item: any) => item.roleCode)
              ?.includes('r_course_manager') || //是否是课程管理员
            userInfo.roles
              ?.map((item: any) => item.roleCode)
              ?.includes('r_second_manager') || //第二权限
            userInfo.roles
              ?.map((item: any) => item.roleCode)
              ?.includes('admin_S1')) && // admin_S1
            (parameterConfig?.microcourse_course_release_review === 'true' ||
              parameterConfig?.microcourse_course_special_review === 'true' ||
              parameterConfig?.mooc_course_release_review === 'true') && (
              <Menu.Item key="/course/myReview">
                <Link to="/course/myReview">
                  <Badge count={reviewCount} offset={[20, 8]}>
                    <IconFont type="iconshenhe" />
                    {t('我的审核')}
                  </Badge>
                </Link>
              </Menu.Item>
            )}

          {permissionModules.includes(ModuleCfg.mylive) && (
            <Menu.Item key="/course/myLive">
              <Link to="/course/myLive">
                <IconFont type="iconhuodongzhiboguanli" />
                {t('我的直播')}
              </Link>
            </Menu.Item>
          )}
          <Menu.Item key="/course/recycle">
            <Link to="/course/recycle">
              <IconFont type="iconhuishouzhan" />
              {t('回收站')}
            </Link>
          </Menu.Item>
        </Menu>
      );
    }
    return menuItem;
  };
  return <div className="sidebar">{getMenu()}</div>;
};

export default Sidebar;
