import React, { FC, useEffect, useState } from 'react';
import './index.less';
import { useDispatch, useSelector } from '@@/plugin-dva/exports';
import { Dropdown, Image, Menu, message, Divider, Popover, Badge } from 'antd';
// import config from '@/utils/config';
import { IconFont } from '@/components/iconFont/index';
import { globalParams, ModuleCfg2 } from '@/permission/moduleCfg';
import perCfg from '@/permission/config';
import { IGlobal } from '@/models/global';
import Icon from '@ant-design/icons';
import { ReactComponent as clip_icon } from '@/assets/imgs/icon/clip_icon.svg';
import { ReactComponent as literature_icon } from '@/assets/imgs/icon/literature_icon.svg';
import themeService from "@/api/theme";
import MessageBox from "@/components/Message";
import { BarsOutlined } from "@ant-design/icons";
import useLocale from '@/hooks/useLocale';
import { divide } from 'lodash';
interface LinkItem {
  key: string;
  name: string;
  href?: string;
  target?: string;
  disabled?: boolean;
}

interface HeaderProps {
  subtitle?: string;
  navList?: LinkItem[];
  showNav?: boolean;
  navActive?: string;
  ifBack?: boolean;
}

interface NavProps {
  list: LinkItem[];
  active?: string;
}

interface UserAvatarProps {
  username: string;
  avatar: string;
  work: boolean;
  // teacher: boolean;
  student: boolean;
  admin: boolean;
  // rman: boolean;
  // joveone: boolean;
  // personal: boolean;
  workUrl: string;
  onLogout: () => void;
  targetCustomer: string;
}

const Nav: FC<NavProps> = ({ list, active }) => {
  const { t } = useLocale();
  const { title } = useSelector<any, any>(
    (state) => state.themes);

  const [navList, setNavList] = useState<any>([]);
  const [dropdownList, setDropdownList] = useState<any>([]);
  useEffect(() => {
    if (list.length > 0) {
      handleChange();
      window.addEventListener('resize', handleChange);
      return () => {
        window.removeEventListener('resize', handleChange);
      };
    }
  }, [list]);
  const handleChange = () => {
    if (list.length > 0) {
      const width = window.innerWidth > 1300 ? window.innerWidth : 1300;
      const logoWidth = title.length * 20; // 一个字20px
      const otherWidth = 470; // logo + 工具栏 + 消息 + 用户 + 间距
      const navWidth = width - logoWidth - otherWidth;
      let curWidth = 0;
      const i = list.findIndex((item: any) => {
        curWidth += item.name.length * 14 + 40;
        console.info(curWidth, navWidth);
        return curWidth > navWidth;
      });
      const index = i === -1 ? list.length : i - 1;
      const nav = list.slice(0, index);
      console.info(nav);
      const other = list.slice(index);
      setNavList(nav);
      setDropdownList(other);
    }
  };
  return (
    <div className="nav-menu-wrapper">
      {navList.
        filter((l) => !l.disabled).
        map((link: any, index: number) =>
          link.href ?
            <a
              key={index}
              className={active === link.key ? 'active' : ''}
              href={link.href}
              target={link.target}>

              <div className="nav-item">{link.name}</div>
            </a> :

            <span key={index}>{link.name}</span>)}


      {navList.length < list.length && <Dropdown menu={{ items: dropdownList }} placement="bottom" getPopupContainer={(e: any) => e.parentNode} dropdownRender={(menus: any) => {
        const items = menus.props.items;
        return <div className='drop-container'>{items.map((item: any) => <div className='drop-item' onClick={() => {
          if (item.href) {
            window.open(item.href, item.target || '_self');
          }
        }}>{item.name}</div>)}</div>;
      }}>
        <span key="more" style={{ cursor: "pointer", color: "#666" }}>{t("更多")}</span>
      </Dropdown>}
    </div>);

};

const UserAvatar: FC<UserAvatarProps> = ({
  workUrl,
  work,
  onLogout,
  student,
  admin,
  username,
  avatar,
  targetCustomer
}) => {
  // let workUrl = ""
  // let target = ""
  // if (teacher) {
  //   workUrl = "#/course"
  //   target = "my_teaching"
  // } else if (rman) {
  //   workUrl = "/rman/#/basic/rmanCenterList"
  //   target = "source_manage"
  // } else if (admin) {
  //   workUrl = "/unifiedplatform/#/basic"
  //   target = "sys_manage"
  // } else if (joveone) {
  //   workUrl = "/joveone"
  //   target = "joveone"
  // }
  const { t } = useLocale();
  const handleStatData = (url: string, name: string) => {
    themeService.statData({ moduleCode: url, moduleName: name })
  }
  const menu =
    <Menu>
      {work &&
        <Menu.Item onClick={() => handleStatData(workUrl, "主页")}>
          <a href={workUrl} target="work">{
            targetCustomer == 'shangHaiTech' ? t("工作空间") : t("工作台")
          }</a>
        </Menu.Item>}

      {student &&
        <Menu.Item onClick={() => handleStatData("/unifiedplatform/#/learn/mycourse", "我的学习")}>
          <a href="/unifiedplatform/#/learn/mycourse" target="my_study">{t("我的学习")}</a>
        </Menu.Item>}

      {admin &&
        <Menu.Item >
          {/* <a href="/unifiedplatform/#/personal/info" target="personal_center">账号管理</a> */}
          <a href="/unifiedplatform/#/management" target="sys_manage">{t("管理中心")}</a>
        </Menu.Item>}

      <Menu.Item onClick={onLogout}>
        <a>{t("退出登录")}</a>
      </Menu.Item>
    </Menu>;

  const microMajorMenu = <Menu><Menu.Item onClick={onLogout}><a>{t("退出登录")}</a></Menu.Item></Menu>

  const getMenu = () => {
    if (location.hash.includes('micromajor')) {
      return microMajorMenu
    }
    return menu
  }

  return (
    <Dropdown overlay={() => getMenu()} className="user-avatar-wrapper">
      <div>
        <Image
          src={avatar || require('@/assets/imgs/myHeader/default-avatar.png')}
          fallback={require('@/assets/imgs/myHeader/default-avatar.png')}
          preview={false} />

        <p className="user-name" title={username}>{username}</p>
      </div>
    </Dropdown>);

};

const OtherNav: FC<NavProps> = ({ list }) => {
  const { t } = useLocale();
  const getIcon = (iconName: string) => {
    const ClipIcon = (props: any) => <Icon component={clip_icon} {...props} />;
    const LiteratureIcon = (props: any) => <Icon component={literature_icon} {...props} />;
    switch (iconName) {
      case "joveone":
        return <IconFont type='iconzaixianbianji' />;
      case "literature":
        return <LiteratureIcon />;
      case "textclip":
        return <IconFont type='iconyuyinbianji' />;
      default:
        return <span></span>;
    }

  };
  const menu =
    <Menu>
      {list.
        filter((l) => !l.disabled).
        map((item: any, index: number) => {
          return <Menu.Item>
            <a key={item.key} className='other' href={item.href} target={item.target}>{getIcon(item.key)}{item.name}</a>
          </Menu.Item>;
        })}

    </Menu>;

  return (
    <div className='other-nav-wrapper'>
      {
        list.filter((l) => !l.disabled).length > 0 &&
        <Dropdown overlay={menu}>
          <div>
            <IconFont type='icongongjuxiang' />{t("工具箱")}

          </div>
        </Dropdown>}

    </div>);

};

const Header: FC<HeaderProps> = ({
  subtitle,
  showNav,
  navActive,
  ifBack = true
}) => {
  const { t } = useLocale();
  const { userInfo } = useSelector<any, IGlobal>((state) => state.global);
  const [headerList, setHeaderList] = useState<any[]>([]);
  const [unReadCount, setUnreadCount] = useState<number>(0);
  const { menuShow } = useSelector<any, any>((state) => state.config);
  const dispatch = useDispatch();

  const microHeader = location.hash.includes('micromajor');  // 微专业版
  const { title, logoUrl, isShow } = useSelector<any, any>(
    (state) => state.themes);

  const { permission, buttonPermission, rmanGlobalParameter, parameterConfig } = useSelector<
    { global: any; },
    { permission: string[]; buttonPermission: string[]; rmanGlobalParameter: any[]; parameterConfig: any }>(
      (state) => state.global);

  const isSH = parameterConfig.target_customer == 'shangHaiTech'
  //是否是成信大
  // const isCx = parameterConfig.target_customer == 'chengXinDa'
  const [isCx, setIsCx] = useState<boolean>(parameterConfig.target_customer == 'kczx');
  useEffect(() => {
    // themeService.fetchHeaderList().then(res => {
    //   if (res.errorCode === 'success') {
    //     const list = res.extendMessage.filter((item: any) => item.name !== '在线剪辑').map((item: any) => ({
    //       key: item.isSystem ? item.link.split('#/')?.[1]?.split("/")?.[0] : "key",
    //       name: item.name,
    //       href: item.link,
    //       target: item.openWay ? item.link.split('/')?.[1] || item.name : null,
    //       disabled: item.disabled
    //     }));
    //     console.log(list);
    //     setHeaderList(list);
    //   }
    // });
    getUnreadCount();
    if (window.innerWidth <= 768) {
      menuShowChange();
    }
    const timer = setInterval(getUnreadCount, 10000);
    return () => {
      clearInterval(timer);
    };
  }, []);

  const handleLogout = async () => {
    window.location.href = `/unifiedlogin/v1/loginmanage/loginout/go`;
    // let res = await api.user.logout();
    // if (res && res.errorCode === 'success') {
    //   message.success('注销成功');
    //   // history.push('/basic?action=login');
    //   // window.location.replace(`/cvodweb`);
    // }
  };

  const getUnreadCount = () => {
    const params = microHeader ? { messageType: 'micro_major' } : undefined;
    themeService.reqUnreadMessageCount(params).then((res) => {
      if (res.errorCode === 'success') {
        setUnreadCount(res.extendMessage);
      }
    });
  };
  const menuShowChange = () => {
    dispatch({
      type: 'config/menuShowChange',
      payload: {
        value: !menuShow
      }
    });
  };

  const handleJump = () => {
    window.location.href = '/rman/#/task/taskprogress';
  }

  const getLogHef = () => {
    let href = ""
    if (location.hash.includes('micromajor')) {
      href = '/learn/search/microMajor'
    }
    else {
      // '/unifiedplatform/v1/app/mainpage/direction'
      href = process.env.NODE_ENV === 'development' ? 'http://172.16.151.202' : '' + '/learn'
    }
    return href
  }

  const getCxdLogTitle = () => {
    return <h2>知了智慧教育资源管理平台</h2>
  }

  const getLogTitle = () => {
    let name: any = ''
    if (location.hash.includes('micromajor')) {
      name = '微专业平台'
    } else {
      name = isShow ? (isSH ? t('工作空间') : t("工作台")) : null
    }
    return name ? <h2>{name}</h2> : null
  }

  return (
    <div className="uf-header-wrapper">
      <div className="uf-header-left-part">
        <IconFont type='iconwenzhangpailie2-221' onClick={() => menuShowChange()} />
        <a
          href={getLogHef()}
          className="home-part">
          <img className="icon-home" src={require('@/assets/imgs/myHeader/home.png')} />
          {/* {ifBack && (
             <div className="go-back-btn">
               <IconFont type="iconjiantouda" />
             </div>
            )} */}
          <img
            src={logoUrl || require('@/assets/imgs/myHeader/default_logo.png')}
            className="uf-header-icon" />

          {isCx ? getCxdLogTitle() : getLogTitle()}
        </a>
      </div>
      <div className="uf-header-right-part">
        {/* {showNav && <Nav list={headerList} active={navActive} />}
          {<OtherNav list={[
           {
             key: 'joveone',
             name: '在线剪辑',
             href: '/joveone',
             target: "joveone",
             disabled:
               !permission.includes(ModuleCfg2.jove),
           },
           {
             key: 'textclip',
             name: '语音剪辑',
             href: '/textclip/#/clip/myTextClip',
             target: "textclip",
             disabled: !permission.includes(ModuleCfg2.textclip),
           }
          ]} />} */}

        <div onClick={handleJump}><IconFont type="iconrenwuguanli" style={{ fontSize: 16, color: '#000', fontWeight: 'bold', marginRight: '10px', cursor: 'pointer' }} /></div>
        <Popover overlayClassName='message-popover' destroyTooltipOnHide={true} content={<MessageBox readChange={getUnreadCount} microHeader={microHeader} />}>
          <Badge count={unReadCount} offset={[5, 0]}>
            <div className='message-container'><IconFont type="iconxiaoxitongzhi" /></div>
          </Badge>
        </Popover>
        <UserAvatar
          username={userInfo?.nickName}
          avatar={userInfo?.avatar}
          workUrl={"/unifiedplatform/v1/app/workbench/direction"}
          work={permission.includes(ModuleCfg2.work)}
          // joveone={permission.includes(ModuleCfg2.jove) && buttonPermission.includes(perCfg.jove_use)}
          // teacher={permission.includes(ModuleCfg2.teacher)}
          student={permission.includes(ModuleCfg2.student)}
          admin={permission.includes(ModuleCfg2.manager)}
          // rman={
          //   permission.includes(ModuleCfg2.rman) &&
          //   buttonPermission.includes(perCfg.show_resource_management)
          // }
          // personal={permission.includes(ModuleCfg2.personal)}
          onLogout={handleLogout}
          targetCustomer={parameterConfig.target_customer}
        />

      </div>
    </div>);

};
export default Header;
