import React, { useState, useEffect, FC } from "react";
import './index.less';
import { Select, Radio, Button, Input, Tooltip, Dropdown } from 'antd';
import type { MenuProps } from 'antd';
import { useLocation, useSelector, useDispatch } from 'umi';
import { DataUri } from '@antv/x6';
import { useBus, useListener } from 'react-bus';
import useLocale from "@/hooks/useLocale";

import { FolderOutlined, HistoryOutlined,UnorderedListOutlined,ExportOutlined,EyeOutlined,EyeInvisibleOutlined, SearchOutlined } from "@ant-design/icons";

const Header: FC<any> = ({ graph,skin='1', setInputtext, search, setSelecttype, typeonselect, maptype, querytype, inputtext, setVisible, perviewtype,setLayouttype,updataskin}) => {
  const [checked, setChecked] = useState<boolean>(true);
  // 获取url参数
  const { query }: any = useLocation();
  const bus = useBus();
  const { t } = useLocale();
  const parameterConfig = useSelector<any, any>(
    ({ global }) => global.parameterConfig,
  );
  
  const items: MenuProps['items'] = [
    {
      key: '1',
      label: (<span onClick={() => {
        graph.toPNG((dataUri: string) => {
          // 下载
          DataUri.downloadDataUri(dataUri, '专业图谱.png');
        }, {
          copyStyles: true,
          backgroundColor: skin == '1' ? '#000' : '#fff',
          padding: {
            top: 20,
            right: 30,
            bottom: 40,
            left: 50
          }
        });
      }}>导出为PNG格式</span>),
    },
    {
      key: '2',
      label: (<span onClick={() => {
        graph.toJPEG((dataUri: string) => {
          // 下载
          DataUri.downloadDataUri(dataUri, '专业图谱.jpg');
        }, {
          copyStyles: true,
          backgroundColor: skin == '1' ? '#000' : '#fff',
          padding: {
            top: 20,
            right: 30,
            bottom: 40,
            left: 50
          }
        });
      }}>导出为JPEG格式</span>),
    }
    // {
    //   key: '3',
    //   label: (<span onClick={() => {
    //     graph.toSVG((dataUri: string) => {
    //       // 下载
    //       DataUri.downloadDataUri(DataUri.svgToDataUrl(dataUri), '专业图谱.svg')
    //     })
    //   }}>导出为SVG格式</span>),
    // }
  ]

  return (
    <div className={skin == '1' ? 'mapv4_heard_view': 'mapv4_heard_view_dark'}>
      {/* <div className="left">


        { maptype == 2 && (perviewtype == 1 || perviewtype == 2) && query.courseid && <Button type="primary" onClick={() => setVisible(12)} style={{ marginTop: '-23px', marginLeft: '25px', backgroundColor: '#404F65', borderColor: '#404F65' }}>{t("知识点达成度")}</Button>}

      </div> */}
      <div className="right_v4">
      <div className="search_view">
          <div className="left_select">
            {/* <img className="icon3" src={require('@/assets/imgs/coursemap/v3/icon3.png')} alt="" /> */}
            <Select
              defaultValue={t("全部")}
              style={{ width: '100%',height:'100%'}}
              bordered={false}
              options={(query.type == 'micromajor' || query.type == 'microMajor') ? [
                {
                  value: '0',
                  label: t("全部")
                },
                {
                  value: '1',
                  label: t("分类节点")
                },
                {
                  value: '2',
                  label: t("知识节点")
                },
                {
                  value: '3',
                  label: t("课程节点")
                },
                {
                  value: '4',
                  label: t("专业节点")
                },
                {
                  value: '5',
                  label: t("教学模块")
                }]:
                [
                {
                  value: '0',
                  label: t("全部")
                },
                {
                  value: '1',
                  label: t("分类节点")
                },
                {
                  value: '2',
                  label: t("知识节点")
                }]}

              onSelect={(e: any) => { setSelecttype(e); }} />

          </div>
          <div className="right_view">
            <Input placeholder={t("请输入关键词")} type="text" bordered={false} value={inputtext} onChange={setInputtext} onPressEnter={search} />
            {/* <input placeholder="请输入关键词" type="text" value={inputtext} onChange={setInputtext} onEncryptedCapture={search} /> */}
            {/* <img className="icon3" src={require('@/assets/imgs/coursemap/v3/icon4.png')} onClick={search} /> */}
            <SearchOutlined className="icon3" onClick={search} />
          </div>
        </div>
        <div className="select1">
          {/* <img className="icon3" src={require('@/assets/imgs/coursemap/v3/bg4.png')} alt="" /> */}
          <Select
            // defaultValue="0"
            value={querytype}
            style={{ width: '90%' }}
            bordered={false}
            options={[
              {
                value: '0',
                label: t("全部节点")
              },
              {
                value: '2',
                label: t("有课程资源")
              },
              {
                value: '9',
                label: t("有试题")
              },
              {
                value: '10',
                label: t("有讲解")
              },
              {
                value: '1',
                label: t("重难点")
              },
              {
                value: '5',
                label: t("核心知识点")
              },
              {
                value: '6',
                label: t("拓展知识点")
              },
              {
                value: '7',
                label: t("案例")
              },{
                value: '8',
                label: t("实验")
              },
              {
                value: '3',
                label: t("对比辨析")
              },
              {
                value: '4',
                label: t("跨课")
              },
              {
                value: '11',
                label: t("无讲解")
              },{
                value: '12',
                label: t("无课程资源")
              }]}

            onSelect={typeonselect} />

        </div>
      <div className="option_view">
          <Tooltip title={checked ? "显示中心节点" : "隐藏中心节点"}>
            {
              checked ? <EyeOutlined style={{color:'#d9d9d9',fontSize:'20px',marginRight:'10px'}} onClick={()=>{
                bus.emit('showcenter', !checked);
                setChecked(!checked);
              }} /> : <EyeInvisibleOutlined style={{color:'#d9d9d9',fontSize:'20px',marginRight:'10px'}} onClick={()=>{
                bus.emit('showcenter', !checked);
                setChecked(!checked);
              }} />
            }
          </Tooltip>

          <Dropdown menu={{items}}  placement="bottom" arrow={{ pointAtCenter: true }}>
            <ExportOutlined style={{color:'#d9d9d9',fontSize:'18px'}}  />
          </Dropdown>
        </div>

        {parameterConfig.layout_updata == 'true' && <Select
          defaultValue={1}
          style={{ width: 120, marginLeft: '20px' }}
          onChange={(e) => setLayouttype(e)}
          options={[
            {
              value: 1,
              label: '辐射布局'
            },
            {
              value: 2,
              label: '树形布局'
            }]} />}

        <Select
          defaultValue="2"
          style={{ width: 120, marginLeft: '20px' }}
          onChange={(e)=>{
            updataskin(e);
            bus.emit('updataskin', e);
          }}
          options={[
            {
              value: '1',
              label: t("黑夜模式")
            },
            {
              value: '2',
              label: t("白天模式")
            }]} />
      </div>
    </div>);

};

export default Header;
