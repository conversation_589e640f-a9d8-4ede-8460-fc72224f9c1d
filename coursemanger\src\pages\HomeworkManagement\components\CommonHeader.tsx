import React, { FC, useEffect, useState } from 'react';
import moment from "moment";
import "./CommonHeader.less";
import PreviewHomeworkModal from "./PreviewHomeworkModal";
import { getHomeworkDetail } from "@/api/homework";
import { useLocation } from "umi";
import useLocale from '@/hooks/useLocale';

interface ICommonHeader {
  homeworkItem: any;
  showPreview?: boolean;
  showFrom?: boolean;
  onGetDetail?: (obj: any) => void;
}

const CommonHeader: FC<ICommonHeader> = ({ homeworkItem, showPreview, showFrom, onGetDetail }) => {
  const { t } = useLocale();
  const location: any = useLocation();
  const [previewVisible, setPreviewVisible] = useState<boolean>(false);
  const [topicData, setTopicData] = useState<any>([]);
  const [showHomeworkItem, setShowHomeworkItem] = useState<any>({});
  useEffect(() => {
    console.log('【homeworkItem】', homeworkItem)
    setShowHomeworkItem({ ...homeworkItem })
    if (showPreview && homeworkItem) {
      handleGetHomeworkDetail();
    }
  }, [homeworkItem]);

  const handleGetHomeworkDetail = () => {
    getHomeworkDetail("resource", homeworkItem.id, homeworkItem.parentId, location.query.id).then((res: any) => {
      // console.log('【detail】',res)
      if (res.status === 200) {
        if (location.query.type === 'microMajor') {
          setShowHomeworkItem({ ...res.data, ...homeworkItem })
        }
        onGetDetail && onGetDetail(res.data);
        setTopicData(res.data.questions.map((item: any) => {
          item.question?.questions_options?.map((item_: any, index: number) => {
            item_.seq = index + 1;
            item_.content = item_.content;
          });
          return {
            ...item.question,
            topicId: item.question?.id,
            ...item
          };
        }));
      }
    });
  };
  const handlePreview = () => {
    if (showPreview) {
      setPreviewVisible(true);
    }
  };
  return <div className='common-header'>
    <div className={`${!showFrom ? 'no-show-from-box' : 'homework-header-style'}`}>
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <div className={`title ${showPreview ? 'preview' : ''}`} onClick={handlePreview}>{showHomeworkItem.name}</div>
        {showFrom && <div className="from">
          <p>{t("完成方式：")}<span style={{ color: 'rgba(0,0,0,0.7)' }}>{showHomeworkItem.howIsDone == 1 ? '个人' : '小组'}</span></p>
          <p>{t("所属章节：")}<span style={{ color: 'rgba(0,0,0,0.7)' }}>{showHomeworkItem.sectionName || showHomeworkItem.chapterName}</span></p>
        </div>}
      </div>

      {
        homeworkItem.resourseType == 'homework' ? <div className='grade-time'>
          <div className="grade">{t("满分：")}<span>{showHomeworkItem.totalScore || 0}{t("分")}</span></div>
          <div className="time">{t("截止时间：")}<span>{moment(showHomeworkItem.closeTime).format("YYYY-MM-DD HH:mm")}</span></div>
        </div> : null
      }
    </div>

    {showPreview && <PreviewHomeworkModal visible={previewVisible} onClose={() => setPreviewVisible(false)} detail={topicData} />}
  </div>;
};

export default CommonHeader;

