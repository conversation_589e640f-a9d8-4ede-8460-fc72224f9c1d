import { message } from 'antd';
import { useEffect, useState } from 'react';
import OOSDK from '@/otherStorage/ctyun';
import aliYun from '@/otherStorage/aliyun';
import { useSelector } from 'umi';
import {
  commonUpload,
  getSinedUrl,
  uploadImport as otherUploadFile,
  storageConfig,
  getAmazonConfig,
} from '@/api/addCourse';

interface ICourse {
  courseName: string;
  courseId: string;
}

export default () => {
  const { fileMap } = useSelector<Models.Store, any>(state => state.global);
  const [loading, setLoading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [uploadStatus, setUploadStatus] = useState<
    'success' | 'exception' | 'active'
  >('active');

  const onUploadProgress = (e: any) => {
    setUploadStatus('active');
    if (e.lengthComputable) {
      const percentComplete = Math.ceil((e.loaded / e.total) * 100); // 获取进度
      setUploadProgress(Math.min(percentComplete, 99));
    }
  };

  const getType = (filename: string) => {
    const fileArr = filename?.split('.');
    return fileArr ? fileArr[fileArr.length - 1]?.toLocaleLowerCase() : '';
  };

  type tFileType = keyof typeof fileMap;

  const getPreviewType = (filePath: string): tFileType => {
    const fileSuffix = getType(filePath);
    const typeArr = Object.keys(fileMap).filter((item: string) =>
      fileMap[item as tFileType].includes(fileSuffix),
    );
    const type = typeArr.length > 0 ? typeArr[0] : '';
    return type as tFileType;
  };

  useEffect(() => {}, []);
  const normalUpload = (file: any, course: ICourse) => {

    const formData = new FormData();
    const { courseName, courseId } = course;
    formData.append('file', file);
    return commonUpload(
      formData,
      { courseName, courseId },
      onUploadProgress,
    ).then((res: any) => {
      if (res.success) {
        return res.data;
      } else {
        message.error(res.error.title || '上传失败');
      }
    });
  };
  const completeUpload = (config: any, course: ICourse) => {
    const { courseName, courseId } = course;
    const params = {
      oosPath: `http://${config.bucket}.${config.endpoint}/${config.key}`,
      fileLength: config.file.size as number,
      courseName,
      courseId,
    };
    return otherUploadFile(params, onUploadProgress).then((res: any) => {
      if (res.success) {
        return res.data;
      } else {
        message.error(res.error.title || '上传失败');
      }
    });
  };
  const otherPlatformsUpload = (config: any, course: ICourse) => {
    return new Promise((resolve, reject) => {
      if (config.product === 'ctyun') {
        OOSDK.init(config);
        OOSDK.putObject({
          Bucket: config.bucket,
          Body: config.file.source,
          Key: config.key,
        }).then((res: any) => {
          if (res.ETag) {
            resolve(config);
          } else {
            reject();
          }
        });
      } else if (config.product === 'aliyun') {
        aliYun.init(config);
        aliYun.putObject(config).then((res: any) => {
          if (res.url) {
            resolve(config);
          } else {
            reject();
          }
        });
      } else if (config.product === 'amazon') {
        getSinedUrl({
          bucket: config.bucket,
          objectPath: config.key,
          requestOper: 1,
          queryParams: {},
          product: config.product,
        })
          .then((res: any) => {
            if (res.data) {
              return res.data;
            } else {
              throw new Error();
            }
          })
          .then(data => {
            const { signUrl, actualSignedRequestHeaders } = data;
            const option = {
              method: 'PUT',
              withCredentials: false,
              headers: actualSignedRequestHeaders || {},
              maxRedirects: 0,
              responseType: 'text',
              data: config.file,
            };
            getAmazonConfig(signUrl, option)
              .then(() => {
                resolve(config);
              })
              .catch(() => {
                reject();
              });
          });
      }
    }).then(config => {
      return completeUpload(config, course);
    });
  };
  const getConfig = (file: any) => {
    const params = [
      {
        fileName: file.name,
        fileLength: file.size,
        fileType: file.type,
      },
    ];
    return storageConfig(params).then((res: any) => {
      if (res.success) {
        const storage_ = res.data[0];
        return { ...storage_, file };
      } else {
        message.error('存储初始化失败，请重试');
        throw new Error();
      }
    });
  };
  const verifyUpload = (name: string, type?: keyof typeof fileMap) => {
    const types: string[] = type ? fileMap[type] :  Reflect.ownKeys(fileMap).reduce(
      (acc, cur) => acc.concat(fileMap[cur]),
      [],
    );
    return types.includes(getType(name));
  };
  const uploadImport = (file: any, course: ICourse, type?: keyof typeof fileMap) => {
    if (!verifyUpload(file.name, type)) {
      message.warning('暂不支持该类型文件上传，请重新上传');
      return Promise.reject();
    }
    setLoading(true);
    return getConfig(file)
      .then((config: any) => {
        if (config.access_type === 'NAS') {
          return normalUpload(file, course);
        } else {
          return otherPlatformsUpload(config, course);
        }
      })
      .then((data: any) => {
        setUploadStatus('success');
        setUploadProgress(100);
        return data;
      })
      .catch(() => {
        setUploadStatus('exception');
        message.error('上传失败');
        throw new Error();
      })
      .finally(() => {
        setLoading(false);
      });
  };
  return {
    loading,
    uploadStatus,
    uploadProgress,
    uploadImport,
    getPreviewType,
  };
};
