import { message } from 'antd';
import HTTP from './index';

export function folder() {
  return HTTP.get(`/rman/v1/folder/init`)
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function folderPublic() {
  return HTTP.get(`/rman/v1/folder/init/public`)
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function folderChildren(folderId: string) {
  // return HTTP.get(`/rman/v1/folder/children?folderId=${folderId}`)
  return HTTP.get(`/rman/v1/folder/children?folderPath=${encodeURIComponent(folderId)}%2F&isChildCount=true`)
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function searchKey(data: any) {
  return HTTP.post('/rman/v1/search/all', data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
export function searchfolder(data: any, params: any) {
  return HTTP.post(`/rman/v1/search/folder?courseId=${params.courseId}`, data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 一键绑定
export function bindretrieval(data: any) {
  return HTTP.post(`/rman/v1/search/association/retrieval?folderPath=${data.folderPath}&pageIndex=${data.pageIndex}&pageSize=${data.pageSize}`, data.keyword)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
export const getSinedUrl = (data: any) => {
  return HTTP.post(`/rman/v1/upload/oss/security/signedurl`, data).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      console.error(error);
    });
};

// 敏感词检测（课程发布）
export function courseOfSensitivew(data: any) {
  return HTTP.post(`/sensitiveword/course`, { ...data })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 敏感词检测（资源引用）
export function sourceOfSensitivew(data: any) {
  return HTTP.post(`/sensitiveword/resource`, data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function searchResList(id: any, path: any, current: number, type: string[] | undefined, pageSize?: number, keyword?: string, condition?: any, sortFields?: any) {
  if (type) {
    condition = [
      ...condition,
      {
        field: 'type_',
        searchRelation: 0,
        value: type,
      },
    ];
  }
  return HTTP.post('/rman/v1/search/folder', {
    folderId: id,
    folderPath: path + '/',
    // path: encodeURIComponent(id),
    keyword: [keyword],
    conditions: condition,
    // sortFields: [
    //   {
    //     field: 'createDate_',
    //     isDesc: true,
    //   },
    // ],
    sortFields: sortFields,
    pageIndex: current,
    pageSize: pageSize || 9,
    anonymous: true
  })
    .then(res => {
      // if (res.status === 200) {
      return res.data;
      // }
    })
    .catch(error => {
      console.error(error);
      return error;
    });
}

export function searchResAll(
  id: any,
  type: string,
  path: string,
  keyword: string,
  starttime: string,
  endtime: string,
  current: number,
  isconvert?: boolean,
  condition?: any,
  sortFields?: any
) {

  let conditions: any = []
  if (type) {
    conditions.push({
      field: 'type_',
      searchRelation: 0,
      value: [type],
    })
  }
  if (isconvert) {
    conditions.push({
      field: "fileext",
      searchRelation: 11,
      value: ["PDF"]
    })
  }
  conditions = conditions.concat(condition ?? []);
  return HTTP.post('/rman/v1/search/all', {
    folderId: id,
    keyword: [keyword],
    folderPath: path,
    conditions: conditions,
    sortFields:sortFields,
    pageIndex: current,
    pageSize: 9,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function folderTree() {
  return HTTP.get(`/rman/v1/folder/all/tree?level=5`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      } else {
        message.error(res);
      }
    })
    .catch(error => {
      console.error(error);
    });
}
export function getTreebylevel(level: number = 2) {
  return HTTP.get(`/rman/v1/folder/all/tree?level=${level}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      } else {
        message.error(res);
      }
    })
    .catch(error => {
      console.error(error);
    });
}
//查询课程
export function courseDetail(id: string) {
  return HTTP.get(`/learn/v1/course/${id}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

//查询资源
export function resourceDetail(id: string, pathType?: number) {
  return HTTP.get(`/rman/v1/entity/base/${id}`, {
    params: {
      pathType,
      isSysAuth: true
    },
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 保存接口
export function saveCourse(data: any) {
  return HTTP.post('/learn/v1/course/create', data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

//删除附件
export function deleteAttchmentApi(courseId: string, fileguid: string) {
  return HTTP.get(`/learn/v1/upload/course/attachment`, { params: { courseId, fileguid } })
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      return error;
    });
}

/**
 * 更新附件名称
 * @param params
 */
export function updateAttachmentName(
  params: MicroCourse.IAttachmentRenameForm,
) {
  return HTTP(
    `/learn/v1/upload/course/attachment/rename`,
    {
      method: 'GET',
      params
    },
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}
//查询我的收藏
export const getmycollectionlist = (data: any) => {
  return HTTP(`/rman/v1/metadata/resource/search/collection`, {
    method: 'POST',
    data: data
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}
//我的录播
export const getmyvideolist = (data: any) => {
  return HTTP(`/rman/v1/search/my/video`, {
    method: 'POST',
    data: data
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}
//查询分享给自己的
export const shareMyself = (data: any) => {
  return HTTP(`/rman/v1/share/search`, {
    method: 'POST',
    data
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}

export const getDetail = (data: any) => {
  return HTTP("/rman/v1/search/entity", {
    method: "POST",
    data
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}
export const commonUpload = (data: any, params: any, onUploadProgress?: any) => {
  return HTTP("/rman/v1/upload/reference/material/import", {
    method: "POST",
    data,
    params,
    timeout: 600000,
    onUploadProgress: event => onUploadProgress(event)
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}
export const storageConfig = (data: any) => {
  return HTTP("/rman/v1/upload/v4/path", {
    method: "POST",
    data
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}
//云储存成功后通知后端逻辑入库
export const uploadImport = (params: any, onUploadProgress?: any) => {
  return HTTP("/rman/v1/upload/v2/reference/material/import", {
    method: "POST",
    params,
    onUploadProgress
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}

//根据名称查询所有符合要求的资源知识点片段
export const getAllpoint = (params: any) => {
  return HTTP("/rman/v1/search/knowledge/point", {
    method: "POST",
    params,
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}

//根据名称查询所有符合要求的资源知识点片段
export const getAllpointbody = (params: any, data: any) => {
  return HTTP("/rman/v1/search/knowledge/point/body", {
    method: "POST",
    params,
    data
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}


//绑定资源和知识点成功添加操作记录
export const addlog = (courseMapNane: string, data: any) => {
  return HTTP("/rman/v1/operation/log/course/map?courseMapNane=" + courseMapNane, {
    method: "POST",
    data
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}

export const reqSemesters = (params: { courseId: string }) => {
  return HTTP("/learn/v1/teaching/semester", {
    method: "GET",
    params
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}

export const addSemester = (data: any) => {
  return HTTP("/learn/v1/teaching/semester", {
    method: "POST",
    data
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}

export const deleteSemester = (data: any) => {
  return HTTP("/learn/v1/teaching/semester/delete", {
    method: "POST",
    data
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}

// 查询所有板块
export const reqAllPlate = () => {
  return HTTP("/learn/v1/dachuanplate/config/list", {
    method: "POST",
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}

// 查询所有专题
export const reqAllSubject = () => {
  return HTTP("/learn/v1/thematic/configure/data/get/config", {
    method: "GET",
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}

// 查询当前板块的审核人
export const reqPlateAuditor = (data: any) => {
  return HTTP("/learn/v1/course/getcheckNames", {
    method: "POST",
    data
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}


// 查询课程推荐资源
export const reqMoreResources = (data: any, params: any) => {
  return HTTP("/rman/v1/search/course/recommend/resource", {
    method: "POST",
    data,
    params
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}
//查询知识点推荐资源
export const queryRecommendResource = (params: any) => {
  return HTTP("/rman/v1/search/knowledge/point/recommend/resource", {
    method: "GET",
    params
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}
//查询推荐知识点
//
export const queryRecommendPoint = (data: any, params: any) => {
  return HTTP("/rman/v1/search/knowledge/point", {
    method: "POST",
    data,
    params
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}

export const getAmazonConfig = (url: string, options: any) => {
  return HTTP(url, options);
};

export const addLog = (data: any) => {
  return HTTP("/learn/v1/course/approval/log/add", {
    method: "POST",
    data
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}

export const reqLogs = (params: { courseId: string }) => {
  return HTTP("/learn/v1/course/approval/log/detail", {
    method: "GET",
    params
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
}


// //添加课程附件关系
// export function addfile(courseId: string,fileId:string) {
//   return HTTP.post(`/learn/v1/micro/add?courseId=${courseId}&fileId=${fileId}`)
//     .then(res => {
//       if (res.status === 200) {
//         return res;
//       }
//     })
//     .catch(error => {
//       console.error(error);
//     });
// }
// // http://172.16.197.74:8080/learn/v1/micro/delete?courseId=7deebd85f6384a86be4ebdd1932b34c6&fileId=b3af2e8e6bde7077f263fa4e075826bd
// //删除课程附件关系
// export function deletefile(courseId: string,fileId:string) {
//   return HTTP.get(`/learn/v1/micro/delete?courseId=${courseId}&fileId=${fileId}`)
//     .then(res => {
//       if (res.status === 200) {
//         return res;
//       }
//     })
//     .catch(error => {
//       console.error(error);
//     });
// }
// //查询附件

// export function queryfile(data:any) {
//   return HTTP.post(`/learn/v1/micro/query`,
//     data
//   )
//     .then(res => {
//       if (res.status === 200) {
//         return res;
//       }
//     })
//     .catch(error => {
//       console.error(error);
//     });
// }
