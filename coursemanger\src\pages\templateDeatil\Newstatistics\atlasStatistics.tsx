import chapterApis from '@/api/chapter';
import { queryColleges } from '@/api/course';
import { querymapbycourse } from '@/api/coursemap';
import statisticsApi from '@/api/statistics';
import { IconFont } from '@/components/iconFont';
import useLocale from '@/hooks/useLocale';
import usePermission from '@/hooks/usePermission';
import { IGlobalModelState } from '@/models/global';
import { ExclamationCircleOutlined, SearchOutlined } from '@ant-design/icons';
import DetailContent3 from "@/pages/templateDeatil/Newstatistics/components/DetailContent3";
import { getNodeInfo } from '@/api/outline';
import {
  Button,
  Empty,
  Input,
  Popconfirm,
  Popover,
  Progress,
  Select,
  Space,
  Table,
  Tabs,
  Tag,
  Tooltip
} from 'antd';
import React, { FC, useEffect, useMemo, useRef, useState } from 'react';
import { history, useLocation, useSelector } from 'umi';
import './atlasStatistics.less';
import Bar<PERSON>hart from './components/BarChart';
import BarLine<PERSON>hart from './components/BarLineChart';
import DetailContent from './components/DetailContent2';
import Echart from './components/Echart';
import KnowledgeDetail from './components/KnowledgeDetail';
import PieChart from './components/PieChart';
import PieChartTwo from './components/PieChartTwo';
import StackLineChart from './components/StackLineChart';
import StudentDetail from './components/StudentDetail';
import { useTargetCustomer } from '@/hooks/useTargetCustomer';
import { useIsSJ } from './hook';
import { Utils } from '@/utils/utils';
import Access from '@/components/Access';
import { useImmer } from 'use-immer';
import { numberToChinese } from '@/utils';
import { SorterResult } from 'antd/es/table/interface';

const { TabPane } = Tabs;
const { Option } = Select;

interface IStudentFilter {
  /** 学院筛选 */
  isSearchCollege?: boolean
  /** 学院列表 */
  collegeList?: string[]
  /** 角色范围筛选 */
  isSearchRole?: boolean
  /** 角色范围 */
  role?: string
}

const Newstatistics: FC = () => {
  const parameterConfig = useSelector<any, any>(
    ({ global }) => global.parameterConfig,
  );
  const { t } = useLocale();
  const resourceoptions: any = [
    { label: t('音频'), value: 'biz_sobey_audio' },
    { label: t('视频'), value: 'biz_sobey_video' },
    { label: t('图片'), value: 'biz_sobey_picture' },
    { label: t('文档'), value: 'biz_sobey_document' },
    { label: t('文件夹'), value: 'biz_sobey_folder' },
    { label: t('超链接'), value: 'biz_sobey_hyperlink' },
    // { label: '作业', value: 'homework' },
    { label: t('其他'), value: 'other' },
  ];
  const { parameterConfigObj, userInfo } = useSelector<
    { global: IGlobalModelState },
    IGlobalModelState
  >(state => state.global);
  // 微专业模块信息
  const microInfo = useSelector<any, any>(state => state.moocCourse)
  const location: any = useLocation();
  // 当前的课程id
  const courseId = location.query.id;
  const courseSemester = location.query.sm;
  const showdetail = location.query.showdetail;
  // 上海交大定制
  const isSJ = useIsSJ()
  const [dataSource1, setDataSource1] = useState<any[]>([]);
  const [dataSource2, setDataSource2] = useState<any[]>([]);

  // 参与学习人数 课程资源数 发布作业数 学生提问数
  const [heardinfo, setHeardinfo] = useState<any>({
    startTime: 0,
    endTime: 0,
    studyTotal: 0,
    publishResourceTotal: 0,
    knowledgePointsTotal: 0,
    publishHomeworkTotal: 0,
    questionTotal: 0,
  });
  const [keyword, setKeyword] = useState<string>('');
  const [keyword2, setKeyword2] = useState<string>('');
  // 排序方式
  const [sortBy, setSortBy] = useState<number>();
  // 排序类型
  const [sortTarget, setSortTarget] = useState<number>();
  // 排序方式
  const [sortBy2, setSortBy2] = useState<number>();
  // 排序类型
  const [sortTarget2, setSortTarget2] = useState<number>();

  // 表格loading
  const [loading, setLoading] = useState<boolean>(false);
  // 分页
  const [pagination, setPagination] = useState<any>({
    current: 1,
    position: ['bottomCenter'],
    pageSize: 10,
    total: 0,
    // size: "small",
    showTotal: (total: number) => t('共{name}条', String(total)),
    showQuickJumper: true,
    showSizeChanger: true,
  });

  // 查询的角色
  const [roles, setRoles] = useState<string>('');

  // 分页2
  const [pagination2, setPagination2] = useState<any>({
    current: 1,
    pageSize: 10,
    position: ['bottomCenter'],
    total: 0,
    // size: "small",
    showTotal: (total: number) => t('共{name}条', String(total)),
    showQuickJumper: true,
    showSizeChanger: true,
  });
  const [searchVal,setSearchVal] = useState('');
  // 所有章节
  const [chapters, setChapters] = useState<any[]>([]);


  //是否查看详情
  const [showDetail, setShowDetail] = useState<any>(false);

  const [showPage, setShowPage] = useState<number>(1);

  //学习进度数据
  const [progressData, setProgressData] = useState<any>({});

  const [showHomework, setShowHomework] = useState<boolean>(false);

  // 知识点统计数据
  const [knowledgePointData, setKnowledgePointData] = useState<any>(null);
  const [pieDate, setPieDate] = useState<any>({});
  // 统计提问数 掌握率 完成率
  const [knowledgePointParams, setKnowledgePointParams] = useState<any>({
    questionTotal: 0,
    totalFinishRate: 0,
    totalMasterRate: 0,
  });

  // 当前选择的模式
  const [knowledgeitem, setKnowledgeitem] = useState<any>(null);
  // 当前选择的tab
  const [selectkey, setSelectkey] = useState<string>('1');

  const [mapInfo, setMapInfo] = useState<any>()
  const [selectNodeid, setSelectNodeid] = useState<any>(null);

  useEffect(() => {
    if (!courseId) return
    initcoursemap()
  }, [courseId])
  useEffect(() => {
    if (showdetail == 'true') {
      setShowPage(4);
      let knowledgeitem: any = sessionStorage.getItem('knowledgeitem') || {};
      setKnowledgeitem(JSON.parse(knowledgeitem));
    }
//     if(isSJ){
// getSJData()
//     }else{
      getData();
    // }
    // 获取所有章节
    setTimeout(() => {
      console.log('mapInfo',mapInfo)
    })
    // 获取资源使用情况
    getResourceData();


  }, [isSJ]);
  useEffect(() => {
    if(mapInfo && mapInfo.id){
      getChapter();
    }
  }, [mapInfo]);
  const { getPermission } = usePermission();
  const [sjResourceParams, setSjResourceParams] = useImmer<Record<string,any>>({
    page:1,
    size:10,
  })
  const [sjTotal, setSjTotal] = useState<number>(0);
  const [sjDatasource, setSjDatasource] = useState<any[]>([]);
   //获取资源使用情况
    const getSJResourceData = () => {
      setLoading(true);
      statisticsApi
        .getSjResource({
          ...sjResourceParams,
          courseId: courseId,
          mapId: mapInfo?.id,
          courseType: 4,
          resourceName: searchVal
          // page: toPage1 ? 1 : pagination2.current,
          // size: pagination2.pageSize,
          // resourceName: keyword2,
          // sortBy: sortBy2, //排序方式  0：降序、1：升序（默认0）
          // sortTarget: sortTarget2, //排序字段 0：学习进度、1：视频学习时长、2：作业提交情况、3：作业得分率、4：提问次数（默认0）
          // chapterName: chapterName,
          // resourceType: resourceType,
        })
        .then(res => {
          if (res.status == 200) {
            setSjDatasource(
              res.data.results.map((item: any, index: number) => {
                return {
                  ...item,
                  key: index,
                };
              }),
            );
            setSjTotal(res.data.total);

          }
        })
        .finally(() => {
          setLoading(false);
        });
    };
  useEffect(() => {
      getSJResourceData()
  },[mapInfo,sjResourceParams,isSJ])

  //#region 课程地图权限关联展示(不展示地图,相应的功能需要隐藏)
  const isShowMap = useRef(getPermission(['map'], 'knowledge_map_display'))
  //#endregion

  //#region 微专业兼容
  const isMicroMajor = useRef(location?.query?.type === 'microMajor')
  //微专业的选择框
  const [microModuleList, setMicroModuleList] = useState<any[]>([])
  //选择的模块id
  const [selectMicroModuleId, setSelectMicroModuleId] = useState('')
  //layout存储了 microPermission ;queryMicroPermission接口
  useEffect(() => {
    if (isMicroMajor.current) {
      setMicroModuleList(microInfo?.microPermission?.moduleInfos || [])
      setSelectMicroModuleId(microInfo?.microPermission?.moduleInfos?.[0]?.nodeId || '')
    }
  }, [microInfo])
  //#endregion

  //#region 作业完成情况
    //作业完成情况
    const [homeworkPieData, setHomeworkPieData] = useState<any>({});
    const [homeworkSubmitData, setHomeworkSubmitData] = useState<any>([]);
    const [homeworkCount, setHomeworkCount] = useState(0)
    const [nodeInfo, setNodeInfo] = useState<any>({});
    const [homeworkParams, setHomeworkParams] = useState<any>({
      chapterId: '',
      sortBy: 0,
      sortName: t('章节'),
    });
    const [resourceStudyParams, setResourceStudyParams] = useState<any>({
      chapterName: '',
      courseId,
      sortBy: 0,
      sortTarget: 0,
      nodeId: ''
    });
      //提交率
  const getHomeworkPieChart1 = () => {
    let rate = homeworkPieData.courseSubmissionRate.toFixed(1);
    let dataSource = [
      { name: t('提交率'), value: rate },
      { name: t('进行中'), value: 100 - rate },
    ];
    let colors = ['#C692FF', '#EBEDF4'];
    return {
      tooltip: {},
      title: [
        {
          text: rate + '%',
          textStyle: {
            fontSize: 14,
            color: 'black',
          },
          textAlign: "center",
          x: '50%',
          y: '47%',
        },
        {
          text: t('提交率'),
          textStyle: {
            fontSize: 12,
            color: 'black',
          },
          textAlign: 'center',
          x: '50%',
          y: '70%',
        },
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true,
      },
      series: [
        {
          type: 'pie',
          center: ['50%', '50%'],
          radius: ['30%', '50%'],
          label: {
            show: false,
          },
          data: dataSource.map((el: any, i: number) => ({
            ...el,
            itemStyle: { color: colors[i] },
            tooltip: { show: i == 0 ? true : false },
            emphasis: { disabled: i == 0 ? false : true },
          })),
        },
      ],
    };
  };

  //得分率
  const getHomeworkPieChart2 = () => {
    let rate = homeworkPieData.courseSoringRate.toFixed(1);
    let dataSource = [
      { name: t('得分率'), value: rate },
      { name: '', value: 100 - rate },
    ];
    let colors = ['#F8CC28', '#EBEDF4'];
    return {
      tooltip: {},
      title: [
        {
          text: rate + '%',
          textStyle: {
            fontSize: 14,
            color: 'black',
          },
          textAlign: 'center',
          x: '50%',
          y: '47%',
        },
        {
          text: t('得分率'),
          textStyle: {
            fontSize: 12,
            color: 'black',
          },
          textAlign: 'center',
          x: '50%',
          y: '70%',
        },
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true,
      },
      series: [
        {
          type: 'pie',
          center: ['50%', '50%'],
          radius: ['30%', '50%'],
          label: {
            show: false,
          },
          data: dataSource.map((el: any, i: number) => ({
            ...el,
            itemStyle: { color: colors[i] },
            tooltip: { show: i == 0 ? true : false },
            emphasis: { disabled: i == 0 ? false : true },
          })),
        },
      ],
    };
  };

  //获取作业数量
  const getHomeworkNums = (course_id: string) => {
    statisticsApi.queryMicroMajorHomeworkNums({courseId: course_id, courseSemester: 1}).then((res: any) => {
      if (res?.status == 200) {
        setHomeworkCount(res?.data || 0)
      }
    })
  }
  //获取作业的完成率、得分率
  const getHomeworkRate = (course_id: string) => {
    statisticsApi.queryMicroMajorScoreAndSub(course_id, {courseSemester: 1}).then(res => {
      if (res.status == 200) {
        setHomeworkPieData({
          courseSubmissionRate: parseFloat(res.data.courseSubmissionRate) || 0,
          courseSoringRate: parseFloat(res.data.courseSoringRate) || 0,
        });
      }
    })
  }

  useEffect(() => {
    if (!courseId) return;
    getHomeworkNums(courseId)
    getHomeworkRate(courseId)
  }, [])

   //获取作业完成情况数据
   const getHomeworkStatistics = () => {
    //章节统计图
    let { chapterId, sortBy, sortName } = homeworkParams;
    statisticsApi
      .getHomeworkSubmitData({ chapterId, sortBy, sortName, weuzhuanye: true }, courseId)
      .then(res => {
        if (res.status == 200) {
          setHomeworkSubmitData(res.data || []);
        }
      });
  };
  useEffect(() => {
    getHomeworkStatistics();
  }, [homeworkParams]);
  //#endregion

  useEffect(() => {
    // 是微专业，但是没有选择模块就不发请求
    if (isMicroMajor.current && !selectMicroModuleId) return
    //获取学习进度分布
    getProgressData();
    // 获取知识点统计
    knowledgePointOptions();
    //学员学习情况
    getStudentData();
  }, [selectMicroModuleId])

  useEffect(() => {
    if (Object.keys(parameterConfigObj).length > 0) {
      if (location.pathname.includes('tempatedetail')) {
        setShowHomework(
          parameterConfigObj.kczx?.includes(
            'course_library_school_assignment_display',
          ),
        );
      } else {
        setShowHomework(
          getPermission(
            ['spoc', 'mooc', 'training'],
            '_school_assignment_display',
            true,
          ),
        );
      }
    }
  }, [parameterConfigObj]);

  const StudentColumns: any[] = useMemo(() => {
    const defaultArr = Utils.strongColumns([
      {
        title: t('姓名'),
        dataIndex: 'userName',
        className: 'table-header-cell',
        key: 'userName',
        align: 'center',
      },
      {
        title: t('学工号'),
        dataIndex: 'userCode',
        className: 'table-header-cell',
        key: 'userCode',
        align: 'center',
      },
      {
        title: t('角色'),
        dataIndex: 'role',
        className: 'table-header-cell',
        key: 'role',
        align: 'center',
        sorter: true,
        render: (text: any) => {
          return text == 0 ? '学生' : '老师';
        },
      },
      {
        title: t('学院'),
        dataIndex: 'college',
        className: 'table-header-cell',
        key: 'college',
        align: 'center',
      },
      {
        title: t('培养层次'),
        dataIndex: 'education',
        className: 'table-header-cell',
        key: 'education',
        align: 'center',
        render: (text: any) => {
          if (text == '1') {
            return '研究生';
          }
          if (text == '0') {
            return '本科生';
          }
          return '';
        },
      },
      {
        title: t('专业'),
        dataIndex: 'major',
        className: 'table-header-cell',
        key: 'major',
        align: 'center',
      },
      {
        title: () => (
          <span>
            <Tooltip title={t('学习进度=学员已学习的课程内容/总课程内容')}>
              <ExclamationCircleOutlined
                style={{ marginRight: '10px', color: 'var(--primary-color)' }}
              />
            </Tooltip>
            {t('学习进度')}
          </span>
        ),
        dataIndex: 'learningProgressRate',
        key: 'learningProgressRate',
        className: 'table-header-cell',
        align: 'center',
        sorter: true,
        render: (text: any, record: any) => {
          return <span>{text < 100 ? text : 100}%</span>;
        },
      },
      {
        title: isSJ? t('学习完成率'): t('知识点完成率'),
        dataIndex: 'totalFinishRate',
        className: 'table-header-cell',
        key: 'totalFinishRate',
        align: 'center',
        sorter: true,
        render: (text: any, record: any) => {
          return <span>{Number(text)}%</span>;
        },
      },
      {
        title: t('知识点掌握率'),
        dataIndex: 'totalMasterRate',
        className: 'table-header-cell',
        key: 'totalMasterRate',
        align: 'center',
        sorter: true,
        render: (text: any, record: any) => {
          return <span>{Number(text)}%</span>;
        },
      },
      {
        title: t('操作'),
        key: 'action',
        className: 'table-header-cell',
        align: 'center',
        //  render: (_: any, record: any) => <Button type="link" onClick={() => { setShowDetail({ courseId, ...record }); setShowPage(2); }}>{t("查看详情")}</Button>
        render: (_: any, record: any) => (
          <Space
            size="middle"
            onClick={() =>
              history.push(
                `/editcourse/completion/detail?id=${courseId}&sm=${courseSemester}&code=${record.userCode}&type=${isMicroMajor.current?'microMajor': 'map'}&microMajor=${selectMicroModuleId}`,
              )
            }
          >
            <a>查看详情</a>
          </Space>
        ),
      },
    ])

    return defaultArr.filter(
      (column: any) =>
        showHomework ||
        (column.dataIndex !== 'submitted' && column.dataIndex !== 'scoreRate'),
    )
  }, [isSJ,showHomework, courseId, isMicroMajor.current, selectMicroModuleId])

  useEffect(() => {
    getStudentData();
  }, [sortBy, sortTarget, pagination.current, pagination.pageSize]);

  useEffect(() => {
    getResourceData();
  }, [
    sortBy2,
    sortTarget2,
    pagination2.current,
    pagination2.pageSize,
    selectMicroModuleId
  ]);

  // 获取所有章节
  const getChapter = async () => {
    console.log('mapInfo',mapInfo);
    chapterApis.getChapterTwo(mapInfo?.id).then(res => {
      console.log('res',res)
      if (res.status == 200) {
        setChapters(res.data.data);
        if(res.data.data && res.data.data.length > 0){
          setSelectNodeid(res.data.data[0]?.nodeId);
          setResourceStudyParams({
            ...resourceStudyParams,
            chapterName: res.data.data[0]?.nodeId,
            nodeId: res.data.data[0]?.nodeId,
          });
          getResourceStudyData({
            ...resourceStudyParams,
            chapterName: res.data.data[0]?.nodeId,
            nodeId: res.data.data[0]?.nodeId,
          });
        }
      }
    });
  };

  // 获取 参与学习人数 课程资源数 发布作业数 学生提问数
  const getData = () => {
    statisticsApi
      .getOverView({
        courseId: courseId,
        courseType: 4,
        type: 0
      })
      .then(res => {
        if (res.status == 200) {
          setHeardinfo(res.data.data || {});
        }
      });
  };
    // const getSJData = () => {
    //   statisticsApi.getacademic({
    //     courseId: courseId,
    //     courseType: location.query.type == 'spoc' ? 2 : 1
    //   }).then((res) => {
    //     if (res.status == 200) {
    //       setHeardinfo(res.data);
    //     }
    //   });
    // };

  // 获取学生学习情况
  const getStudentData = (toPage1?: boolean, searchParam?: IStudentFilter) => {
    setLoading(true);
    if (toPage1) {
      setPagination({
        ...pagination,
        current: 1,
      });
    }
    const param: any = {
      courseId: courseId,
      role: searchParam?.isSearchRole? searchParam?.role : roles,
      courseSemester: courseSemester,
      page: toPage1 ? 1 : pagination.current,
      size: pagination.pageSize,
      keyword: keyword,
      college:searchParam?.isSearchCollege ? searchParam?.collegeList?.join(',') : checkColleges.join(','),
      sortField: sortTarget,
      sortType: sortBy
    }
    if (isMicroMajor.current) {
      param.nodeId = selectMicroModuleId
    }
    statisticsApi
      .getstudentStudy(param)
      .then(res => {
        if (res?.status == 200 && res?.data?.status == 200) {
          setDataSource1(res.data.data.results);
          setPagination({
            ...pagination,
            current: res.data.data.page,
            size: res.data.data.size,
            total: res.data.data.total,
          });
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  //获取资源使用情况
  const getResourceData = (toPage1?: boolean) => {
    setLoading(true);
    if (toPage1) {
      setPagination2({
        ...pagination2,
        current: 1,
      });
    }
    const param: any = {
      courseId: courseId,
      page: toPage1 ? 1 : pagination2.current,
      size: pagination2.pageSize,
      nodeName: keyword2,
      sortType: sortBy2, //排序方式  0：升序、1：倒序
      sortField : sortTarget2,//（排序字段：0完成率 1掌握率）
      ourseSemester: courseSemester,
      // code: userInfo?.userCode,
    }
    if(isMicroMajor.current) {
      const nodeItem = microModuleList.find(item => item?.nodeId === selectMicroModuleId)
      param.nodeId = nodeItem?.nodeId
      param.nodeName = nodeItem?.nodeName
    }
    statisticsApi
      .getKnowlegeStudy(param)
      .then(res => {
        if (res.data.status == 200) {
          setDataSource2(
            res.data.data.results.map((item: any, index: number) => {
              return {
                ...item,
                key: index,
              };
            }),
          );
          setPagination2({
            ...pagination2,
            current: res.data.data.page,
            size: res.data.data.size,
            total: res.data.data.total,
          });
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  //查询课程关联的地图
  const initcoursemap = () => {
   querymapbycourse({
       courseId: courseId,
       isShow: 2,
       courseSemester: courseSemester
   }).then(({data}:any)=>{
       if(data.length == 0){
         // message.info('当前课程暂未关联图谱！');
       }else{
         setMapInfo(data[0]);
         data[0]?.id && getNodeInfo(data[0]?.id).then((res: any) => {
          if(res.status == 200) {
            setNodeInfo(res?.data)
          }
        })
       }
   })
 };


  // 表格切换排序条件
  const tablechange = (pagination2: any, filters: any, sorter: any) => {
    setPagination({
      ...pagination,
      current: pagination2.current,
      pageSize: pagination2.pageSize,
    });
    // 升序
    if (sorter.order == 'ascend') {
      setSortBy(1);
      if (sorter.columnKey == 'learningProgressRate') {
        setSortTarget(2);
      } else if (sorter.columnKey == 'totalFinishRate') {
        setSortTarget(0);
      } else if (sorter.columnKey == 'totalMasterRate') {
        setSortTarget(1);
      }
    } else if (sorter.order == 'descend') {
      setSortBy(0);
      if (sorter.columnKey == 'learningProgressRate') {
        setSortTarget(2);
      } else if (sorter.columnKey == 'totalFinishRate') {
        setSortTarget(0);
      } else if (sorter.columnKey == 'totalMasterRate') {
        setSortTarget(1);
      }
    } else if (sorter.order === undefined) {
      setSortBy(undefined);
      setSortTarget(undefined);
    }
  };

  // 表格切换排序条件
  const tablechange2 = (paginati: any, filters: any, sorter: any) => {
    setPagination2({
      ...pagination2,
      current: paginati.current,
      pageSize: paginati.pageSize,
    });
    console.log(sorter, 'sorter')
    // 升序
    if (sorter.order == 'ascend') {
      setSortBy2(0);
      // （排序字段：0完成率 1掌握率）
      if (sorter.columnKey === 'finishRate') {
        setSortTarget2(0)
      }
      if (sorter.columnKey === 'masterRate') {
        setSortTarget2(1)
      }
    } else if (sorter.order == 'descend') {
      setSortBy2(1);
      if (sorter.columnKey === 'finishRate') {
        setSortTarget2(0)
      }
      if (sorter.columnKey === 'masterRate') {
        setSortTarget2(1)
      }
    } else if (sorter.order === undefined) {
      setSortBy2(undefined);
      setSortTarget2(undefined);
    }
  };

  const onChange = (key: string) => {
    setSelectkey(key);
  };

  // 导出学习记录
  const exportLearning = () => {
    const exportPath = isMicroMajor.current ? (
      `/learn/m1/statistics/student/learning/export?courseId=${courseId}&courseSemester=${location.query.sm}&nodeId=${selectMicroModuleId}`
    ) : (
      `/learn/m1/statistics/student/learning/export?courseId=${courseId}&courseSemester=${location.query.sm}`)

    window.open(exportPath);
  };

    //#region
  //学院列表
  const [colleges, setColleges] = useState<any[]>([]);
  const [checkColleges, setCheckColleges] = useState<any[]>([])
  const getColleges = () => {
    queryColleges().then((res: any) => {
      if (res.status === 200) {
        setColleges(res.data.organization ?? []);
      }
    });
  };
  useEffect(() => {
    getColleges();
  }, []);
  //#endregion

  // 导出知识点学习情况
  const exportresource = () => {
    // window.open(
    //   `/learn/m1/statistics/knowledge/learning/export?courseId=${courseId}&courseSemester=${location.query.sm}&mapId=${mapInfo?.id}`,
    // );
    statisticsApi.SjResourceExport({courseId,courseSemester, mapId: mapInfo?.id }).then(res => {
      console.log(res)
      const fileUrl = window.URL.createObjectURL(res);
      const a = document.createElement('a');
      a.href = fileUrl;
      console.log('url', fileUrl);
      a.setAttribute('download', '资源学习情况.xlsx');
      a.style.display = 'none';
      a.click();
      a.remove();
    })
  };

  const columns2: any = [
    {
      title: t('知识点'),
      dataIndex: 'nodeName',
      className: 'table-header-cell',
      key: 'nodeName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: t('上级节点'),
      dataIndex: 'parentNodeName',
      className: 'table-header-cell',
      key: 'parentNodeName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: t('完成率'),
      dataIndex: 'finishRate',
      className: 'table-header-cell',
      key: 'finishRate',
      align: 'center',
      sorter: true,
      render(text: any, record: any) {
        if (text == '-') {
          return '-';
        } else {
          return Number(text) + '%';
        }
      },
    },
    {
      title: t('掌握率'),
      dataIndex: 'masterRate',
      className: 'table-header-cell',
      key: 'masterRate',
      align: 'center',
      sorter: true,
      render(text: any, record: any) {
        if (text == '-') {
          return '-';
        } else {
          // return Number(text) + '%';
          const value = Number(text);
          if (value >= 0 && value <= 30) {
            return <span> {value + '%'} <Tag color="magenta">薄弱</Tag></span>;
          } else {
            return value + '%';
          }
        }
      },
    },
    {
      title: t('操作'),
      key: 'action',
      className: 'table-header-cell',
      align: 'center',
      render: (_: any, record: any) => (
        <Button
          type="link"
          onClick={() => {
            setShowDetail({ courseId, type: 'map', ...record });
            setShowPage(5);
          }}
        >
          {t('查看详情')}
        </Button>
      ),
    },
  ];
  const sjColumns:any[]= [
      {
        title: t('资源名称'),
        dataIndex: 'resourceName',
        key: 'resourceName',
        align: 'center',
        ellipsis: true,
      },
      // {
      //   title: t('学习要求'),
      //   dataIndex: 'necessaryStudy',
      //   key: 'necessaryStudy',
      //   align: 'center',
      //   ellipsis: true,
      //   render: (text: any) => (text ? t('必学') : t('选学')),
      // },
      {
        title: t('学习次数'),
        dataIndex: 'numberOfLearners',
        key: 'numberOfLearners',
        align: 'center',
        sorter: true,
      },
      // {
      //   title: t('学习人数'),
      //   dataIndex: 'studyNumber',
      //   key: 'studyNumber',
      //   align: 'center',
      //   sorter: true,
      // },

      {
        title: () => (
          <span>
            <Tooltip title={t('学习完成率=已完成学习的学生/学生总数')}>
              <ExclamationCircleOutlined
                style={{ marginRight: '10px', color: 'var(--primary-color)' }}
              />
            </Tooltip>
            {t('学习完成率')}
          </span>
        ),
        dataIndex: 'learningCompletionRate',
        key: 'learningCompletionRate',
        align: 'center',
        sorter: true,
      },
        {
        title: t('知识点'),
        dataIndex: 'nodeName',
        key: 'nodeName',
        align: 'center',
      },
        {
        title: t('上级节点'),
        dataIndex: 'parentNodeName',
        key: 'parentNodeName',
        align: 'center',
      },
      {
        title: t('资源类型'),
        dataIndex: 'resourceType',
        key: 'resourceType',
        align: 'center',
        render: (text: any, record: any) => {
          // { label: '音频', value: 'audio' },
          // { label: '视频', value: 'video' },
          // { label: '图片', value: 'picture' },
          // { label: '文档', value: 'document' },
          // { label: '文件夹', value: 'folder' },
          // { label: '其他', value: 'other' },
          if (text == 'biz_sobey_audio') {
            return <span>{t('音频')}</span>;
          } else if (text == 'biz_sobey_video') {
            return <span>{t('视频')}</span>;
          } else if (text == 'biz_sobey_picture') {
            return <span>{t('图片')}</span>;
          } else if (text == 'biz_sobey_document') {
            return <span>{t('文档')}</span>;
          } else if (text == 'biz_sobey_folder') {
            return <span>{t('文件夹')}</span>;
          } else if (text == 'biz_sobey_homework') {
            return <span>{t('作业')}</span>;
          } else if (text == 'biz_sobey_hyperlink') {
            return <span>{t('超链接')}</span>;
          } else if (text == 'other') {
            return <span>{t('其他')}</span>;
          } else if (text == 'case') {
            return <span>{t('案例')}</span>;
          } else {
            return <span>{text}</span>;
          }
        },
      },
      {
        title: t('操作'),
        key: 'action',
        align: 'center',
        render: (_: any, record: any) => (
          <Button
            type="link"
            onClick={() => {
              setShowDetail({ courseId, ...record });
              setShowPage(3);
            }}
          >
            {t('查看详情')}
          </Button>
        ),
      },
    ];
  const getlearningTimeTotal = (text: number) => {
    let h = Math.floor(text / 1000 / 60 / 60) || 0;
    let min = Math.floor((text % 3600000) / 60000) || 0;
    let seconds = Math.floor((text / 1000) % 60) || 0;
    return (
      (h == 0 ? '' : h + t('时')) +
      (min == 0 && h == 0 ? '' : min + t('分')) +
      (seconds == 0 && min != 0 ? '' : seconds + t('秒'))
    );
  };
  //获取学习进度分布数据
  const getProgressData = () => {
    let data: any = {
      progressDistribute: [],
      complateData: [],
      collegeDistribute: [],
      personDistribute: [],
    };
    const param: any = {
      courseId
    }
    if (isMicroMajor.current) {
      param.nodeId = selectMicroModuleId
    }
    statisticsApi.getDistributed(param).then(res => {
      if (res.status == 200) {
        let obj = res.data;
        const { completeTotal, undoneTotal } = obj;
        delete obj.completeTotal;
        delete obj.undoneTotal;
        data = {
          ...data,
          progressDistribute: Object.keys(obj).map((key: any) => ({
            name: key + '%',
            value: obj[key],
          })),
          complateData: [
            { name: t('已完成学习'), value: completeTotal || 0 },
            { name: t('未完成学习'), value: undoneTotal || 0 },
          ],
        };
        setProgressData(data);
      }
    });
    statisticsApi.getCollegeDistributed(param).then(res => {
      if (res.status == 200) {
        const collegeDistribute = res.data?.map((el: any) => ({
          name: el.college,
          value: el.distribution,
        }));
        data = {
          ...data,
          collegeDistribute,
        };
        setProgressData(data);
      }
    });
    statisticsApi.getPersonDistributed(param).then(res => {
      if (res.status == 200) {
        const personDistribute = res.data?.map((el: any) => ({
          name: el.student,
          value: el.learningProgressRateString,
        }));
        data = {
          ...data,
          personDistribute,
        };
        setProgressData(data);
      }
    });
  };
    //资源学习情况
    const [resourceStudyData, setResourceStudyData] = useState<any>([]);
//  useEffect(() => {
//   // if (isSJ) {
//     getResourceStudyData();
//   // }
//   }, [mapInfo,resourceStudyParams]);
    //获取资源学习情况图表数据
    const getResourceStudyData = (obj:any=resourceStudyParams) => {
      console.log(obj,'resourceStudyParams')
      if (!mapInfo?.id) return;
      chapterApis.getRecourceData({ ...obj,mapId: mapInfo?.id }).then(res => {
        console.log('getResourceStudyData', res.data);
        if (res.status == 200) {
          setResourceStudyData(res.data.data);
        }
      });
    };
  //学习进度分布按个人统计图表
  const getPersonDistributeOptions = () => {
    return {
      tooltip: {
        formatter: function(param: any) {
          let str = `<div style="display:flex;align-items:center;">${param.marker}<span style="flex:1;display:flex;justify-content: space-between;">${param.name}<span style="margin-left:20px;">${param.data}%</span></span></div>`;
          return str;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: progressData.personDistribute.map((el: any) => el.name),
        axisLine: {
          lineStyle: {
            color: '#eee',
          },
        },
        axisLabel: {
          color: '#333',
        },
      },
      yAxis: {
        type: 'value',
        splitLine: {
          lineStyle: {
            color: '#eee',
          },
        },
        axisLabel: {
          color: '#333',
          formatter: '{value}%',
        },
        minInterval: 1,
      },
      series: [
        {
          type: 'bar',
          data: progressData.personDistribute.map((el: any) => el.value),
          itemStyle: {
            color: '#549cff',
            barBorderRadius: [4, 4, 0, 0],
          },
          barWidth: 24,
        },
      ],

      dataZoom:
        progressData.personDistribute?.length > 12
          ? [
              // 有滚动条 平移
              {
                type: 'slider',
                realtime: true,
                start: 0,
                end:
                  progressData.personDistribute?.length > 12
                    ? (
                        (12 / progressData.personDistribute.length) *
                        100
                      ).toFixed(0)
                    : 100, // 初始展示20%
                height: 4,
                fillerColor: 'rgba(17, 100, 210, 0.42)', // 滚动条颜色
                borderColor: 'rgba(17, 100, 210, 0.12)',
                handleSize: 0, // 两边手柄尺寸
                showDetail: false, // 拖拽时是否展示滚动条两侧的文字
                top: '96%',

                // zoomLock:true, // 是否只平移不缩放
                // moveOnMouseMove:true, //鼠标移动能触发数据窗口平移
                // zoomOnMouseWheel :true, //鼠标移动能触发数据窗口缩放
              },
              {
                type: 'inside', // 支持内部鼠标滚动平移
                start: 0,
                end: 10,
                zoomOnMouseWheel: false, // 关闭滚轮缩放
                moveOnMouseWheel: true, // 开启滚轮平移
                moveOnMouseMove: true, // 鼠标移动能触发数据窗口平移
              },
            ]
          : [],
    };
  };

  function transformData(originalData: any[]) {
    let transformedData: { name: any; value: any; data: never[] }[] = [];

    originalData.forEach(chapter => {
      let chapterObj: any = {
        name: chapter.chapterNodeName,
        value: chapter.knowledgeNodeNumber,
        data: [],
      };

      chapter.knowledgeLearningInfos.forEach(
        (knowledge: {
          finishNotExamine: any;
          masterNotExamine: any;
          nodeName: any;
          masterRate: string;
        }) => {
          let knowledgeObj = {
            name: knowledge.nodeName,
            value: parseFloat(knowledge.masterRate) || 0,
            finishNotExamine: knowledge.finishNotExamine,
            masterNotExamine: knowledge.masterNotExamine,
          };
          chapterObj.data.push(knowledgeObj);
        },
      );

      transformedData.push(chapterObj);
    });

    return transformedData;
  }
  function transformData2(originalData: any[]) {
    let transformedData: { name: any; value: any; data: never[] }[] = [];
    originalData.forEach(chapter => {
      let chapterObj: any = {
        name: chapter.chapterNodeName,
        value: chapter.knowledgeNodeNumber,
        data: [],
      };
      chapter.knowledgeLearningInfos.forEach(
        (knowledge: {
          finishNotExamine: any;
          masterNotExamine: any;
          nodeName: any;
          finishRate: string;
        }) => {
          let knowledgeObj = {
            name: knowledge.nodeName,
            value: parseFloat(knowledge.finishRate) || 0,
            finishNotExamine: knowledge.finishNotExamine,
            masterNotExamine: knowledge.masterNotExamine,
          };
          chapterObj.data.push(knowledgeObj);
        },
      );

      transformedData.push(chapterObj);
    });

    return transformedData;
  }

  const [leftActiveTab, setLeftActiveTab] = useState(1);
  const handleChange = (tab: number) => {
    setLeftActiveTab(tab);
    if (tab == 1 || tab == 3) {
      setKnowledgePointData(transformData(pieDate));
    } else {
      setKnowledgePointData(transformData2(pieDate));
    }
  };
  // 知识点学习情况图表
  const knowledgePointOptions = () => {
    const param: any = {
      courseId: courseId,
      courseSemester: courseSemester,
      // userCode: userInfo?.userCode,
    }
    if (isMicroMajor.current) {
      param.nodeId = selectMicroModuleId
    }
    statisticsApi
      .getKnowledgeCompletion(param)
      .then((res: any) => {
        if (res?.data?.status == 200) {
          //完成率假数据
          let data: any = []
          data = res.data?.data?.chapterKnowledgeInfos || []
          setPieDate(data);
          setKnowledgePointData(
            transformData2(data),
          );

          setKnowledgePointParams({
            questionTotal: res.data.data.questionTotal,
            totalFinishRate: res.data.data.totalFinishRate,
            totalMasterRate: res.data.data.totalMasterRate,
          });
        }
      });
  };

  // 课程目标完成度-饼状图
  const completionOfCourseObjectives = useMemo(() => {
    return !!knowledgePointData ? (
      <PieChartTwo
        dataSource={knowledgePointData}
        leftActiveTab={leftActiveTab}
      />
    ) : (
      <Empty style={{ marginTop: '100px' }} />
    );
  }, [knowledgePointData]);

  function formatDuration(duration: any) {
    const totalSeconds = duration / 10000000; // 转换为秒
    const totalSecs = Math.floor(totalSeconds); // 取整数部分
    const minutes = Math.floor(totalSecs / 60);
    const seconds = totalSecs % 60;
    return `${minutes}分${seconds}秒`;
  }

  return (
    <div
      style={{
        minHeight: '100%',
        // margin: -15,
        // padding: 15,
        backgroundColor: '#fff',
      }}
    >
      {showPage == 4 ? (
        <KnowledgeDetail
          knowledgeitem={knowledgeitem}
          onback={() => setShowPage(1)}
        ></KnowledgeDetail>
      ) : showPage === 3 ? (
        <div className="view_box">
          <DetailContent
            detail={showDetail}
            onBack={() => {
              setShowDetail(null);
              setShowPage(1);
            }}
          />
        </div>
      ) : showPage === 2 ? (
        <StudentDetail
          chapters={chapters}
          // detail={showDetail}
          code={showDetail?.code}
          onBack={() => {
            setShowDetail(null);
            setShowPage(1);
          }}
        />
      ) : showPage === 5 ?  (<div className="view_box">
        <DetailContent3
          detail={showDetail}
          onBack={() => {
              setShowDetail(null);
              setShowPage(1);
            }}
        />
      </div>) : (
        <div
          style={{ display: !Boolean(showDetail) ? 'block' : 'none' }}
          className="view_box"
        >
          <div className="top_box box_panel">
            <div className="header">
              <div className="title">
                <span>{t('课程情况')}</span>
              </div>
              { isMicroMajor.current ? (
                <Select className="title_select" value={selectMicroModuleId} onChange={setSelectMicroModuleId} options={microModuleList.map(item => ({label: item?.nodeName, value: item?.nodeId}))} />
              ) : null}
            </div>
          {/*<Access accessible={!isSJ}>*/}
          {/*  <div className="item_boxs">*/}
          {/*    <div className="view_box">*/}
          {/*      <div className="left_box">*/}
          {/*        <IconFont*/}
          {/*          type="iconbianzubeifen1"*/}
          {/*          style={{ color: '#549CFF', fontSize: '50px' }}*/}
          {/*        ></IconFont>*/}
          {/*      </div>*/}
          {/*      <div className="right_box">*/}
          {/*        <span>{t('开课时间')}</span>*/}
          {/*        <span style={{ fontSize: 16 }}>*/}
          {/*          {heardinfo.startTime}-{heardinfo.endTime}*/}
          {/*        </span>*/}
          {/*        /!*  *!/*/}
          {/*      </div>*/}
          {/*    </div>*/}
          {/*    <div className="view_box">*/}
          {/*      <div className="left_box">*/}
          {/*        <IconFont*/}
          {/*          type="iconziyuanku1"*/}
          {/*          style={{ color: '#549CFF', fontSize: '50px' }}*/}
          {/*        ></IconFont>*/}
          {/*      </div>*/}
          {/*      <div className="right_box">*/}
          {/*        <span>{t('累计参与学习人数')}</span>*/}
          {/*        <span>*/}
          {/*          {heardinfo.studyTotal}*/}
          {/*          <p style={{ display: 'inline-block', fontSize: '16px' }}>*/}
          {/*            {t('个')}*/}
          {/*          </p>*/}
          {/*        </span>*/}
          {/*      </div>*/}
          {/*    </div>*/}
          {/*    {*/}
          {/*      isMicroMajor.current && (*/}
          {/*        <div className="view_box">*/}
          {/*        <div className="left_box">*/}
          {/*          <IconFont*/}
          {/*            type="iconjiaocaijiaofu"*/}
          {/*            style={{ color: '#549CFF', fontSize: '50px' }}*/}
          {/*          ></IconFont>*/}
          {/*        </div>*/}
          {/*        <div className="right_box">*/}
          {/*          <span>{t('发布作业数')}</span>*/}
          {/*          <span>*/}
          {/*            {homeworkCount}*/}
          {/*            <p style={{ display: 'inline-block', fontSize: '16px' }}>*/}
          {/*              {t('个')}*/}
          {/*            </p>*/}
          {/*          </span>*/}
          {/*          /!* <span>*/}
          {/*            {t('作业提交率')}*/}
          {/*            {homeworkPieData.courseSubmissionRate}%*/}
          {/*          </span> *!/*/}
          {/*        </div>*/}
          {/*      </div>*/}
          {/*      )*/}
          {/*    }*/}
          {/*    <div className="view_box">*/}
          {/*      <div className="left_box">*/}
          {/*        <IconFont*/}
          {/*          type="iconjiaocaijiaofu"*/}
          {/*          style={{ color: '#549CFF', fontSize: '50px' }}*/}
          {/*        ></IconFont>*/}
          {/*      </div>*/}
          {/*      <div className="right_box">*/}
          {/*        <span>{t('累计发布课程资源')}</span>*/}
          {/*        <span>*/}
          {/*          {heardinfo.publishResourceTotal}*/}
          {/*          <p style={{ display: 'inline-block', fontSize: '16px' }}>*/}
          {/*            {t('个')}*/}
          {/*          </p>*/}
          {/*        </span>*/}
          {/*      </div>*/}
          {/*    </div>*/}
          {/*    { isShowMap.current && (*/}
          {/*        <div className="view_box">*/}
          {/*      <div className="left_box">*/}
          {/*        <IconFont*/}
          {/*          type="iconwenhao"*/}
          {/*          style={{ color: '#549CFF', fontSize: '50px' }}*/}
          {/*        ></IconFont>*/}
          {/*      </div>*/}
          {/*      <div className="right_box">*/}
          {/*        <span>{t('累计知识点')}</span>*/}
          {/*        <span>*/}
          {/*          {heardinfo.knowledgePointsTotal}*/}
          {/*          <p style={{ display: 'inline-block', fontSize: '16px' }}>*/}
          {/*            {t('个')}*/}
          {/*          </p>*/}
          {/*        </span>*/}
          {/*      </div>*/}
          {/*    </div>*/}
          {/*      ) }*/}
          {/*    <div className="view_box">*/}
          {/*      <div className="left_box">*/}
          {/*        <IconFont*/}
          {/*          type="iconwenhao"*/}
          {/*          style={{ color: '#549CFF', fontSize: '50px' }}*/}
          {/*        ></IconFont>*/}
          {/*      </div>*/}
          {/*      <div className="right_box">*/}
          {/*        <span>{t('累计提问')}</span>*/}
          {/*        <span>*/}
          {/*          {heardinfo.questionTotal}*/}
          {/*          <p style={{ display: 'inline-block', fontSize: '16px' }}>*/}
          {/*            {t('个')}*/}
          {/*          </p>*/}
          {/*        </span>*/}
          {/*      </div>*/}
          {/*    </div>*/}
          {/*  </div>*/}
          {/*</Access>*/}
          <Access accessible={true}>
  <div className="item_boxs" style={{
    gap:30
  }}>
              <div className="view_box">
                <div className="left_box">
                  <IconFont
                    type="iconbianzubeifen1"
                    style={{ color: '#549CFF', fontSize: '50px' }}
                  ></IconFont>
                </div>
                <div className="right_box">
                  <span>{t('参与学习人数')}</span>
                  <span>
                    {heardinfo.participateNumber}
                    <p style={{ display: 'inline-block', fontSize: '16px' }}>
                      {t('人')}
                    </p>
                  </span>
                  {/*  */}
                  <span>
                    {t('图谱学习完成率')}
                    {Number(heardinfo.completeRate?.toFixed(2))}%
                    =图谱总完成率
                  </span>
                  {/* <span>
                    {t('视频学习总时长')}
                    {getlearningTimeTotal(heardinfo?.learningTimeTotal)}
                  </span> */}
                </div>
              </div>
              {/* <div className="view_box max-box"> */}
              <div className="view_box sepical_box">
                <div className="left_box">
                  <IconFont
                    type="iconziyuanku1"
                    style={{ color: '#549CFF', fontSize: '50px' }}
                  ></IconFont>
                </div>
                {/* <div className="right_box middle_box"> */}
                <div className="right_box">
                  <span>{t('图谱资源数')}</span>
                  <span>
                    {heardinfo.resources}
                    <p style={{ display: 'inline-block', fontSize: '16px' }}>
                      {t('个')}
                    </p>
                  </span>
                  <span>
                    {t('资源学习次数')}
                    {heardinfo.resourceLearningTimes}
                  </span>
                </div>
                  <div className="new_box">
                    <span>{`视频总时长 ${nodeInfo?.duration ? formatDuration(nodeInfo?.duration) : '0分0秒'}`}</span>
                    <span>{`课件总份数${nodeInfo?.wordsNumber ? nodeInfo?.wordsNumber: '0'}`}</span>
                    <span>{`试题总字数${ nodeInfo?.questionsNumber ? nodeInfo?.questionsNumber: '0'}`}</span>
                  </div>
              </div>

                <div className="view_box sepical_box">
                  <div className="left_box">
                    <IconFont
                      type="iconjiaocaijiaofu"
                      style={{ color: '#549CFF', fontSize: '50px' }}
                    ></IconFont>
                  </div>
                  <div className="right_box">
                    <span>{t('知识点数')}</span>
                    <span>
                      {heardinfo.homeworks}
                      <p style={{ display: 'inline-block', fontSize: '16px' }}>
                        {t('个')}
                      </p>
                    </span>
                    <span>
                      {t('掌握率')}
                      {heardinfo.totalMasterRate}%
                    </span>
                  </div>
                  <div className="new_box">

                    <span>{`视频总时长 ${nodeInfo?.duration ? formatDuration(nodeInfo?.duration) : '0分0秒'}`}</span>
                    <span>{`课件总份数${nodeInfo?.wordsNumber ? nodeInfo?.wordsNumber: '0'}`}</span>
                    <span>{`试题总字数${ nodeInfo?.questionsNumber ? nodeInfo?.questionsNumber: '0'}`}</span>
                  </div>
                </div>

              <div className="view_box min-box">
                <div className="left_box">
                  <IconFont
                    type="iconwenhao"
                    style={{ color: '#549CFF', fontSize: '50px' }}
                  ></IconFont>
                </div>
                <div className="right_box">
                  <span>{t('学员提问数')}</span>
                  <a
                    href={`#/editcourse/${
                      location.query.type === 'mooc'
                        ? 'mooccourseqa'
                        : 'courseqa'
                    }?id=${location.query.id}&type=${
                      location.query.type
                    }&sm=${location.query.sm ?? 1}`}
                  >
                    {heardinfo.askNumber}
                    <p style={{ display: 'inline-block', fontSize: '16px' }}>
                      {t('次')}
                    </p>
                  </a>
                  <span>
                    {heardinfo.askStudentNumber || 0}/
                    {heardinfo.participateNumber || 0}
                    {t('学员进行了提问')}
                  </span>
                </div>
              </div>
            </div>
          </Access>

          </div>


          <div className="chart_boxs box_panel">
            <div className="header">
              <div className="title">

                <span>{t('学习进度分布')}</span>
              </div>
            </div>
            <div style={{ padding: '0 15px' }}>
              <Tabs
                className="study_static_tabs"
                defaultActiveKey="total"
                items={parameterConfig?.target_customer === 'sjtu'? [
                  {
                    key: 'total',
                    label: t('总览'),
                    children: (
                      <div className="row">
                        <PieChart
                          height="260px"
                          dataSource={progressData.complateData}
                        />
                        <BarChart
                          height="260px"
                          dataSource={progressData.progressDistribute}
                        />
                      </div>
                    ),
                  },
                  {
                    key: 'person',
                    label: t('按个人统计'),
                    children: (
                      <div
                        className="row"
                        style={{ transform: 'translateY(-22px)' }}
                      >
                        {progressData.personDistribute &&
                        progressData.personDistribute.length ? (
                          <Echart options={getPersonDistributeOptions} />
                        ) : (
                          <Empty style={{ marginTop: '40px' }} />
                        )}
                      </div>
                    ),
                  },
                ] :[
                  {
                    key: 'total',
                    label: t('总览'),
                    children: (
                      <div className="row">
                        <PieChart
                          height="260px"
                          dataSource={progressData.complateData}
                        />
                        <BarChart
                          height="260px"
                          dataSource={progressData.progressDistribute}
                        />
                      </div>
                    ),
                  },
                  // ( parameterConfig.target_customer !== 'scut' &&
                  {
                    key: 'college',
                    label: t('按学院统计'),
                    children: (
                      <div
                        className="row"
                        style={{ transform: 'translateY(-22px)' }}
                      >
                        {progressData.collegeDistribute &&
                        progressData.collegeDistribute.length ? (
                          <StackLineChart
                            height="282px"
                            dataSource={progressData.collegeDistribute}
                          />
                        ) : (
                          <Empty style={{ marginTop: '40px' }} />
                        )}
                      </div>
                    ),
                  },

                  {
                    key: 'person',
                    label: t('按个人统计'),
                    children: (
                      <div
                        className="row"
                        style={{ transform: 'translateY(-22px)' }}
                      >
                        {progressData.personDistribute &&
                        progressData.personDistribute.length ? (
                          <Echart options={getPersonDistributeOptions} />
                        ) : (
                          <Empty style={{ marginTop: '40px' }} />
                        )}
                      </div>
                    ),
                  },
                ]}
              />
            </div>
          </div>

          {
            isMicroMajor.current && (
              <div className="box_panel">
            <div className="chart_boxs chart_boxs_homework">
              <div className="header">
                <div className="title">
                  <span>{t('作业完成情况')}</span>
                  <div className="right">
                    <Select
                      placeholder={t('全部章')}
                      onChange={e => {
                        setHomeworkParams({
                          ...resourceStudyParams,
                          chapterId: e,
                        });
                      }}
                      allowClear
                    >
                      {chapters.map((item: any, index: number) => {
                        return (
                          <Option value={item.id} key={index}>
                            {item.name}
                          </Option>
                        );
                      })}
                    </Select>
                    <Select
                      defaultValue={homeworkParams.sortName}
                      onChange={(e, op) => {
                        setHomeworkParams({ ...homeworkParams, sortName: e });
                      }}
                    >
                      {[
                        { name: t('章节顺序'), value: t('章节') },
                        { name: t('提交人数'), value: t('提交人数') },
                        { name: t('作业得分率'), value: t('作业得分率') },
                      ].map((item: any, index: number) => {
                        return (
                          <Option value={item.value} key={index}>
                            {item.name} {<IconFont type="iconjiangxu1" />}
                          </Option>
                        );
                      })}
                    </Select>
                  </div>
                </div>
              </div>
              <div className="homeword_complate_chart_box">
                <div className="left">
                  {Object.keys(homeworkPieData).length ? (
                    <>
                      <Echart options={getHomeworkPieChart1} />
                      <Echart options={getHomeworkPieChart2} />
                    </>
                  ) : (
                    <Empty style={{ marginTop: '100px' }} />
                  )}
                </div>
                <div className="right">
                  {homeworkSubmitData.length ? (
                    <>
                      <div className="tips">
                        <div>{t('作业提交人数')}</div>
                        <Popover
                          placement="bottomRight"
                          content={t(
                            '作业得分率=已批改学员得分/已批改作业总分',
                          )}
                          trigger="hover"
                        >
                          <div>
                            {t('作业得分率')}
                            <ExclamationCircleOutlined
                              style={{
                                color: 'var(--primary-color)',
                                marginLeft: '4px',
                              }}
                            />
                          </div>
                        </Popover>
                      </div>
                      <BarLineChart
                        colors={['#C692FF', '#F9D13E']}
                        names={[t('作业得分率'), t('作业提交人数')]}
                        dataSource={homeworkSubmitData.map((el: any) => ({
                          name: el.title,
                          value1: el.stuSubmitNum,
                          value2: parseFloat(el.homeworkSoringRate),
                        }))}
                      />
                    </>
                  ) : (
                    <Empty style={{ marginTop: '100px' }} />
                  )}
                </div>
              </div>
            </div>
          </div>
            )
          }
          <Access accessible={true}>
          <div className="chart_boxs box_panel">
            <div className="header">
              <div className="title">
                <span>{t('资源学习情况')}</span>
                <div className="right">
                  <Select
                    placeholder={t('全部知识点')}
                    value={selectNodeid}
                    onChange={e => {
                      setResourceStudyParams({
                        ...resourceStudyParams,
                        chapterName: e,
                        nodeId: e,
                      });
                      getResourceStudyData({
                        ...resourceStudyParams,
                        chapterName: e,
                        nodeId: e,
                      });
                    }}
                    allowClear
                  >
                    {chapters.map((item: any, index: number) => {
                      return (
                        <Option value={item.nodeId} key={index}>
                          {item.entity}
                        </Option>
                      );
                    })}
                  </Select>
                  {/*<Select*/}
                  {/*  placeholder={t('学习要求')}*/}
                  {/*  onChange={e =>{*/}
                  {/*    setResourceStudyParams({*/}
                  {/*      ...resourceStudyParams,*/}
                  {/*      necessary: e,*/}
                  {/*    })*/}
                  {/*    getResourceStudyData({*/}
                  {/*      ...resourceStudyParams,*/}
                  {/*      necessary: e,*/}
                  {/*    });*/}
                  {/*  }*/}
                  {/*  }*/}
                  {/*  allowClear*/}
                  {/*>*/}
                  {/*  <Option value={0} key="0">*/}
                  {/*    {t('选学内容')}*/}
                  {/*  </Option>*/}
                  {/*  <Option value={1} key="1">*/}
                  {/*    {t('必学内容')}*/}
                  {/*  </Option>*/}
                  {/*</Select>*/}
                  <Select
                    defaultValue={resourceStudyParams.sortTarget}
                    onChange={(e, op) => {
                      setResourceStudyParams({
                        ...resourceStudyParams,
                        sortTarget: e,
                      });
                      getResourceStudyData({
                        ...resourceStudyParams,
                        sortTarget: e,
                      });
                    }}
                  >
                    {[
                      { name: t('学习次数'), value: 0 },
                      // { name: t('学习人数'), value: 1 },
                      // { name: t('学习完成率'), value: 2 },
                      // { name: t('下载次数'), value: 3 },
                      // { name: t('章节顺序'), value: 4 },
                    ].map((item: any, index: number) => {
                      return (
                        <Option value={item.value} key={index}>
                          {item.name} {<IconFont type="iconjiangxu1" />}
                        </Option>
                      );
                    })}
                  </Select>
                </div>
              </div>
            </div>
            <div className="resource_study_chart_box">
              <div className="tips">
                <div>{t('学生学习次数')}</div>
                <Popover
                  placement="bottomRight"
                  content={t('学习完成率=已完成学习的学生/学生总数')}
                  trigger="hover"
                >
                  <div>
                    {t('学习完成率')}
                    <ExclamationCircleOutlined
                      style={{
                        color: 'var(--primary-color)',
                        marginLeft: '4px',
                      }}
                    />
                  </div>
                </Popover>
              </div>
              {resourceStudyData.length ? (
                <BarLineChart dataSource={resourceStudyData} />
              ) : (
                <Empty />
              )}
            </div>
          </div>
          </Access>
          {
            isShowMap.current || isMicroMajor.current ? (
              <div className="box_panel">
            <div className="chart_boxs chart_boxs_homework">
              <div className="header">
                <div className="title">
                  <span>{t('知识点完成情况')}</span>
                </div>
              </div>
              <div className="homeword_complate_chart_box">
                <div className="left2">

                    <>
                      <div className="item">
                        <div className="value_box">
                          <span>
                            {Number(knowledgePointParams.questionTotal)}
                          </span>
                        </div>
                        <div className="title">
                          <span>{t('总提问数')}</span>
                        </div>
                      </div>
                      <div className="item">
                        <Progress
                          type="circle"
                          strokeWidth={16}
                          format={percent => `${percent}%`}
                          percent={Number(knowledgePointParams.totalFinishRate)}
                        />
                        <div className="title">
                          <span>{t('总完成率')}</span>
                        </div>
                      </div>
                      <div className="item">
                        <Progress
                          type="circle"
                          strokeWidth={16}
                          format={percent => `${percent}%`}
                          percent={Number(knowledgePointParams.totalMasterRate)}
                        />
                        <div className="title">
                          <span>{t('总掌握率')}</span>
                        </div>
                      </div>
                    </>

                </div>
                <div className="right2">
                  <Select
                    defaultValue={2}
                    style={{
                      width: 120,
                      position: 'absolute',
                      right: 30,
                      top: -30,
                    }}
                    onChange={handleChange}
                    options={[
                      { value: 2, label: '完成率' },
                      { value: 3, label: '掌握率' },
                    ]}
                  />
                  {/* {knowledgePointData ? <PieChartTwo dataSource={knowledgePointData} onClick={ChartClick}/> : <Empty style={{ marginTop: '40px' }} />} */}
                  {completionOfCourseObjectives}
                </div>
              </div>
            </div>
          </div>
            ) : null
          }

          <div className="box_panel">
            <div className="tabs_box">
              <Tabs
                defaultActiveKey="1"
                activeKey={selectkey}
                onChange={onChange}
                destroyInactiveTabPane
              >
                <TabPane tab={t('学员学习情况')} key="1">
                  <div className="search_box">
                    <Input.Group compact>
                      <Select defaultValue="" onChange={e => {
                        setRoles(e)
                        getStudentData(true, {isSearchRole: true, role: e})
                      }}
                        getPopupContainer={(triggerNode): any =>triggerNode.parentNode}>
                        <Option value="">{t('全部')}</Option>
                        <Option value="0">{t('学生')}</Option>
                        <Option value="1">{t('老师')}</Option>
                      </Select>
                      <Select
                      placeholder={t('请选择学院')}
                      allowClear
                      style={{ width: 300 }}
                      mode="multiple"
                      onChange={(value) => {
                        setCheckColleges(value)
                        getStudentData(true, {isSearchCollege: true, collegeList: value})
                      }}
                    >
                      {colleges.map((item: any) => (
                        <Select.Option key={item.code} value={item.name}>
                          {item.name}
                        </Select.Option>
                      ))}
                    </Select>
                      <div className="search" style={{borderRightWidth: '0'}}>
                        <Input
                          autoComplete="off"
                          placeholder={t('请输入学员姓名/学工号')}
                          onChange={(e: any) => setKeyword(e.target.value)}
                          onPressEnter={() => getStudentData(true)}
                          style={{borderRadius: 0}}
                        />
                        <SearchOutlined
                          onClick={() => getStudentData(true)}
                          title={t('搜索')}
                        />
                      </div>
                    </Input.Group>
                    <Popconfirm
                      title={t('确认导出学员学习情况?')}
                      onConfirm={exportLearning}
                      okText={t('导出')}
                      cancelText={t('取消')}
                    >
                      <Button
                        type="primary"
                        icon={<IconFont type="iconexport"></IconFont>}
                      >
                        {t('导出Excel')}
                      </Button>
                    </Popconfirm>
                  </div>
                  <Table
                    size="small"
                    dataSource={dataSource1}
                    loading={loading}
                    rowKey={(record: any) => record.userCode}
                    columns={StudentColumns}
                    rowClassName={(record, index) => {
                      return index % 2 == 0 ? 'odd' : 'aaa';
                    }}
                    pagination={{ ...pagination, size: 'small' }}
                    showSorterTooltip={false}
                    onChange={tablechange}
                  />
                </TabPane>

             <TabPane tab={'资源学习情况'} key="3">
            <div className="search_box">
              <div>
                {/* <Search className='search_input' placeholder="请输入资源名称" onSearch={(e) => setKeyword2(e)} enterButton /> */}
                <div className="search">
                  <Input
                    autoComplete="off"
                    placeholder={t('请输入资源名称')}
                    onChange={(e: any) =>
                      // setSjResourceParams(arg=>{
                      // return {
                      //   ...arg,
                      //   page:1,
                      //   resourceName: e.currentTarget.value
                      // }
                      setSearchVal(e.currentTarget.value)

                    // })
                  }
                  />
                {/*  onPressEnter={() => {*/}
                {/*  setSjResourceParams(arg=>{*/}
                {/*    arg.timesnap=Date.now()*/}
                {/*  })*/}
                {/*}}*/}
                  <SearchOutlined
                    onClick={() => {
                      setSjResourceParams(arg=>{
                        arg.timesnap=Date.now()
                      })
                    }}
                    title={t('搜索')}
                  />
                </div>
                {/*<Select*/}
                {/*  placeholder={t('全部章')}*/}
                {/*  style={{ width: 120, marginLeft: '30px' }}*/}
                {/*  onChange={e => {*/}
                {/*    setSjResourceParams(arg=>{*/}
                {/*      return {*/}
                {/*        ...arg,*/}
                {/*        current:1,*/}
                {/*        chapterName: e*/}
                {/*      }*/}
                {/*    })*/}
                {/*  }}*/}
                {/*  allowClear*/}
                {/*>*/}
                {/*  {chapters.map((item: any, index: number) => {*/}
                {/*    return (*/}
                {/*      <Option value={item.name} key={index}>*/}
                {/*        {item.name}*/}
                {/*      </Option>*/}
                {/*    );*/}
                {/*  })}*/}
                {/*</Select>*/}
                <Select
                  placeholder={t('全部类型')}
                  style={{ width: 120, marginLeft: '30px' }}
                  onChange={e => {
                    setSjResourceParams(arg=>{
                      return {
                        ...arg,
                        page:1,
                        resourceType: e
                      }
                    })
                  }}
                  allowClear
                >
                  {resourceoptions.map((item: any, index: number) => {
                    return (
                      <Option key={index} value={item.value}>
                        {item.label}
                      </Option>
                    );
                  })}
                </Select>
              </div>
              <Popconfirm
                title={t('确认导出资源学习情况?')}
                onConfirm={exportresource}
                okText={t('导出')}
                cancelText={t('取消')}
              >
                <Button
                  type="primary"
                  icon={<IconFont type="iconexport"></IconFont>}
                >
                  {t('导出Excel')}
                </Button>
              </Popconfirm>
            </div>
            <Table
              size="small"
              dataSource={sjDatasource}
              loading={loading}
              columns={sjColumns}
              rowKey={(record: any) => record.key}
              pagination={{ ...sjResourceParams, size: 'small' ,
                total:sjTotal,
                onShowSizeChange(current, size) {
                  setSjResourceParams(arg=>{
                    return {
                      ...arg,
                      page:current,
                      size:size
                    }
                  })
                },
              }}
              showSorterTooltip={false}
              onChange={(pagination,filters,sorter)=>{
                const localSort = sorter as SorterResult<any>;
                const key = localSort.columnKey as string;

                // const keyMap = {
                //   "learningNumber":0,
                //   "studyNumber":1,
                //   "completeRateConvert":2,
                //   "download":3,
                //   "chapterOrder":4,
                //   'keyDifficultPoints':5
                // }
                setSjResourceParams(arg=>{
                  return {
                    ...arg,
                    page:pagination.current,
                    size:pagination.pageSize,
                    sorting:localSort.order==='ascend'? false : true,
                    orderType:key
                  }
                })
              }}

            /></TabPane>


                {
                  isShowMap.current ? (
                    <TabPane tab={t('知识点学习情况')} key="2">
                  <div className="search_box">
                    <div>
                      <div className="search">
                        <Input
                          autoComplete="off"
                          placeholder={t('请输入知识点名称')}
                          onChange={(e: any) => setKeyword2(e.target.value)}
                          onPressEnter={() => getResourceData(true)}
                        />
                        <SearchOutlined
                          onClick={() => getResourceData(true)}
                          title={t('搜索')}
                        />
                      </div>
                    </div>
                    <Popconfirm
                      title={t('确认导出知识点学习情况?')}
                      onConfirm={exportresource}
                      okText={t('导出')}
                      cancelText={t('取消')}
                    >
                      <Button
                        type="primary"
                        icon={<IconFont type="iconexport"></IconFont>}
                      >
                        {t('导出Excel')}
                      </Button>
                    </Popconfirm>
                  </div>
                  <Table
                    size="small"
                    dataSource={dataSource2}
                    loading={loading}
                    columns={columns2}
                    rowKey={(record: any) => record.key}
                    rowClassName={(record, index) => {
                      return index % 2 == 0 ? 'odd' : 'aaa';
                    }}
                    pagination={{ ...pagination2, size: 'small' }}
                    showSorterTooltip={false}
                    onChange={tablechange2}
                  />
                </TabPane>
                  ) : null
                }
              </Tabs>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Newstatistics;
