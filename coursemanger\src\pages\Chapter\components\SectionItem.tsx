import React, { useState, useEffect, useMemo } from 'react';
import { FC } from 'react';
import StatusIcon from './StatusIcon';
import { Space, Button, message, Input, Popover } from 'antd';
import {
  PlusCircleFilled,
  CloseOutlined,
  DeleteOutlined,
  EditOutlined,
  CheckOutlined,
} from '@ant-design/icons';
import { IGlobalModelState } from '@/models/global';
import { useSelector, useLocation } from 'umi';
import { IconFont } from '@/components/iconFont';
import './ChapterSectionItem.less';
import { getSensitiveWord } from '@/utils';
import usePermission from '@/hooks/usePermission';
import useLocale from '@/hooks/useLocale';
import { CUSTOMER_NPU, CUSTOMER_PPSUC } from '@/permission/moduleCfg';

/*
 * @Author: 李晋
 * @Date: 2021-12-13 18:24:08
 * @LastEditTime: 2022-03-15 16:50:05
 * @Description: file information
 * @Company: Sobey
 */
interface ISectionProps {
  info: {
    name: string;
    children: any[];
    guid: string;
    id: string;
    layer: number;
    orders: number[];
    status: number; // "1"--已发布；“-1”--草稿；“0”--待发布
  };
  editId: string;
  canEdit: boolean;
  showChapterPre?: boolean;
  selectKeys: any[];
  onEditSuccess: (info: ISectionProps['info']) => void;
  onEdit: (info: ISectionProps['info']) => void;
  onChangeName: (info: ISectionProps['info'], inputValue: string) => void;
  onSectionDelete: (info: ISectionProps['info'], e?: React.MouseEvent) => void;
  onAdd: (info: ISectionProps['info'], type: string, e?: React.MouseEvent) => void;
  onOffItem: (itemIds: string[], status: number) => void;
  onDraftItem: (itemIds: string[], status: number) => void;
  onToPublishItem: (itemIds: string[], status: number) => void;
}
const SectionItem: FC<ISectionProps> = ({
  canEdit = true,
  editId,
  info,
  selectKeys,
  showChapterPre = true,
  onEditSuccess,
  onEdit,
  onChangeName,
  onSectionDelete,
  onAdd,
  onOffItem,
  onDraftItem,
  onToPublishItem,
}) => {
  const { t } = useLocale();
  const location: any = useLocation();
  const [inputValue, setInputValue] = useState<string>(info.name);
  const { parameterConfigObj, parameterConfig } = useSelector<any, any>(
    state => state.global,
  );
  const [isAddHomework, setIsAddHomework] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);
  const { getPermission } = usePermission();

  useEffect(() => {
    if (Object.keys(parameterConfigObj).length > 0) {
      if (location.pathname.includes('tempatedetail')) {
        setIsAddHomework(
          parameterConfigObj.kczx?.includes(
            'course_library_school_assignment_display',
          ),
        );
      } else {
        setIsAddHomework(
          getPermission(
            ['spoc', 'mooc', 'training'],
            '_school_assignment_display',
            true,
          ),
        );
      }
    }
  }, [parameterConfigObj]);
  const showCase = useMemo(() => {
    if (location.pathname.includes("tempatedetail")) {
      return parameterConfigObj.kczx?.includes('course_library_case_display');
    } else {
      return getPermission(['mooc', 'spoc', 'training', 'map'], '_case_display', true);
    }
  }, [parameterConfigObj]);
  useEffect(() => {
    console.info(showChapterPre);
  }, [showChapterPre]);

  const handleEditFinish = () => {
    if (inputValue === '') {
      message.error(t('输入不能为空！'));
      return;
    }
    onChangeName(info, inputValue);
    onEditSuccess(info);
  };
  const content = () => {
    return (
      <div>
        <div
          className="pop-item"
          onClick={e => {
            onAdd(info, "courseware", e);
            setOpen(false);
          }}
        >
          {t('课程资源')}
        </div>
        {isAddHomework && (
          <div
            className="pop-item"
            onClick={e => {
              // window.sessionStorage.setItem('addFromChapter', 'true');
              onAdd(info, "homework", e);
              setOpen(false);
            }}
          >
            {t('作业')}
          </div>
        )}
        <div
          className="pop-item"
          onClick={e => {
            onAdd(info, "reference", e);
            setOpen(false);
          }}
        >
          {t('参考资料')}
        </div>
        <div
          className="pop-item"
          onClick={e => {
            onAdd(info, "hyperlink", e);
            setOpen(false);
          }}
        >
          {t('超链接')}
        </div>
        {showCase && <div
          className="pop-item"
          onClick={e => {
            onAdd(info, "case", e);
            setOpen(false);
          }}
        >
          {t('案例')}
        </div>}
        {parameterConfig?.show_yunque_material === "true" && <div
          className="pop-item"
          onClick={e => {
            onAdd(info, "material", e);
            setOpen(false);
          }}
        >
          {t('数字教材')}
        </div>}
        {parameterConfig?.show_training_case === "true" && parameterConfig.target_customer === CUSTOMER_PPSUC && <div className='pop-item' onClick={(e) => { onAdd(info, "training_case", e); setOpen(false); }}>{t("实训案例")}</div>}
      </div>
    );
  };
  return (
    <div
      key={info.id}
      className={`learn_item_container ${info.status === 1 ? 'publish' : 'unpublish'
        } ${editId === '' ? 'noedit' : 'editing'}`}
    >
      <div className="info_container">
        <div className="item_title section_item_title">
          {showChapterPre && (
            <span className="item_order">{info.orders?.join('.')}</span>
          )}
          {editId === info.id && canEdit ? (
            <Input
              value={inputValue}
              onChange={e => {
                console.log(e);
                setInputValue(e.target.value);
              }}
              showCount
              maxLength={99}
            // onPressEnter={handleEditFinish}
            // onBlur={handleEditFinish}
            />
          ) : (
            <span className={info.status !== 1 ? 'unpublish_name' : ''}>
              {info.name}
            </span>
          )}
        </div>

        {canEdit && <StatusIcon
          canEdit={canEdit}
          info={info as any}
          className="status_icon"
          onOffItem={onOffItem}
          onDraftItem={onDraftItem}
          onToPublishItem={onToPublishItem}
        />}

        {// info.status !== 1 &&
          canEdit && // 未发布显示
          (editId !== info.id ? (
            <>
              <IconFont
                type="iconedit"
                className="delete_wrapper"
                onClick={e => {
                  e.stopPropagation();
                  onEdit(info);
                }}
              />

              <IconFont
                type="icondelete"
                className="delete_wrapper"
                onClick={e => {
                  e.stopPropagation();
                  onSectionDelete(info, e);
                }}
              />
            </>
          ) : (
            <>
              <CheckOutlined
                onClick={(e: any) => {
                  e.stopPropagation();
                  getSensitiveWord(inputValue, '小节', handleEditFinish);
                }}
              />

              <CloseOutlined
                onClick={(e: any) => {
                  e.stopPropagation();
                  setInputValue(info.name);
                  onEditSuccess(info);
                }}
              />
            </>
          ))}
      </div>
      {selectKeys.includes(info.id) &&
        // info.status !== 1 &&
        canEdit && (
          <Space className="opt_container">
            <Popover
              content={content}
              placement="bottom"
              open={open}
              trigger="click"
              onOpenChange={(open: boolean) => setOpen(open)}
            >
              <Button
                type="primary"
                ghost
                icon={<PlusCircleFilled />}
                onClick={(e: any) => e.stopPropagation()}
              >
                {t('教学内容')}
              </Button>
            </Popover>
          </Space>
        )}
    </div>
  );
};

export default SectionItem;
