import React, { useState } from 'react';
import {
    Modal,
    Form,
    Input,
    InputNumber,
    message,
    Select
} from 'antd';
import useLocale from '@/hooks/useLocale';
import './index.less'

interface CreateModalProps {
    modalVisible: boolean; // 控制弹窗显示
    modalClose: () => void; // 控制弹窗隐藏
    onSubmit: (values: MicroCourse.FormValues) => Promise<void>; // 表单提交的回调
}

const AddThirdcourseModal: React.FC<CreateModalProps> = (props) => {
    const { modalVisible, modalClose, onSubmit } = props;
    const { t } = useLocale();
    const [form] = Form.useForm();
    const [addLoading, setAddLoading] = useState<boolean>(false);

    const handleSubmit = async (values: MicroCourse.FormValues) => {
        try {
            if (addLoading) {
                return false;
            }
            setAddLoading(true);
            await onSubmit(values);
            handleClose();
            message.success('添加成功');
        } catch (error) {
            message.error('添加失败');
        } finally {
            setAddLoading(false);
        }
    };

    // 关闭弹窗
    const handleClose = () => {
        form.resetFields();
        modalClose();
    };

    return (
        <Modal
            title={t('添加课程')}
            open={modalVisible}
            onCancel={handleClose}
            onOk={() => form.submit()}
            className="Third-addcourse"
            confirmLoading={addLoading}
        >
            <Form
                form={form}
                layout="horizontal"
                onFinish={handleSubmit}
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 16 }}
            >
                <Form.Item
                    label={t('院校名称')}
                    name="school"
                    rules={[{ required: true, message: t('请输入院校名称') }]}
                >
                    <Input placeholder={t('请输入院校名称')} autoComplete="off" />
                </Form.Item>
                <Form.Item
                    label={t('课程名称')}
                    name="name_"
                    rules={[{ required: true, message: t('请输入课程名称') }]}
                >
                    <Input placeholder={t('请输入课程名称')} autoComplete="off" />
                </Form.Item>
                <Form.Item
                    label={t('课程链接')}
                    name="jump_address"
                    rules={[{ required: true, message: t('请输入课程链接') }]}
                >
                    <Input placeholder={t('请输入课程链接')} autoComplete="off" />
                </Form.Item>

                <Form.Item
                    label={t('课程封面链接')}
                    name="cover"
                    rules={[{ required: true, message: t('请输入课程封面链接') }]}
                >
                    <Input placeholder={t('请输入课程封面链接')} autoComplete="off" />
                </Form.Item>
                <Form.Item
                    label={t('授课教师')}
                    name="teacher_names"
                    rules={[
                        { 
                            required: true, 
                            message: t('请至少输入一位授课教师'),
                            type: 'array'  // 指定类型为数组
                        }
                    ]}
                >
                    <Select
                        mode="tags"
                        style={{ width: '100%' }}
                        placeholder={t('请输入授课教师')}
                        tokenSeparators={[',']}
                        maxTagCount={5}
                    />
                </Form.Item>
                <Form.Item
                    label={t(' 课程详情')}
                    name="description_"
                >
                    <Input.TextArea rows={4} placeholder={t('请输入课程详情')} autoComplete="off" />
                </Form.Item>
                <Form.Item
                    label={t('浏览量')}
                    name="hits"
                    rules={[{ required: true, message: t('请输入浏览量') }]}
                    initialValue={0}
                >
                    <InputNumber min={0} style={{ width: '100%' }} autoComplete="off" />
                </Form.Item>
            </Form>
        </Modal>
    );
}

export default AddThirdcourseModal;