enum ModuleCfg {
  micro = 'microcourse_kcgl',
  mooc = 'mooc_kcgl',
  spoc = 'spoc_kcgl',
  classreview = 'classreview_kcgl',
  mylive = 'my_live_broadcast',
  tpl_mine = 'wdkczygl',
  tpl_share = 'gxkczygl',
  training = 'training_kcgl',
  map = 'map_kcgl',
  microMajor = 'microMajor_kcgl',
  courseMap = 'coursemap',
  aiAgent = "ai_agent"
}

export enum ModuleCfg2 {
  teacher = 'wdkc', // 我的教学
  student = 'MyLearning', // 我的学习
  personal = 'PersonalCenter', // 个人中心
  admin = 'SystemManagement', // 系统管理
  manager = 'management_center', // 管理中心
  rman = 'zyzx', // 资源管理
  course = 'wdkc', //教学空间
  coursemap = 'coursemap', //课程地图'
  template = 'kczx', //课程中心
  jove = 'zxjj', // 在线剪辑
  textclip = 'textclip', // 语音剪辑
  work = 'workbench',
  exam = 'examManagement', // 试题库
  statistisc = 'statistisc', // 统计
  platformOverview = 'platformOverview', // 平台概览
  resourceOverview = 'resourceOverview', // 资源建设总量
  spjj = 'toolbox_ai_cutting',
  gif = 'toolbox_video_generate_gif',
}
export const globalParams = {
  composite_display: 'composite_display', // 合成选项隐藏
  knowledge_analysis_display: 'knowledge_analysis_display', //知识点分析隐藏
  speech_analysis_display: 'speech_analysis_display', //是否显示语音分析
  smart_tag_display: 'smart_tag_display', //是否显示智能标签
  colorimprove_display: 'colorimprove_display', //校色
  noisereduce_display: 'noisereduce_display', // 去噪
  enhance_display: 'enhance_display', //声音增强
  file_group_display: 'file_group_display', //下载是否显示原格式
  file_group_proxy_display: 'file_group_proxy_display', //下载是否显示低质量
  my_collection_display: 'my_collection_display', //是否显示我的收藏
  my_shared_display: 'my_shared_display', //是否显示我的收藏
  folder_group_display: 'folder_group_display', //是否显示群组资源
  my_video_display: 'my_video_display', //是否显示我的录播
  knowledge_map_display: 'knowledge_map_display', //是否显示知识点绑定
  vedio_resource_display: 'vedio_resource_display', //是否显示录播资源
  video_keywords_display: 'video_keywords_display', //是否显示关键词
  course_resource_area: 'course_resource_area',
};
export const CUSTOMER_NPU = "npu"
export const CUSTOMER_UTCM = "tcm"
export const CUSTOMER_PPSUC = "ppsuc"
export const CUSTOMER_YNU = "ynu"
export const CUSTOMER_SHTECH = "shangHaiTech"
export const CUSTOMER_CSU = "csu"
export const CUSTOMER_CDOU= "CDOU"
export const CUSTOMER_CXD="kczx"
export const CUSTOMER_HNLG = 'scut'
export const COURSE_TYPE = [
  'micro',
  'mooc',
  'spoc',
  'training',
  'map',
  'microMajor',
];

export const typeEn = {
  spoc: 2,
  mooc: 1,
  training: 3,
  map: 4,
  microMajor: 5,
};

export const logTypeText = [
  '对课程进行了预览。',
  '对课程进行了编辑。',
  '发起了课程发布审核。',
  '驳回了课程。',
  '课程通过审核，已发布。',
  '下架了课程。',
  '撤回了课程。',
];

export default ModuleCfg;
