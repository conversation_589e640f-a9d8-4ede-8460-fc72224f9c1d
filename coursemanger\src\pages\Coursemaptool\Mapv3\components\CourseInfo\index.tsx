// 初始化tsx 默认模版
import React, { useState, useEffect, FC } from 'react';
import { <PERSON>er, Button, Tabs, message } from 'antd';
import { getCourseInfo } from '@/api/coursemap';
import './index.less';
import useLocale from '@/hooks/useLocale';

const CourseInfo: FC<any> = ({ visible, courseid, onCancel }) => {
  //课程详情
  const [Info, setInfo] = useState<any>({});
  const { t } = useLocale();

  useEffect(() => {

    if (visible == 14 && courseid != '') {
      getCourseInfo({
        courseCode: courseid,
        isBaseInfo: true
      }).then((res) => {
        if (res.success) {
          setInfo(res.data);
        } else {
          message.error(res.message);
        }
      });
    }
  }, [courseid, visible]);

  const courseinfotab = () => {
    return (
      <div className='course_info_view'>
        <div className='course_info_item'>
          <span className='course_info_item_label'>{t("课程名称：")}</span>
          <span className='course_info_item_value'>{Info.courseName}</span>
        </div>
        <div className='course_info_item'>
          <span className='course_info_item_label'>{t("课程代码：")}</span>
          <span className='course_info_item_value'>{Info.courseId}</span>
        </div>
        <div className='course_info_item'>
          <span className='course_info_item_label'>{t("学院：")}</span>
          <span className='course_info_item_value'>{Info.collegeName}</span>
        </div>
        <div className='course_info_item'>
          <span className='course_info_item_label'>{t("修读要求：")}</span>
          {
            Info.courseRequire != null && <span className='course_info_item_value'>{Info.courseRequire == 0 ? t("必修") : t("选修")}</span>}

        </div>
        <div className='course_info_item'>
          <span className='course_info_item_label'>{t("课程负责人：")}</span>
          <span className='course_info_item_value'>{Info.userName}</span>
        </div>
        <div className='course_info_item'>
          <span className='course_info_item_label'>{t("授课学期：")}</span>
          <span className='course_info_item_value'>{Info.courseSemesters?.length > 0 ? Info.courseSemesters[0].semester.name : ''}</span>
        </div>
        <div className='course_info_item'>
          <span className='course_info_item_label'>{t("学分：")}</span>
          <span className='course_info_item_value'>{Info.credit}</span>
        </div>
        <div className='course_info_item'>
          <span className='course_info_item_label'>{t("总学时：")}</span>
          <span className='course_info_item_value'>{Info.totalClassHour}</span>
        </div>
        <div className='course_info_item'>
          <span className='course_info_item_label'>{t("周学时：")}</span>
          <span className='course_info_item_value'>{Info.weekClassHour}</span>
        </div>
        <div className='course_info_item'>
          <span className='course_info_item_label'>{t("实践学时：")}</span>
          <span className='course_info_item_value'>{Info.practiceClassHour}</span>
        </div>
        <div className='course_info_item'>
          <span className='course_info_item_label'>{t("先修课程：")}</span>
          {
            Info.beforeCourseRelations?.map((item: any, index: number) => {
              return <span className='course_info_item_value' key={index}>{item.courseName}</span>;
            })}

        </div>
        <div className='course_info_item'>
          <span className='course_info_item_label'>{t("后续课程：")}</span>
          {
            Info.nextCourseNames?.map((item: any, index: number) => {
              return <span className='course_info_item_value' key={index}>{item}</span>;
            })}

        </div>
        <div className='course_info_item'>
          <span className='course_info_item_label'>{t("课程简介：")}</span>
          {/* <span className='course_info_item_value'>{Info.desc}</span> */}
        </div>

        <div style={{ color: '#fff' }}>
          <p>{Info.desc}</p>
        </div>
      </div>);

  };

  return (
    <Drawer
      placement="right"
      mask={false}
      closable={false}
      onClose={onCancel}
      title={
        <div style={{ position: 'relative', width: '100%', height: '100%' }}>
          <img style={{ width: '100%', height: 'auto' }} src={require('@/assets/imgs/coursemap/v3/title_bg.png')}></img>
          <img onClick={() => onCancel(0)} style={{ position: 'absolute', right: '20px', width: '15px', top: '18px' }} src={require('@/assets/imgs/coursemap/v3/close.png')} alt="" />
        </div>}

      visible={visible == 14}
      getContainer={false}
      style={{ position: 'absolute' }}
      width="500px"
      className="courseinfo_drawer">

      <Tabs
        defaultActiveKey="1"
        onChange={() => { }}
        items={[
          {
            label: t(`基本信息`),
            key: '1',
            children: courseinfotab()
          }
          //   {
          //     label: `课程大纲`,
          //     key: '2',
          //     children: `Content of Tab Pane 2`,
          //   }                
        ]} />

    </Drawer>);

};


export default CourseInfo;