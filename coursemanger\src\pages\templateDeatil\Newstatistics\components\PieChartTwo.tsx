import React, { FC, useState, useEffect } from 'react';
import ReactEcharts from 'echarts-for-react';
import './PieChartTwo.less';
import useLocale from '@/hooks/useLocale';
import { color } from 'echarts';
import { TEST_ONEW } from '../testData';

const PieChartTwo = ({ dataSource, leftActiveTab, height, chartsDes }: any) => {
  // console.log(leftActiveTab);
  const [option, setOption] = useState<any>({});
  useEffect(() => {
    if (dataSource && dataSource.length) {
      if (leftActiveTab && leftActiveTab == 2) {
        setOption(getOption2());
      } else {
        setOption(getOption());
      }
    }
  }, [dataSource]);
  const getOption = () => {
    // const total = dataSource.reduce((prev: any, curr: any) => (prev?.value || 0) + (curr?.value || 0));
    let data: any[] = [];
    if (dataSource && dataSource.length) {
      data = dataSource;
      // data = TEST_ONEW;
    }
    let total = data
      .map((el: { value: any }) => el.value)
      .reduce((a: any, b: any) => a + b, 0);

    let rate = total * 0.02;
    data = data.map((el: { value: number }, index: number) => {
      let start = 0;
      let end = 0;
      if (index == 0) {
        start = 0;
        end = el.value;
      } else {
        for (let i = 0; i <= index; i++) {
          if (i < index) {
            start += data[i].value;
          }
          end += data[i].value;
        }
      }
      return Object.assign(el, { start, end });
    });
    // console.log(data,'data')
    return {
      polar: {
        center: ['50%', '50%'],
      },
      legend: {
        data: [
          {
            name: 111,
          },
        ],
      },
      tooltip: {
        show: true,
        trigger: 'item',
      },
      angleAxis: {
        type: 'value',
        min: 0,
        max: total,
        interval: 1,
        axisLine: false,
        axisLabel: false,
        axisTick: false,
      },
      radiusAxis: {
        show: false,
      },
      animationDuration: 500,
      series: [
        ...data.map(
          (
            el: { data: any[]; name: any; start: number; end: number },
            index: any,
          ) => {
            return {
              coordinateSystem: 'polar',
              name: 'scatter',
              type: 'scatter',
              data: el.data.map((d: any) => {
                //#62C20E
                let colorPick = ['#62C20E', '#00B6F8', '#FFAB15', '#52C6D4'];
                let color = '';
                if (
                  d.value <= 100 &&
                  d.value > 70 &&
                  d.masterNotExamine === 1
                ) {
                  color = colorPick[0];
                } else if (
                  d.value <= 70 &&
                  d.value > 30 &&
                  d.masterNotExamine === 1
                ) {
                  color = colorPick[1];
                } else if (
                  d.value <= 30 &&
                  d.value > 0 &&
                  d.masterNotExamine === 1
                ) {
                  color = colorPick[2];
                } else if (d.value == 0 && d.masterNotExamine === 1) {
                  color = '#fff';
                  return {
                    name: d.name + ': ' + d.value,
                    value: [
                      Math.random() * 10 + 7,
                      el.start +
                      rate +
                      Math.random() * (el.end - el.start - rate * 2),
                    ],
                    itemStyle: {
                      color: color,
                      borderWidth: 1,
                      borderColor: '#a8a8a8',
                    },
                  };
                } else {
                  color = colorPick[3];
                }
                return {
                  name: d.name + ': ' + d.value,
                  value: [
                    Math.random() * 10 + 7,
                    el.start +
                    rate +
                    Math.random() * (el.end - el.start - rate * 2),
                  ],
                  itemStyle: {
                    color: color,
                  },
                };
              }),
              tooltip: {
                formatter: '{b0}%',
              },
            };
          },
        ),
        {
          name: '外圆盘',
          type: 'pie',
          radius: '90%',
          center: 'center',
          itemStyle: {
            color: '#e4eefa',
          },
          data: [0],
          animation: false,
          labelLine: false,
          tooltip: false,
          hoverAnimation: false,
          silent: true,
        },
        {
          name: '内圆盘',
          type: 'pie',
          radius: ['25%', '85%'],
          center: 'center',
          data: data,
          label: {
            color: '#000',
          },
          itemStyle: {
            color: '#fff',
            borderColor: '#e4eefa',
            borderWidth: 10,
          },
          silent: true,
          animation: false,
          tooltip: false,
          animationType: false,
          hoverAnimation: false,
        },
      ],
    };
  };
  const getOption2 = () => {
    // console.log('dataSource', dataSource);
    // const test1 =
    // const total = dataSource.reduce((prev: any, curr: any) => (prev?.value || 0) + (curr?.value || 0));
    let data: any[] = [];
    if (dataSource && dataSource.length) {
      data = dataSource;
    }
    let total = data
      .map((el: { value: any }) => el.value)
      .reduce((a: any, b: any) => a + b, 0);

    let rate = total * 0.02;
    data = data.map((el: { value: number }, index: number) => {
      let start = 0;
      let end = 0;
      if (index == 0) {
        start = 0;
        end = el.value;
      } else {
        for (let i = 0; i <= index; i++) {
          if (i < index) {
            start += data[i].value;
          }
          end += data[i].value;
        }
      }
      return Object.assign(el, { start, end });
    });
    return {
      polar: {
        center: ['50%', '50%'],
      },
      legend: {
        data: [
          {
            name: 111,
          },
        ],
      },
      tooltip: {
        show: true,
        trigger: 'item',
      },
      angleAxis: {
        type: 'value',
        min: 0,
        max: total,
        interval: 1,
        axisLine: false,
        axisLabel: false,
        axisTick: false,
      },
      radiusAxis: {
        show: false,
      },
      animationDuration: 500,
      series: [
        ...data.map(
          (
            el: { data: any[]; name: any; start: number; end: number },
            index: any,
          ) => {
            return {
              coordinateSystem: 'polar',
              name: 'scatter',
              type: 'scatter',
              data: el.data.map((d: any) => {
                let colorPick = ['#62C20E', '#00B6F8', '#FFAB15', '#52C6D4'];
                let color = '';
                if (
                  d.value <= 100 &&
                  d.value > 70 &&
                  d.finishNotExamine === 1
                ) {
                  color = colorPick[0];
                } else if (
                  d.value <= 70 &&
                  d.value > 30 &&
                  d.finishNotExamine === 1
                ) {
                  color = colorPick[1];
                } else if (
                  d.value <= 30 &&
                  d.value > 0 &&
                  d.finishNotExamine === 1
                ) {
                  color = colorPick[2];
                } else if (d.value == 0 && d.finishNotExamine === 1) {
                  color = '#fff';
                  return {
                    name: d.name + ': ' + d.value,
                    value: [
                      Math.random() * 10 + 7,
                      el.start +
                      rate +
                      Math.random() * (el.end - el.start - rate * 2),
                    ],
                    itemStyle: {
                      color: color,
                      borderWidth: 1,
                      borderColor: '#a8a8a8',
                    },
                  };
                } else {
                  color = colorPick[3];
                }
                return {
                  name: d.name + ': ' + d.value,
                  value: [
                    Math.random() * 10 + 7,
                    el.start +
                    rate +
                    Math.random() * (el.end - el.start - rate * 2),
                  ],
                  itemStyle: {
                    color: color,
                  },
                };
              }),
              tooltip: {
                formatter: '{b0}%',
              },
            };
          },
        ),
        {
          name: '外圆盘',
          type: 'pie',
          radius: '90%',
          center: 'center',
          itemStyle: {
            color: '#e4eefa',
          },
          data: [0],
          animation: false,
          labelLine: false,
          tooltip: false,
          hoverAnimation: false,
          silent: true,
        },
        {
          name: '内圆盘',
          type: 'pie',
          radius: ['25%', '85%'],
          center: 'center',
          data: data,
          label: {
            color: '#000',
          },
          itemStyle: {
            color: '#fff',
            borderColor: '#e4eefa',
            borderWidth: 10,
          },
          silent: true,
          animation: false,
          tooltip: false,
          animationType: false,
          hoverAnimation: false,
        },
      ],
    };
  };
  const handleClick = (params: any) => {
    // console.log(params);
    // onClick(params.name);
    const clickedName = params.name;
  };
  return (
    <div className="study_pie_chart">
      {/* <span>课程笔记数目</span> */}
      <ReactEcharts
        style={{ height: height || '300px' }}
        option={option}
        onEvents={{ click: handleClick }}
        className="chart"
      />
      <div className='pie_title'>
        {chartsDes && <div className='charts_des'>{chartsDes}</div>}
        <div className="legend">
          <span>
            <span className="dot" style={{ backgroundColor: '#62C20E' }}></span>{' '}
            100%-71%
          </span>
          <span>
            <span className="dot" style={{ backgroundColor: '#00B6F8' }}></span>{' '}
            70%-31%
          </span>
          <span>
            <span className="dot" style={{ backgroundColor: '#FFAB15' }}></span>{' '}
            30%-1%
          </span>
          <span>
            <span
              className="dot"
              style={{ backgroundColor: '#fff', border: '1px solid #a8a8a8' }}
            ></span>{' '}
            0%
          </span>
          <span>
            <span className="dot" style={{ backgroundColor: '#52C6D4' }}></span>{' '}
            免考核
          </span>
        </div>
      </div>
    </div>
  );
};

export default PieChartTwo;
