import perCfg from '@/permission/config';
import ModuleCfg from '@/permission/moduleCfg';
import { useLocation, useSelector } from 'umi';

export const usePermission = () => {
  const location: any = useLocation();
  const { parameterConfigObj } = useSelector<any, any>(state => state.global);
  const parameterConfig = useSelector<any, any>(
    ({ global }) => global.parameterConfig);
  const jurisdictionList = useSelector<any, any>(
    ({ jurisdiction }) => jurisdiction.jurisdictionList,
  );

  const getPermission = (
    types: Array<keyof typeof ModuleCfg>,
    permissions: string[] | string,
    addPrefix: boolean = false,
    courseType?: string,
  ) => {
    // console.log(permissions, parameterConfigObj);

    return types.reduce(
      (acc, type, index) =>
        acc ||
        (parameterConfigObj[ModuleCfg[type]]?.includes(
          permissions instanceof Array
            ? permissions[index]
            : addPrefix
            ? `${type}${permissions}`
            : permissions,
        ) &&
          (courseType ?? location.query.type) === type),
      false,
    );
  };

  const courseTypes = [
    'microcourse',
    'mooc',
    'spoc',
    'training',
    'map',
    'microMajor',
  ] as const;

  const getOptAuth = (
    opt: string,
    curType: number | null,
    types: Array<number> = [0, 1, 2, 3, 4, 5],
  ) => {
    return types.reduce(
      (acc, type) =>
        acc ||
        (curType === type &&
          jurisdictionList.includes(
            perCfg[`${courseTypes[type]}_${opt}` as keyof typeof perCfg],
          )),
      false,
    );
  };

  const hasPermission = (permissionKey: string) => {
    return jurisdictionList?.includes(permissionKey);
  };


  /**
   * 获取课程参数
   * @param key 参数键名，如 'display_column' 
   * @param courseType 课程类型，默认使用当前路由中的类型
   * @param splitChar 如果需要分割成数组，提供分割字符
   * @returns 对应的参数值，如果未找到则返回空字符串；如果提供splitChar则返回分割后的数组
   */
  const getCourseParameter = (key: string, courseType?: string, splitChar?: string): string | string[] => {
    const type = courseType || location.query.type;
    const paramKey = `${type}_${key}`;
    const value = parameterConfig[paramKey] || '';
    
    return splitChar && value ? value.split(splitChar) : value;
  };

  return {
    getPermission,
    getOptAuth,
    hasPermission,
    getCourseParameter,
  };
};

export default usePermission;
